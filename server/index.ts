// This is an ultra-minimal server script that just executes the Vite CLI command
// No additional dependencies needed

import { spawn } from 'child_process';
import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import { WebSocketServer, WebSocket } from 'ws';
import http from 'http';
import bodyParser from 'body-parser';
import multer from 'multer';
import fs from 'fs/promises';
import crypto from 'crypto';

console.log("🚀 Starting CoinScout Frontend Application...");
console.log("📢 NOTE: Backend has been removed. All data operations will be performed through the external PHP API.");

// Get the directory name for the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Check if we're in production mode
const isProd = process.env.NODE_ENV === 'production';

// Notification Clients
const clients = new Map<string, WebSocket>();

// Helper to validate JWT tokens (simplified for demo)
const validateToken = (token: string) => {
  // In a real implementation, this would validate the JWT token
  // For now, we'll just return a mock user ID if the token is not empty
  if (!token) return null;
  try {
    // Simulate checking a token
    return { userId: 'demo-user-id', username: 'demo-user' };
  } catch (error) {
    console.error('Token validation error:', error);
    return null;
  }
};

// Setup WebSocket server handlers
const setupWebSocketServer = (server: http.Server) => {
  // Create WebSocket server on a specific path to avoid conflicts with Vite HMR
  const wss = new WebSocketServer({ server, path: '/ws' });

  console.log('WebSocket server initialized on path /ws');

  wss.on('connection', (ws) => {
    let userId: string | null = null;

    console.log('WebSocket client connected');

    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        console.log('Received message:', data);

        if (data.type === 'auth') {
          // Authenticate the client
          const userData = validateToken(data.token);
          if (userData) {
            userId = userData.userId;
            clients.set(userId, ws);
            ws.send(JSON.stringify({
              type: 'auth_response',
              status: 'success',
              userData: { id: userData.userId, username: userData.username }
            }));

            // In a real implementation, this would query the database for unread notifications
            // For now, we'll only send unread notifications if there are actual ones
            // No automatic mock notifications on connection

            console.log(`Client authenticated: ${userId}`);
          } else {
            ws.send(JSON.stringify({
              type: 'auth_response',
              status: 'error',
              message: 'Invalid authentication token'
            }));
          }
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    });

    ws.on('close', () => {
      if (userId) {
        clients.delete(userId);
        console.log(`Client disconnected: ${userId}`);
      } else {
        console.log('Unauthenticated client disconnected');
      }
    });
  });

  return {
    // Function to send notification to a specific user
    sendNotification: (userId: string, notification: any) => {
      const client = clients.get(userId);
      if (client && client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({
          type: 'new_notification',
          notification: notification
        }));
        return true;
      }
      return false;
    },
    // Function to broadcast a notification to all connected clients
    broadcastNotification: (notification: any) => {
      let sentCount = 0;
      clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify({
            type: 'new_notification',
            notification: notification
          }));
          sentCount++;
        }
      });
      return sentCount;
    },
    // Function to send crypto price alerts
    sendCryptoAlert: (userId: string, alertData: { coinId: string, coinName: string, coinSymbol: string, currentPrice: number, targetPrice: number, alertType: 'above' | 'below' }) => {
      const notification = {
        id: `crypto-alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: `${alertData.coinName} Fiyat Uyarısı`,
        message: `${alertData.coinSymbol} ${alertData.alertType === 'above' ? 'hedef fiyatın üzerine çıktı' : 'hedef fiyatın altına düştü'}: $${alertData.currentPrice.toLocaleString()}`,
        type: 'price',
        priority: 'high',
        status: 'unread',
        timestamp: new Date(),
        coinId: alertData.coinId,
        coinName: alertData.coinName,
        coinSymbol: alertData.coinSymbol,
        data: {
          currentPrice: alertData.currentPrice,
          targetPrice: alertData.targetPrice,
          alertType: alertData.alertType
        }
      };

      const client = clients.get(userId);
      if (client && client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({
          type: 'new_notification',
          notification: notification
        }));
        return true;
      }
      return false;
    },
    // Function to broadcast system notifications
    broadcastSystemNotification: (title: string, message: string, priority: 'high' | 'medium' | 'low' = 'medium') => {
      const notification = {
        id: `system-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: title,
        message: message,
        type: 'system',
        priority: priority,
        status: 'unread',
        timestamp: new Date()
      };

      let sentCount = 0;
      clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify({
            type: 'new_notification',
            notification: notification
          }));
          sentCount++;
        }
      });
      return sentCount;
    }
  };
};

if (isProd) {
  // In production, serve static files directly
  const app = express();
  const distPath = path.resolve(__dirname, '../dist/public');

  // Parse JSON request bodies
  app.use(bodyParser.json());

  // Multer configuration for file uploads
  const storage = multer.diskStorage({
    destination: async (req, file, cb) => {
      const uploadDir = path.resolve(__dirname, '../uploads/avatars');
      try {
        await fs.mkdir(uploadDir, { recursive: true });
        cb(null, uploadDir);
      } catch (error) {
        cb(error as Error, '');
      }
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + crypto.randomBytes(6).toString('hex');
      const extension = path.extname(file.originalname);
      cb(null, `avatar-${uniqueSuffix}${extension}`);
    }
  });

  const upload = multer({
    storage,
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, cb) => {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Sadece JPEG, PNG, GIF ve WebP formatları desteklenmektedir.'));
      }
    }
  });

  // Serve static files
  app.use(express.static(distPath));

  // Serve uploaded files
  app.use('/uploads', express.static(path.resolve(__dirname, '../uploads')));

  // Avatar endpoints - using predefined avatars instead of file uploads

  // Get all available avatars
  app.get('/api/avatars', async (req, res) => {
    try {
      res.json({
        success: true,
        data: {
          avatars: [],
          categories: []
        },
        message: 'Avatars retrieved successfully'
      });
    } catch (error) {
      console.error('Avatar fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Avatarlar yüklenirken bir hata oluştu.'
      });
    }
  });

  // Get avatar by ID
  app.get('/api/avatars/:id', async (req, res) => {
    try {
      const avatarId = parseInt(req.params.id);

      if (isNaN(avatarId)) {
        return res.status(400).json({
          success: false,
          message: 'Geçersiz avatar ID'
        });
      }

      res.json({
        success: true,
        data: {
          avatar: null
        },
        message: 'Avatar retrieved successfully'
      });
    } catch (error) {
      console.error('Avatar fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Avatar yüklenirken bir hata oluştu.'
      });
    }
  });

  // Update user avatar (save selected avatar ID)
  app.post('/api/profile/avatar', async (req, res) => {
    try {
      const { avatarId } = req.body;

      if (!avatarId || isNaN(parseInt(avatarId))) {
        return res.status(400).json({
          success: false,
          message: 'Geçersiz avatar ID'
        });
      }

      res.json({
        success: true,
        data: {
          avatarId: parseInt(avatarId)
        },
        message: 'Avatar başarıyla güncellendi!'
      });
    } catch (error) {
      console.error('Avatar update error:', error);
      res.status(500).json({
        success: false,
        message: 'Avatar güncellenirken bir hata oluştu.'
      });
    }
  });

  // Get current user's avatar
  app.get('/api/profile/avatar', async (req, res) => {
    try {
      res.json({
        success: true,
        data: {
          avatarId: null,
          avatarUrl: null
        },
        message: 'User avatar retrieved successfully'
      });
    } catch (error) {
      console.error('User avatar fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Kullanıcı avatarı yüklenirken bir hata oluştu.'
      });
    }
  });

  // Create HTTP server
  const port = parseInt(process.env.PORT || '5000');
  const httpServer = http.createServer(app);

  // Setup WebSocket server
  const notificationService = setupWebSocketServer(httpServer);

  // API endpoint for real-time notifications
  app.post('/api/notification', (req, res) => {
    try {
      // Validate the incoming data
      const { title, message, type, priority, userId, coinData, alertId } = req.body;

      if (!title || !message || !type) {
        return res.status(400).json({
          success: false,
          message: 'Missing required fields (title, message, type)'
        });
      }

      // Create notification object
      const notification = {
        id: alertId || `alert-${Date.now()}`,
        title,
        message,
        type,
        priority: priority || 'medium',
        status: 'unread',
        timestamp: new Date(),
        // Add coin data if provided
        ...(coinData ? {
          coinId: coinData.id,
          coinName: coinData.name,
          coinSymbol: coinData.symbol,
          link: coinData.id ? `/coin/${coinData.id}` : undefined
        } : {})
      };

      // For targeted notification to specific user
      if (userId) {
        const sent = notificationService.sendNotification(userId, notification);
        if (sent) {
          console.log(`Sent notification to user ${userId}:`, notification);
          return res.status(200).json({
            success: true,
            message: `Notification sent to user ${userId}`,
            notification
          });
        } else {
          console.log(`User ${userId} not connected, notification not delivered`);
          return res.status(202).json({
            success: false,
            message: `User ${userId} not connected, notification not delivered`,
            notification
          });
        }
      }
      // For broadcast notification to all users
      else {
        const sentCount = notificationService.broadcastNotification(notification);
        console.log(`Broadcast notification to ${sentCount} clients:`, notification);

        return res.status(200).json({
          success: true,
          message: `Notification broadcast to ${sentCount} clients`,
          notification
        });
      }
    } catch (error) {
      console.error('Error processing notification:', error);
      res.status(500).json({
        success: false,
        message: 'Error processing notification request'
      });
    }
  });

  // For any other routes, serve index.html (SPA fallback)
  app.get('*', (req, res) => {
    res.sendFile(path.join(distPath, 'index.html'));
  });

  // Start server
  httpServer.listen(port, '0.0.0.0', () => {
    console.log(`Server running on port ${port} with WebSocket support`);
    console.log('Notification service is ready for real-time alerts');
  });
} else {
  // In development, start Express server with WebSocket support to run alongside Vite
  const app = express();
  const httpServer = http.createServer(app);

  // Parse JSON request bodies
  app.use(bodyParser.json());

  // Multer configuration for file uploads (development)
  const storage = multer.diskStorage({
    destination: async (req, file, cb) => {
      const uploadDir = path.resolve(__dirname, '../uploads/avatars');
      try {
        await fs.mkdir(uploadDir, { recursive: true });
        cb(null, uploadDir);
      } catch (error) {
        cb(error as Error, '');
      }
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + crypto.randomBytes(6).toString('hex');
      const extension = path.extname(file.originalname);
      cb(null, `avatar-${uniqueSuffix}${extension}`);
    }
  });

  const upload = multer({
    storage,
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, cb) => {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Sadece JPEG, PNG, GIF ve WebP formatları desteklenmektedir.'));
      }
    }
  });

  // Serve uploaded files
  app.use('/uploads', express.static(path.resolve(__dirname, '../uploads')));

  // Avatar endpoints - using predefined avatars instead of file uploads

  // Get all available avatars
  app.get('/api/avatars', async (req, res) => {
    try {
      res.json({
        success: true,
        data: {
          avatars: [],
          categories: []
        },
        message: 'Avatars retrieved successfully'
      });
    } catch (error) {
      console.error('Avatar fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Avatarlar yüklenirken bir hata oluştu.'
      });
    }
  });

  // Get avatar by ID
  app.get('/api/avatars/:id', async (req, res) => {
    try {
      const avatarId = parseInt(req.params.id);

      if (isNaN(avatarId)) {
        return res.status(400).json({
          success: false,
          message: 'Geçersiz avatar ID'
        });
      }

      res.json({
        success: true,
        data: {
          avatar: null
        },
        message: 'Avatar retrieved successfully'
      });
    } catch (error) {
      console.error('Avatar fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Avatar yüklenirken bir hata oluştu.'
      });
    }
  });

  // Update user avatar (save selected avatar ID)
  app.post('/api/profile/avatar', async (req, res) => {
    try {
      const { avatarId } = req.body;

      if (!avatarId || isNaN(parseInt(avatarId))) {
        return res.status(400).json({
          success: false,
          message: 'Geçersiz avatar ID'
        });
      }

      res.json({
        success: true,
        data: {
          avatarId: parseInt(avatarId)
        },
        message: 'Avatar başarıyla güncellendi!'
      });
    } catch (error) {
      console.error('Avatar update error:', error);
      res.status(500).json({
        success: false,
        message: 'Avatar güncellenirken bir hata oluştu.'
      });
    }
  });

  // Get current user's avatar
  app.get('/api/profile/avatar', async (req, res) => {
    try {
      res.json({
        success: true,
        data: {
          avatarId: null,
          avatarUrl: null
        },
        message: 'User avatar retrieved successfully'
      });
    } catch (error) {
      console.error('User avatar fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Kullanıcı avatarı yüklenirken bir hata oluştu.'
      });
    }
  });

  // Setup WebSocket server
  const notificationService = setupWebSocketServer(httpServer);

  // API endpoint for real-time notifications
  app.post('/api/notification', (req, res) => {
    try {
      // Validate the incoming data
      const { title, message, type, priority, userId, coinData, alertId } = req.body;

      if (!title || !message || !type) {
        return res.status(400).json({
          success: false,
          message: 'Missing required fields (title, message, type)'
        });
      }

      // Create notification object
      const notification = {
        id: alertId || `alert-${Date.now()}`,
        title,
        message,
        type,
        priority: priority || 'medium',
        status: 'unread',
        timestamp: new Date(),
        // Add coin data if provided
        ...(coinData ? {
          coinId: coinData.id,
          coinName: coinData.name,
          coinSymbol: coinData.symbol,
          link: coinData.id ? `/coin/${coinData.id}` : undefined
        } : {})
      };

      // For targeted notification to specific user
      if (userId) {
        const sent = notificationService.sendNotification(userId, notification);
        if (sent) {
          console.log(`Sent notification to user ${userId}:`, notification);
          return res.status(200).json({
            success: true,
            message: `Notification sent to user ${userId}`,
            notification
          });
        } else {
          console.log(`User ${userId} not connected, notification not delivered`);
          return res.status(202).json({
            success: false,
            message: `User ${userId} not connected, notification not delivered`,
            notification
          });
        }
      }
      // For broadcast notification to all users
      else {
        const sentCount = notificationService.broadcastNotification(notification);
        console.log(`Broadcast notification to ${sentCount} clients:`, notification);

        return res.status(200).json({
          success: true,
          message: `Notification broadcast to ${sentCount} clients`,
          notification
        });
      }
    } catch (error) {
      console.error('Error processing notification:', error);
      res.status(500).json({
        success: false,
        message: 'Error processing notification request'
      });
    }
  });

  // Start WebSocket server on port 8081 (different from Vite dev server)
  const wsPort = parseInt(process.env.WS_PORT || '8081');
  httpServer.listen(wsPort, '0.0.0.0', () => {
    console.log(`WebSocket server running on port ${wsPort}`);

    // Bildirim servisi hazır, gerçek veriler için hazır
    console.log('Notification service is ready for real-time alerts');
  });

  // In development, use Vite dev server
  // Using --port 5000 to match Replit's expected port
  const viteDevServer = spawn('npx', ['vite', '--port', '5000', '--host', '0.0.0.0'], {
    stdio: 'inherit',
    shell: true
  });

  // Handle process events
  viteDevServer.on('error', (err) => {
    console.error('Failed to start Vite:', err);
    process.exit(1);
  });

  viteDevServer.on('close', (code) => {
    console.log(`Vite process exited with code ${code}`);
    process.exit(code || 0);
  });

  // Handle termination signals
  process.on('SIGINT', () => {
    viteDevServer.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    viteDevServer.kill('SIGTERM');
  });
}