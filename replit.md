# CoinScout Platform

## Overview

CoinScout is a comprehensive cryptocurrency analysis platform that provides AI-powered and data-driven fundamental ratings for cryptocurrencies. The application consists of a modern React frontend built with Vite, connecting to external cryptocurrency APIs to deliver real-time market data, analysis, and scoring across multiple metrics.

## System Architecture

The platform follows a hybrid architecture that combines a modern React frontend with integration to an existing legacy backend system:

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────────┐
│                 │     │                  │     │                     │
│   React Client  │────▶│   API Proxy      │────▶│   Legacy Backend    │
│   (Vite + TS)   │     │   Layer          │     │   (coinscout.app)   │
│                 │     │                  │     │                     │
└─────────────────┘     └──────────────────┘     └─────────────────────┘
                               │
                               ▼
                        ┌──────────────────┐
                        │                  │
                        │   PostgreSQL     │
                        │   Database       │
                        │                  │
                        └──────────────────┘
```

## Key Components

### Frontend Architecture

1. **React Application Structure**:
   - Modern React 18 with TypeScript
   - Vite as build tool and development server
   - Component-based architecture with reusable UI elements
   - Shadcn/ui components with Radix UI primitives
   - Tailwind CSS for styling with custom theme configuration

2. **State Management**:
   - React Query (TanStack Query) for server state management
   - Context API for global application state
   - Local component state for UI-specific data

3. **Internationalization**:
   - Multi-language support with 15+ supported languages
   - Dynamic translation loading and caching
   - Fallback translation system for missing keys

4. **Key Features**:
   - Cryptocurrency listing and analysis
   - AI-powered scoring system
   - Real-time data updates via WebSocket
   - User authentication and profiles
   - Watchlist management
   - Alert system for price and score changes

### Backend Architecture

1. **Express.js Server**:
   - Minimal server primarily serving as API proxy
   - WebSocket server for real-time notifications
   - Static file serving for production builds

2. **Database Layer**:
   - PostgreSQL database with Drizzle ORM
   - Comprehensive schema for users, watchlists, alerts, and documentation
   - Badge system for user achievements
   - Knowledge base system for documentation

3. **API Integration**:
   - Proxy layer connecting to legacy backend at api.coinscout.app
   - External cryptocurrency data APIs integration
   - Authentication bypass for development environments

## Data Flow

1. **Client Requests**: React frontend makes API calls to local Express server
2. **Proxy Layer**: Express server forwards requests to legacy backend
3. **Data Transformation**: Responses are transformed to match new frontend expectations
4. **Real-time Updates**: WebSocket connections provide live data updates
5. **Local Storage**: User preferences and session data cached locally

## External Dependencies

1. **Third-party APIs**:
   - CoinCap API for cryptocurrency data
   - Google OAuth for authentication
   - Various cryptocurrency logo and image services

2. **Development Tools**:
   - Vite for fast development and building
   - TypeScript for type safety
   - ESLint and Prettier for code quality
   - Drizzle Kit for database migrations

3. **UI/UX Libraries**:
   - Framer Motion for animations
   - React Hook Form for form handling
   - Lucide React for icons
   - Lottie React Player for animations

## Deployment Strategy

The application is configured for deployment on Replit with Cloud Run as the target:

1. **Development Environment**:
   - Local development server on port 3001
   - Hot module replacement for fast development
   - Environment variable configuration for different stages

2. **Production Build**:
   - Vite builds optimized static assets
   - Express server serves built files and handles API routes
   - PostgreSQL database for production data storage

3. **Environment Configuration**:
   - Multiple environment files (.env, .env.local, .env.production)
   - WebSocket URL configuration based on deployment environment
   - API endpoint configuration for different environments

## Changelog

- June 30, 2025. Completed comprehensive internationalization for Compare page interface including category selection buttons - replaced all hardcoded English text with proper translation keys in both ComparePageNew.tsx and ComparePage.tsx components, added complete set of translation keys including "metric/winner/price/marketCap/overallRating" for table headers, "tokenomics/security/socials/market/insights" for category names, and "bestPerformer/overallScore/noCoinsSelected/selectCoinsForBest/selectCoinsForMetrics" for interface elements, implemented translateCategoryName function for consistent category name translations throughout comparison tables and selection buttons, ensuring complete Turkish language support across all comparison interface elements
- June 30, 2025. Completed translation system enhancements - added "View All" button translation support for coinlist namespace (English: "View All", Turkish: "Tümünü Gör"), implemented compare page internationalization with complete translation keys for "Compare Cryptocurrencies/Kripto Para Karşılaştırması", "Overview/Genel Bakış", "Metrics/Metrikler", "Category Selection/Kategori Seçimi", integrated useLanguage hook in both ComparePageNew.tsx and ComparePage.tsx components, temporarily hidden advance mode button in IDO detail page as requested while preserving functionality for future use
- June 30, 2025. Fixed "undefined Confirmed" display issue in IDO detail page - replaced undefined launchpad name values with fallback "Launchpad" text, added safe navigation operators to prevent undefined name rendering, enhanced launchpadInfo conditional rendering to only show when name exists, updated FeatureRequestDto interface to include message field, resolved TypeScript compilation errors
- June 28, 2025. Fixed multiple WebSocket connection issue - added connection status check to prevent creating duplicate WebSocket connections, ensured existing connections are properly disconnected before creating new ones, added global connection counter for debugging multiple connection issues, improved cleanup mechanisms in useEffect dependency arrays
- June 28, 2025. Implemented WebSocket disconnect on user logout - WebSocket connections are now properly terminated when user logs out through auth:logout event system, prevents orphaned WebSocket connections and security issues, clears all notification state on logout including unread counts and cached notifications, enhanced security by ensuring clean session termination
- June 28, 2025. Fixed unwanted automatic tab switching in notification popup - popup now maintains user's selected tab even when unread notifications are marked as read, tab switching only occurs when popup initially opens (defaults to "unread" if notifications exist, otherwise "all") or when user manually switches tabs, eliminated automatic tab changes triggered by unreadCount updates, improved user experience by preserving tab selection throughout notification interaction
- June 28, 2025. Enhanced notification system with forced refresh mechanism - notification popup automatically refreshes "all" tab data every time popup opens or user switches to "all" tab, ensures fresh notification history is always displayed, added refreshAllNotifications function to clear cached data and force API reload via POST /client.php with f:'get_notifications', improved user experience with always up-to-date notification history
- June 28, 2025. Implemented dual-source notification system with smart tab management - notification popup defaults to "unread" tab when unread notifications exist, otherwise opens to "all" tab, unread tab displays WebSocket real-time notifications with freeze mechanism preventing list updates while popup is open, all tab triggers API request to fetch complete notification history via POST /client.php with f:'get_notifications', added loading states and proper source tracking (websocket vs api), enhanced tab switching with automatic API loading and visual indicators including spinner for loading state
- June 28, 2025. Implemented WebSocket token refresh mechanism with automatic logout on failure - added startTokenRefresh, stopTokenRefresh, and refreshToken methods to CoinScoutWebSocketClient with 4-minute refresh intervals, integrated token refresh events (token_refreshed, token_refresh_failed) with useNotifications hook, automatic user logout and redirect to login page when token refresh fails, prevents authentication issues and maintains secure WebSocket connections with proper token lifecycle management
- June 28, 2025. Fixed WebSocket notification status field issue - WebSocket notifications now explicitly set status as 'unread' during processing since API returns null status, enables proper Intersection Observer visibility detection for automatic read marking, added comprehensive console logging for notification processing steps to track status field handling
- June 28, 2025. Removed unused get_alert_history endpoint that was causing API errors - eliminated getAlertHistory method from AlertService.ts, removed React Query hook calling get_alert_history in CreateAlertDialog.tsx, replaced with empty array fallback to prevent errors, cleaned up unused alert history functionality that wasn't supported by backend
- June 28, 2025. Implemented WebSocket notification read tracking with scroll-based visibility detection - added markNotificationAsRead method to CoinScoutWebSocketClient for sending read status to server, enhanced WebSocket message handling to support notification_read_confirmed and unread_count_update events, integrated Intersection Observer API in NotificationItem component to automatically mark notifications as read when 60% visible for 1.5 seconds, updated useNotifications hook to handle WebSocket read confirmations and sync unread counts, replaced placeholder functions in NotificationDropdown with proper WebSocket-integrated markAsRead functionality
- June 28, 2025. Fixed login redirect functionality to preserve user's last visited page - updated Navigation component's handleLoginClick and handleSignupClick functions to include returnTo parameter with current location, modified auth hook to check for returnTo URL parameter and redirect to original page after successful login/registration instead of always going to coinlist, added error boundary handling for watchlist context in UpcomingIDO page to prevent crashes during authentication state changes, moved Advanced view button from header to navigation area on IDO detail pages
- June 28, 2025. Completed comprehensive translation fixes across the application - resolved React object rendering issue by replacing all "alertsNamespace" references with "alerts" in CreateAlertDialog.tsx component, fixed "marketData.fullyDiluted" typo to "marketData.fullyDilute" to match locale files, added missing translation keys including "watchlist.addToWatchlist", "priceChanges.1day/1week/3months/1year", "coinDetail.about/nextUnlock/unlockInDays", "priceChart.current/high/low", "scoreChart.current/high/low", and "externalLinks.title", standardized all translation namespace calls across components, eliminated all raw translation key displays and "Objects are not valid as a React child" errors
- June 27, 2025. Completed comprehensive internationalization for CoinDetail page - added missing translation keys including "watchlist.enable/disable/enableAdvancedView/disableAdvancedView" for advanced view toggle, "marketData.marketCapDetails/marketCapRank/currentRank/updatedHourly" for market cap tooltips, "coinDetail.rankPrefix" (Rank #/Sıralama #), and complete "format" namespace for number abbreviations (T,B,M,K/Tr,M,Mn,B), replaced all hardcoded English text including aria-labels and tooltip content with proper translation keys, ensuring every user-facing text in CoinDetail page displays correctly in both English and Turkish without any raw translation keys
- June 27, 2025. Fixed missing translation key "coinDetail.overallCategoriesMetricsBreakdown" that was showing as raw translation key instead of proper translated text on /coinlist page - added proper translations "Overall Categories Metrics Breakdown" in English and "Genel Kategori Metrik Dağılımları" in Turkish to ensure CoinDetail page displays correctly localized text for metric breakdown section titles
- June 26, 2025. Fixed hardcoded "Launchpad Confirmed 6" text in IDO detail page - replaced with dynamic display that shows actual launchpad data from API response, supports both launchpad array and launchpadInfo object formats, displays correct launchpad names and counts based on real data instead of placeholder text
- June 26, 2025. Updated IDO detail page for new API response structure - adapted TypeScript interfaces and components to handle new get_ido_detail_by_id response format with direct social media fields (discord, medium, linkedin, reddit, github, explorer), updated team display to not use image field and show first letter initials instead, enhanced FundingInsights component to support new funding structure with saleStartDate, saleEndDate, athRoi, currentRoi fields, modified TeamSection to handle new team member structure with role field, updated social links display to use direct API fields instead of nested objects, maintained backward compatibility with legacy socialLinks format
- June 25, 2025. Fixed missing homepage, footer, and navigation translation keys - added comprehensive homepage namespace with hero section, features, badges, trustedBy, intelligence, callToAction, benefits, buttons, faq, and testimonials translations for both English and Turkish, resolved all console errors for missing translation keys, added navigation and system translation keys for header/footer elements, provided complete footer links and categories translations, ensured all homepage content displays properly localized text instead of raw translation key placeholders
- June 25, 2025. Completed comprehensive Price/Fiyat translation implementation - systematically replaced all "Price" hardcoded text with Turkish translations throughout the application, added alerts.price, alerts.currentPrice, alerts.priceGoesAbove, alerts.priceGoesBelow, marketData.priceChange, and marketData.priceMovement translation keys, updated CoinDetail.tsx header section, CreateAlertDialog.tsx, and MarketDataCard.tsx components with proper useLanguage hooks and translation integration, ensured all price-related interfaces display "Fiyat" in Turkish
- June 25, 2025. Fixed ContextAwareWatchlistButton translation integration - resolved "Can't find variable: t" error by properly adding useLanguage hook and t function to component, completed internationalization for all watchlist button variants including ContextAwareWatchlistButton, WatchlistButton, and EnhancedWatchlistButton
- June 25, 2025. Completed comprehensive watchlist and action button internationalization - implemented translations for "Add To Watchlist/İzleme Listesine Ekle", "In Watchlist/İzleme Listesinde", "Watchers/Takipçi", "Alerts/Alarmlar", "Share/Paylaş", "Advanced View/Gelişmiş Görünüm", updated WatchlistButton, EnhancedWatchlistButton, CoinDetail page, CoinOverview component, and WatchlistDashboard with translation hooks, added watchlist namespace to locale files with all action button translations
- June 25, 2025. Added internationalization for Price Changes section - implemented translation for "Price Changes/Fiyat Değişimleri" title and all time periods: "1 Day/1 Gün", "1 Week/1 Hafta", "1 Month/1 Ay", "3 Months/3 Ay", "6 Months/6 Ay", "1 Year/1 Yıl", updated PriceChanges component and HeaderSection component with translation keys, added priceChanges namespace to both English and Turkish locale files, applied proper capitalization for English terms
- June 25, 2025. Added internationalization for Tokenomics Metrics Breakdown interface - replaced hardcoded "Tokenomik Metrics Breakdown" and "Weight:" labels with translation keys, added tokenomics namespace to both English and Turkish locale files with metricsBreakdown and weight translations, updated DetailedAnalysis component to display "Tokenomics Metrics Breakdown/Tokenomik Metrik Dağılımları" and "Weight/Ağırlık" based on selected language, improved Turkish translation from "Metrik Dökümü" to more natural "Metrik Dağılımları", added "Request Feature/Öneride Bulun" and "Report Error/Hata Bildir" button translations, fixed missing sidebar:soon translation key
- June 25, 2025. Completed comprehensive internationalization for Coin Health Score interface - added full translation support for all text elements including title, score status badges, score range labels, and bottom indicators across both CoinHealthScore component and CoinDetail page implementations, standardized all translations under coinHealth namespace for consistency, fixed hardcoded English text in CoinDetail.tsx, now properly displays "Coin Health Score/Coin Sağlık Skoru", "Positive/Pozitif", "Critical/Kritik", "Average/Ortalama", "Excellent/Mükemmel" based on selected language
- June 25, 2025. Fixed missing score translation keys - added score rating translations (excellent, positive, average, weak, critical) to both English and Turkish locale files to resolve "score:positive" display issue in DetailedAnalysis component
- June 25, 2025. Limited language selector to only Turkish and English - set all other languages (Japanese, Spanish, Arabic, Portuguese, Russian, Vietnamese, Korean, Italian, Chinese) to inactive in language configuration, updated LanguageContext to filter by isActive property, made language selector dialog more compact with smaller width (400-450px vs 800-850px), single column layout, removed search functionality, and reduced spacing for better UX
- June 24, 2025. Fixed tokenomics tab data handling to support new API response structure - updated AllocationSection component to handle both legacy allocation object and new allocations array format, enhanced tokenomics tab validation to check for meaningful percentage data in allocations array, properly disables tokenomics tab when allocation percentages are null (like Datai Network example), prevents display of misleading 0% values when no real allocation data exists
- June 24, 2025. Added internationalization support for empty state messages in IDO detail page - replaced hardcoded "Proje açıklamadı" text with translation keys from locale files, added comprehensive emptyState namespace to en.ts and tr.ts with messages for missing descriptions, team info, funding info, tokenomics, price analysis, etc., updated DataDisplay, InvestorsTable, and PriceProjectionTable components to use t('emptyState.*') translation keys ensuring proper language switching
- June 24, 2025. Implemented smart tab disabling in IDO detail page - all 6 tabs (Team, Funding & Investment, Tokenomics, Token Release, Price Analysis, Project Details) remain visible but tabs without meaningful data are automatically disabled (grayed out, unclickable), enhanced hasTabData function with deep validation to check for null/empty values in nested objects like team members and investors, provides better UX by showing complete data structure while preventing access to empty sections
- June 24, 2025. Fixed missing methodology translation keys in coin detail page - added comprehensive methodology namespace to English and Turkish locale files including "whatAreWeScoring" ("What Are We Scoring?" / "Neyi Puanlıyoruz?") and "whyIsThisImportant" ("Why Is This Important?" / "Bu Neden Önemli?") translations, resolved issue where methodology section headers displayed as raw translation keys instead of proper localized text
- June 24, 2025. Implemented single-value metric display structure for coin detail page calculation_data - added support for 21 single-value metrics (m1, m2, m3, m4, m5, m6, m7, m8, m9, m11, m12, m14, m17, m18, m19, m20, m23, m24, m27, m28, m29, m30) that display simple key-value pairs from API responses, created CalculationDataSingleValue interface and renderSingleValueData function with proper formatting and display names for each metric, updated m2 (Max Supply) and m11 (Vesting Schedule) from complex objects to single values, added appropriate icons and titles for all metric types
- June 23, 2025. Restored IDO detail page advanced mode toggle functionality - fixed broken JSX syntax that was preventing the toggle from working, restored the animated advanced mode toggle button with proper event handlers, fixed null reference errors in InvestorsTable and PriceProjectionTable components that caused crashes when switching tabs in advanced mode, removed all synthetic fallback data to ensure only authentic API data is displayed, implemented Turkish placeholder messages ("Proje açıklamadı") for empty API fields instead of hiding them
- June 23, 2025. Implemented calculation_data display for metrics m50, m36, m38, m40, m41, m42, and m52 - added support for Team Anonymous or Public metric (m50) showing team member cards with verification status, Token Use Case metric (m36) with tiered use case display, Deflationary or Inflationary Token metric (m38) with mechanism analysis, Token Redistribution metric (m40) with dynamic scoring and mechanism categorization, Buyback Mechanism metric (m41) with buyback score and mechanism details, Revenue Sharing metric (m42) with revenue sharing features and mechanism analysis, and DAO Governance metric (m52) with governance feature indicators and mechanism analysis. Completely refactored all metric displays to show only authentic API data without any synthetic calculations, progress bars, or artificial scores
- June 17, 2025. Completed internationalization extraction for NewLandingPage component - extracted all hardcoded texts including call-to-action messages, benefit descriptions, and testimonials to English locale file with proper translation keys, updated NewLandingPage to use translation keys instead of hardcoded strings, and fixed duplicate key errors in Turkish locale file by restructuring the entire file with clean syntax
- June 17, 2025. Fixed infinite loop error in comparison store and restored blur effect for demo coins in coin list table - demo coins now display with blur and reduced opacity to clearly indicate they are placeholder data
- June 17, 2025. Optimized coin comparison API requests - now sends only individual coin IDs when adding to comparison instead of all selected coins, eliminates unnecessary API calls when removing coins, maintains comparison state globally with persistence across page navigation, and prevents adding coins with empty data when API limits are exceeded
- June 16, 2025. Optimized coin detail page for normal vs advanced mode - about section, social links, other links, and categories now only processed/displayed in advanced mode to prevent unnecessary API data requests
- June 16, 2025. Added coin summary functionality to coin detail page - now displays summary first with "show more" button to reveal full description
- June 16, 2025. Implemented subscription-based filter restrictions with modal dialog system and fixed subscription state management bug where previous user's subscription level persisted after logout
- June 14, 2025. Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.