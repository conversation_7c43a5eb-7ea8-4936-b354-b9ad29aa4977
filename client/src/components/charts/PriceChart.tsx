import React, { useState, useCallback } from "react";
import { LineChart } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

// Data types for the price chart
export interface PriceDataPoint {
  date: string;
  value: number;
}

export interface PriceChartData {
  "7D": PriceDataPoint[];
  "14D": PriceDataPoint[];
  "30D": PriceDataPoint[];
}

export interface PriceChartProps {
  data: PriceChartData;
  currentPrice?: number | null;
  coinSymbol?: string;
  className?: string;
}

export default function PriceChart({
  data,
  currentPrice,
  coinSymbol = "",
  className = "",
}: PriceChartProps) {
  const { t } = useLanguage();
  // State for timeframe selection and interaction
  const [priceTimeframe, setPriceTimeframe] = useState<"7D" | "14D" | "30D">(
    "7D"
  );
  const [priceHoverPosition, setPriceHoverPosition] = useState<number | null>(
    null
  );
  const [tooltipStyle, setTooltipStyle] = useState<{
    left: number;
    top: number;
    display: string;
  }>({
    left: 0,
    top: 0,
    display: "none",
  });
  const [tooltipData, setTooltipData] = useState<{
    date: string;
    value: number;
  } | null>(null);

  // Format date for display
  const formatDateDDMM = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    return `${day}/${month}`;
  };

  // Format currency value
  const formatCurrencyValue = (value: number | null | undefined): string => {
    if (value === null || value === undefined) return "$0.00";

    if (value < 1) {
      return `$${value.toFixed(5)}`;
    } else if (value < 100) {
      return `$${value.toFixed(2)}`;
    } else if (value < 1000) {
      return `$${value.toFixed(2)}`;
    } else if (value < 10000) {
      return `$${value.toFixed(1)}`;
    } else {
      return `$${Math.round(value).toLocaleString()}`;
    }
  };

  // Get price data for selected timeframe
  const currentPriceData = data[priceTimeframe] || [];

  return (
    <div
      className={`bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg border border-slate-700/20 hover:border-emerald-500/30 px-6 py-6 shadow-lg transition-all duration-300 relative group ${className}`}
    >
      <div className="absolute inset-0 bg-emerald-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
      <div className="absolute top-0 right-0 w-20 h-20 bg-emerald-600/10 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-3 sm:gap-0">
        <div className="flex items-center gap-4">
          <div className="bg-emerald-500/10 p-3 rounded-md group-hover:bg-emerald-500/20 transition-colors duration-300 flex items-center justify-center">
            <LineChart className="h-6 w-6 text-emerald-400 group-hover:text-emerald-300 transition-colors duration-300" />
          </div>
          <h3 className="text-2xl font-semibold text-white group-hover:text-emerald-100 transition-colors duration-300 my-auto">
            {t('priceChart.title')}
          </h3>
        </div>
        <div className="flex items-center gap-2 h-9">
          <button
            className={`text-xs font-medium text-[11px] ${
              priceTimeframe === "7D"
                ? "bg-emerald-500/30 hover:bg-emerald-500/40 text-emerald-100 hover:text-white"
                : "bg-emerald-500/10 hover:bg-emerald-500/20 text-emerald-300 hover:text-emerald-200"
            }
              px-2 py-1 rounded-md transition-all duration-300 shadow-sm hover:shadow-md flex items-center justify-center h-full`}
            onClick={() => setPriceTimeframe("7D")}
          >
            {t('priceChart.sevenDays')}
          </button>
          <button
            className={`text-xs font-medium text-[11px] ${
              priceTimeframe === "14D"
                ? "bg-emerald-500/30 hover:bg-emerald-500/40 text-emerald-100 hover:text-white"
                : "bg-emerald-500/10 hover:bg-emerald-500/20 text-emerald-300 hover:text-emerald-200"
            }
              px-2 py-1 rounded-md transition-all duration-300 shadow-sm hover:shadow-md flex items-center justify-center h-full`}
            onClick={() => setPriceTimeframe("14D")}
          >
            {t('priceChart.fourteenDays')}
          </button>
          <button
            className={`text-xs font-medium text-[11px] ${
              priceTimeframe === "30D"
                ? "bg-emerald-500/30 hover:bg-emerald-500/40 text-emerald-100 hover:text-white"
                : "bg-emerald-500/10 hover:bg-emerald-500/20 text-emerald-300 hover:text-emerald-200"
            }
              px-2 py-1 rounded-md transition-all duration-300 shadow-sm hover:shadow-md flex items-center justify-center h-full`}
            onClick={() => setPriceTimeframe("30D")}
          >
            {t('priceChart.thirtyDays')}
          </button>
        </div>
      </div>

      <div className="space-y-4 mt-2">
        {/* Interactive Price Chart */}
        <div
          className="w-full h-64 relative chart-wrapper"
          onMouseMove={(e) => {
            if (currentPriceData.length === 0) return;

            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const position = Math.floor(
              (x / rect.width) * currentPriceData.length
            );
            const posIndex = Math.min(
              Math.max(0, position),
              currentPriceData.length - 1
            );
            setPriceHoverPosition(posIndex);

            setTooltipData(currentPriceData[posIndex]);
            setTooltipStyle({
              left: x,
              top: e.clientY - rect.top - 50,
              display: "block",
            });
          }}
          onMouseLeave={() => {
            setPriceHoverPosition(null);
            setTooltipStyle({ left: 0, top: 0, display: "none" });
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-emerald-500/5 to-transparent group-hover:from-emerald-500/10 transition-colors duration-300 rounded-lg"></div>

          {/* Chart content */}
          {currentPriceData.length > 0 ? (
            (() => {
              const priceValues = currentPriceData.map((d) => d.value);
              const minPrice = Math.min(...priceValues) * 0.995;
              const maxPrice = Math.max(...priceValues) * 1.005;
              const priceRange = maxPrice - minPrice;

              const width = 100;
              const height = 60;
              const padding = 2;

              // Create smooth curve path using bezier curves
              const points = currentPriceData.map((point, index) => ({
                x:
                  padding +
                  (index / (currentPriceData.length - 1)) *
                    (width - 2 * padding),
                y:
                  height -
                  padding -
                  ((point.value - minPrice) / priceRange) *
                    (height - 2 * padding),
              }));

              let pathD = `M${points[0].x},${points[0].y}`;

              for (let i = 0; i < points.length - 1; i++) {
                const x1 = points[i].x;
                const y1 = points[i].y;
                const x2 = points[i + 1].x;
                const y2 = points[i + 1].y;

                // Control points for smooth curve
                const smoothing = 0.2;
                const cpx1 = x1 + (x2 - x1) * smoothing;
                const cpx2 = x2 - (x2 - x1) * smoothing;

                pathD += ` C${cpx1},${y1} ${cpx2},${y2} ${x2},${y2}`;
              }

              return (
                <svg
                  viewBox={`0 0 ${width} ${height}`}
                  className="w-full h-full"
                  preserveAspectRatio="none"
                >
                  {/* Gradient definitions */}
                  <defs>
                    <linearGradient
                      id="priceGradient"
                      x1="0%"
                      y1="0%"
                      x2="0%"
                      y2="100%"
                    >
                      <stop offset="0%" stopColor="#10b981" stopOpacity="0.3" />
                      <stop
                        offset="100%"
                        stopColor="#10b981"
                        stopOpacity="0.05"
                      />
                    </linearGradient>
                  </defs>

                  {/* Area under the curve */}
                  <path
                    d={`${pathD} L ${width - padding} ${
                      height - padding
                    } L ${padding} ${height - padding} Z`}
                    fill="url(#priceGradient)"
                    className="transition-all duration-300"
                  />

                  {/* Main price line */}
                  <path
                    d={pathD}
                    fill="none"
                    stroke="#10b981"
                    strokeWidth="0.5"
                    className="drop-shadow-sm transition-all duration-300 group-hover:stroke-emerald-400"
                  />

                  {/* Data points */}
                  {points.map((point, i) => {
                    const isHovered = priceHoverPosition === i;

                    return (
                      <circle
                        key={i}
                        cx={point.x}
                        cy={point.y}
                        r={isHovered ? 1.2 : 0.6}
                        fill={isHovered ? "#34d399" : "#10b981"}
                        className="transition-all duration-200"
                        opacity={isHovered ? 1 : 0.7}
                      />
                    );
                  })}

                  {/* Current price indicator */}
                  {points.length > 0 && (
                    <g
                      transform={`translate(${
                        points[points.length - 1].x + 7
                      }, ${points[points.length - 1].y})`}
                    >
                      <rect
                        x={-7}
                        y={-2.5}
                        width={14}
                        height={5}
                        rx={2}
                        fill="#1e293b"
                        fillOpacity={0.7}
                        stroke="#10b981"
                        strokeWidth={0.15}
                        strokeOpacity={0.3}
                      />
                      <text
                        x={0}
                        y={0.5}
                        fontSize={1.9}
                        fill="#34d399"
                        textAnchor="middle"
                        fontWeight="500"
                        opacity="0.95"
                      >
                        {formatCurrencyValue(
                          currentPriceData[currentPriceData.length - 1].value
                        ).replace("$", "")}
                      </text>
                    </g>
                  )}

                  {/* Date labels on X-axis */}
                  {currentPriceData.length > 0 && (
                    <g>
                      {/* Show all dates for 7D, otherwise show 5 dates max */}
                      {(() => {
                        let indices;

                        if (
                          priceTimeframe === "7D" &&
                          currentPriceData.length <= 7
                        ) {
                          // Show all dates for 7 days
                          indices = Array.from(
                            { length: currentPriceData.length },
                            (_, i) => i
                          );
                        } else {
                          // Show 5 dates distributed across the chart for longer periods
                          const labelCount = Math.min(
                            5,
                            currentPriceData.length
                          );
                          const step = Math.floor(
                            currentPriceData.length / (labelCount - 1)
                          );
                          indices = Array.from({ length: labelCount }, (_, i) =>
                            i === labelCount - 1
                              ? currentPriceData.length - 1
                              : i * step
                          );
                        }

                        return indices.map((index) => {
                          const point = currentPriceData[index];
                          const x =
                            padding +
                            (index / (currentPriceData.length - 1)) *
                              (width - 2 * padding);

                          return (
                            <text
                              key={index}
                              x={x}
                              y={height - 2}
                              fontSize={2.2}
                              fill="#64748b"
                              textAnchor="middle"
                              opacity="0.9"
                              fontWeight="500"
                            >
                              {formatDateDDMM(point.date)}
                            </text>
                          );
                        });
                      })()}
                    </g>
                  )}
                </svg>
              );
            })()
          ) : (
            <div className="flex items-center justify-center h-full text-slate-400">
              {t('priceChart.noDataAvailable')}
            </div>
          )}

          {/* Tooltip */}
          {tooltipData && (
            <div
              className="absolute pointer-events-none z-10 bg-slate-800/95 border border-emerald-500/30 rounded-lg px-3 py-2 text-sm font-medium text-white shadow-lg backdrop-blur-sm"
              style={{
                left: `${tooltipStyle.left}px`,
                top: `${tooltipStyle.top}px`,
                display: tooltipStyle.display,
                transform: "translateX(-50%)",
              }}
            >
              <div className="text-emerald-300 text-xs mb-1">
                {formatDateDDMM(tooltipData.date)}
              </div>
              <div className="text-white font-semibold">
                {formatCurrencyValue(tooltipData.value)}
              </div>
            </div>
          )}
        </div>

        {/* Price summary */}
        {currentPriceData.length > 0 && (
          <div className="grid grid-cols-3 gap-4 pt-4 border-t border-slate-700/30">
            <div className="text-center">
              <div className="text-xs font-medium text-emerald-400 mb-1">
                {t('priceChart.current')}
              </div>
              <div className="text-sm font-semibold text-white">
                {formatCurrencyValue(currentPrice)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs font-medium text-emerald-400 mb-1">
                {t('priceChart.high')}
              </div>
              <div className="text-sm font-semibold text-white">
                {formatCurrencyValue(
                  Math.max(...currentPriceData.map((d) => d.value))
                )}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs font-medium text-emerald-400 mb-1">
                {t('priceChart.low')}
              </div>
              <div className="text-sm font-semibold text-white">
                {formatCurrencyValue(
                  Math.min(...currentPriceData.map((d) => d.value))
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
