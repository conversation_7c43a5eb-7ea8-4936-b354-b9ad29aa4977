import React, { useState, useCallback, useMemo } from "react";
import { BarChart4 } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

// Data types for the score chart
export interface ScoreDataPoint {
  date: string;
  score: number;
  isEmpty?: boolean; // Flag for empty timeline positions
}

export interface ScoreChartProps {
  data: ScoreDataPoint[];
  currentScore?: number;
  coinSymbol?: string;
  className?: string;
}

type TimeFrame = '1M' | '3M' | '6M';

export default function ScoreChart({
  data,
  currentScore,
  coinSymbol = "",
  className = "",
}: ScoreChartProps) {
  const { t } = useLanguage();
  // State for timeframe selection
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<TimeFrame>('3M');
  
  // State for interaction
  const [scoreHoverPosition, setScoreHoverPosition] = useState<number | null>(null);
  const [scoreTooltipStyle, setScoreTooltipStyle] = useState<{
    left: number;
    top: number;
    display: string;
  }>({
    left: 0,
    top: 0,
    display: "none",
  });
  const [scoreTooltipData, setScoreTooltipData] = useState<{
    date: string;
    score: number;
  } | null>(null);

  // Filter data based on selected timeframe with timeline positioning
  const filteredData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Sort data by date to ensure proper ordering
    const sortedData = [...data].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    // Group by date (without time) to handle duplicate dates
    const dateGroups = new Map<string, ScoreDataPoint[]>();
    
    sortedData.forEach(item => {
      const dateOnly = item.date.split(' ')[0]; // Get date part only (before space)
      if (!dateGroups.has(dateOnly)) {
        dateGroups.set(dateOnly, []);
      }
      dateGroups.get(dateOnly)!.push(item);
    });
    
    // Merge duplicate dates by taking the average score for each date
    const mergedData = Array.from(dateGroups.entries()).map(([dateOnly, items]) => {
      // Calculate average score for each date
      const averageScore = items.reduce((sum, item) => sum + item.score, 0) / items.length;
      return {
        date: dateOnly, // Use date only without time
        score: Math.round(averageScore * 10) / 10 // Round to 1 decimal place
      };
    });
    
    // Calculate how many days to show based on timeframe
    const daysToShow = {
      '1M': 30,
      '3M': 90,
      '6M': 180
    }[selectedTimeFrame];

    // If we have more data than timeframe, show the most recent data
    if (mergedData.length > daysToShow) {
      return mergedData.slice(-daysToShow);
    }
    
    // Return all available data
    return mergedData;
  }, [data, selectedTimeFrame]);

  // Available timeframes - always show all options
  const availableTimeFrames = useMemo(() => {
    const timeFrames: { key: TimeFrame; label: string; minDays: number }[] = [
      { key: '1M', label: t('scoreChart.oneMonth'), minDays: 30 },
      { key: '3M', label: t('scoreChart.threeMonths'), minDays: 90 },
      { key: '6M', label: t('scoreChart.sixMonths'), minDays: 180 }
    ];

    return timeFrames; // Always show all timeframes, data will be filtered accordingly
  }, [t]);

  // Format date for display
  const formatDateDDMM = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // Use the filtered data
  const currentScoreData = filteredData;

  return (
    <div className={`bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg border border-slate-700/20 hover:border-violet-500/30 px-6 py-6 shadow-lg transition-all duration-300 relative group ${className}`}>
      <div className="absolute inset-0 bg-violet-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
      <div className="absolute top-0 right-0 w-20 h-20 bg-violet-600/10 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>

      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="bg-violet-500/10 p-3 rounded-md group-hover:bg-violet-500/20 transition-colors duration-300 flex items-center justify-center">
            <BarChart4 className="h-6 w-6 text-violet-400 group-hover:text-violet-300 transition-colors duration-300" />
          </div>
          <h3 className="text-2xl font-semibold text-white group-hover:text-violet-100 transition-colors duration-300 my-auto">
            {t('scoreChart.title')}
          </h3>
        </div>
        
        {/* Timeframe Selection Buttons */}
        {availableTimeFrames.length > 1 && (
          <div className="flex items-center gap-2 h-9">
            {availableTimeFrames.map((timeFrame) => (
              <button
                key={timeFrame.key}
                onClick={() => setSelectedTimeFrame(timeFrame.key)}
                className={`text-xs font-medium text-[11px] ${
                  selectedTimeFrame === timeFrame.key
                    ? "bg-violet-500/30 hover:bg-violet-500/40 text-violet-100 hover:text-white"
                    : "bg-violet-500/10 hover:bg-violet-500/20 text-violet-300 hover:text-violet-200"
                } px-2 py-1 rounded-md transition-all duration-300 shadow-sm hover:shadow-md flex items-center justify-center h-full`}
              >
                {timeFrame.label}
              </button>
            ))}
          </div>
        )}
      </div>

      <div className="space-y-4 mt-2">
        {/* Interactive Score Chart */}
        <div
          className="w-full h-64 relative chart-wrapper"
          onMouseMove={(e) => {
            if (currentScoreData.length === 0) return;
            
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const position = Math.floor((x / rect.width) * currentScoreData.length);
            const posIndex = Math.min(Math.max(0, position), currentScoreData.length - 1);
            setScoreHoverPosition(posIndex);

            setScoreTooltipData(currentScoreData[posIndex]);
            setScoreTooltipStyle({
              left: x,
              top: e.clientY - rect.top - 50,
              display: "block",
            });
          }}
          onMouseLeave={() => {
            setScoreHoverPosition(null);
            setScoreTooltipStyle({ left: 0, top: 0, display: "none" });
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-violet-500/5 to-transparent group-hover:from-violet-500/10 transition-colors duration-300 rounded-lg"></div>

          {/* Chart content */}
          {currentScoreData.length > 0 ? (
            (() => {
              const scoreValues = currentScoreData.map((d) => d.score);
              const minScore = Math.min(...scoreValues) * 0.992;
              const maxScore = Math.max(...scoreValues) * 1.008;
              const scoreRange = maxScore - minScore;

              const width = 100;
              const height = 30;
              const padding = 2;

              const pathD = currentScoreData
                .map((point, i) => {
                  const x = padding + (i / (currentScoreData.length - 1)) * (width - 2 * padding);
                  const y = height - padding - ((point.score - minScore) / scoreRange) * (height - 2 * padding);
                  return i === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
                })
                .join(" ");

              return (
                <svg
                  viewBox="0 0 100 30"
                  className="w-full h-full"
                  preserveAspectRatio="none"
                >
                  {/* Gradient definitions */}
                  <defs>
                    <linearGradient id="scoreGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.3" />
                      <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.05" />
                    </linearGradient>
                  </defs>

                  {/* Area under the curve */}
                  <path
                    d={`${pathD} L ${width - padding} ${height - padding} L ${padding} ${height - padding} Z`}
                    fill="url(#scoreGradient)"
                    className="transition-all duration-300"
                  />

                  {/* Main score line */}
                  <path
                    d={pathD}
                    fill="none"
                    stroke="#8b5cf6"
                    strokeWidth="0.35"
                    className="drop-shadow-sm transition-all duration-300 group-hover:stroke-violet-400"
                  />

                  {/* Data points - only show on hover */}
                  {scoreHoverPosition !== null && (() => {
                    const i = scoreHoverPosition;
                    const x = padding + (i / (currentScoreData.length - 1)) * (width - 2 * padding);
                    const y = height - padding - ((currentScoreData[i].score - minScore) / scoreRange) * (height - 2 * padding);

                    return (
                      <circle
                        key={i}
                        cx={x}
                        cy={y}
                        r={0.9}
                        fill="#8b5cf6"
                        stroke="#ffffff"
                        strokeWidth={0.3}
                        className="transition-all duration-200"
                      />
                    );
                  })()}

                  {/* Current score indicator */}
                  {currentScoreData.length > 0 && (
                    <g
                      transform={`translate(${width - padding - 7}, ${
                        height -
                        padding -
                        ((currentScoreData[currentScoreData.length - 1].score - minScore) / scoreRange) *
                          (height - 2 * padding)
                      })`}
                    >
                      <rect
                        x={-7}
                        y={-2.5}
                        width={14}
                        height={5}
                        rx={2}
                        fill="#1e293b"
                        fillOpacity={0.7}
                        stroke="#8b5cf6"
                        strokeWidth={0.15}
                        strokeOpacity={0.3}
                      />
                      <text
                        x={0}
                        y={0.5}
                        fontSize={1.9}
                        fill="#b794f4"
                        textAnchor="middle"
                        fontWeight="500"
                        opacity="0.95"
                      >
                        {currentScoreData[currentScoreData.length - 1].score.toFixed(1)}
                      </text>
                    </g>
                  )}
                </svg>
              );
            })()
          ) : (
            <div className="flex items-center justify-center h-full text-slate-400">
              No score data available
            </div>
          )}

          {/* Tooltip */}
          {scoreTooltipData && (
            <div
              className="absolute pointer-events-none z-10 bg-slate-800/95 border border-violet-500/30 rounded-lg px-3 py-2 text-sm font-medium text-white shadow-lg backdrop-blur-sm"
              style={{
                left: `${scoreTooltipStyle.left}px`,
                top: `${scoreTooltipStyle.top}px`,
                display: scoreTooltipStyle.display,
                transform: "translateX(-50%)",
              }}
            >
              <div className="text-violet-300 text-xs mb-1">
                {formatDateDDMM(scoreTooltipData.date)}
              </div>
              <div className="text-white font-semibold">
                {scoreTooltipData.score.toFixed(1)}
              </div>
            </div>
          )}
        </div>

        {/* Score summary */}
        {currentScoreData.length > 0 && (
          <div className="space-y-4 pt-4 border-t border-slate-700/30">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-xs font-medium text-violet-400 mb-1">{t('scoreChart.current')}</div>
                <div className="text-sm font-semibold text-white">
                  {currentScore?.toFixed(1) || 'N/A'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-xs font-medium text-violet-400 mb-1">{t('scoreChart.high')}</div>
                <div className="text-sm font-semibold text-white">
                  {Math.max(...currentScoreData.map(d => d.score)).toFixed(1)}
                </div>
              </div>
              <div className="text-center">
                <div className="text-xs font-medium text-violet-400 mb-1">{t('scoreChart.low')}</div>
                <div className="text-sm font-semibold text-white">
                  {Math.min(...currentScoreData.map(d => d.score)).toFixed(1)}
                </div>
              </div>
            </div>
            

          </div>
        )}
      </div>
    </div>
  );
}