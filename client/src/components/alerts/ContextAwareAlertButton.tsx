import React, { useState } from "react";
import { <PERSON> } from "lucide-react";
import { cn } from "../../lib/utils";
import { useLanguage } from "../../contexts/LanguageContext";
import { Button } from "../../components/ui/button";
import { CreateAlertDialog } from "./CreateAlertDialog";
import { useAuth } from "../../hooks/use-auth";
import { LoginPromptButton } from "../../components/auth/LoginPromptButton";
import { Badge } from "../../components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "../../components/ui/tooltip";
import { useToast } from "../../hooks/use-toast";
import AlertService from "../../lib/services/AlertService";
import { AlertFormValues } from "../../api/alertsApi";

// Media query hook as inline implementation
function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = React.useState<boolean>(() => {
    if (typeof window !== "undefined") {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  React.useEffect(() => {
    if (typeof window === "undefined") return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => {
      mediaQuery.removeEventListener("change", handleChange);
    };
  }, [query]);

  return matches;
}

interface ContextAwareAlertButtonProps {
  coinId: string;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  condition?: "above" | "below" | "both" | "none";
  tooltipText?: string;
  onToggleComplete?: () => void;
}

export function ContextAwareAlertButton({
  coinId,
  variant = "outline",
  size = "sm",
  className = "",
  condition = "none",
  tooltipText,
  onToggleComplete,
}: ContextAwareAlertButtonProps) {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { t } = useLanguage();

  // In a real app, this would fetch the active alerts from an API
  const activeAlerts = 3; // Mock data - would be replaced with actual data

  const isDesktop = useMediaQuery("(min-width: 768px)");
  const isMobile = !isDesktop;

  // Function to open the alert dialog
  const handleOpenAlertDialog = () => {
    setOpen(true);
  };

  // Handle dialog close with callback
  const handleDialogChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen && onToggleComplete) {
      onToggleComplete();
    }
  };

  // Handle form submission
  const handleSubmit = async (data: AlertFormValues) => {
    setIsSubmitting(true);
    
    try {
      const result = await AlertService.createAlert(data);
      
      if (result) {
        // Close the dialog when alert is created successfully
        // The notification is now handled by AlertService
        setOpen(false);
      }
    } catch (error) {
      console.error("Error creating alert:", error);
      // Error notification is now handled by AlertService
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate tooltip text
  const getTooltipText = () => {
    if (tooltipText) return tooltipText;
    return activeAlerts > 0 ? `Manage alerts` : `Create alert`;
  };

  // The button component based on configuration
  const renderButton = () => {
    // Button for non-authenticated users
    if (!user) {
      return (
        <LoginPromptButton
          className={cn(
            "flex items-center gap-1.5 text-xs bg-transparent hover:bg-slate-700/30 text-slate-200 transition-all duration-200 font-medium",
            className,
          )}
          variant="ghost"
          size={size}
          feature="alerts"
        >
          <span className="flex items-center">
            <Bell className="h-4 w-4 mr-2" />
            Create Alert
          </span>
        </LoginPromptButton>
      );
    }

    // Icon-only variant for authenticated users
    if (size === "icon") {
      return (
        <Button
          variant={variant}
          size={size}
          className={cn("relative", className)}
          onClick={handleOpenAlertDialog}
          aria-label={getTooltipText()}
        >
          <Bell className="h-4 w-4" />
          {activeAlerts > 0 && (
            <Badge
              variant="secondary"
              className="absolute -top-2 -right-2 min-w-[1.25rem] h-5 px-1.5 py-0 flex items-center justify-center text-[0.65rem] rounded-full bg-[#66B2FF] text-white"
            >
              {activeAlerts}
            </Badge>
          )}
        </Button>
      );
    }

    // Standard button for authenticated users
    return (
      <button
        className={cn(
          "text-xs bg-transparent hover:bg-slate-700/30 text-slate-200 px-2 h-7 rounded-md flex items-center justify-center gap-1.5 transition-all duration-200 font-medium",
          className,
        )}
        onClick={handleOpenAlertDialog}
        aria-label={getTooltipText()}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className={`${activeAlerts > 0 ? "text-blue-400 fill-blue-400/30" : "text-blue-400"} hover:text-blue-300 transition-colors`}
        >
          <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
          <path d="M13.73 21a2 2 0 0 1-3.46 0" />
        </svg>
        <span className="whitespace-nowrap">
          {activeAlerts > 0 ? t('watchlist.alerts') : "Create Alert"}
        </span>
      </button>
    );
  };

  return (
    <>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>{renderButton()}</TooltipTrigger>
        <TooltipContent side={isMobile ? "bottom" : "top"}>
          {getTooltipText()}
        </TooltipContent>
      </Tooltip>

      {/* Alert Dialog - only rendered when user is authenticated */}
      {user && (
        <CreateAlertDialog
          open={open}
          onOpenChange={handleDialogChange}
          initialCoinId={coinId}
          onSubmit={handleSubmit}
        />
      )}
    </>
  );
}
