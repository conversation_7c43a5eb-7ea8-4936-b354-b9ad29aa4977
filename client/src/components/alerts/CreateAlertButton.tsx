import React, { useState, useEffect } from 'react';
import { AlertButton } from '../../components/ui/alert-button';
import { CreateAlertDialog } from './CreateAlertDialog';
import { useAuth } from '../../hooks/use-auth';
import { LoginPromptButton } from '../../components/auth/LoginPromptButton';
import { Bell } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { useToast } from '../../hooks/use-toast';
import AlertService from '../../lib/services/AlertService';
import { AlertFormValues } from '../../api/alertsApi';
import { useQuery } from '@tanstack/react-query';
import { useNotifications } from '../../hooks/useNotifications';
import { useLanguage } from '../../contexts/LanguageContext';

interface CreateAlertButtonProps {
  selectedCoin?: string;  // Eski adı hala dışarıdan gelen şey için koruyoruz
  className?: string;
  condition?: 'above' | 'below' | 'both' | 'none';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export function CreateAlertButton({
  selectedCoin,
  className,
  condition = 'none',
  variant = 'outline',
  size = 'sm'
}: CreateAlertButtonProps) {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Get real notifications from WebSocket
  const notifications = useNotifications();
  const alertCount = notifications.unreadCount;

  // Function to open the alert dialog (only called when user is authenticated)
  const handleOpenAlertDialog = () => {
    setOpen(true);
  };

  // Handle form submission
  const handleSubmit = async (data: AlertFormValues) => {
    setIsSubmitting(true);

    try {
      const result = await AlertService.createAlert(data);

      if (result) {
        // Close the dialog when alert is created successfully
        // The notification is now handled by AlertService
        setOpen(false);
      }
    } catch (error) {
      console.error("Error creating alert:", error);
      // Error notification is now handled by AlertService
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* If user is logged in, use the AlertButton with the dialog */}
      {user ? (
        <>
          {size === "icon" ? (
            <Button
              className={className}
              onClick={handleOpenAlertDialog}
              variant={variant}
              size={size}
            >
              <Bell className="h-4 w-4" />
              <Badge
                variant="secondary"
                className="absolute -top-2 -right-2 min-w-[1.25rem] h-5 px-1.5 py-0 flex items-center justify-center text-[0.65rem] rounded-full bg-[#132F4C]/70 border border-[#1E4976]/30 text-[#E7EBF0]/80"
              >
                {alertCount}
              </Badge>
            </Button>
          ) : (
            <Button
              className={(className || "flex items-center gap-2") + " group"}
              onClick={handleOpenAlertDialog}
              variant={variant}
              size={size}
            >
              <Bell className="h-4 w-4" />
              <span className="text-[#E7EBF0]/80 font-medium mx-2 group-hover:text-[#66B2FF]">{t("alertsTitle") || "Alerts"}</span>
              <Badge
                variant="secondary"
                className="min-w-[1.5rem] px-2 py-0 text-center text-xs rounded-full bg-[#132F4C]/70 border border-[#1E4976]/30 text-[#E7EBF0]/80"
              >
                {alertCount}
              </Badge>
            </Button>
          )}

          <CreateAlertDialog
            open={open}
            onOpenChange={setOpen}
            initialCoinId={selectedCoin}
            onSubmit={handleSubmit}
          />
        </>
      ) : (
        /* If user is not logged in, use LoginPromptButton */
        <LoginPromptButton
          className={(className || "") + " group"}
          variant={variant}
          size={size}
          feature="alerts"
        >
          <span className="flex items-center">
            <Bell className="h-4 w-4" />
            <span className="mx-2 text-[#E7EBF0]/80 group-hover:text-[#66B2FF]">{t("alertsTitle") || "Alerts"}</span>
            <Badge
              variant="secondary"
              className="min-w-[1.5rem] px-2 py-0 text-xs rounded-full bg-[#132F4C]/70 border border-[#1E4976]/30 text-[#E7EBF0]/80"
            >
              {alertCount}
            </Badge>
          </span>
        </LoginPromptButton>
      )}
    </>
  );
}