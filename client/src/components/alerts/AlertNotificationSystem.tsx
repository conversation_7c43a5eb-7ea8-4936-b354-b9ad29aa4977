/**
 * Alert Notification System
 *
 * Manages crypto alert notifications and WebSocket connections
 */

import { useEffect, useRef } from 'react';
import CoinScoutWebSocketClient, { getWebSocketUrl } from '@/lib/CoinScoutWebSocketClient';
import { useNotifications } from '@/hooks/useNotifications';
import { useAuth } from '@/hooks/use-auth';

interface AlertNotificationSystemProps {
  children: React.ReactNode;
}

export function AlertNotificationSystem({ children }: AlertNotificationSystemProps) {
  const { user, isLoggedIn } = useAuth();
  const wsClient = useRef<CoinScoutWebSocketClient | null>(null);

  // Get real auth token or generate a unique one
  const authToken = localStorage.getItem('auth_token') || `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const notifications = useNotifications(authToken);

  useEffect(() => {
    // Initialize WebSocket for crypto alerts
    const initializeAlertSystem = () => {
      const wsUrl = getWebSocketUrl();
      const token = authToken; // Use real auth token

      console.log('Initializing crypto alert notification system');

      wsClient.current = new CoinScoutWebSocketClient(wsUrl, token, {
        debug: true,
        reconnectInterval: 3000,
        maxReconnectAttempts: 5
      });

      // Connection status logging
      wsClient.current.on('connect', () => {
        console.log('Alert system connected');
      });

      wsClient.current.on('disconnect', () => {
        console.log('Alert system disconnected');
      });

      wsClient.current.on('auth_success', () => {
        console.log('Alert system authenticated');
      });

      wsClient.current.on('auth_error', (error: any) => {
        console.error('Alert system authentication failed:', error);
      });

      // Crypto alert specific handlers
      wsClient.current.on('notification', (notification: any) => {
        console.log('Crypto alert received:', notification);

        // Handle different types of crypto alerts
        if (notification.type === 'price') {
          handlePriceAlert(notification);
        } else if (notification.type === 'system') {
          handleSystemNotification(notification);
        }
      });

      wsClient.current.on('error', (error: any) => {
        console.error('Alert system error:', error);
      });
    };

    const handlePriceAlert = (notification: any) => {
      // Show browser notification for price alerts
      if (notification.priority === 'high') {
        notifications.showBrowserNotification(notification);
      }

      // Play sound for critical alerts (if enabled)
      if (notification.priority === 'high' && notification.coinSymbol) {
        playAlertSound();
      }
    };

    const handleSystemNotification = (notification: any) => {
      // Handle system notifications (updates, maintenance, etc.)
      console.log('System notification:', notification);
    };

    const playAlertSound = () => {
      try {
        // Create a simple beep sound for alerts
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = 800;
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
      } catch (error) {
        console.log('Alert sound not available');
      }
    };

    initializeAlertSystem();

    return () => {
      if (wsClient.current) {
        wsClient.current.disconnect();
      }
    };
  }, [notifications]);

  return <>{children}</>;
}

export default AlertNotificationSystem;