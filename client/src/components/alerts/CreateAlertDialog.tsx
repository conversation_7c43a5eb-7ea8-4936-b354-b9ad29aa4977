import React, { useState, useEffect, useCallback, useRef } from "react";
import { useLanguage } from "../../contexts/LanguageContext";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "../../components/ui/dialog";
import { useQuery } from "@tanstack/react-query";
import AlertService from "../../lib/services/AlertService";
import { Alert } from "../../api/alertsApi";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import { Input } from "../../components/ui/input";
import { Button } from "../../components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "../../components/ui/tabs";
import { Slider } from "../../components/ui/slider";
import {
  Bell,
  Brain,
  TrendingUp,
  BarChart3,
  History,
  PlayCircle,
  Send,
  Mail,
  Globe,
  AlertCircle,
  AlertOctagon,
  Info,
  FileText,
  PauseCircle,
  PlayCircle as PlayIcon,
  Edit3,
  Trash2,
  ArrowUp,
  ArrowDown,
  X,
  Check,
  Search,
  Loader2,
} from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "../../components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../components/ui/popover";
import SearchService, { SearchCoin } from "../../lib/services/SearchService";
import { debounce } from "../../lib/utils";
import { useNotifications } from "../../hooks/useNotifications";

import { ScrollArea } from "../../components/ui/scroll-area";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "../../components/ui/tooltip";
import { cn } from "../../lib/utils";
import { Checkbox } from "../../components/ui/checkbox";

// Define alert types as string literals
const ALERT_TYPES = ["price", "ai-score", "social"] as const;
type AlertType = (typeof ALERT_TYPES)[number];

// Alert form schema
const alertFormSchema = z
  .object({
    coin: z.string().min(1, "Please select a coin"),
    notificationType: z.enum(["email", "browser", "telegram"]),
    type: z.enum(ALERT_TYPES),
    conditions: z
      .object({
        above: z.boolean(),
        below: z.boolean(),
      })
      .refine((data) => data.above || data.below, {
        message: "Select at least one condition",
      }),
    currentPrice: z.string().optional(), // Current price for reference
    priceAbove: z
      .string()
      .optional()
      .refine((val) => !val || parseFloat(val) > 0, {
        message: "Price must be greater than 0",
      }),
    priceBelow: z
      .string()
      .optional()
      .refine((val) => !val || parseFloat(val) > 0, {
        message: "Price must be greater than 0",
      }),
    thresholdAbove: z
      .string()
      .optional()
      .refine(
        (val) => !val || (parseFloat(val) >= 0 && parseFloat(val) <= 100),
        {
          message: "Threshold must be between 0 and 100",
        },
      ),
    thresholdBelow: z
      .string()
      .optional()
      .refine(
        (val) => !val || (parseFloat(val) >= 0 && parseFloat(val) <= 100),
        {
          message: "Threshold must be between 0 and 100",
        },
      ),
  })
  .superRefine((data, ctx) => {
    if (data.type === "price") {
      if (data.conditions.above && !data.priceAbove) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Price above is required when above condition is selected",
          path: ["priceAbove"],
        });
      }
      if (data.conditions.below && !data.priceBelow) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Price below is required when below condition is selected",
          path: ["priceBelow"],
        });
      }

      // Add cross-field validation when both conditions are selected
      if (
        data.conditions.above &&
        data.conditions.below &&
        data.priceAbove &&
        data.priceBelow &&
        parseFloat(data.priceAbove) <= parseFloat(data.priceBelow)
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Above price must be higher than below price",
          path: ["priceAbove"],
        });
      }
    } else {
      if (data.conditions.above && !data.thresholdAbove) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            "Threshold above is required when above condition is selected",
          path: ["thresholdAbove"],
        });
      }
      if (data.conditions.below && !data.thresholdBelow) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            "Threshold below is required when below condition is selected",
          path: ["thresholdBelow"],
        });
      }

      // Add cross-field validation when both conditions are selected
      if (
        data.conditions.above &&
        data.conditions.below &&
        data.thresholdAbove &&
        data.thresholdBelow &&
        parseFloat(data.thresholdAbove) <= parseFloat(data.thresholdBelow)
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Above threshold must be higher than below threshold",
          path: ["thresholdAbove"],
        });
      }
    }
  });

type AlertFormValues = z.infer<typeof alertFormSchema>;

// Coin bileşeni için arayüz
interface CoinOption {
  id: string;
  symbol: string;
  name: string;
  logo?: string;
}

// ApiContext kullanarak kullanıcının aktif alertlerini alacağız
// Eski mock datayı sildik

// AlertService kullanarak apiden alert geçmişini alacağız
// Şimdilik boş bir array ile başlıyoruz

interface CreateAlertDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialCoinId?: string;  // Prop adını değiştirdik: selectedCoin -> initialCoinId
  compact?: boolean;
  onSubmit?: (data: AlertFormValues) => void;
}

const InfoTooltip = ({ content }: { content: string }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <Info className="h-4 w-4 text-[#66B2FF]/70 hover:text-[#66B2FF] cursor-help ml-1" />
      </TooltipTrigger>
      <TooltipContent className="bg-[#132F4C] border-[#1E4976] text-[#E7EBF0]">
        <p className="max-w-[200px] text-sm">{content}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

export function CreateAlertDialog({
  open,
  onOpenChange,
  initialCoinId,
  compact = true,
  onSubmit,
}: CreateAlertDialogProps) {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState<"create" | "active" | "history">(
    "create",
  );
  // AlertType is "price" | "ai-score" | "social"
  const [createAlertType, setCreateAlertType] = useState<AlertType>("price");
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchCoin[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [selectedCoin, setSelectedCoin] = useState<SearchCoin | null>(null);

  // Get real notifications from WebSocket
  const notifications = useNotifications();

  // Fetch user alerts when the dialog is open
  const {
    data: userAlerts = [],
    isLoading: isLoadingAlerts
  } = useQuery({
    queryKey: ['userAlerts'],
    queryFn: async () => {
      const alerts = await AlertService.getUserAlerts();
      console.log('Fetched user alerts:', alerts);
      return alerts;
    },
    enabled: open, // Sadece dialog açıkken çalıştır
  });

  // Alert history functionality removed - endpoint doesn't exist
  const alertHistory: Alert[] = [];
  const isLoadingHistory = false;

  // Ref for dialog content to prevent autofocus
  const dialogContentRef = useRef<HTMLDivElement>(null);

  // Function to perform the actual search
  const performSearch = useCallback(async (query: string) => {
    if (query.trim().length < 2) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      const results = await SearchService.searchCoins({ query });
      setSearchResults(results.coins || []);
    } catch (error: any) {
      console.error("Error in coin search:", error);

      // SearchService now handles 403 errors internally and triggers the modal
      // So we just need to clear the search results
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Create debounced search function
  const debouncedSearch = useRef(
    debounce((query: string) => {
      performSearch(query);
    }, 300),
  ).current;

  // Effect to handle search input changes
  useEffect(() => {
    if (searchQuery.trim().length >= 2) {
      setIsSearching(true);
      debouncedSearch(searchQuery);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  }, [searchQuery, debouncedSearch]);

  // Get the alert type from URL if coming back from user guide
  React.useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const typeFromUrl = params.get("type");
    if (typeFromUrl && ALERT_TYPES.includes(typeFromUrl as AlertType)) {
      setCreateAlertType(typeFromUrl as AlertType);
      // Clear the URL parameter after setting the type
      window.history.replaceState({}, "", window.location.pathname);
    }
  }, []);

  // Prevent form field autofocus when dialog opens
  useEffect(() => {
    if (open) {
      // Store the dialog open time for focus prevention
      (window as any)._lastDialogOpenTime = Date.now();

      // This function will run in each animation frame to ensure focus stays away from inputs
      const preventFocus = () => {
        // Only run for a limited time after dialog opens
        const now = Date.now();
        if (now - (window as any)._lastDialogOpenTime > 1000) {
          return; // Stop preventing focus after 1 second
        }

        // Check if any form element has focus
        const activeElement = document.activeElement as HTMLElement;
        if (
          activeElement &&
          activeElement.tagName !== "BODY" &&
          dialogContentRef.current?.contains(activeElement)
        ) {
          // Blur the active element
          activeElement.blur();

          // Focus dialog container instead (which is not focusable)
          if (dialogContentRef.current) {
            // Create a temporary non-focusable element and focus it
            const temp = document.createElement("div");
            temp.tabIndex = -1;
            dialogContentRef.current.appendChild(temp);
            temp.focus();
            dialogContentRef.current.removeChild(temp);
          }

          // Continue checking in next frame
          requestAnimationFrame(preventFocus);
        } else {
          // Continue checking in next frame
          requestAnimationFrame(preventFocus);
        }
      };

      // Start the focus prevention loop
      requestAnimationFrame(preventFocus);

      // Delay to ensure DOM is updated - disable all inputs temporarily
      setTimeout(() => {
        // Force blur on all input elements in the dialog
        if (dialogContentRef.current) {
          const inputs = dialogContentRef.current.querySelectorAll(
            "input, button, [tabindex]",
          );
          inputs.forEach((input) => {
            input.setAttribute("tabindex", "-1");

            // Re-enable tabindex after a brief delay
            setTimeout(() => {
              // Only re-enable if element still exists in DOM
              if (document.body.contains(input)) {
                if (input.getAttribute("data-original-tabindex")) {
                  const originalTabIndex = input.getAttribute(
                    "data-original-tabindex",
                  );
                  input.setAttribute("tabindex", originalTabIndex || "0");
                  input.removeAttribute("data-original-tabindex");
                } else {
                  input.removeAttribute("tabindex");
                }
              }
            }, 1000);
          });
        }
      }, 50);
    }
  }, [open]);

  const form = useForm<AlertFormValues>({
    resolver: zodResolver(alertFormSchema),
    defaultValues: {
      coin: initialCoinId || "",
      notificationType: "browser", // Sadece browser bildirim türü aktif
      type: createAlertType as AlertType,
      conditions: { above: true, below: false },
      ...(createAlertType === "price"
        ? {
            priceAbove: "",
            priceBelow: "",
          }
        : {
            thresholdAbove: "75",
            thresholdBelow: "25",
          }),
    },
    mode: "onSubmit", // Sadece form submit olduğunda validasyon yap
  });

  // SearchResults coin listesini kullanıyoruz, mockCoins yerine

  const handleSubmit = async (data: AlertFormValues) => {
    try {
      console.log("Creating alert:", data);

      // Dış onSubmit fonksiyonu varsa çağır
      if (onSubmit) {
        onSubmit(data);
      } else {
        // Eğer dışarıdan bir onSubmit fonksiyonu verilmemişse varsayılan olarak uyarı göster
        window.alert(
          `Alert created successfully! You will be notified when ${data.coin} meets your conditions.`,
        );
        onOpenChange(false);
      }

      // Formu sıfırla
      form.reset();
    } catch (error) {
      console.error("Error creating alert:", error);
    }
  };

  const handleCreateTabChange = (value: string) => {
    // Ensure value is one of the valid AlertType values
    let alertType: AlertType;
    if (value === "price" || value === "ai-score" || value === "social") {
      alertType = value;
    } else {
      // Default to price if value is invalid
      alertType = "price";
    }

    setCreateAlertType(alertType);
    form.reset({
      coin: form.getValues("coin"),
      notificationType: form.getValues("notificationType"),
      type: alertType,
      conditions: { above: true, below: false },
      ...(alertType === "price"
        ? {
            priceAbove: "",
            priceBelow: "",
          }
        : {
            thresholdAbove: "75",
            thresholdBelow: "25",
          }),
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        ref={dialogContentRef}
        className="sm:max-w-[480px] w-full bg-slate-800 text-white border-slate-700 p-6"
        onOpenAutoFocus={(e) => {
          // Prevent default autofocus behavior
          e.preventDefault();
        }}
      >
        <DialogHeader className="pb-2 space-y-1">
          <DialogTitle className="text-lg font-medium flex items-center gap-2">
            <Bell className="h-4 w-4 text-blue-500" />
{t("alerts.title")}
            <Badge
              variant="secondary"
              className="ml-0.5 px-1.5 py-0 h-5 bg-blue-900/30 text-blue-400 text-xs"
            >
              {isLoadingAlerts ? "..." : userAlerts.length} Active
            </Badge>
          </DialogTitle>
          <DialogDescription className="text-slate-400 text-sm">
{t("alerts.description")}
          </DialogDescription>
        </DialogHeader>

        <div className="mt-2 space-y-2">
          {/* Primary Action - Create Alert Button */}
          <Button
            onClick={() => setActiveTab("create")}
            className={cn(
              "w-full transition-all duration-300",
              activeTab === "create"
                ? "bg-blue-600 hover:bg-blue-500 text-white"
                : "bg-slate-700 hover:bg-slate-600 text-slate-100",
            )}
          >
            <Bell
              className={cn(
                "h-4 w-4 mr-2",
                activeTab === "create" ? "text-white" : "text-blue-400",
              )}
            />
{t("alerts.createNewAlert")}
          </Button>

          {/* Secondary Actions */}
          <div className="flex gap-2">
            <Button
              onClick={() => setActiveTab("active")}
              variant="outline"
              className={cn(
                "flex-1 border-slate-700 text-sm",
                activeTab === "active"
                  ? "bg-slate-700 text-blue-400 border-blue-700/40"
                  : "bg-transparent hover:bg-slate-700 text-slate-300",
              )}
            >
              <PlayCircle className="h-4 w-4 mr-2" />
{t("alerts.activeAlerts")}
            </Button>
            <Button
              onClick={() => setActiveTab("history")}
              variant="outline"
              className={cn(
                "flex-1 border-slate-700 text-sm",
                activeTab === "history"
                  ? "bg-slate-700 text-blue-400 border-blue-700/40"
                  : "bg-transparent hover:bg-slate-700 text-slate-300",
              )}
            >
              <History className="h-4 w-4 mr-2" />
{t("alerts.notifications")}
              {notifications.notifications.length > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-1 px-1.5 py-0 h-4 bg-blue-900/30 text-blue-400 text-xs"
                >
                  {notifications.notifications.length}
                </Badge>
              )}
            </Button>
          </div>
        </div>

        {/* Content based on active tab */}
        <div className="mt-4">
          {activeTab === "create" && (
            <Tabs
              value={createAlertType}
              onValueChange={(value) =>
                handleCreateTabChange(value as AlertType)
              }
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2 bg-slate-700/50 p-1">
                <TabsTrigger
                  value="price"
                  className={cn(
                    "flex items-center justify-center gap-2 text-sm py-2 transition-all duration-300",
                    "data-[state=active]:bg-slate-600 data-[state=active]:text-blue-400",
                  )}
                >
                  <TrendingUp className="h-4 w-4" />
{t('alerts.price')}
                </TabsTrigger>
                <TabsTrigger
                  value="ai-score"
                  className={cn(
                    "flex items-center justify-center gap-2 text-sm py-2 transition-all duration-300",
                    "data-[state=active]:bg-slate-600 data-[state=active]:text-blue-400",
                  )}
                >
                  <Brain className="h-4 w-4" />
                  {t("alerts.aiScore")}
                </TabsTrigger>
              </TabsList>

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit((data) => handleSubmit(data))}
                  className="space-y-3 mt-4"
                  autoComplete="off"
                  // onFocus olay işleyicisini kaldırdık
                >
                  <FormField
                    control={form.control}
                    name="coin"
                    render={({ field }) => (
                      <FormItem className="relative">
                        <FormLabel className="text-[#E7EBF0] text-xs flex items-center">
{t("alerts.coin")} <span className="text-red-500 ml-0.5">*</span>
                          <InfoTooltip content="Enter the cryptocurrency symbol or name" />
                        </FormLabel>
                        <FormControl>
                          {/* Basitleştirilmiş coin arama mekanizması */}
                          <div className="space-y-1">
                            <Button
                              type="button"
                              variant="outline"
                              className={cn(
                                "bg-slate-800 border-slate-700 hover:border-blue-500/50 hover:bg-slate-700 text-slate-300 w-full justify-between h-10",
                                field.value
                                  ? "text-white"
                                  : "text-slate-400",
                                popoverOpen && "border-blue-500/70 ring-1 ring-blue-500/40"
                              )}
                              onClick={() => {
                                // Select butonuna tıklandığında popover'ı aç/kapat
                                setPopoverOpen(!popoverOpen);

                                // Eğer açılıyorsa, search input'a focus vermek için bir defa
                                // zamanlayıcı kur (DOM güncellensin diye)
                                if (!popoverOpen) {
                                  setTimeout(() => {
                                    const searchInput = document.querySelector('[data-coin-search-input]') as HTMLInputElement;
                                    if (searchInput) {
                                      searchInput.focus();
                                    }
                                  }, 50);
                                }
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <Search className="h-4 w-4 opacity-50" />
                                {field.value ? (
                                  <span>{selectedCoin ? selectedCoin.name : field.value}</span>
                                ) : (
                                  <span>{t("alerts.selectCoin")}</span>
                                )}
                              </div>
                              <TrendingUp className="h-4 w-4 opacity-50" />
                            </Button>

                            {/* Coin search dropdown - Popover yerine doğrudan div kullanıyoruz */}
                            {popoverOpen && (
                              <div className="relative">
                                <div className="absolute z-50 top-0 left-0 right-0 bg-slate-800 border border-slate-700/60 shadow-lg shadow-slate-900/50 rounded-md overflow-hidden">
                                  <div className="p-2 border-b border-slate-700 flex items-center gap-2">
                                    <Search className="h-4 w-4 text-slate-400" />
                                    <input
                                      data-coin-search-input
                                      type="text"
                                      value={searchQuery}
                                      onChange={(e) => setSearchQuery(e.target.value)}
                                      placeholder="Search coins..."
                                      className="flex-1 bg-transparent border-none text-slate-300 text-sm focus:outline-none focus:ring-0 h-8"
                                      autoFocus
                                    />
                                    {searchQuery && (
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        className="h-6 w-6"
                                        onClick={() => setSearchQuery("")}
                                      >
                                        <X className="h-3 w-3" />
                                      </Button>
                                    )}
                                  </div>

                                  <div className="max-h-[300px] overflow-y-auto">
                                    {isSearching ? (
                                      <div className="py-6 text-center">
                                        <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2 text-blue-500" />
                                        <p className="text-sm text-slate-400">
                                          Searching coins...
                                        </p>
                                      </div>
                                    ) : searchResults.length > 0 ? (
                                      <div>
                                        {searchResults.map((coin) => (
                                          <div
                                            key={coin.id}
                                            role="option"
                                            className="flex items-center gap-2 p-2 hover:bg-slate-700/50 cursor-pointer"
                                            onClick={() => {
                                              // Coin ID'sini form verisine kaydet, ancak görünür değer olarak coin name'i kullan
                                              field.onChange(coin.id);
                                              // Seçilen coini state'e kaydet ki gösterim için kullanabilelim
                                              setSelectedCoin({
                                                id: coin.id,
                                                name: coin.name,
                                                symbol: coin.symbol,
                                                logo: coin.logo,
                                                image: coin.image
                                              });
                                              setPopoverOpen(false);
                                              setSearchQuery("");
                                            }}
                                          >
                                            <div className="h-6 w-6 rounded-full bg-blue-600/20 flex items-center justify-center text-blue-400 text-xs font-bold overflow-hidden">
                                              {coin.image || coin.logo ? (
                                                <img
                                                  src={coin.image || coin.logo}
                                                  alt={coin.name}
                                                  className="h-full w-full object-cover"
                                                  onError={(e) => {
                                                    const target = e.target as HTMLImageElement;
                                                    target.src = `https://via.placeholder.com/24x24/3b82f6/ffffff?text=${coin.symbol?.charAt(0) || 'C'}`;
                                                  }}
                                                />
                                              ) : (
                                                coin.symbol
                                                  .slice(0, 2)
                                                  .toUpperCase()
                                              )}
                                            </div>
                                            <div className="flex-1">
                                              <p className="text-sm font-medium text-slate-300">
                                                {coin.name}
                                              </p>
                                              <p className="text-xs text-slate-400">
                                                {coin.symbol?.toUpperCase()}
                                              </p>
                                            </div>
                                            {field.value === coin.id && (
                                              <Check className="h-4 w-4 text-blue-500" />
                                            )}
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <div className="py-6 text-center text-sm text-slate-400">
                                        {searchQuery.length >= 2
                                          ? "No coins found."
                                          : "Type at least 2 characters to search"}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </FormControl>
                        {form.formState.submitCount > 0 && <FormMessage />}
                      </FormItem>
                    )}
                  />

                  {createAlertType === "price" && (
                    <>
                      <FormField
                        control={form.control}
                        name="conditions"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[#E7EBF0] text-xs flex items-center">
                              Conditions{" "}
                              <span className="text-red-500 ml-0.5">*</span>
                              <InfoTooltip content="Select when you want to be alerted about price changes" />
                            </FormLabel>
                            <div className="space-y-2 mt-1">
                              <div
                                className={cn(
                                  "flex items-center space-x-2 p-2 rounded-md bg-[#132F4C]/50 hover:bg-[#132F4C] transition-colors duration-300 group cursor-pointer",
                                  compact ? "h-auto" : "",
                                )}
                              >
                                <Checkbox
                                  checked={field.value.above}
                                  onCheckedChange={(checked) =>
                                    field.onChange({
                                      ...field.value,
                                      above: checked === true,
                                    })
                                  }
                                  className="border-[#1E4976] data-[state=checked]:bg-[#66B2FF] data-[state=checked]:border-[#66B2FF] h-4 w-4"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center">
                                    <ArrowUp className="h-3.5 w-3.5 mr-1 text-emerald-500" />
                                    <span className="text-xs font-medium text-[#E7EBF0] group-hover:text-[#66B2FF] transition-colors duration-300">
{t('alerts.priceAbove')}
                                    </span>
                                  </div>
                                  {!compact && (
                                    <p className="text-xs text-[#E7EBF0]/50 mt-1 ml-6">
                                      {t("alerts.priceAboveDesc")}
                                    </p>
                                  )}
                                </div>
                              </div>

                              <div
                                className={cn(
                                  "flex items-center space-x-2 p-2 rounded-md bg-[#132F4C]/50 hover:bg-[#132F4C] transition-colors duration-300 group cursor-pointer",
                                  compact ? "h-auto" : "",
                                )}
                              >
                                <Checkbox
                                  checked={field.value.below}
                                  onCheckedChange={(checked) =>
                                    field.onChange({
                                      ...field.value,
                                      below: checked === true,
                                    })
                                  }
                                  className="border-[#1E4976] data-[state=checked]:bg-[#66B2FF] data-[state=checked]:border-[#66B2FF] h-4 w-4"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center">
                                    <ArrowDown className="h-3.5 w-3.5 mr-1 text-red-500" />
                                    <span className="text-xs font-medium text-[#E7EBF0] group-hover:text-[#66B2FF] transition-colors duration-300">
{t('alerts.priceBelow')}
                                    </span>
                                  </div>
                                  {!compact && (
                                    <p className="text-xs text-[#E7EBF0]/50 mt-1 ml-6">
                                      {t("alerts.priceBelowDesc")}
                                    </p>
                                  )}
                                </div>
                              </div>
                            </div>
                            {form.formState.submitCount > 0 && <FormMessage />}
                          </FormItem>
                        )}
                      />

                      {/* Current Price Information - API entegrasyonu sonrası görüntülenir */}
                      {form.watch("coin") && (
                        <div className="mb-3 p-2 bg-[#132F4C]/70 rounded-md border border-[#1E4976]/30">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1.5">
                              <TrendingUp className="h-3.5 w-3.5 text-[#66B2FF]" />
                              <span className="text-xs font-medium text-[#E7EBF0]">
                                {t('alerts.currentPrice', 'Current Price')}
                              </span>
                            </div>
                            <div className="flex items-center gap-1.5">
                              <span className="text-xs font-bold text-[#E7EBF0]">
                                Retrieving price...
                              </span>
                              <Badge
                                variant="outline"
                                className="px-1 h-4 bg-blue-500/10 text-blue-400 border-blue-500/30 text-[9px]"
                              >
                                <Loader2 className="animate-spin h-2.5 w-2.5 mr-1" />
                                Loading
                              </Badge>
                            </div>
                          </div>
                        </div>
                      )}

                      {form.watch("conditions.above") && (
                        <FormField
                          control={form.control}
                          name="priceAbove"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[#E7EBF0] text-xs flex items-center">
{t("alerts.targetPriceAbove")} (USD)
                                <InfoTooltip content="Alert when price exceeds this value" />
                              </FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#E7EBF0]/70">
                                    $
                                  </span>
                                  <Input
                                    {...field}
                                    type="number"
                                    step="0.000001"
                                    min="0"
                                    placeholder={t("alerts.enterUpperTargetPrice")}
                                    className="bg-[#132F4C] border-[#1E4976] text-[#E7EBF0] placeholder-[#E7EBF0]/50 pl-8"
                                    tabIndex={3}
                                  />
                                  <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-[#E7EBF0]/50">
                                    {form.watch("coin") || "BTC"}
                                  </div>
                                </div>
                              </FormControl>
                              {form.formState.submitCount > 0 && (
                                <FormMessage />
                              )}
                            </FormItem>
                          )}
                        />
                      )}

                      {form.watch("conditions.below") && (
                        <FormField
                          control={form.control}
                          name="priceBelow"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[#E7EBF0] text-xs flex items-center">
{t("alerts.targetPriceBelow")} (USD)
                                <InfoTooltip content="Alert when price falls below this value" />
                              </FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#E7EBF0]/70">
                                    $
                                  </span>
                                  <Input
                                    {...field}
                                    type="number"
                                    step="0.000001"
                                    min="0"
                                    placeholder={t("alerts.enterLowerTargetPrice")}
                                    className="bg-[#132F4C] border-[#1E4976] text-[#E7EBF0] placeholder-[#E7EBF0]/50 pl-8"
                                    tabIndex={4}
                                  />
                                  <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-[#E7EBF0]/50">
                                    {form.watch("coin") || "BTC"}
                                  </div>
                                </div>
                              </FormControl>
                              {form.formState.submitCount > 0 && (
                                <FormMessage />
                              )}
                            </FormItem>
                          )}
                        />
                      )}
                    </>
                  )}

                  {createAlertType === "ai-score" && (
                    <>
                      <FormField
                        control={form.control}
                        name="conditions"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[#E7EBF0] text-xs flex items-center">
                              Conditions{" "}
                              <span className="text-red-500 ml-0.5">*</span>
                              <InfoTooltip content="Select when you want to be alerted about AI score changes" />
                            </FormLabel>
                            <div className="space-y-2 mt-1">
                              <div
                                className={cn(
                                  "flex items-center space-x-2 p-2 rounded-md bg-[#132F4C]/50 hover:bg-[#132F4C] transition-colors duration-300 group cursor-pointer",
                                  compact ? "h-auto" : "",
                                )}
                              >
                                <Checkbox
                                  checked={field.value.above}
                                  onCheckedChange={(checked) =>
                                    field.onChange({
                                      ...field.value,
                                      above: checked === true,
                                    })
                                  }
                                  className="border-[#1E4976] data-[state=checked]:bg-[#66B2FF] data-[state=checked]:border-[#66B2FF] h-4 w-4"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center">
                                    <ArrowUp className="h-3.5 w-3.5 mr-1 text-emerald-500" />
                                    <span className="text-xs font-medium text-[#E7EBF0] group-hover:text-[#66B2FF] transition-colors duration-300">
                                      Score goes above threshold
                                    </span>
                                  </div>
                                  {!compact && (
                                    <p className="text-xs text-[#E7EBF0]/50 mt-1 ml-6">
                                      {t("alerts.aiScoreAbove")}
                                    </p>
                                  )}
                                </div>
                              </div>

                              <div
                                className={cn(
                                  "flex items-center space-x-2 p-2 rounded-md bg-[#132F4C]/50 hover:bg-[#132F4C] transition-colors duration-300 group cursor-pointer",
                                  compact ? "h-auto" : "",
                                )}
                              >
                                <Checkbox
                                  checked={field.value.below}
                                  onCheckedChange={(checked) =>
                                    field.onChange({
                                      ...field.value,
                                      below: checked === true,
                                    })
                                  }
                                  className="border-[#1E4976] data-[state=checked]:bg-[#66B2FF] data-[state=checked]:border-[#66B2FF] h-4 w-4"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center">
                                    <ArrowDown className="h-3.5 w-3.5 mr-1 text-red-500" />
                                    <span className="text-xs font-medium text-[#E7EBF0] group-hover:text-[#66B2FF] transition-colors duration-300">
                                      Score goes below threshold
                                    </span>
                                  </div>
                                  {!compact && (
                                    <p className="text-xs text-[#E7EBF0]/50 mt-1 ml-6">
                                      {t("alerts.aiScoreBelow")}
                                    </p>
                                  )}
                                </div>
                              </div>
                            </div>
                            {form.formState.submitCount > 0 && <FormMessage />}
                          </FormItem>
                        )}
                      />

                      {form.watch("conditions.above") && (
                        <FormField
                          control={form.control}
                          name="thresholdAbove"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[#E7EBF0] text-xs flex items-center">
                                AI Score Upper Threshold
                                <InfoTooltip content="Alert when AI score exceeds this value" />
                              </FormLabel>
                              <FormControl>
                                <div className="space-y-2">
                                  <Slider
                                    value={[parseFloat(field.value || "75")]}
                                    onValueChange={(values) =>
                                      field.onChange(values[0].toString())
                                    }
                                    max={100}
                                    step={1}
                                    className="[&_[role=slider]]:bg-[#66B2FF]"
                                  />
                                  <div className="text-right text-sm text-[#E7EBF0]/60">
                                    {field.value || "75"}
                                  </div>
                                </div>
                              </FormControl>
                              {form.formState.submitCount > 0 && (
                                <FormMessage />
                              )}
                            </FormItem>
                          )}
                        />
                      )}

                      {form.watch("conditions.below") && (
                        <FormField
                          control={form.control}
                          name="thresholdBelow"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[#E7EBF0] text-xs flex items-center">
                                AI Score Lower Threshold
                                <InfoTooltip content="Alert when AI score falls below this value" />
                              </FormLabel>
                              <FormControl>
                                <div className="space-y-2">
                                  <Slider
                                    value={[parseFloat(field.value || "25")]}
                                    onValueChange={(values) =>
                                      field.onChange(values[0].toString())
                                    }
                                    max={100}
                                    step={1}
                                    className="[&_[role=slider]]:bg-[#66B2FF]"
                                  />
                                  <div className="text-right text-sm text-[#E7EBF0]/60">
                                    {field.value || "25"}
                                  </div>
                                </div>
                              </FormControl>
                              {form.formState.submitCount > 0 && (
                                <FormMessage />
                              )}
                            </FormItem>
                          )}
                        />
                      )}
                    </>
                  )}

                  <FormField
                    control={form.control}
                    name="notificationType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#E7EBF0] text-xs flex items-center">
{t("alerts.notificationType")}
                          <InfoTooltip content="Choose how you want to receive alert notifications" />
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-slate-800/50 border-slate-700/30 text-white h-9">
                              <SelectValue>
                                {field.value === "browser" && (
                                  <>
                                    <Globe className="h-4 w-4 text-green-500 inline-block mr-2" />
{t("alerts.browserNotification")}
                                  </>
                                )}
                                {field.value === "email" && (
                                  <>
                                    <Mail className="h-4 w-4 text-purple-500 inline-block mr-2" />
                                    Email Notification
                                  </>
                                )}
                                {field.value === "telegram" && (
                                  <>
                                    <Send className="h-4 w-4 text-blue-500 inline-block mr-2" />
                                    Telegram Notification
                                  </>
                                )}
                              </SelectValue>
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-slate-800 border-slate-700">
                            <SelectItem
                              value="browser"
                              className="text-white hover:bg-slate-700 focus:bg-slate-700"
                            >
                              <div className="flex items-center">
                                <Globe className="h-4 w-4 text-green-500 mr-2" />
<span>{t("alerts.browserNotification")}</span>
                              </div>
                            </SelectItem>
                            <SelectItem
                              value="email"
                              disabled
                              className="text-slate-500 cursor-not-allowed"
                            >
                              <div className="flex items-center">
                                <Mail className="h-4 w-4 text-purple-500/60 mr-2" />
                                <span>Email Notification</span>
                                <Badge
                                  variant="outline"
                                  className="ml-2 text-[0.6rem] h-4 px-1 border-amber-600/50 text-amber-500"
                                >
                                  SOON
                                </Badge>
                              </div>
                            </SelectItem>
                            <SelectItem
                              value="telegram"
                              disabled
                              className="text-slate-500 cursor-not-allowed"
                            >
                              <div className="flex items-center">
                                <Send className="h-4 w-4 text-blue-500/60 mr-2" />
                                <span>Telegram Notification</span>
                                <Badge
                                  variant="outline"
                                  className="ml-2 text-[0.6rem] h-4 px-1 border-amber-600/50 text-amber-500"
                                >
                                  SOON
                                </Badge>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.formState.submitCount > 0 &&
                    Object.keys(form.formState.errors).length > 0 && (
                      <div className="mt-2 p-2 border border-red-500/20 bg-red-500/10 rounded-md">
                        <p className="text-xs text-red-400 flex items-start gap-1">
                          <AlertOctagon className="h-3.5 w-3.5 mt-0.5 flex-shrink-0" />
                          <span>
                            Lütfen formda işaretli olan zorunlu alanları
                            doldurun.
                          </span>
                        </p>
                      </div>
                    )}

                  <div className="flex justify-between space-x-2 mt-6">
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab("active")}
                      className="text-slate-300 hover:bg-slate-700 hover:text-white border-slate-700"
                    >
{t("alerts.back")}
                    </Button>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        onClick={() => onOpenChange(false)}
                        className="text-slate-300 hover:bg-slate-700 hover:text-white"
                      >
                        <X className="h-4 w-4 mr-2" />
{t("alerts.cancel")}
                      </Button>
                      <Button
                        type="submit"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                        disabled={form.formState.isSubmitting}
                        onClick={() => {
                          // Bu noktada validasyon aktif olacak
                          console.log(
                            "Submit button clicked, triggering validations",
                          );
                        }}
                      >
                        {form.formState.isSubmitting ? (
                          <>
                            <svg
                              className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            Processing...
                          </>
                        ) : (
                          <>
                            <Check className="h-4 w-4 mr-2" />
{t("alerts.save")}
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            </Tabs>
          )}

          {activeTab === "active" && (
            <ScrollArea
              className={cn("pr-3", compact ? "h-[280px]" : "h-[320px]")}
            >
              <div className="space-y-2">
                {isLoadingAlerts ? (
                  <div className="flex items-center justify-center h-full py-8">
                    <Loader2 className="h-6 w-6 animate-spin mr-2 text-blue-500" />
                    <span className="text-sm text-slate-400">Loading your alerts...</span>
                  </div>
                ) : userAlerts.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full py-8 text-center">
                    <Bell className="h-8 w-8 text-slate-500 mb-2" />
                    <p className="text-sm text-slate-400">You don't have any active alerts yet.</p>
                    <Button
                      variant="link"
                      className="mt-1 text-blue-400 hover:text-blue-300"
                      onClick={() => setActiveTab("create")}
                    >
                      Create your first alert
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {userAlerts.map((alert: Alert) => (
                      <Card
                        key={alert.id}
                        className="bg-[#132F4C] border-[#1E4976] text-[#E7EBF0] p-3"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1.5">
                            {alert.coinImage && (
                              <img
                                src={alert.coinImage}
                                alt={alert.coinSymbol}
                                className="h-5 w-5 rounded-full mr-0.5"
                              />
                            )}
                            <div className="flex flex-col">
                              <div className="flex items-center gap-1">
                                <span className="text-xs font-medium text-[#E7EBF0]">
                                  {alert.coinName || alert.coin}
                                </span>
                                <span className="text-[10px] text-slate-400">
                                  {alert.coinSymbol}
                                </span>
                                <Badge
                                  variant="secondary"
                                  className={`px-1.5 h-4 text-[9px] ${
                                    alert.status === "active"
                                      ? "bg-green-500/20 text-green-500"
                                      : "bg-yellow-500/20 text-yellow-500"
                                  }`}
                                >
                                  {alert.status}
                                </Badge>
                              </div>

                              <div className="text-[9px] text-[#E7EBF0]/60 mt-0.5">
                                <div className="flex items-center gap-1">
                                  <AlertTypeIcon type={alert.type} />
                                  {alert.type === "price" ? (
                                    <span>
                                      {alert.conditionAbove ? `Above $${alert.priceAbove}` : ""}
                                      {alert.conditionBelow && alert.conditionAbove ? " and " : ""}
                                      {alert.conditionBelow ? `Below $${alert.priceBelow}` : ""}
                                    </span>
                                  ) : alert.type === "ai-score" ? (
                                    <span>
                                      {alert.conditionAbove ? `AI score > ${alert.thresholdAbove}` : ""}
                                      {alert.conditionBelow && alert.conditionAbove ? " and " : ""}
                                      {alert.conditionBelow ? `< ${alert.thresholdBelow}` : ""}
                                    </span>
                                  ) : null}
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-1">
                            <NotificationIcon type={alert.notificationType} />
                            <div className="flex items-center gap-0.5">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-[#66B2FF] hover:bg-[#1E4976] h-5 w-5 p-0"
                              >
                                <Edit3 className="h-3 w-3" />
                              </Button>
                              {alert.status === "active" ? (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-[#66B2FF] hover:bg-[#1E4976] h-5 w-5 p-0"
                                >
                                  <PauseCircle className="h-3 w-3" />
                                </Button>
                              ) : (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-[#66B2FF] hover:bg-[#1E4976] h-5 w-5 p-0"
                                >
                                  <PlayIcon className="h-3 w-3" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-500 hover:bg-[#1E4976] h-5 w-5 p-0"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          )}

          {activeTab === "history" && (
            <ScrollArea
              className={cn("pr-3", compact ? "h-[280px]" : "h-[320px]")}
            >
              <div className="space-y-2">
                {notifications.notifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full py-8 text-center">
                    <History className="h-8 w-8 text-slate-500 mb-2" />
                    <p className="text-sm text-slate-400">No notifications found.</p>
                    <p className="text-xs text-slate-500 mt-1">
                      When you receive notifications, they will appear here.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {notifications.notifications.map((notification) => (
                      <Card
                        key={notification.id}
                        className="bg-[#132F4C] border-[#1E4976] text-[#E7EBF0] p-3"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1.5">
                            <div className="flex flex-col flex-1">
                              <div className="flex items-center gap-1">
                                <span className="text-xs font-medium text-[#E7EBF0]">
                                  {notification.title}
                                </span>
                                <Badge
                                  variant="secondary"
                                  className={`px-1.5 h-4 text-[9px] ${
                                    notification.status === "unread"
                                      ? "bg-blue-500/20 text-blue-500"
                                      : "bg-slate-500/20 text-slate-400"
                                  }`}
                                >
                                  {notification.status}
                                </Badge>
                                <Badge
                                  variant="secondary"
                                  className={`px-1.5 h-4 text-[9px] ${
                                    notification.priority === "high"
                                      ? "bg-red-500/20 text-red-500"
                                      : notification.priority === "medium"
                                      ? "bg-yellow-500/20 text-yellow-500"
                                      : "bg-green-500/20 text-green-500"
                                  }`}
                                >
                                  {notification.priority}
                                </Badge>
                              </div>

                              <div className="text-[10px] text-[#E7EBF0]/70 mt-1">
                                {notification.message}
                              </div>

                              <div className="text-[9px] text-[#E7EBF0]/50 mt-1 flex items-center gap-1">
                                <AlertTypeIcon type={notification.type} />
                                <span>Type: {notification.type}</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-[9px] text-[#E7EBF0]/60">
                            {new Date(notification.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

const NotificationIcon = ({ type }: { type: string }) => {
  switch (type) {
    case "telegram":
      return <Send className="h-3.5 w-3.5 text-blue-500 mr-1.5" />;
    case "email":
      return <Mail className="h-3.5 w-3.5 text-purple-500 mr-1.5" />;
    default:
      return <Globe className="h-3.5 w-3.5 text-green-500 mr-1.5" />;
  }
};

const AlertTypeIcon = ({ type }: { type: string }) => {
  switch (type) {
    case "price":
      return <TrendingUp className="h-3.5 w-3.5 text-blue-500" />;
    case "ai-score":
      return <Brain className="h-3.5 w-3.5 text-purple-500" />;

    default:
      return <AlertCircle className="h-3.5 w-3.5" />;
  }
};
