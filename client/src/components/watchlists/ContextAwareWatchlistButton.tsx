import React from "react";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";
import { useEnhancedWatchlist } from "@/hooks/useEnhancedWatchlist";
import { useLanguage } from "@/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { SharedWatchlistPopup, PageType } from "./SharedWatchlistPopup";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Media query hook as inline implementation
function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = React.useState<boolean>(() => {
    if (typeof window !== "undefined") {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  React.useEffect(() => {
    if (typeof window === "undefined") return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => {
      mediaQuery.removeEventListener("change", handleChange);
    };
  }, [query]);

  return matches;
}

interface ContextAwareWatchlistButtonProps {
  coinId: string;
  id?: string; // İlave edilen id özelliği
  isUpcoming?: boolean;
  variant?: "default" | "compact" | "icon";
  className?: string;
  tooltipText?: string;
  pageType: PageType;
  onToggleComplete?: () => void;
  colorOverride?: string; // Renk geçersiz kılma özelliği
  isInWatchlist?: boolean; // İlave edilen isInWatchlist özelliği
  toggle?: () => void; // İlave edilen toggle özelliği
  size?: string; // İlave edilen size özelliği
}

export function ContextAwareWatchlistButton({
  coinId,
  id,
  isUpcoming = false,
  variant = "default",
  className = "",
  tooltipText,
  pageType,
  onToggleComplete,
  colorOverride,
  isInWatchlist: isPropInWatchlist,
  toggle: propToggle,
  size: propSize,
}: ContextAwareWatchlistButtonProps) {
  const { isInWatchlist: checkIsInWatchlist, getCurrentWatchlist } = useEnhancedWatchlist();
  const { t } = useLanguage();
  const [forceUpdate, setForceUpdate] = React.useState(0);

  const isDesktop = useMediaQuery("(min-width: 768px)");
  const isMobile = !isDesktop;

  // Get the current watchlist
  const currentWatchlist = getCurrentWatchlist();
  const currentWatchlistId = currentWatchlist?.id || '';

  // Event listener for watchlist updates
  React.useEffect(() => {
    // Watchlist değişikliklerini dinle
    const handleWatchlistUpdate = (event: CustomEvent) => {
      const detail = event.detail;
      
      // Eğer bu coin için bir işlem olduysa UI'ı güncelle
      if (detail && detail.coinId === coinId) {
        console.log(`Watchlist update for coin ${coinId} detected, refreshing UI`);
        setForceUpdate(prev => prev + 1);
      }
    };

    // Event listener ekle
    window.addEventListener('watchlistsUpdated', handleWatchlistUpdate as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('watchlistsUpdated', handleWatchlistUpdate as EventListener);
    };
  }, [coinId]);

  // Coinlist sayfasında herhangi bir watchlist içinde olması yeterli
  // Watchlist sayfasında ise sadece seçili watchlist içinde olmalı
  let isInCurrentWatchlist;
  
  if (pageType === "coinlist") {
    // CoinList sayfasında: Herhangi bir watchlist içinde mi kontrolü yap (watchlistId parametresini boş bırak)
    isInCurrentWatchlist = checkIsInWatchlist(coinId);
  } else {
    // Watchlist sayfasında: Sadece mevcut watchlist içinde mi kontrolü yap
    isInCurrentWatchlist = checkIsInWatchlist(coinId, currentWatchlistId);
  }

  // Generate tooltip text
  const getTooltipText = () => {
    if (tooltipText) return tooltipText;

    const watchlistName = currentWatchlist?.name || "watchlist";
    return isInCurrentWatchlist ? t('watchlist.inWatchlist') : t('watchlist.addToWatchlist');
  };

  // The button component based on variant
  const renderButton = () => {
    // Eğer colorOverride verilmişse, external durumu override etmek için kullan
    const isHighlighted = colorOverride ? true : isInCurrentWatchlist;
    const fillColor = colorOverride || "yellow-400";
    
    switch (variant) {
      case "compact":
        return (
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "p-0 h-auto",
              isHighlighted
                ? `text-${fillColor}`
                : "text-muted-foreground hover:text-foreground",
              className,
            )}
            aria-label={getTooltipText()}
          >
            <Star
              className={cn(
                "w-4 h-4 transition-all",
                isHighlighted ? `fill-${fillColor}` : "fill-none",
              )}
            />
          </Button>
        );

      case "icon":
        return (
          <button
            className={cn(
              "group flex items-center justify-center transition-all duration-300 rounded-full p-1.5 hover:bg-[#132F4C]/50",
              isHighlighted
                ? `text-${fillColor}`
                : "text-gray-600/60 hover:text-gray-300",
              className,
            )}
            aria-label={getTooltipText()}
          >
            <Star
              className={cn(
                "w-5 h-5 transition-all scale-100 duration-300",
                isHighlighted
                  ? `fill-${fillColor} hover:scale-110`
                  : "fill-none hover:scale-110 group-hover:fill-yellow-400/20",
              )}
            />
          </button>
        );

      default:
        return (
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "flex items-center gap-1.5",
              isHighlighted
                ? `text-${fillColor} hover:text-${fillColor}`
                : "text-muted-foreground hover:text-foreground",
              className,
            )}
            aria-label={getTooltipText()}
          >
            <Star
              className={cn(
                "w-4 h-4 transition-all",
                isHighlighted ? `fill-${fillColor}` : "fill-none",
              )}
            />
            <span>
              {isHighlighted ? t('watchlist.inWatchlist') : t('watchlist.addToWatchlist')}
            </span>
          </Button>
        );
    }
  };

  return (
    <Tooltip delayDuration={300}>
      <TooltipTrigger asChild>
        <SharedWatchlistPopup
          coinId={coinId}
          isUpcoming={isUpcoming}
          pageType={pageType}
        >
          {renderButton()}
        </SharedWatchlistPopup>
      </TooltipTrigger>
      <TooltipContent side={isMobile ? "bottom" : "top"}>
        {getTooltipText()}
      </TooltipContent>
    </Tooltip>
  );
}
