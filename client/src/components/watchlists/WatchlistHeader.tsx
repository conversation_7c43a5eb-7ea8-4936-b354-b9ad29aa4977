import React from "react";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { exportToPDF } from "@/lib/pdfExport";
import { Button } from "@/components/ui/button";
import { 
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { WatchlistCreationDialog } from "./WatchlistCreationDialog";
import { Plus, Trash2, Share2, Copy, Star, Download, ChevronDown,
  Flame, Coins, Rocket, FileText, Droplets, Diamond, Gem, Sparkles, Palette, Leaf, Database, Cpu, Music, Trophy, Edit3 } from "lucide-react";
import { FaXTwitter, FaDiscord, FaTelegram, FaTiktok } from "react-icons/fa6";

interface Watchlist {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
  icon_id?: number; // Ikon ID'si için alan eklendi
  isDefault: boolean;
  items: string[];
  upcomingItems?: string[]; // Optional support for upcoming items
}

interface WatchlistHeaderProps {
  watchlists: Watchlist[];
  selectedWatchlist: Watchlist | null; // Using Watchlist object for provider pattern
  onSwitchWatchlist: (id: number) => void;
  onCreateWatchlist: (name: string, description: string, icon: string, iconId: number) => void;
  onDeleteWatchlist: (id: number) => void;
  className?: string;
  activeTab: 'listed' | 'upcoming';
  setActiveTab: (tab: 'listed' | 'upcoming') => void;
}

export function WatchlistHeader({
  watchlists,
  selectedWatchlist,
  onSwitchWatchlist,
  onCreateWatchlist,
  onDeleteWatchlist,
  className = "",
  activeTab,
  setActiveTab,
}: WatchlistHeaderProps) {
  const { toast } = useToast();
  const { t } = useLanguage();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = React.useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [isShareDialogOpen, setIsShareDialogOpen] = React.useState(false);
  
  // With the provider pattern, selectedWatchlist is already the watchlist object
  const currentWatchlist = selectedWatchlist;
  const watchlistCount = watchlists.length;
  
  const handleCopyLink = () => {
    const shareUrl = `${window.location.origin}/watchlist/${currentWatchlist?.id}`;
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: "Link copied",
      description: "Watchlist link copied to clipboard",
    });
  };
  
  return (
    <div className={`flex flex-col gap-4 mb-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-primary text-[#13b9ec]">
            {t('watchlist.aiPoweredTitle')}
          </h1>
          <p className="text-base text-muted-foreground">
            {t('watchlist.trackDescription')}
          </p>
        </div>
        
        {/* Header controls section, all aligned horizontally */}
        <div className="flex items-center space-x-4">
          {/* Project Type Tabs - Moved to right side of header */}
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setActiveTab('listed')}
              className={`px-4 py-1.5 rounded-md font-medium text-sm relative transition-all duration-200 ${
                activeTab === 'listed'
                  ? 'text-white bg-blue-600/90'
                  : 'text-blue-300 hover:text-white hover:bg-blue-500/20'
              }`}
            >
              {t('watchlist.coins')}
            </button>
            <button
              onClick={() => setActiveTab('upcoming')}
              className={`px-4 py-1.5 rounded-md font-medium text-sm relative transition-all duration-200 ${
                activeTab === 'upcoming'
                  ? 'text-white bg-blue-600/90'
                  : 'text-blue-300 hover:text-white hover:bg-blue-500/20'
              }`}
            >
              {t('watchlist.idos')}
            </button>
          </div>
          
          {/* Watchlist Container */}
          <div className="flex items-center bg-[#132F4C]/80 p-1 px-3 rounded-md shadow-md border border-[#1E4976]/30">
            <span className="mr-2 text-sm font-medium text-white">Watchlist</span>
            <Select
              value={selectedWatchlist?.id.toString() || ""}
              onValueChange={(value) => onSwitchWatchlist(Number(value))}
            >
              <SelectTrigger 
                className="border-none bg-transparent hover:bg-[#1E4976] hover:border hover:border-[#3399FF]/50 p-0 px-2 m-0 shadow-none focus:ring-0 focus:ring-offset-0 h-7 w-[auto] min-w-20 rounded-md transition-all duration-200"
              >
                <SelectValue placeholder="Default" />
                <ChevronDown className="h-3.5 w-3.5 ml-1 text-blue-300 opacity-70" />
              </SelectTrigger>
              <SelectContent className="bg-[#0A1929] border-blue-500/30 text-[#E7EBF0] rounded-md overflow-hidden shadow-lg shadow-blue-900/20">
                <SelectGroup>
                  <SelectLabel className="text-blue-400 font-medium px-3 py-2 border-b border-blue-500/20">Your Watchlists ({watchlistCount})</SelectLabel>
                  {watchlists.map((watchlist) => (
                    <SelectItem 
                      key={watchlist.id} 
                      value={watchlist.id.toString()}
                      className="hover:bg-blue-600/10 focus:bg-blue-600/10 cursor-pointer transition-colors duration-150"
                    >
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{watchlist.name}</span>
                        {watchlist.isDefault && (
                          <span className="text-xs bg-blue-500/20 text-blue-400 px-1.5 py-0.5 rounded-full font-medium">Default</span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
            
            {/* Create Button */}
            <WatchlistCreationDialog
              open={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
              onCreateWatchlist={onCreateWatchlist}
              trigger={
                <Button 
                  variant="ghost" 
                  size="icon"
                  className="h-7 w-7 hover:bg-[#1E4976] hover:border hover:border-[#3399FF]/50 text-slate-300 rounded-md transition-all duration-200"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              }
            />
            
            {/* Only show these buttons when a watchlist is selected */}
            {currentWatchlist && (
              <>
                {/* Edit Button */}
                <Dialog>
                  <DialogTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="h-7 w-7 hover:bg-[#1E4976] hover:border hover:border-[#3399FF]/50 text-slate-300 rounded-md transition-all duration-200"
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px] bg-[#0A1929] border-blue-500/30 text-white rounded-lg shadow-xl">
                    <DialogHeader className="pb-2">
                      <DialogTitle className="text-xl font-semibold text-[#E7EBF0] flex items-center gap-2">
                        <Edit3 className="h-5 w-5 text-blue-400" />
                        Edit Watchlist
                      </DialogTitle>
                      <DialogDescription className="text-[#E7EBF0]/70">
                        Make changes to your watchlist settings below.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4 space-y-6">
                      {/* Icon Selection */}
                      <div className="space-y-2">
                        <div className="text-[#E7EBF0]">Choose an Icon</div>
                        <div className="grid grid-cols-5 gap-2">
                          {[
                            { id: 1, name: "star", component: <Star className="h-5 w-5" /> },
                            { id: 2, name: "flame", component: <Flame className="h-5 w-5" /> },
                            { id: 3, name: "coins", component: <Coins className="h-5 w-5" /> },
                            { id: 4, name: "rocket", component: <Rocket className="h-5 w-5" /> },
                            { id: 5, name: "fileText", component: <FileText className="h-5 w-5" /> },
                            { id: 6, name: "droplets", component: <Droplets className="h-5 w-5" /> },
                            { id: 7, name: "diamond", component: <Diamond className="h-5 w-5" /> },
                            { id: 8, name: "gem", component: <Gem className="h-5 w-5" /> },
                            { id: 9, name: "sun", component: <Sparkles className="h-5 w-5" /> },
                            { id: 10, name: "palette", component: <Palette className="h-5 w-5" /> },
                            { id: 11, name: "leaf", component: <Leaf className="h-5 w-5" /> },
                            { id: 12, name: "database", component: <Database className="h-5 w-5" /> },
                            { id: 13, name: "cpu", component: <Cpu className="h-5 w-5" /> },
                            { id: 14, name: "music", component: <Music className="h-5 w-5" /> },
                            { id: 15, name: "trophy", component: <Trophy className="h-5 w-5" /> }
                          ].map((icon) => (
                            <Button
                              key={icon.name}
                              type="button"
                              size="sm"
                              variant="outline"
                              className={`w-10 h-10 p-0 ${
                                (currentWatchlist.icon_id ? currentWatchlist.icon_id === icon.id : currentWatchlist.icon === icon.name)
                                  ? "bg-blue-500 text-white border-blue-500" 
                                  : "bg-[#132F4C]/40 text-blue-400 border-[#1E4976]/30 hover:text-blue-300 hover:border-[#1E4976]"
                              }`}
                              onClick={() => {
                                // Set the icon input value and icon_id value to the selected icon
                                const iconInput = document.getElementById('editIcon') as HTMLInputElement;
                                const iconIdInput = document.getElementById('editIconId') as HTMLInputElement;
                                if (iconInput) iconInput.value = icon.name;
                                if (iconIdInput) iconIdInput.value = icon.id.toString();
                              }}
                            >
                              {icon.component}
                              <span className="sr-only">{icon.name}</span>
                            </Button>
                          ))}
                        </div>
                      </div>
                      
                      {/* Hidden inputs to store selected icon and icon_id */}
                      <input 
                        type="hidden" 
                        id="editIcon" 
                        defaultValue={currentWatchlist.icon || 'star'} 
                      />
                      <input 
                        type="hidden" 
                        id="editIconId" 
                        defaultValue={currentWatchlist.icon_id || '1'} 
                      />
                      
                      {/* Name Field */}
                      <div className="space-y-2">
                        <div className="text-[#E7EBF0] flex items-center">
                          Name <span className="text-red-500 ml-1">*</span>
                        </div>
                        <Input
                          id="editName"
                          defaultValue={currentWatchlist.name}
                          className="bg-[#132F4C]/80 border-blue-500/30 text-[#E7EBF0] rounded-md shadow-sm focus:border-blue-400 focus:ring-1 focus:ring-blue-400/30"
                        />
                      </div>
                      
                      {/* Description Field */}
                      <div className="space-y-2">
                        <div className="text-[#E7EBF0] flex items-center">
                          Description <span className="text-[#E7EBF0]/50 text-xs ml-2">(Optional)</span>
                        </div>
                        <Input
                          id="editDescription"
                          defaultValue={currentWatchlist.description || ''}
                          className="bg-[#132F4C]/80 border-blue-500/30 text-[#E7EBF0] rounded-md shadow-sm focus:border-blue-400 focus:ring-1 focus:ring-blue-400/30"
                        />
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Button 
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                        onClick={() => {
                          const nameInput = document.getElementById('editName') as HTMLInputElement;
                          const descriptionInput = document.getElementById('editDescription') as HTMLInputElement;
                          const iconInput = document.getElementById('editIcon') as HTMLInputElement;
                          const iconIdInput = document.getElementById('editIconId') as HTMLInputElement;
                          
                          if (nameInput && nameInput.value.trim()) {
                            const updatedWatchlists = watchlists.map(watchlist => {
                              if (watchlist.id === currentWatchlist.id) {
                                return {
                                  ...watchlist,
                                  name: nameInput.value.trim(),
                                  description: descriptionInput?.value || null,
                                  icon: iconInput?.value || '⭐',
                                  icon_id: iconIdInput?.value ? parseInt(iconIdInput.value) : 1,
                                };
                              }
                              return watchlist;
                            });
                            
                            localStorage.setItem('coinscout_watchlists', JSON.stringify(updatedWatchlists));
                            toast({
                              title: "Watchlist updated",
                              description: "Your changes have been saved",
                            });
                            
                            // Simple reload to reflect changes
                            window.location.reload();
                          }
                        }}
                      >
                        Save changes
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
                
                {/* Delete Button */}
                <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                  <AlertDialogTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="h-7 w-7 hover:bg-[#1E4976] hover:border hover:border-[#3399FF]/50 text-slate-300 rounded-md transition-all duration-200"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-[#0A1929] border-blue-500/30 text-white rounded-lg shadow-xl">
                    <AlertDialogHeader className="pb-2">
                      <AlertDialogTitle className="text-xl font-semibold text-[#E7EBF0] flex items-center gap-2">
                        <Trash2 className="h-5 w-5 text-red-500" />
                        Delete Watchlist
                      </AlertDialogTitle>
                      <AlertDialogDescription className="text-[#E7EBF0]/70">
                        Are you sure you want to delete <span className="font-medium text-blue-400">"{currentWatchlist.name}"</span>? This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="pt-4">
                      <AlertDialogCancel className="border-blue-500/40 text-[#E7EBF0] hover:bg-blue-500/20 hover:text-white transition-colors duration-200 rounded-md font-medium shadow-sm">
                        Cancel
                      </AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={() => onDeleteWatchlist(currentWatchlist.id)}
                        className="bg-red-600 hover:bg-red-700 text-white transition-colors duration-200 rounded-md font-medium shadow-sm"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                {/* Share Button */}
                <Dialog open={isShareDialogOpen} onOpenChange={setIsShareDialogOpen}>
                  <DialogTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="h-7 w-7 hover:bg-[#1E4976] hover:border hover:border-[#3399FF]/50 text-slate-300 rounded-md transition-all duration-200"
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[480px] bg-[#0A1929] border-blue-500/30 rounded-lg shadow-xl p-0 overflow-hidden">
                    <DialogHeader className="p-6 pb-4">
                      <DialogTitle className="text-xl text-[#E7EBF0] font-semibold flex items-center gap-2">
                        <Share2 className="h-5 w-5 text-blue-400" />
                        Share Watchlist
                      </DialogTitle>
                      <DialogDescription className="text-[#E7EBF0]/70">
                        Share your {currentWatchlist ? <span className="font-medium text-blue-400">{currentWatchlist.name}</span> : 'watchlist'} with friends and community.
                      </DialogDescription>
                    </DialogHeader>
                    
                    {/* Preview Card */}
                    <div className="px-6">
                      <div className="bg-[#132F4C] rounded-lg overflow-hidden border border-[#1E4976]/30 mb-5 shadow-lg shadow-blue-900/10">
                        {/* Preview Label */}
                        <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-1 text-xs text-white font-medium">
                          Preview
                        </div>
                        
                        {/* Watchlist Preview */}
                        <div className="p-4">
                          <div className="flex items-center gap-3.5">
                            <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-900/50 to-blue-800/50 flex items-center justify-center text-2xl text-amber-300 shadow-inner shadow-black/10">
                              {currentWatchlist?.icon || '⭐'}
                            </div>
                            <div>
                              <h3 className="text-[#E7EBF0] font-medium text-lg">{currentWatchlist?.name || 'My Watchlist'}</h3>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Share Link */}
                      <div className="mb-4">
                        <h4 className="text-[#E7EBF0] font-medium mb-2">Watchlist Link</h4>
                        <div className="flex items-center gap-2">
                          <Input 
                            value={`${window.location.origin}/watchlist/${currentWatchlist?.id || ''}`}
                            readOnly
                            className="flex-1 bg-[#132F4C]/80 border-blue-500/30 text-[#E7EBF0] text-sm rounded-md shadow-sm focus:border-blue-400 focus:ring-1 focus:ring-blue-400/30"
                          />
                          <Button
                            variant="default"
                            size="sm"
                            className="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md shadow-sm transition-all duration-200"
                            onClick={handleCopyLink}
                          >
                            <Copy className="h-4 w-4 mr-1" />
                            Copy
                          </Button>
                        </div>
                        <p className="text-xs text-[#E7EBF0]/50 mt-2">
                          Note: By sharing this link, you make your watchlist publicly viewable
                        </p>
                      </div>
                      
                      {/* Social Media Sharing */}
                      <div className="mb-6">
                        <h4 className="text-[#E7EBF0] font-medium mb-3">Share on Social Media</h4>
                        <div className="grid grid-cols-4 gap-3">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex flex-col items-center gap-1.5 h-auto py-3 bg-[#132F4C]/50 border-blue-500/30 hover:bg-blue-600/10 group transition-all duration-200 rounded-md shadow-sm"
                            onClick={() => {
                              const shareUrl = `${window.location.origin}/watchlist/${currentWatchlist?.id}`;
                              window.open(`https://twitter.com/intent/tweet?text=Check out my ${currentWatchlist?.name || 'CoinScout'} watchlist!&url=${encodeURIComponent(shareUrl)}`, '_blank');
                            }}
                          >
                            <FaXTwitter className="h-5 w-5 text-[#E7EBF0] group-hover:text-white" />
                            <span className="text-xs text-[#E7EBF0]/70 group-hover:text-white font-medium">Twitter</span>
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex flex-col items-center gap-1.5 h-auto py-3 bg-[#132F4C]/50 border-blue-500/30 hover:bg-[#5865F2]/10 group transition-all duration-200 rounded-md shadow-sm"
                            onClick={() => {
                              const shareUrl = `${window.location.origin}/watchlist/${currentWatchlist?.id}`;
                              toast({
                                title: "Link copied",
                                description: "Discord share link copied to clipboard",
                              });
                              navigator.clipboard.writeText(shareUrl);
                            }}
                          >
                            <FaDiscord className="h-5 w-5 text-[#5865F2] group-hover:text-[#5865F2]" />
                            <span className="text-xs text-[#E7EBF0]/70 group-hover:text-[#E7EBF0] font-medium">Discord</span>
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex flex-col items-center gap-1.5 h-auto py-3 bg-[#132F4C]/50 border-blue-500/30 hover:bg-[#0088cc]/10 group transition-all duration-200 rounded-md shadow-sm"
                            onClick={() => {
                              const shareUrl = `${window.location.origin}/watchlist/${currentWatchlist?.id}`;
                              window.open(`https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=Check out my ${currentWatchlist?.name || 'CoinScout'} watchlist!`, '_blank');
                            }}
                          >
                            <FaTelegram className="h-5 w-5 text-[#0088cc] group-hover:text-[#0088cc]" />
                            <span className="text-xs text-[#E7EBF0]/70 group-hover:text-[#E7EBF0] font-medium">Telegram</span>
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex flex-col items-center gap-1.5 h-auto py-3 bg-[#132F4C]/50 border-blue-500/30 hover:bg-black/10 group transition-all duration-200 rounded-md shadow-sm"
                            onClick={() => {
                              const shareUrl = `${window.location.origin}/watchlist/${currentWatchlist?.id}`;
                              toast({
                                title: "Link copied",
                                description: "TikTok share link copied to clipboard",
                              });
                              navigator.clipboard.writeText(shareUrl);
                            }}
                          >
                            <FaTiktok className="h-5 w-5 text-white group-hover:text-white" />
                            <span className="text-xs text-[#E7EBF0]/70 group-hover:text-[#E7EBF0] font-medium">TikTok</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                {/* Export Button */}
                <Button 
                  variant="ghost" 
                  size="icon"
                  className="h-7 w-7 hover:bg-[#1E4976] hover:border hover:border-[#3399FF]/50 text-slate-300 rounded-md transition-all duration-200"
                  onClick={() => {
                    if (currentWatchlist) {
                      const element = document.getElementById('watchlist-content');
                      if (element) {
                        toast({
                          title: "Processing PDF",
                          description: "Preparing your watchlist for download..."
                        });
                        
                        setTimeout(() => {
                          exportToPDF('watchlist-content', {
                            title: `${currentWatchlist.name} - Watchlist`,
                            includeDate: true,
                            includeBranding: true,
                            template: 'professional'
                          }).then((success) => {
                            if (success) {
                              toast({
                                title: "Success",
                                description: "Your watchlist has been downloaded as PDF"
                              });
                            } else {
                              toast({
                                title: "Error",
                                description: "There was a problem generating the PDF",
                                variant: "destructive"
                              });
                            }
                          });
                        }, 500);
                      }
                    }
                  }}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
      

    </div>
  );
}