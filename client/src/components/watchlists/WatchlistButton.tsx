import React, { forwardRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Star } from 'lucide-react';
import { useWatchlist } from './WatchlistProvider';
import { SharedWatchlistPopup, PageType } from './SharedWatchlistPopup';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { useLanguage } from '@/contexts/LanguageContext';

/**
 * Button variants for different states and sizes
 */
const watchlistButtonVariants = cva(
  "flex items-center justify-center gap-1.5 transition-all duration-200 shadow-none hover:shadow-none focus:shadow-none",
  {
    variants: {
      variant: {
        default: "bg-primary hover:bg-primary/90 text-primary-foreground shadow-none hover:shadow-none",
        outline: "border border-primary/30 hover:bg-primary/10 text-primary shadow-none hover:shadow-none",
        ghost: "hover:bg-slate-700/30 text-muted-foreground hover:text-foreground shadow-none hover:shadow-none",
        secondary: "bg-secondary hover:bg-secondary/80 text-secondary-foreground shadow-none hover:shadow-none",
        subtle: "bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground shadow-none hover:shadow-none",
        inWatchlist: "bg-transparent hover:bg-slate-700/30 text-white shadow-none hover:shadow-none",
        icon: "p-2 h-auto w-auto rounded-full shadow-none hover:shadow-none",
      },
      size: {
        sm: "text-xs px-2.5 py-1 h-7 rounded-md",
        md: "text-sm px-3 py-1.5 h-9 rounded-md",
        lg: "text-base px-4 py-2 h-10 rounded-md",
        xl: "text-lg px-5 py-2.5 h-12 rounded-md",
      },
    },
    defaultVariants: {
      variant: "outline",
      size: "md",
    },
  }
);

export interface WatchlistButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * The unique ID of the coin or project
   */
  coinId: string;
  /**
   * Indicates if this is for an upcoming project (true) or a regular coin (false)
   */
  isUpcoming?: boolean;
  /**
   * The current page type the button is being rendered on
   */
  pageType: PageType;
  /**
   * Visual variant for styling the button
   */
  watchlistVariant?: 'default' | 'outline' | 'ghost' | 'secondary' | 'subtle' | 'icon';
  /**
   * Size of the button
   */
  watchlistSize?: 'sm' | 'md' | 'lg' | 'xl';
  /**
   * Custom class names for the root button
   */
  className?: string;
  /**
   * Whether to show status text like "In Watchlist" or "Add to Watchlist"
   */
  showStatusText?: boolean;
  /**
   * Set the watchlist ID to target a specific watchlist
   * If not provided, will use the default watchlist
   */
  targetWatchlistId?: number;
  /**
   * Called after a successful addition to or removal from watchlist
   */
  onWatchlistToggle?: (isInWatchlist: boolean) => void;
  /**
   * Icon to show when item is not in the watchlist
   */
  notInWatchlistIcon?: React.ReactNode;
  /**
   * Icon to show when item is in the watchlist
   */
  inWatchlistIcon?: React.ReactNode;
}

/**
 * A flexible watchlist button that can be used across the application to maintain consistent
 * visual design and behavior while being adaptable to different contexts.
 */
export const WatchlistButton = forwardRef<HTMLButtonElement, WatchlistButtonProps>(({
  coinId,
  isUpcoming = false,
  pageType,
  watchlistVariant = 'outline',
  watchlistSize = 'md',
  className,
  showStatusText = true,
  targetWatchlistId,
  onWatchlistToggle,
  notInWatchlistIcon = <Star className="w-[14px] h-[14px] text-yellow-400 hover:text-yellow-300 transition-colors" />,
  inWatchlistIcon = <Star className="w-[14px] h-[14px] text-yellow-400 fill-yellow-400/30 hover:text-yellow-300 transition-colors" />,
  ...props
}, ref) => {
  // Access the watchlist context
  const { watchlists, isInWatchlist, toggleWatchlistItem, getCurrentWatchlist } = useWatchlist();
  const { t } = useLanguage();
  
  // Local state to track if this item is in the watchlist
  const [isInList, setIsInList] = useState(false);
  
  // Target watchlist ID, either provided or default
  const [effectiveWatchlistId, setEffectiveWatchlistId] = useState<number | undefined>(targetWatchlistId);
  
  // Update the effective watchlist ID when targetWatchlistId changes or when watchlists change
  useEffect(() => {
    if (targetWatchlistId !== undefined) {
      setEffectiveWatchlistId(targetWatchlistId);
    } else {
      const currentWatchlist = getCurrentWatchlist();
      setEffectiveWatchlistId(currentWatchlist?.id);
    }
  }, [targetWatchlistId, watchlists, getCurrentWatchlist]);
  
  // Update the isInList state when relevant dependencies change
  useEffect(() => {
    if (effectiveWatchlistId !== undefined) {
      setIsInList(isInWatchlist(coinId, effectiveWatchlistId, isUpcoming));
    } else {
      // If no watchlist ID, check across all watchlists
      setIsInList(isInWatchlist(coinId, undefined, isUpcoming));
    }
  }, [coinId, effectiveWatchlistId, isInWatchlist, isUpcoming, watchlists]);
  
  // Handle direct toggle to the effective watchlist
  const handleDirectToggle = async () => {
    if (effectiveWatchlistId !== undefined) {
      try {
        await toggleWatchlistItem(coinId, effectiveWatchlistId, isUpcoming);
        const updatedStatus = isInWatchlist(coinId, effectiveWatchlistId, isUpcoming);
        setIsInList(updatedStatus);
        if (onWatchlistToggle) {
          onWatchlistToggle(updatedStatus);
        }
      } catch (error) {
        console.error('Error toggling watchlist item:', error);
      }
    }
  };
  
  // Determine if this is a direct-action button (when effectiveWatchlistId is defined)
  // or a dropdown-trigger button (when effectiveWatchlistId is undefined)
  const isDirectActionButton = effectiveWatchlistId !== undefined;
  
  return isDirectActionButton ? (
    // Direct action button with toggle functionality
    <Button
      ref={ref}
      className={cn(
        watchlistButtonVariants({ 
          variant: isInList ? 'inWatchlist' : watchlistVariant, 
          size: watchlistSize 
        }),
        className
      )}
      onClick={handleDirectToggle}
      {...props}
    >
      {isInList ? inWatchlistIcon : notInWatchlistIcon}
      {showStatusText && (
        <span>{isInList ? t('watchlist.inWatchlist') : t('watchlist.addToWatchlist')}</span>
      )}
    </Button>
  ) : (
    // Dropdown trigger button
    <SharedWatchlistPopup 
      coinId={coinId} 
      isUpcoming={isUpcoming} 
      pageType={pageType}
    >
      <Button
        ref={ref}
        className={cn(
          watchlistButtonVariants({ 
            variant: isInList ? 'inWatchlist' : watchlistVariant, 
            size: watchlistSize 
          }),
          className
        )}
        {...props}
      >
        {isInList ? inWatchlistIcon : notInWatchlistIcon}
        {showStatusText && (
          <span>{isInList ? t('watchlist.inWatchlist') : t('watchlist.addToWatchlist')}</span>
        )}
      </Button>
    </SharedWatchlistPopup>
  );
});

WatchlistButton.displayName = 'WatchlistButton';

export default WatchlistButton;