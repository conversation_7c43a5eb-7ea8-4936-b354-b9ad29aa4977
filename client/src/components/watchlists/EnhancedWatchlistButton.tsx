import React, { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEnhancedWatchlist } from '@/hooks/useEnhancedWatchlist';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useLanguage } from '@/contexts/LanguageContext';

// Media query hook as inline implementation to avoid circular dependencies
function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);
    
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [query]);

  return matches;
}

interface EnhancedWatchlistButtonProps {
  coinId: string;
  isUpcoming?: boolean;
  variant?: 'default' | 'compact' | 'icon';
  className?: string;
  tooltipText?: string;
  watchlistId?: number; // Optional specific watchlist ID
  onToggleComplete?: () => void; // Callback when toggle completes
}

/**
 * Enhanced Watchlist Button component that correctly handles both listed coins and upcoming projects
 * with proper visual feedback and interactions
 */
export function EnhancedWatchlistButton({
  coinId,
  isUpcoming = false,
  variant = 'default',
  className = '',
  tooltipText,
  watchlistId,
  onToggleComplete
}: EnhancedWatchlistButtonProps) {
  const {
    isInWatchlist,
    toggleWatchlistItem,
    getCurrentWatchlist
  } = useEnhancedWatchlist();
  const { t } = useLanguage();
  
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const isMobile = !isDesktop;
  
  // Get the current watchlist (for default behavior)
  const currentWatchlist = getCurrentWatchlist();
  const currentWatchlistId = currentWatchlist?.id || 0;
  
  // Check if coin is in the specified or current watchlist
  const targetWatchlistId = watchlistId !== undefined ? watchlistId : currentWatchlistId;
  const isInCurrentWatchlist = isInWatchlist(
    coinId, 
    targetWatchlistId, 
    isUpcoming
  );
  
  // Generate tooltip text
  const getTooltipText = () => {
    const watchlistName = currentWatchlist?.name || 'watchlist';
    
    if (tooltipText) return tooltipText;
    
    return isInCurrentWatchlist
      ? `Remove from ${watchlistName}`
      : `Add to ${watchlistName}`;
  };
  
  // Handle click on the button
  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      if (targetWatchlistId === 0) {
        console.error("No watchlist ID available");
        return;
      }
      
      await toggleWatchlistItem(
        coinId, 
        targetWatchlistId,
        isUpcoming
      );
      
      // Trigger callback if provided
      if (onToggleComplete) {
        onToggleComplete();
      }
    } catch (error) {
      console.error("Error toggling watchlist item:", error);
    }
  };
  
  // Render different button variants
  switch (variant) {
    case 'compact':
      return (
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "p-0 h-auto",
                isInCurrentWatchlist ? "text-yellow-400" : "text-muted-foreground hover:text-foreground",
                className
              )}
              onClick={handleClick}
              aria-label={isInCurrentWatchlist ? "Remove from watchlist" : "Add to watchlist"}
            >
              <Star 
                className={cn(
                  "w-4 h-4 transition-all", 
                  isInCurrentWatchlist ? "fill-yellow-400" : "fill-none"
                )}
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={isMobile ? "bottom" : "top"}>
            {getTooltipText()}
          </TooltipContent>
        </Tooltip>
      );
      
    case 'icon':
      return (
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <button
              className={cn(
                "group flex items-center justify-center transition-colors",
                isInCurrentWatchlist ? "text-yellow-400" : "text-muted-foreground hover:text-foreground",
                className
              )}
              onClick={handleClick}
              aria-label={isInCurrentWatchlist ? "Remove from watchlist" : "Add to watchlist"}
            >
              <Star 
                className={cn(
                  "w-5 h-5 transition-all", 
                  isInCurrentWatchlist ? "fill-yellow-400" : "fill-none group-hover:fill-yellow-400/20"
                )}
              />
            </button>
          </TooltipTrigger>
          <TooltipContent side={isMobile ? "bottom" : "top"}>
            {getTooltipText()}
          </TooltipContent>
        </Tooltip>
      );
      
    default:
      return (
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "flex items-center gap-1.5",
                isInCurrentWatchlist 
                  ? "text-yellow-400 hover:text-yellow-500" 
                  : "text-muted-foreground hover:text-foreground",
                className
              )}
              onClick={handleClick}
              aria-label={isInCurrentWatchlist ? "Remove from watchlist" : "Add to watchlist"}
            >
              <Star 
                className={cn(
                  "w-4 h-4 transition-all", 
                  isInCurrentWatchlist ? "fill-yellow-400" : "fill-none"
                )}
              />
              <span>
                {isInCurrentWatchlist ? t('watchlist.inWatchlist') : t('watchlist.addToWatchlist')}
              </span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side={isMobile ? "bottom" : "top"}>
            {getTooltipText()}
          </TooltipContent>
        </Tooltip>
      );
  }
}