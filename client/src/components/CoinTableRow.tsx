import React from "react";
import { useLocation } from "wouter";
import { TableCell, TableRow } from "@/components/ui/table";
import { CoinLogo } from "@/components/CoinLogo";
import { ContextAwareWatchlistButton } from "@/components/watchlists/ContextAwareWatchlistButton";
import { SevenDayScoreBadge } from "@/components/SevenDayScoreBadge";
import UpcomingIdoBadgeRenderer from "@/components/ui/UpcomingIdoBadgeRenderer";
import TableScoreGauge from "@/components/ui/TableScoreGauge";
import { cn, formatCoinAgeWithTranslation } from "@/lib/utils";
import { Trash2 } from "lucide-react";
import { useWatchlist } from "@/components/watchlists/WatchlistProvider";
import { useToast } from "@/hooks/use-toast";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { LazyHoverContent } from "@/components/LazyHoverContent";
import { Coin } from "@/types/CoinTypes";
import { CoinStatus } from "@/types/CoinStatus";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  getScoreRatingStyles,
  getStatusTranslationInComponent,
  getStatusFromScore,
} from "../utils/scoreUtils";
import {
  coinRowBaseClasses,
  coinRowEvenClass,
  coinRowOddClass,
} from "../styles/coinRowStyles.ts";
// LazyHoverContent component is used to display the coin details in hover cards
// It will fetch additional data like price if needed

interface CoinTableRowProps {
  coin: Coin;
  index: number;
  currentPage: number;
  pageSize: number;
  onRowClick?: (coinId: string) => void;
  showAgeBadge?: boolean;
  showDeleteButton?: boolean;
  watchlistId?: string;
}

const CoinTableRow = ({
  coin,
  index,
  currentPage,
  pageSize,
  onRowClick,
  showAgeBadge = false,
  showDeleteButton = false,
  watchlistId,
}: CoinTableRowProps) => {
  const [, setLocation] = useLocation();
  const { t } = useLanguage();
  const { toggleWatchlistItem } = useWatchlist();
  const { toast } = useToast();

  const handleRowClick = () => {
    if (onRowClick) {
      onRowClick(coin.id);
    } else {
      setLocation(`/coin/${coin.id}`);
    }
  };

  const handleDeleteFromWatchlist = async (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    
    if (!watchlistId) {
      toast({
        title: t("watchlist.error"),
        description: t("watchlist.noWatchlistSelected"),
        variant: "destructive",
      });
      return;
    }

    try {
      await toggleWatchlistItem(coin, watchlistId, t("watchlist.removedFromWatchlist"));
      toast({
        title: t("watchlist.success"),
        description: t("watchlist.coinRemovedFromWatchlist").replace("{coinName}", coin.name),
      });
    } catch (error) {
      console.error("Failed to remove coin from watchlist:", error);
      toast({
        title: t("watchlist.error"),
        description: t("watchlist.failedToRemoveCoin"),
        variant: "destructive",
      });
    }
  };

  return (
    <TableRow
      key={coin.id}
      id={index === 0 ? "coin-list-row-example" : undefined}
      data-tour="coinlist-table-row"
      className={cn(
        coinRowBaseClasses,
        index % 2 === 0 ? coinRowEvenClass : coinRowOddClass,
        coin.demo && "blur-sm opacity-70 pointer-events-none" // Demo coinler için blur efekti
      )}
      onClick={handleRowClick}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleRowClick();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${coin.name}`}
    >
      <TableCell className="font-semibold group-hover/row:bg-[#132F4C]/20 group-hover/row:text-[#E7EBF0] transition-all duration-300 py-3.5 px-1 first:rounded-l-sm text-center">
        <div className="flex items-center justify-center">
          <span className="text-center text-sm font-semibold text-[#E7EBF0] group-hover/row:text-white">
            {(currentPage - 1) * pageSize + index + 1}
          </span>
        </div>
      </TableCell>

      {/* Favori Sütunu - UpcomingProjects ile aynı stilde */}
      <TableCell className="py-3.5 px-0 text-center w-[3%]">
        <div className="flex items-center justify-center">
          <div
            id={index === 0 ? "watchlist-btn-example" : undefined}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              console.log("Watchlist wrapper clicked for", coin.id);
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.stopPropagation();
                e.preventDefault();
              }
            }}
            className="flex items-center justify-center cursor-pointer"
            style={{ isolation: "isolate", zIndex: 10, position: "relative" }}
          >
            <ContextAwareWatchlistButton
              coinId={coin.id}
              variant="icon"
              pageType="coinlist"
              className="watchlist-button transition-all duration-300 hover:scale-110 group-hover/row:opacity-100 opacity-80"
            />
          </div>
          {showDeleteButton && (
            <TableCell className="text-center py-1.5 bg-[#0A1929]/50 group-hover/row:bg-[#132F4C]/5 transition-all duration-300 last:rounded-r-sm w-1 px-0" >
              <div className="flex items-center justify-center w-full h-full">
                <button
                  onClick={handleDeleteFromWatchlist}
                  className=" text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded transition-all duration-200 opacity-50 group-hover/row:opacity-100 w-5 h-5 flex items-center justify-center"
                  title={t("watchlist.removeFromWatchlist")}
                  aria-label={t("watchlist.removeCoinFromWatchlist").replace("{coinName}", coin.name)}
                >
                  <Trash2 className="h-3 w-3" />
                </button>
              </div>
            </TableCell>
          )}
        </div>
      </TableCell>

      {/* Name Column - Styled like UpcomingTableRow */}
      <TableCell className="group-hover/row:bg-[#132F4C]/20 transition-all duration-300 py-3.5 px-2 xl:table-cell name-column w-[10%]">
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="cursor-pointer w-full">
              <div className="flex items-center ml-1">
                {/* Coin Logo */}
                <div
                  className="w-8 h-8 flex items-center justify-center flex-shrink-0 mr-2"
                  style={{ pointerEvents: "none" }}
                >
                  <CoinLogo
                    symbol={coin.symbol}
                    size="md"
                    className="opacity-90"
                    imageUrl={coin.image}
                    hideText={true}
                  />
                </div>

                {/* Coin Name and Symbol */}
                <div
                  className="flex flex-col"
                  style={{ pointerEvents: "none" }}
                >
                  <div className="flex items-center space-x-1">
                    <span 
                      className="text-xs font-medium text-gray-200 group-hover/row:text-[#66B2FF] transition-colors duration-300 tracking-tight text-left truncate max-w-[120px]"
                      title={coin.name}
                    >
                      {coin.name.length > 15 ? `${coin.name.substring(0, 15)}...` : coin.name}
                    </span>
                    <span className="text-[10px] text-left text-gray-500 group-hover/row:text-[#94A3B8] transition-colors duration-300 truncate">
                      {coin.symbol}
                    </span>
                  </div>
                  <div className="flex items-center">
                    {/* Coin age badge - showing relative time format like "X gün önce" */}
                    {showAgeBadge && coin.coin_age && (
                      <span className="inline-flex px-1.5 bg-[#1E293B] text-[9px] font-medium text-white rounded-md transition-colors duration-300 tracking-tight whitespace-nowrap h-[20px] flex items-center">
                        {formatCoinAgeWithTranslation(coin.coin_age, true, t)}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <div className="block sm:hidden mt-1">
                <div className="flex items-center justify-center gap-2">
                  <div className="w-[70px] text-[10px] px-2 py-[0.85] rounded-md font-semibold transition-colors duration-300 text-center">
                    {coin.totalScore.score} •{" "}
                    {(() => {
                      const score = coin.totalScore.score;
                      // Doğrudan iç sistem CoinStatus değerini hesaplayalım
                      const statusValue = getStatusFromScore(score);

                      // Çeviriyi getStatusTranslationInComponent ile alalım
                      return getStatusTranslationInComponent(statusValue, t);
                    })()}
                  </div>
                  <SevenDayScoreBadge
                    scoreChange={coin.sevenDayChange}
                    className="h-[24px] w-[70px]"
                    alwaysColored={false}
                  />
                </div>
              </div>
            </div>
          </HoverCardTrigger>
          <HoverCardContent
            className="w-80 bg-[#132F4C]/95 border border-[#1E4976]/60 backdrop-blur-md overflow-visible rounded-md z-[9999]"
            sideOffset={8}
            side="right"
            align="start"
            avoidCollisions={true}
          >
            <LazyHoverContent
              coinId={coin.id}
              initialData={{
                name: coin.name,
                symbol: coin.symbol,
                rank: coin.rank,
                image: coin.image,
              }}
            />
          </HoverCardContent>
        </HoverCard>
      </TableCell>

      {/* Tokenomics Column */}
      <TableCell className="table-cell pl-12 md:pl-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/30 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.tokenomics.score}
                  value=""
                  badgeType="initialCap"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Security Column */}
      <TableCell className="table-cell ml-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/25 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.security.score}
                  value=""
                  badgeType="investors"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Social Column */}
      <TableCell className="table-cell ml-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/20 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.social.score}
                  value=""
                  badgeType="raised"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Market Column */}
      <TableCell className="table-cell ml-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/15 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.market.score}
                  value=""
                  badgeType="raised"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Insights Column */}
      <TableCell className="table-cell ml-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/10 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.insights.score}
                  value=""
                  badgeType="launchpad"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Total Score Column */}
      <TableCell
        id="total-score-column"
        className="text-center ml-4 opacity-95 bg-[#0A1929] transition-all duration-300 group-hover/row:bg-[#132F4C]/40 group-hover/row:opacity-100 py-1.5 px-2 relative total-score-col w-[10%]"
      >
        <div className="absolute inset-0 w-full h-full bg-[#0A1929] group-hover/row:bg-[#132F4C]/40 transition-all duration-300 -z-10"></div>
        <div className="flex items-center justify-center h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <TableScoreGauge
                  score={coin.totalScore.score}
                  size="sm"
                  showValue={true}
                  className="flex items-center"
                />
              </div>
              {(() => {
                const score = coin.totalScore.score;
                // utils/scoreUtils içindeki fonksiyonu kullan
                const { text, bgClass, colorClass } =
                  getScoreRatingStyles(score);
                // Doğrudan iç sistem CoinStatus değerini hesaplayalım
                const statusValue = getStatusFromScore(score);

                // Çeviriyi getStatusTranslationInComponent ile alalım
                return (
                  <div
                    className={`w-[90px] h-10 flex items-center justify-center px-3 rounded-md text-xs font-medium ${bgClass}/10 ${colorClass} pointer-events-none`}
                  >
                    {getStatusTranslationInComponent(statusValue, t)}
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      </TableCell>

      {/* 7D Change Column */}
      <TableCell className={`text-center ml-4 py-1.5 bg-[#0A1929]/50 group-hover/row:bg-[#132F4C]/5 transition-all duration-300 seven-day-change-col w-[6%] ${!showDeleteButton ? 'last:rounded-r-sm' : ''}`}>
        <div className="flex items-center justify-center w-full h-full">
          <SevenDayScoreBadge
            scoreChange={coin.sevenDayChange}
            className="transition-all duration-300 mx-auto"
            alwaysColored={false}
          />
        </div>
      </TableCell>

      
    </TableRow>
  );
};

export default CoinTableRow;
