import { useState, useEffect, useCallback } from 'react';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Languages, Check, Info } from "lucide-react";
import { cn } from "@/lib/utils";
import { useLanguage } from '@/contexts/LanguageContext';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const getCountryCode = (languageCode: string) => {
  // Map language codes to country codes for flag display
  switch (languageCode.toLowerCase()) {
    case 'en': return "gb";
    case 'es': return "es";
    case 'fr': return "fr";
    case 'ar': return "sa";
    case 'de': return "de";
    case 'zh': return "cn";
    case 'ja': return "jp";
    case 'ko': return "kr";
    case 'pt': return "pt";
    case 'ru': return "ru";
    case 'tr': return "tr";
    case 'it': return "it";
    case 'nl': return "nl";
    case 'hi': return "in";
    case 'pl': return "pl";
    default: return null;
  }
};

export function LanguageSelector() {
  const [isOpen, setIsOpen] = useState(false);
  const [search, setSearch] = useState('');
  const { currentLanguage, availableLanguages, isLoading, setLanguage, t } = useLanguage();
  const [localTranslations, setLocalTranslations] = useState<Record<string, string>>({
    'title': 'Change Language',
    'label': 'Select a language',
    'search': 'Search languages...',
    'available': 'Available Languages',
    'searchResults': 'Search Results',
    'noResults': 'No languages found'
  });

  // Use a mix of client-side fallbacks and server translations to ensure UI always works
  const getTranslation = (key: string) => {
    const fullKey = `language.selector.${key}`;
    // Try server translation first
    const serverTranslation = t(fullKey, 'system');
    // If it returns the key unchanged, fall back to local translations
    return serverTranslation === fullKey ? localTranslations[key] : serverTranslation;
  };
  
  const filteredLanguages = availableLanguages.filter(lang => 
    lang.name.toLowerCase().includes(search.toLowerCase()) ||
    lang.code.toLowerCase().includes(search.toLowerCase()) ||
    lang.nativeName?.toLowerCase().includes(search.toLowerCase())
  );

  // Direct button handler, no need for useCallback as buttons are recreated with dialog anyway
  const handleLanguageSelect = async (code: string) => {
    console.log('Setting language to:', code);
    // Close dialog immediately for better UX
    setIsOpen(false);
    
    try {
      if (code) {
        // Use the LanguageContext's setLanguage function instead of reloading
        await setLanguage(code);
        console.log('Language changed successfully to:', code);
      }
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="relative min-h-[40px] min-w-[40px] w-[40px] h-[40px] rounded-md bg-card/40 hover:bg-primary hover:text-white transition-colors flex items-center justify-center shadow-sm border border-border/40 backdrop-blur-sm"
        title={getTranslation('title')}
      >
        {currentLanguage ? (
          getCountryCode(currentLanguage.code) ? (
            <img 
              src={`https://flagicons.lipis.dev/flags/4x3/${getCountryCode(currentLanguage.code)}.svg`}
              alt={`${currentLanguage.name} flag`}
              className="w-5 h-4 rounded-sm object-cover"
            />
          ) : (
            <Languages className="h-[18px] w-[18px] text-muted-foreground" />
          )
        ) : (
          <Languages className="h-[18px] w-[18px] text-muted-foreground" />
        )}
        <span className="sr-only">{getTranslation('label')}</span>
      </button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[400px] md:max-w-[450px] p-0 fixed left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 max-h-[70vh]">
          <div className="p-5 space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Languages className="h-5 w-5 text-primary" />
                <h2 className="text-lg font-semibold tracking-tight">
                  {getTranslation('title')}
                </h2>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="text-sm font-medium text-muted-foreground">
                {getTranslation('available')}
              </h3>
              <div className="grid grid-cols-1 gap-2 max-h-[200px] overflow-y-auto pr-1">
                {isLoading ? (
                  Array.from({ length: 6 }).map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))
                ) : filteredLanguages.length === 0 ? (
                  <p className="text-center text-muted-foreground py-2">
                    {getTranslation('noResults')}
                  </p>
                ) : (
                  filteredLanguages.map(lang => (
                    <button
                      key={lang.code}
                      onClick={() => handleLanguageSelect(lang.code)}
                      className={cn(
                        "flex items-center gap-3 p-3 rounded-md text-sm",
                        "transition-all duration-200",
                        "hover:bg-accent",
                        "bg-card",
                        "border border-border",
                        currentLanguage?.code === lang.code && "ring-2 ring-primary/50 bg-accent/30 border-primary/30"
                      )}
                      dir={lang.isRTL ? 'rtl' : 'ltr'}
                    >
                      <div className="flex items-center justify-center w-6 h-6">
                        {getCountryCode(lang.code) ? (
                          <img 
                            src={`https://flagicons.lipis.dev/flags/4x3/${getCountryCode(lang.code)}.svg`}
                            alt={`${lang.name} flag`}
                            className="w-5 h-3 rounded-sm object-cover"
                          />
                        ) : (
                          <Languages className="h-3 w-3 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex flex-col items-start flex-1">
                        <span className="font-medium text-sm">{lang.nativeName}</span>
                        <span className="text-xs text-muted-foreground">{lang.name}</span>
                      </div>
                      {currentLanguage?.code === lang.code && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </button>
                  ))
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}