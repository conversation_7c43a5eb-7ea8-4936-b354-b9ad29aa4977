import React from 'react';
import { Button } from '@/components/ui/button';
import { useNotifications } from '@/hooks/useNotifications';

const NotificationTester = () => {
  const { notifications, unreadCount, markAsRead } = useNotifications();

  // Create a test notification
  const createTestNotification = () => {
    // Simulate a new notification by directly adding to the test data
    const testNotification = {
      id: `test-${Date.now()}`,
      title: 'Test Notification',
      message: 'This is a test notification to verify visibility tracking',
      type: 'system' as const,
      priority: 'medium' as const,
      status: 'unread' as const,
      timestamp: new Date(),
      coinSymbol: 'BTC'
    };

    console.log('🧪 Test notification created:', testNotification);
    console.log('📊 Current notifications count:', notifications.length);
    console.log('📬 Current unread count:', unreadCount);
  };

  const testMarkAsRead = () => {
    if (notifications.length > 0) {
      const firstNotification = notifications[0];
      console.log('🧪 Testing mark as read for:', firstNotification.id);
      markAsRead(firstNotification.id);
    } else {
      console.log('🧪 No notifications to mark as read');
    }
  };

  return (
    <div className="p-4 border rounded-md bg-card space-y-2">
      <h3 className="font-medium">Notification Tester</h3>
      <div className="text-sm text-muted-foreground">
        <p>Notifications: {notifications.length}</p>
        <p>Unread: {unreadCount}</p>
      </div>
      <div className="space-x-2">
        <Button size="sm" onClick={createTestNotification}>
          Create Test Notification
        </Button>
        <Button size="sm" variant="outline" onClick={testMarkAsRead}>
          Test Mark as Read
        </Button>
      </div>
    </div>
  );
};

export default NotificationTester;