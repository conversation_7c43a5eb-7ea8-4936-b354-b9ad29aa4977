import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Bell, X, Settings, CheckCircle2, AlertTriangle, TrendingUp, InfoIcon, ExternalLink, Wifi } from 'lucide-react';
import { Link } from 'wouter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useNotifications } from '@/hooks/useNotifications';

// Notification types
export type NotificationType = 'alert' | 'update' | 'system' | 'security' | 'price';

// Notification priority
export type NotificationPriority = 'high' | 'medium' | 'low';

// Notification status
export type NotificationStatus = 'unread' | 'read';

// Notification model
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  status: NotificationStatus;
  timestamp: Date;
  link?: string;
  coinId?: string;
  coinName?: string;
  coinSymbol?: string;
  data?: Record<string, any>;
  source?: 'websocket' | 'manual' | 'system'; // Track notification source
}

// Demo notifications removed - using real WebSocket data

const getIconForType = (type: NotificationType) => {
  switch (type) {
    case 'alert':
      return AlertTriangle;
    case 'price':
      return TrendingUp;
    case 'security':
      return AlertTriangle;
    case 'update':
      return InfoIcon;
    case 'system':
      return CheckCircle2;
    default:
      return InfoIcon;
  }
};

const getColorForType = (type: NotificationType) => {
  switch (type) {
    case 'alert':
    case 'security':
      return 'text-amber-500';
    case 'price':
      return 'text-cyan-500';
    case 'update':
      return 'text-blue-500';
    case 'system':
      return 'text-green-500';
    default:
      return 'text-primary';
  }
};

// Price alert notifications removed - using real WebSocket data

const NotificationItem = ({
  notification,
  onMarkAsRead,
  onRemove
}: {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onRemove: (id: string) => void;
}) => {
  const Icon = getIconForType(notification.type);
  const colorClass = getColorForType(notification.type);
  const isUnread = notification.status === 'unread';
  const itemRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Intersection Observer to detect when notification becomes visible
  useEffect(() => {
    const element = itemRef.current;
    if (!element || !isUnread) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible && isUnread) {
          setIsVisible(true);
          // Mark as read when visible for the first time
          const timeout = setTimeout(() => {
            // Double-check the notification is still unread before marking
            if (notification.status === 'unread') {
              console.log('👁️ [NotificationRead] Auto-marking notification as read due to visibility:', notification.id);
              console.log('⏱️ [NotificationRead] Notification was visible for 1.5 seconds');
              onMarkAsRead(notification.id);
            }
          }, 1500); // 1.5 second delay to ensure user actually saw it

          return () => clearTimeout(timeout);
        }
      },
      {
        threshold: 0.6, // 60% of the notification must be visible
        rootMargin: '0px'
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [notification.id, notification.status, isVisible, onMarkAsRead]);

  // Format timestamp to relative time (like "5m ago", "3h ago", etc.)
  const formatRelativeTime = (timestamp: Date | string | number) => {
    try {
      // Convert to Date object safely
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Just now';
      }

      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffSec = Math.floor(diffMs / 1000);

      if (diffSec < 60) return `${diffSec}s ago`;

      const diffMin = Math.floor(diffSec / 60);
      if (diffMin < 60) return `${diffMin}m ago`;

      const diffHour = Math.floor(diffMin / 60);
      if (diffHour < 24) return `${diffHour}h ago`;

      const diffDay = Math.floor(diffHour / 24);
      if (diffDay < 7) return `${diffDay}d ago`;

      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Just now';
    }
  };

  return (
    <motion.div
      ref={itemRef}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, height: 0, marginTop: 0, marginBottom: 0 }}
      transition={{ duration: 0.2 }}
      className={cn(
        "relative p-4 border-b border-border last:border-0 transition-all",
        isUnread ? "bg-primary/5" : "",
        isUnread && notification.priority === 'high' ? "bg-amber-500/10 dark:bg-amber-950/20" : ""
      )}
    >
      <div className="absolute top-2 right-2 flex space-x-1">
        {isUnread && (
          <span className="h-2 w-2 rounded-full bg-primary animate-pulse" />
        )}
        <button
          onClick={() => onRemove(notification.id)}
          className="h-6 w-6 rounded-full flex items-center justify-center text-muted-foreground hover:bg-accent hover:text-foreground transition-colors"
          aria-label="Remove notification"
        >
          <X className="h-3.5 w-3.5" />
        </button>
      </div>

      <div className="flex gap-3">
        <div className={cn(
          "mt-1 p-2 rounded-full bg-background border border-border shadow-sm",
          isUnread ? "border-primary/40 bg-gradient-to-br from-primary/5 to-transparent" : ""
        )}>
          <Icon className={cn("h-4 w-4", colorClass)} />
        </div>

        <div className="flex-1">
          <div className="flex flex-col">
            <h4 className="text-sm font-medium mb-1">{notification.title}</h4>
            <p className="text-xs text-muted-foreground mb-2">{notification.message}</p>

            {/* Show coin info if available */}
            {notification.coinSymbol && (
              <div className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary mb-2 w-fit">
                {notification.coinSymbol}
              </div>
            )}

            <div className="flex items-center justify-between mt-1">
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">
                  {formatRelativeTime(notification.timestamp)}
                </span>
                {/* WebSocket indicator for real-time notifications */}
                {(notification as any).source === 'websocket' && (
                  <div className="flex items-center gap-1 px-1.5 py-0.5 rounded-full bg-green-500/10 border border-green-500/20">
                    <Wifi className="h-2.5 w-2.5 text-green-600 dark:text-green-400" />
                    <span className="text-[9px] text-green-600 dark:text-green-400 font-medium">LIVE</span>
                  </div>
                )}
              </div>

              {notification.coinId ? (
                <Link href={`/coin/${notification.coinId}`}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 px-2 text-xs hover:bg-primary/10"
                    onClick={() => onMarkAsRead(notification.id)}
                  >
                    View
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </Button>
                </Link>
              ) : notification.link && (
                <Link href={notification.link}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 px-2 text-xs hover:bg-primary/10"
                    onClick={() => onMarkAsRead(notification.id)}
                  >
                    View
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export const NotificationDropdown = () => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('unread');
  const dropdownRef = React.useRef<HTMLDivElement>(null);
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Get real notifications from WebSocket
  const notificationsData = useNotifications();
  const { 
    notifications, 
    allNotifications, 
    unreadCount, 
    markAsRead, 
    removeNotification, 
    markAllAsRead, 
    setPopupOpen, 
    getDisplayNotifications, 
    loadAllNotifications,
    refreshAllNotifications,
    allNotificationsLoading,
    allNotificationsLoaded
  } = notificationsData;
  const loading = false; // useNotifications handles loading state

  // Set default tab only when popup first opens (not when unreadCount changes)
  const [hasSetInitialTab, setHasSetInitialTab] = useState(false);
  
  useEffect(() => {
    if (isOpen && !hasSetInitialTab) {
      const defaultTab = unreadCount > 0 ? 'unread' : 'all';
      setActiveTab(defaultTab);
      setHasSetInitialTab(true);
      console.log('📱 [Popup] Setting initial tab to:', defaultTab, 'unreadCount:', unreadCount);
    } else if (!isOpen) {
      setHasSetInitialTab(false); // Reset when popup closes
    }
  }, [isOpen, unreadCount, hasSetInitialTab]);

  // Handle tab switching - load all notifications when switching to 'all' tab
  const handleTabChange = async (tab: string) => {
    console.log('📱 [Tab] Switching to tab:', tab);
    setActiveTab(tab);
    
    // Always refresh all notifications when switching to "all" tab
    if (tab === 'all') {
      console.log('🔄 [Tab] Refreshing all notifications for "all" tab...');
      await refreshAllNotifications(); // Force refresh every time
    }
  };

  // Get notifications based on active tab
  const getTabNotifications = () => {
    if (activeTab === 'unread') {
      // Unread tab: Show WebSocket notifications (frozen when popup is open)
      const displayNotifications = getDisplayNotifications();
      return displayNotifications.filter(notif => {
        const isUnread = notif.status === 'unread';
        if (isUnread && (notif as any).source === 'websocket') {
          console.log('📱 [Unread Tab] Showing WebSocket notification:', notif.id, notif.title);
        }
        return isUnread;
      });
    } else {
      // All tab: Show API notifications
      console.log('📱 [All Tab] Showing', allNotifications.length, 'API notifications');
      return allNotifications;
    }
  };

  const filteredNotifications = getTabNotifications();

  // Count WebSocket notifications in unread list (use display notifications)
  const websocketUnreadCount = getDisplayNotifications().filter((notif: any) => 
    notif.status === 'unread' && notif.source === 'websocket'
  ).length;

  // Handle popup open/close state management
  const handleSetIsOpen = (open: boolean) => {
    setIsOpen(open);
    setPopupOpen(open);

    // The initial tab setting is now handled by useEffect to prevent conflicts
    // We only handle the API refresh logic here
    if (open) {
      // If we would default to "all" tab and haven't set initial tab yet, refresh notifications
      const wouldDefaultToAll = unreadCount === 0;
      if (wouldDefaultToAll && !hasSetInitialTab) {
        console.log('🔄 [Popup] Opening popup, will default to "all" tab, refreshing notifications...');
        refreshAllNotifications();
      }
    }
  };

  // Click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        handleSetIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Auto-close timeout
  useEffect(() => {
    if (isOpen) {
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set a new timeout to auto-close after 5 seconds of inactivity
      timeoutRef.current = setTimeout(() => {
        handleSetIsOpen(false);
      }, 5000);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isOpen]);

  // Reset timeout on user interaction
  const resetAutoCloseTimeout = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      handleSetIsOpen(false);
    }, 5000);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Button with Badge */}
      <button
        className="relative min-h-[40px] min-w-[40px] w-[40px] h-[40px] rounded-md bg-card/40 hover:bg-primary hover:text-white transition-colors duration-600 flex items-center justify-center shadow-sm border border-border/40 backdrop-blur-sm group"
        onClick={() => handleSetIsOpen(!isOpen)}
        aria-label="View Notifications"
      >
        <Bell className="h-5 w-5 text-muted-foreground group-hover:text-white transition-colors duration-300" />

        {/* Notification Badge */}
        {unreadCount > 0 && (
          <Badge
            variant="default"
            className="absolute -top-2 -right-2 min-w-[1.25rem] h-5 px-1.5 py-0 flex items-center justify-center text-[0.65rem] rounded-full bg-primary text-white"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </button>

      {/* Dropdown Panel */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay to capture clicks outside */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => handleSetIsOpen(false)}
              aria-hidden="true"
            />

            {/* Notification Panel */}
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 mt-2 z-50 w-[380px] animate-in fade-in zoom-in-95 duration-100 origin-top-right"
              role="dialog"
              aria-modal="true"
              style={{ maxHeight: 'calc(100vh - 100px)', overscrollBehavior: 'contain' }}
              onMouseMove={resetAutoCloseTimeout}
              onClick={resetAutoCloseTimeout}
            >
              <div className="rounded-xl shadow-lg ring-1 ring-black/5 dark:ring-white/10 overflow-hidden backdrop-blur-lg bg-background/95 border border-border">
                {/* Header with gradient background matching profile dropdown */}
                <div className="p-4 border-b border-border bg-gradient-to-br from-primary/5 to-transparent dark:from-primary/10">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-base">{t('notifications.title')}</h3>
                    <div className="flex gap-2">
                      {unreadCount > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={markAllAsRead}
                          className="h-8 text-xs hover:bg-primary/10"
                        >
                          Mark all as read
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Tabs */}
                  <Tabs
                    value={activeTab}
                    onValueChange={handleTabChange}
                    className="mt-2"
                  >
                    <TabsList className="grid grid-cols-2 h-8">
                      <TabsTrigger value="unread" className="text-xs">
                        Unread
                        {unreadCount > 0 && (
                          <div className="flex items-center gap-1 ml-1.5">
                            <Badge variant="outline" className="text-[0.65rem] h-4 px-1 bg-background">
                              {unreadCount}
                            </Badge>
                            {websocketUnreadCount > 0 && (
                              <Badge variant="outline" className="text-[0.65rem] h-4 px-1 bg-green-500/10 border-green-500/30 text-green-600 dark:text-green-400">
                                <Wifi className="h-2 w-2 mr-0.5" />
                                {websocketUnreadCount}
                              </Badge>
                            )}
                          </div>
                        )}
                      </TabsTrigger>
                      <TabsTrigger value="all" className="text-xs">
                        All
                        {allNotificationsLoading && (
                          <div className="ml-1.5 animate-spin h-3 w-3 border border-current border-t-transparent rounded-full" />
                        )}
                        {allNotifications.length > 0 && !allNotificationsLoading && (
                          <Badge variant="outline" className="ml-1.5 text-[0.65rem] h-4 px-1 bg-background">
                            {allNotifications.length}
                          </Badge>
                        )}
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                {/* Content */}
                <ScrollArea className="max-h-[400px] overflow-y-auto" style={{ height: 'auto', maxHeight: '60vh', minHeight: '200px' }}>
                  {(activeTab === 'all' && allNotificationsLoading) ? (
                    <div className="flex items-center justify-center h-[200px]">
                      <div className="animate-pulse flex flex-col items-center">
                        <div className="h-12 w-12 rounded-full bg-primary/10 mb-3" />
                        <div className="h-4 w-32 bg-primary/10 rounded-md mb-2" />
                        <div className="h-3 w-24 bg-primary/5 rounded-md" />
                      </div>
                    </div>
                  ) : filteredNotifications.length > 0 ? (
                    <div>
                      <AnimatePresence initial={false}>
                        {filteredNotifications.map(notification => (
                          <NotificationItem
                            key={notification.id}
                            notification={notification}
                            onMarkAsRead={markAsRead}
                            onRemove={removeNotification}
                          />
                        ))}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 text-center text-muted-foreground">
                      <Bell className="h-12 w-12 text-muted-foreground/40 mb-3" />
                      <h4 className="text-sm font-medium mb-1">
                        {activeTab === 'unread' ? 'No unread notifications' : 'No notifications'}
                      </h4>
                      <p className="text-xs">You're all caught up!</p>
                    </div>
                  )}
                </ScrollArea>

                {/* Footer removed */}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};