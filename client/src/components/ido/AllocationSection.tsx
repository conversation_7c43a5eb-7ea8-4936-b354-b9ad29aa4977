import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer } from 'recharts';

interface AllocationSectionProps {
  allocation?: {
    teamAndAdvisors: number;
    publicSale: number;
    privateSale: number;
    ecosystem: number;
    liquidity: number;
    treasury: number;
    marketing: number;
  };
  allocations?: Array<{
    name: string;
    value: number | null;
    percentage: number | null;
  }>;
  className?: string;
}

const COLORS = [
  '#3B82F6', // Blue
  '#14b8a6', // Teal
  '#8b5cf6', // Purple
  '#F59E0B', // Amber
  '#10b981', // Green
  '#6366f1', // Indigo
  '#ef4444', // Red
];

const AllocationSection: React.FC<AllocationSectionProps> = ({ allocation, allocations, className = '' }) => {
  // Convert allocation data to format required by recharts
  let data: Array<{ name: string; value: number }> = [];
  
  if (allocations && Array.isArray(allocations)) {
    // Use allocations array from API response
    data = allocations
      .filter(alloc => alloc.percentage !== null && alloc.percentage !== undefined)
      .map(alloc => ({
        name: alloc.name,
        value: alloc.percentage || 0
      }));
  } else if (allocation) {
    // Use legacy allocation object
    data = [
      { name: 'Team & Advisors', value: allocation.teamAndAdvisors },
      { name: 'Public Sale', value: allocation.publicSale },
      { name: 'Private Sale', value: allocation.privateSale },
      { name: 'Ecosystem', value: allocation.ecosystem },
      { name: 'Liquidity', value: allocation.liquidity },
      { name: 'Treasury', value: allocation.treasury },
      { name: 'Marketing', value: allocation.marketing },
    ];
  }

  // Custom tooltip for the pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-slate-800 p-2 text-xs rounded border border-slate-700">
          <p className="font-medium">{`${payload[0].name}: ${payload[0].value}%`}</p>
        </div>
      );
    }
    return null;
  };
  
  return (
    <div className={`${className} bg-slate-800 rounded-lg p-4 border border-slate-700`}>
      <h3 className="text-lg font-semibold mb-4">Token Allocation</h3>
      
      <div className="flex flex-col md:flex-row items-center">
        <div className="w-full md:w-1/2 h-[250px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        <div className="w-full md:w-1/2 mt-4 md:mt-0">
          <div className="grid grid-cols-1 gap-2">
            {data.map((item, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-4 h-4 rounded-sm mr-2" 
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                />
                <span className="text-sm">{item.name}</span>
                <span className="ml-auto text-sm font-medium">{item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllocationSection;