import React from "react";
import { Globe, Twitter, Linkedin, MailPlus } from "lucide-react";

interface TeamMember {
  name: string;
  role: string;
  position?: string;
  secondaryPosition?: string;
  avatar?: string;
  image?: string;
  bio?: string;
  linkedin?: string;
  twitter?: string;
  links?: {
    website?: string;
    twitter?: string;
    linkedin?: string;
    email?: string;
  };
}

interface TeamSectionProps {
  projectName: string;
  members: TeamMember[];
  condensed?: boolean;
  maxMembers?: number;
  onViewAll?: () => void;
}

const TeamSection: React.FC<TeamSectionProps> = ({
  projectName,
  members,
  condensed = false,
  maxMembers = 4,
  onViewAll,
}) => {
  // If condensed mode is true, only show the specified number of members
  const displayMembers = condensed ? members.slice(0, maxMembers) : members;

  return (
    <div className="bg-slate-800/90 border border-slate-700/40 shadow-lg shadow-slate-900/60 rounded-xl p-6 transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden group">
      <div className="flex justify-between items-center mb-5 relative z-10">
        <div className="flex items-center group-hover:text-blue-400 transition-colors duration-300">
          <div className="p-2 rounded-md bg-slate-700/70 border border-slate-700/40 shadow-md shadow-slate-900/40 mr-2.5">
            <svg
              className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors duration-300"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-white group-hover:text-white transition-colors duration-300">
            {projectName} Team
          </h2>
        </div>
        {condensed && members.length > maxMembers && onViewAll && (
          <button
            onClick={onViewAll}
            className="text-xs bg-slate-800/90 text-blue-400 hover:text-blue-300 px-3 py-1.5 rounded-md flex items-center justify-center gap-1.5 transition-all duration-300 font-medium border border-slate-700/40 shadow-md shadow-slate-900/40 hover:bg-slate-700/80 hover:border-blue-500/30 group"
          >
            <span>View All</span>{" "}
            <span className="ml-1 text-slate-300 group-hover:text-white">
              ({members.length})
            </span>
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 relative z-10">
        {displayMembers.map((member, index) => (
          <div
            key={index}
            className="bg-slate-800/90 border border-slate-700/40 shadow-md shadow-slate-900/40 rounded-xl p-4 transition-all duration-300 hover:border-blue-500/30 group"
          >
            <div className="flex items-center mb-3">
              <div className="h-16 w-16 rounded-full overflow-hidden bg-slate-700/80 border border-slate-700/40 mr-4 transition-all duration-300 group-hover:border-blue-500/20 flex items-center justify-center">
                <div className="text-2xl font-bold text-slate-300">
                  {member.name ? member.name.charAt(0).toUpperCase() : "?"}
                </div>
              </div>
              <div>
                <h3 className="text-white font-semibold text-base">
                  {member.name}
                </h3>
                <div className="flex flex-wrap gap-1.5 mt-1.5">
                  <span className="bg-slate-700/70 text-slate-300 text-xs px-2.5 py-1 rounded-full border border-slate-700/40 shadow-md shadow-slate-900/40 font-medium">
                    {member.role || member.position}
                  </span>
                  {member.secondaryPosition && (
                    <span className="bg-slate-700/70 text-blue-400 text-xs px-2.5 py-1 rounded-full border border-slate-700/40 shadow-md shadow-slate-900/40 font-medium">
                      {member.secondaryPosition}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {(member.twitter || member.linkedin || (member.links && Object.keys(member.links).length > 0)) && (
              <div className="flex gap-2 mt-3">
                {(member.links?.website) && (
                  <a
                    href={member.links.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-slate-800/90 border border-slate-700/40 hover:bg-slate-700/80 hover:border-blue-500/30 text-slate-300 hover:text-white p-2 rounded-md transition-all duration-300 shadow-md shadow-slate-900/40 group"
                    aria-label={`${member.name}'s website`}
                  >
                    <Globe
                      size={16}
                      className="text-blue-400 group-hover:text-blue-300"
                    />
                  </a>
                )}
                {(member.twitter || member.links?.twitter) && (
                  <a
                    href={member.twitter || member.links?.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-slate-800/90 border border-slate-700/40 hover:bg-slate-700/80 hover:border-blue-500/30 text-slate-300 hover:text-white p-2 rounded-md transition-all duration-300 shadow-md shadow-slate-900/40 group"
                    aria-label={`${member.name}'s Twitter`}
                  >
                    <Twitter
                      size={16}
                      className="text-blue-400 group-hover:text-blue-300"
                    />
                  </a>
                )}
                {(member.linkedin || member.links?.linkedin) && (
                  <a
                    href={member.linkedin || member.links?.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-slate-800/90 border border-slate-700/40 hover:bg-slate-700/80 hover:border-blue-500/30 text-slate-300 hover:text-white p-2 rounded-md transition-all duration-300 shadow-md shadow-slate-900/40 group"
                    aria-label={`${member.name}'s LinkedIn`}
                  >
                    <Linkedin
                      size={16}
                      className="text-blue-400 group-hover:text-blue-300"
                    />
                  </a>
                )}
                {member.links?.email && (
                  <a
                    href={`mailto:${member.links.email}`}
                    className="bg-slate-800/90 border border-slate-700/40 hover:bg-slate-700/80 hover:border-blue-500/30 text-slate-300 hover:text-white p-2 rounded-md transition-all duration-300 shadow-md shadow-slate-900/40 group"
                    aria-label={`Email ${member.name}`}
                  >
                    <MailPlus
                      size={16}
                      className="text-blue-400 group-hover:text-blue-300"
                    />
                  </a>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Visual element for depth */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-slate-500/5 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  );
};

export default TeamSection;
