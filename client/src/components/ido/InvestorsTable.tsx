import React from 'react';
import { ExternalLink } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface Investor {
  name: string;
  type: string;
  amount: string;
  logo: string;
}

interface InvestorsTableProps {
  investors: Investor[];
  className?: string;
}

const InvestorsTable: React.FC<InvestorsTableProps> = ({ investors, className = '' }) => {
  const { t } = useLanguage();
  
  return (
    <div className={`${className} bg-slate-800 rounded-lg p-4 border border-slate-700`}>
      <h3 className="text-lg font-semibold mb-4">Key Investors</h3>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="text-left border-b border-slate-700">
              <th className="py-2 pl-2 font-medium text-sm text-slate-400">Investor</th>
              <th className="py-2 font-medium text-sm text-slate-400">Type</th>
              <th className="py-2 pr-2 font-medium text-sm text-slate-400 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            {investors.map((investor, index) => (
              <tr 
                key={index} 
                className={`border-b border-slate-700 hover:bg-slate-700/30 transition-colors`}
              >
                <td className="py-3 pl-2">
                  <div className="flex items-center">
                    <div className="w-8 h-8 mr-3 bg-slate-700 rounded-full overflow-hidden flex items-center justify-center">
                      {investor.logo ? (
                        <img 
                          src={investor.logo} 
                          alt={investor.name} 
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/logos/default-investor.png';
                          }}
                        />
                      ) : (
                        <span className="text-xs font-medium">
                          {investor.name ? investor.name.substring(0, 2).toUpperCase() : "?"}
                        </span>
                      )}
                    </div>
                    <div className="font-medium text-sm">
                      {investor.name || <span className="text-slate-400 italic">{t('emptyState.noInvestorInfo')}</span>}
                    </div>
                  </div>
                </td>
                <td className="py-3">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    investor.type === 'Lead Investor' 
                      ? 'bg-primary/20 text-primary' 
                      : investor.type === 'Strategic Investor'
                        ? 'bg-amber-500/20 text-amber-500'
                        : 'bg-slate-600/50 text-slate-300'
                  }`}>
                    {investor.type}
                  </span>
                </td>
                <td className="py-3 pr-2 text-right font-medium text-sm">{investor.amount}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="mt-4 text-center">
        <a 
          href="#" 
          className="text-sm text-primary hover:text-primary/80 inline-flex items-center"
        >
          View all investors <ExternalLink className="w-3 h-3 ml-1" />
        </a>
      </div>
    </div>
  );
};

export default InvestorsTable;