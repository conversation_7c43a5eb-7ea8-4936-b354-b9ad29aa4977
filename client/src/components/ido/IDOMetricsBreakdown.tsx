import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  BarChart4,
  ChevronDown,
  ChevronUp,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  XCircle,
} from "lucide-react";
import IMCScoreIcon from "@/components/icons/IMCScoreIcon";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import ScoreGauge from "@/components/coin-detail/ScoreGauge";
import { useLanguage } from "@/contexts/LanguageContext";

// Define the interface for metric items from API
interface MetricItem {
  name: string;
  description: string;
  what_are_we_scoring?: string;
  why_is_this_important?: string;
  weight: number;
  score: number;
}

interface IDOMetricsBreakdownProps {
  metrics: MetricItem[];
  overallScore: number;
  onRequestFeature: () => void;
  onReportError: () => void;
}

// Helper function to get the appropriate icon for a score - matches coin detail styling
const getStatusIcon = (score: number) => {
  if (score >= 90) return <CheckCircle className="h-[18px] w-[18px] text-[#00D88A]" />;
  if (score >= 75) return <CheckCircle className="h-[18px] w-[18px] text-[#00B8D9]" />;
  if (score >= 65) return <AlertCircle className="h-[18px] w-[18px] text-[#FFAB00]" />;
  if (score >= 50) return <AlertTriangle className="h-[18px] w-[18px] text-[#FF5630]" />;
  return <XCircle className="h-[18px] w-[18px] text-[#FF3B3B]" />;
};

// Metric icon mapping
const metricIcons: Record<string, React.ReactNode> = {
  "IMC Score": <IMCScoreIcon className="h-5 w-5 flex-shrink-0 text-blue-400" />,
  "Funding Score": (
    <svg
      className="h-5 w-5 flex-shrink-0 text-blue-400"
      style={{ display: 'inline-block', minWidth: '20px' }}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
      <g id="SVGRepo_iconCarrier">
        <path
          d="M11.7255 17.1019C11.6265 16.8844 11.4215 16.7257 11.1734 16.6975C10.9633 16.6735 10.7576 16.6285 10.562 16.5636C10.4743 16.5341 10.392 16.5019 10.3158 16.4674L10.4424 16.1223C10.5318 16.1622 10.6239 16.1987 10.7182 16.2317L10.7221 16.2331L10.7261 16.2344C11.0287 16.3344 11.3265 16.3851 11.611 16.3851C11.8967 16.3851 12.1038 16.3468 12.2629 16.2647L12.2724 16.2598L12.2817 16.2544C12.5227 16.1161 12.661 15.8784 12.661 15.6021C12.661 15.2955 12.4956 15.041 12.2071 14.9035C12.062 14.8329 11.8559 14.7655 11.559 14.6917C11.2545 14.6147 10.9987 14.533 10.8003 14.4493C10.6553 14.3837 10.5295 14.279 10.4161 14.1293C10.3185 13.9957 10.2691 13.7948 10.2691 13.5319C10.2691 13.2147 10.3584 12.9529 10.5422 12.7315C10.7058 12.5375 10.9381 12.4057 11.2499 12.3318C11.4812 12.277 11.6616 12.1119 11.7427 11.8987C11.8344 12.1148 12.0295 12.2755 12.2723 12.3142C12.4751 12.3465 12.6613 12.398 12.8287 12.4677L12.7122 12.8059C12.3961 12.679 12.085 12.6149 11.7841 12.6149C10.7848 12.6149 10.7342 13.3043 10.7342 13.4425C10.7342 13.7421 10.896 13.9933 11.1781 14.1318L11.186 14.1357L11.194 14.1393C11.3365 14.2029 11.5387 14.2642 11.8305 14.3322C12.1322 14.4004 12.3838 14.4785 12.5815 14.5651L12.5856 14.5669L12.5897 14.5686C12.7365 14.6297 12.8624 14.7317 12.9746 14.8805L12.9764 14.8828L12.9782 14.8852C13.0763 15.012 13.1261 15.2081 13.1261 15.4681C13.1261 15.7682 13.0392 16.0222 12.8604 16.2447C12.7053 16.4377 12.4888 16.5713 12.1983 16.6531C11.974 16.7163 11.8 16.8878 11.7255 17.1019Z"
          fill="#3B82F6"
        ></path>
        <path
          d="M11.9785 18H11.497C11.3893 18 11.302 17.9105 11.302 17.8V17.3985C11.302 17.2929 11.2219 17.2061 11.1195 17.1944C10.8757 17.1667 10.6399 17.115 10.412 17.0394C10.1906 16.9648 9.99879 16.8764 9.83657 16.7739C9.76202 16.7268 9.7349 16.6312 9.76572 16.5472L10.096 15.6466C10.1405 15.5254 10.284 15.479 10.3945 15.5417C10.5437 15.6262 10.7041 15.6985 10.8755 15.7585C11.131 15.8429 11.3762 15.8851 11.611 15.8851C11.8129 15.8851 11.9572 15.8628 12.0437 15.8181C12.1302 15.7684 12.1735 15.6964 12.1735 15.6021C12.1735 15.4929 12.1158 15.411 12.0004 15.3564C11.8892 15.3018 11.7037 15.2422 11.4442 15.1777C11.1104 15.0933 10.8323 15.0039 10.6098 14.9096C10.3873 14.8103 10.1936 14.6514 10.0288 14.433C9.86396 14.2096 9.78156 13.9092 9.78156 13.5319C9.78156 13.095 9.91136 12.7202 10.1709 12.4074C10.4049 12.13 10.7279 11.9424 11.1401 11.8447C11.2329 11.8227 11.302 11.7401 11.302 11.6425V11.2C11.302 11.0895 11.3893 11 11.497 11H11.9785C12.0862 11 12.1735 11.0895 12.1735 11.2V11.6172C12.1735 11.7194 12.2487 11.8045 12.3471 11.8202C12.7082 11.8777 13.0255 11.9866 13.2989 12.1469C13.3765 12.1924 13.4073 12.2892 13.3775 12.3756L13.0684 13.2725C13.0275 13.3914 12.891 13.4417 12.7812 13.3849C12.433 13.2049 12.1007 13.1149 11.7841 13.1149C11.4091 13.1149 11.2216 13.2241 11.2216 13.4425C11.2216 13.5468 11.2773 13.6262 11.3885 13.6809C11.4998 13.7305 11.6831 13.7851 11.9386 13.8447C12.2682 13.9192 12.5464 14.006 12.773 14.1053C12.9996 14.1996 13.1953 14.356 13.3602 14.5745C13.5291 14.7929 13.6136 15.0908 13.6136 15.4681C13.6136 15.8851 13.4879 16.25 13.2365 16.5628C13.0176 16.8354 12.7145 17.0262 12.3274 17.1353C12.2384 17.1604 12.1735 17.2412 12.1735 17.3358V17.8C12.1735 17.9105 12.0862 18 11.9785 18Z"
          fill="#3B82F6"
        ></path>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.59235 5H13.8141C14.8954 5 14.3016 6.664 13.8638 7.679L13.3656 8.843L13.2983 9C13.7702 8.97651 14.2369 9.11054 14.6282 9.382C16.0921 10.7558 17.2802 12.4098 18.1256 14.251C18.455 14.9318 18.5857 15.6958 18.5019 16.451C18.4013 18.3759 16.8956 19.9098 15.0182 20H8.38823C6.51033 19.9125 5.0024 18.3802 4.89968 16.455C4.81587 15.6998 4.94656 14.9358 5.27603 14.255C6.12242 12.412 7.31216 10.7565 8.77823 9.382C9.1696 9.11054 9.63622 8.97651 10.1081 9L10.0301 8.819L9.54263 7.679C9.1068 6.664 8.5101 5 9.59235 5Z"
          stroke="#3B82F6"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M13.2983 9.75C13.7125 9.75 14.0483 9.41421 14.0483 9C14.0483 8.58579 13.7125 8.25 13.2983 8.25V9.75ZM10.1081 8.25C9.69391 8.25 9.35812 8.58579 9.35812 9C9.35812 9.41421 9.69391 9.75 10.1081 9.75V8.25ZM15.9776 8.64988C16.3365 8.44312 16.4599 7.98455 16.2531 7.62563C16.0463 7.26671 15.5878 7.14336 15.2289 7.35012L15.9776 8.64988ZM13.3656 8.843L13.5103 9.57891L13.5125 9.57848L13.3656 8.843ZM10.0301 8.819L10.1854 8.08521L10.1786 8.08383L10.0301 8.819ZM8.166 7.34357C7.80346 7.14322 7.34715 7.27469 7.1468 7.63722C6.94644 7.99976 7.07791 8.45607 7.44045 8.65643L8.166 7.34357ZM13.2983 8.25H10.1081V9.75H13.2983V8.25ZM15.2289 7.35012C14.6019 7.71128 13.9233 7.96683 13.2187 8.10752L13.5125 9.57848C14.3778 9.40568 15.2101 9.09203 15.9776 8.64988L15.2289 7.35012ZM13.2209 8.10709C12.2175 8.30441 11.1861 8.29699 10.1854 8.08525L9.87486 9.55275C11.0732 9.80631 12.3086 9.81521 13.5103 9.57891L13.2209 8.10709ZM10.1786 8.08383C9.47587 7.94196 8.79745 7.69255 8.166 7.34357L7.44045 8.65643C8.20526 9.0791 9.02818 9.38184 9.88169 9.55417L10.1786 8.08383Z"
          fill="#3B82F6"
        ></path>
      </g>
    </svg>
  ),
  "Launchpad Score": (
    <svg
      className="h-5 w-5 flex-shrink-0 text-blue-400"
      style={{ display: 'inline-block', minWidth: '20px' }}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
      <g id="SVGRepo_iconCarrier">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.7242 8.45192L7.28876 7.19383C6.55281 8.18469 5.92055 9.24884 5.40213 10.3696L3.7242 8.45192ZM1.24742 8.6585L4.54987 12.4327L4.07152 13.6286C3.92296 14 4.01003 14.4242 4.29289 14.7071L4.60651 15.0207C2.90342 16.9411 1.9627 18.9496 1.05132 21.6838C0.931539 22.0431 1.02506 22.4393 1.29289 22.7071C1.56073 22.9749 1.95689 23.0685 2.31623 22.9487C5.05037 22.0373 7.05889 21.0966 8.97928 19.3935L9.29289 19.7071C9.57576 19.99 9.99997 20.077 10.3714 19.9285L11.5673 19.4501L15.3415 22.7526C15.5911 22.971 15.9327 23.0514 16.2535 22.9673C16.5744 22.8832 16.8326 22.6456 16.943 22.3328L19.9291 13.8722C21.8977 11.5428 23 8.57479 23 5.48078V2C23 1.44772 22.5523 1 22 1H18.5192C15.4252 1 12.4572 2.10225 10.1278 4.0709L1.66718 7.05701C1.35444 7.16739 1.11676 7.42565 1.03268 7.74646C0.948589 8.06728 1.02903 8.40891 1.24742 8.6585ZM3.68527 20.3147C4.31277 18.7992 5.017 17.5929 6.02356 16.4378L7.56223 17.9764C6.40713 18.983 5.20083 19.6872 3.68527 20.3147ZM10.2408 17.8266L9.70711 17.2929L6.70711 14.2929L6.17337 13.7592L6.88327 11.9844C7.53465 10.356 8.44936 8.84567 9.59079 7.51401L10.1674 6.84129C12.2572 4.40319 15.308 3 18.5192 3H21V5.48078C21 8.69196 19.5968 11.7428 17.1587 13.8326L16.486 14.4092C15.1543 15.5506 13.644 16.4653 12.0156 17.1167L10.2408 17.8266ZM15.5481 20.2758L13.6304 18.5979C14.7512 18.0795 15.8153 17.4472 16.8062 16.7112L15.5481 20.2758ZM15 8C15 7.44772 15.4477 7 16 7C16.5523 7 17 7.44772 17 8C17 8.55228 16.5523 9 16 9C15.4477 9 15 8.55228 15 8ZM16 5C14.3431 5 13 6.34315 13 8C13 9.65685 14.3431 11 16 11C17.6569 11 19 9.65685 19 8C19 6.34315 17.6569 5 16 5Z"
          fill="#3B82F6"
        />
      </g>
    </svg>
  ),
  "Investor Score": (
    <svg
      className="h-5 w-5 flex-shrink-0 text-blue-400"
      style={{ display: 'inline-block', minWidth: '20px' }}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
      <g id="SVGRepo_iconCarrier">
        <path
          d="M2 14C2 10.2288 2 8.34315 3.17157 7.17157C4.34315 6 6.22876 6 10 6H14C17.7712 6 19.6569 6 20.8284 7.17157C22 8.34315 22 10.2288 22 14C22 17.7712 22 19.6569 20.8284 20.8284C19.6569 22 17.7712 22 14 22H10C6.22876 22 4.34315 22 3.17157 20.8284C2 19.6569 2 17.7712 2 14Z"
          stroke="#3B82F6"
          strokeWidth="1.5"
        ></path>
        <path
          d="M16 6C16 4.11438 16 3.17157 15.4142 2.58579C14.8284 2 13.8856 2 12 2C10.1144 2 9.17157 2 8.58579 2.58579C8 3.17157 8 4.11438 8 6"
          stroke="#3B82F6"
          strokeWidth="1.5"
        ></path>
        <path
          d="M12 17.3333C13.1046 17.3333 14 16.5871 14 15.6667C14 14.7462 13.1046 14 12 14C10.8954 14 10 13.2538 10 12.3333C10 11.4129 10.8954 10.6667 12 10.6667M12 17.3333C10.8954 17.3333 10 16.5871 10 15.6667M12 17.3333V18M12 10V10.6667M12 10.6667C13.1046 10.6667 14 11.4129 14 12.3333"
          stroke="#3B82F6"
          strokeWidth="1.5"
          strokeLinecap="round"
        ></path>
      </g>
    </svg>
  ),
  "Social Score": (
    <svg
      className="h-5 w-5 flex-shrink-0 text-blue-400"
      style={{ display: 'inline-block', minWidth: '20px' }}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g>
        <path
          d="M9.5 8.5C9.5 10.1569 8.15685 11.5 6.5 11.5C4.84315 11.5 3.5 10.1569 3.5 8.5C3.5 6.84315 4.84315 5.5 6.5 5.5C8.15685 5.5 9.5 6.84315 9.5 8.5Z"
          stroke="#3B82F6"
          strokeWidth="1.5"
        />
        <path
          d="M20.5 8.5C20.5 10.1569 19.1569 11.5 17.5 11.5C15.8431 11.5 14.5 10.1569 14.5 8.5C14.5 6.84315 15.8431 5.5 17.5 5.5C19.1569 5.5 20.5 6.84315 20.5 8.5Z"
          stroke="#3B82F6"
          strokeWidth="1.5"
        />
        <path
          d="M14.5 17.5C14.5 19.1569 13.1569 20.5 11.5 20.5C9.84315 20.5 8.5 19.1569 8.5 17.5C8.5 15.8431 9.84315 14.5 11.5 14.5C13.1569 14.5 14.5 15.8431 14.5 17.5Z"
          stroke="#3B82F6"
          strokeWidth="1.5"
        />
        <path
          d="M9 10.5L14 15.5"
          stroke="#3B82F6"
          strokeWidth="1.5"
          strokeLinecap="round"
        />
        <path
          d="M14.5 10.5L9 15.5"
          stroke="#3B82F6"
          strokeWidth="1.5"
          strokeLinecap="round"
        />
      </g>
    </svg>
  ),
  // Fallback icon
  default: (
    <BarChart4 className="h-5 w-5 flex-shrink-0 text-blue-400" />
  ),
};

const IDOMetricsBreakdown: React.FC<IDOMetricsBreakdownProps> = ({
  metrics,
  overallScore,
  onRequestFeature,
  onReportError,
}) => {
  const [activeMetricIndex, setActiveMetricIndex] = useState<number | null>(
    null,
  );

  const { t } = useLanguage();

  // Helper function to get score status - matches coin detail thresholds
  const getScoreStatus = (score: number): string => {
    if (score >= 90) return t("score:excellent", "Excellent");
    if (score >= 75) return t("score:positive", "Positive");
    if (score >= 65) return t("score:average", "Average");
    if (score >= 50) return t("score:weak", "Weak");
    return t("score:critical", "Critical");
  };

  // Helper function to get score color class based on score value directly
  // This matches the coin detail color scheme
  const getScoreColorClass = (score: number): string => {
    if (score >= 90) return "bg-[#00D88A]"; // Excellent
    if (score >= 75) return "bg-[#00B8D9]"; // Positive
    if (score >= 65) return "bg-[#FFAB00]"; // Average
    if (score >= 50) return "bg-[#FF5630]"; // Weak
    return "bg-[#FF3B3B]";                  // Critical
  };

  return (
    <div className="space-y-5">
      {/* Score and action buttons */}
      <div className="flex flex-wrap justify-between items-center gap-3 mb-5">
        <div className="flex items-center gap-4">
          {/* ScoreGauge component for visual consistency with the top section */}
          <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 p-3 rounded-lg border border-slate-700/30 shadow-md">
            <ScoreGauge
              score={overallScore}
              size="md"
              id="ido-metrics-overall-score"
              showLabel={false}
              animated={true}
              className="shadow-md shadow-slate-900/40"
            />
            <div className="text-xs text-slate-400 mt-2 text-center">
              Overall Score
            </div>
          </div>
          <div className="space-y-0.5">
            <div className="flex items-center gap-1.5">
              {getStatusIcon(overallScore)}
              <span
                className={`text-sm font-medium ${
                  overallScore >= 90
                    ? "text-[#00D88A]"
                    : overallScore >= 75
                      ? "text-[#00B8D9]"
                      : overallScore >= 65
                        ? "text-[#FFAB00]"
                        : overallScore >= 50
                          ? "text-[#FF5630]"
                          : "text-[#FF3B3B]"
                }`}
              >
                {getScoreStatus(overallScore)}
              </span>
            </div>
            <div className="text-xs text-slate-400">
              Based on {metrics.length} metrics
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="bg-slate-800 border-slate-700 border-[0.5px] hover:bg-slate-700 text-slate-300 hover:text-white flex items-center gap-1.5 py-1 px-2.5"
            onClick={onRequestFeature}
          >
            <BarChart4 className="h-3.5 w-3.5 text-blue-400" />
            <span className="text-xs font-medium">Request Feature</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="bg-slate-800 border-slate-700 border-[0.5px] hover:bg-slate-700 text-slate-300 hover:text-white flex items-center gap-1.5 py-1 px-2.5"
            onClick={onReportError}
          >
            <AlertTriangle className="h-3.5 w-3.5 text-red-400" />
            <span className="text-xs font-medium">Report Error</span>
          </Button>
        </div>
      </div>

      {/* Metrics breakdown */}
      <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg border border-slate-700/30 overflow-hidden shadow-md">
        <div className="px-5 py-4 border-b border-slate-700/20 bg-slate-800/80">
          <div className="flex items-center gap-3">
            <div className="bg-blue-500/20 p-3 rounded-md text-blue-400 flex-shrink-0">
              <BarChart4 className="h-5 w-5" />
            </div>
            <h3 className="text-lg font-bold text-white">
              IDO Metrics Breakdown
            </h3>
          </div>
        </div>

        <div className="divide-y divide-slate-700/10">
          {metrics.map((metric, index) => (
            <div key={index} className="overflow-hidden">
              {/* Metric header - always visible */}
              <button
                className={`w-full text-left px-5 py-3.5 flex items-center justify-between transition-colors ${
                  activeMetricIndex === index
                    ? "bg-slate-700/30"
                    : "hover:bg-slate-700/10"
                }`}
                onClick={() =>
                  setActiveMetricIndex(
                    activeMetricIndex === index ? null : index,
                  )
                }
              >
                <div className="flex items-center gap-2">
                  <div className="flex-shrink-0">
                    {getStatusIcon(metric.score)}
                  </div>
                  <span className="font-medium text-white text-sm">
                    {metric.name}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    <div className="text-right w-16">
                      <span className="text-xs font-semibold text-white">
                        {metric.score}
                        <span className="text-slate-400">/100</span>
                      </span>
                    </div>
                    <div
                      className={`ml-2 px-2 py-0.5 text-xs rounded-full min-w-[70px] text-center ${
                        metric.score >= 90
                          ? "bg-[#00D88A]/20 text-[#00D88A]" // Excellent
                          : metric.score >= 75
                            ? "bg-[#00B8D9]/20 text-[#00B8D9]" // Good/Positive
                            : metric.score >= 65
                              ? "bg-[#FFAB00]/20 text-[#FFAB00]" // Fair/Average
                              : metric.score >= 50
                                ? "bg-[#FF5630]/20 text-[#FF5630]" // Poor/Weak
                                : "bg-[#FF3B3B]/20 text-[#FF3B3B]" // Bad/Critical
                      }`}
                    >
                      {getScoreStatus(metric.score)}
                    </div>
                  </div>
                  <ChevronDown
                    className={`h-4 w-4 ml-2 text-slate-400 transition-transform ${
                      activeMetricIndex === index ? "rotate-180" : ""
                    }`}
                  />
                </div>
              </button>

              {/* Expandable metric details */}
              {activeMetricIndex === index && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="bg-slate-800/50 border-t border-slate-700/10 p-5"
                >
                  <div className="space-y-4">
                    {/* Score Section */}
                    <div>
                      <div className="text-base font-semibold text-blue-400 flex items-center mb-3">
                        <span className="mr-2">📊</span>
                        Score Details
                      </div>
                      <div className="bg-slate-800/80 rounded-lg px-4 py-3.5 border border-slate-700/40">
                        <div className="flex items-center gap-2 mb-2">
                          <Progress
                            value={metric.score}
                            max={100}
                            className="h-2 w-full bg-slate-700/50"
                            indicatorClassName={getScoreColorClass(metric.score)}
                          />
                          <span className="text-xs text-white font-medium w-12 text-right">
                            {metric.score}/100
                          </span>
                        </div>
                        <div className="text-xs text-slate-400">
                          Weight: {metric.weight}% of total score
                        </div>
                      </div>
                    </div>

                    {/* Description Section */}
                    <div className="space-y-4">
                      {/* Main Description */}
                      <div>
                        <div className="text-base font-semibold text-blue-400 flex items-center mb-3">
                          <span className="mr-2">📄</span>
                          Description
                        </div>
                        <div className="bg-slate-800/80 rounded-lg px-4 py-3.5 border border-slate-700/40">
                          <div className="text-[13px] leading-relaxed text-slate-300 ido-metric-content">
                            <div
                              dangerouslySetInnerHTML={{
                                __html: metric.description,
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* What Are We Scoring */}
                      {metric.what_are_we_scoring && (
                        <div>
                          <div className="text-base font-semibold text-blue-400 flex items-center mb-3">
                            <span className="mr-2">🎯</span>
                            What Are We Scoring
                          </div>
                          <div className="bg-slate-800/80 rounded-lg px-4 py-3.5 border border-slate-700/40">
                            <div className="text-[13px] leading-relaxed text-slate-300 ido-metric-content">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: metric.what_are_we_scoring,
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Why Is This Important */}
                      {metric.why_is_this_important && (
                        <div>
                          <div className="text-base font-semibold text-blue-400 flex items-center mb-3">
                            <span className="mr-2">💡</span>
                            Why Is This Important
                          </div>
                          <div className="bg-slate-800/80 rounded-lg px-4 py-3.5 border border-slate-700/40">
                            <div className="text-[13px] leading-relaxed text-slate-300 ido-metric-content">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: metric.why_is_this_important,
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          ))}
        </div>

        {metrics.length === 0 && (
          <div className="py-10 px-5 text-center">
            <AlertCircle className="h-8 w-8 text-amber-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">
              Analysis Data Unavailable
            </h3>
            <p className="text-slate-400">
              Detailed metric data is not available for this IDO.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default IDOMetricsBreakdown;
