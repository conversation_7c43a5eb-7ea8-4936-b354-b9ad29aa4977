import React from 'react';
import { ArrowDownRight, ArrowUpRight, Info } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface PriceProjection {
  multiplier: string;
  usdPrice: number;
  btcPrice: number;
  marketCap: string;
}

interface PriceProjectionTableProps {
  projections: PriceProjection[];
  btcPrice: number;
}

export const PriceProjectionTable: React.FC<PriceProjectionTableProps> = ({
  projections,
  btcPrice
}) => {
  const { t } = useLanguage();
  // Find the baseline projection (x1 or closest)
  const baselineIndex = projections.findIndex(proj => proj.multiplier === 'x1') || 0;

  return (
    <div className="bg-slate-800/90 border border-slate-700/40 shadow-lg shadow-slate-900/40 rounded-xl p-6 transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden group">
      <div className="flex justify-between items-center mb-5 relative z-10">
        <h2 className="text-white text-xl font-semibold flex items-center">
          <div className="bg-slate-700/70 p-2 rounded-md border border-slate-700/40 shadow-md shadow-slate-900/40 mr-2.5">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"></path>
              <path d="M12 7L12 13"></path>
              <path d="M12 17L12 17.01"></path>
            </svg>
          </div>
          Price Projections
        </h2>
        <button className="flex items-center bg-slate-800/90 border border-slate-700/40 hover:bg-slate-700/80 text-slate-300 hover:text-white px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-300 shadow-md shadow-slate-900/40 group">
          <Info size={13} className="mr-1.5 text-blue-400 group-hover:text-blue-300" />
          <span className="text-blue-400 group-hover:text-blue-300">Explanatory Note</span>
        </button>
      </div>

      <div className="overflow-x-auto relative z-10 rounded-md">
        <table className="w-full border-collapse bg-slate-800/90 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40">
          <thead>
            <tr className="border-b border-slate-700/50">
              <th className="text-left p-4 text-slate-300 text-sm font-semibold">Scenario</th>
              <th className="text-left p-4 text-slate-300 text-sm font-semibold">USD Price</th>
              <th className="text-left p-4 text-slate-300 text-sm font-semibold">BTC Price</th>
              <th className="text-left p-4 text-slate-300 text-sm font-semibold">Market Cap</th>
              <th className="text-left p-4 text-slate-300 text-sm font-semibold">Change</th>
            </tr>
          </thead>
          <tbody>
            {projections.map((projection, index) => {
              const isBaseline = index === baselineIndex;
              const isPriceUp = index > baselineIndex;
              const isPriceDown = index < baselineIndex;

              return (
                <tr 
                  key={index} 
                  className={`
                    border-b border-slate-700/50 
                    ${isBaseline ? 'bg-slate-800/90 hover:bg-slate-700/90' : ''}
                    ${!isBaseline && index % 2 === 0 ? 'bg-slate-800/70' : 'bg-slate-800/60'}
                    hover:bg-slate-700/30 transition-all duration-300
                  `}
                >
                  <td className="p-4">
                    <div className="flex items-center">
                      <div 
                        className={`
                          w-2 h-9 rounded-md mr-3.5 shadow-md shadow-slate-900/40
                          ${isPriceUp 
                            ? 'bg-green-500' 
                            : isPriceDown 
                              ? 'bg-red-500' 
                              : 'bg-blue-500'
                          }
                        `}
                      ></div>
                      <span className={`
                        font-semibold text-base
                        ${isPriceUp 
                          ? 'text-green-400' 
                          : isPriceDown 
                            ? 'text-red-400' 
                            : 'text-blue-400'
                        }
                      `}>
                        {projection.multiplier}
                      </span>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className="text-white font-medium">
                      {projection.usdPrice ? `$ ${projection.usdPrice.toFixed(2)}` : <span className="text-slate-400 italic">{t('emptyState.noPriceAnalysis')}</span>}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className="text-white font-medium">
                      {projection.btcPrice ? projection.btcPrice.toFixed(6) : <span className="text-slate-400 italic">{t('emptyState.noPriceAnalysis')}</span>}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className="text-white font-medium">{projection.marketCap}</span>
                  </td>
                  <td className="p-4">
                    {isBaseline ? (
                      <div className="inline-flex items-center justify-center px-3 py-1.5 rounded-md bg-slate-700/70 text-blue-400 text-xs font-medium border border-slate-700/40 shadow-md shadow-slate-900/40">
                        Baseline
                      </div>
                    ) : isPriceUp ? (
                      <div className="inline-flex items-center justify-center px-3 py-1.5 rounded-md bg-slate-700/70 text-green-400 text-xs font-medium border border-slate-700/40 shadow-md shadow-slate-900/40">
                        <ArrowUpRight size={14} className="mr-1.5" />
                        {projection.multiplier ? `+${(parseFloat(projection.multiplier.replace('x', '')) - 1) * 100}%` : <span className="text-slate-400 italic">{t('emptyState.noPriceAnalysis')}</span>}
                      </div>
                    ) : (
                      <div className="inline-flex items-center justify-center px-3 py-1.5 rounded-md bg-slate-700/70 text-red-400 text-xs font-medium border border-slate-700/40 shadow-md shadow-slate-900/40">
                        <ArrowDownRight size={14} className="mr-1.5" />
                        {projection.multiplier ? `${(parseFloat(projection.multiplier.replace('x', '')) - 1) * 100}%` : <span className="text-slate-400 italic">{t('emptyState.noPriceAnalysis')}</span>}
                      </div>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      <div className="mt-5 bg-slate-700/70 p-4 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 relative z-10">
        <div className="flex items-center text-white text-sm mb-3">
          <div className="bg-slate-700/70 p-1.5 rounded-md border border-slate-700/40 shadow-md shadow-slate-900/40 mr-2.5">
            <Info size={16} className="text-blue-400" />
          </div>
          <span className="font-medium">Current BTC Price: $ {btcPrice.toLocaleString()}</span>
        </div>
        <p className="text-xs text-slate-300 ml-10 pl-0.5">
          These projections represent potential price scenarios based on market performance. The 'x1' multiplier represents the token price at launch. Actual results may vary based on market conditions, token performance, and other factors.
        </p>
      </div>

      {/* Visual element for depth */}
      <div className="absolute top-0 left-0 w-40 h-40 bg-blue-500/5 rounded-full -translate-x-20 -translate-y-20 opacity-0 group-hover:opacity-30 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  );
};

export default PriceProjectionTable;