import React from 'react';
import { DollarSign, TrendingUp } from 'lucide-react';

interface FundingRound {
  name: string;
  amount: number;
  date: string;
}

interface Valuations {
  seed?: number | null;
  private?: number | null;
  public?: number | null;
}

interface FundingInsightsProps {
  funding: {
    total?: number;
    totalRaised?: number;
    projectName?: string;
    description?: string | null;
    publicSale?: number;
    fundingRounds?: number | null;
    rounds?: FundingRound[];
    valuations?: Valuations;
    saleStartDate?: number;
    saleEndDate?: number;
    athRoi?: number;
    currentRoi?: number;
  };
  className?: string;
}

const FundingInsights: React.FC<FundingInsightsProps> = ({ funding, className = '' }) => {
  const totalRaised = funding.totalRaised || funding.total;
  const formatCurrency = (value: number | null | undefined) => {
    if (value === null || value === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className={`${className} bg-slate-800 rounded-lg p-4 border border-slate-700`}>
      <h3 className="text-lg font-semibold mb-4">Funding Insights</h3>
      
      {/* Total Raised */}
      <div className="flex items-center p-3 bg-slate-900 rounded-md border border-slate-700 mb-4">
        <div className="bg-primary/20 p-2 rounded-full mr-3">
          <DollarSign className="w-5 h-5 text-primary" />
        </div>
        <div>
          <div className="text-sm text-slate-400">Total Raised</div>
          <div className="text-lg font-semibold">{formatCurrency(totalRaised)}</div>
        </div>
      </div>

      {/* ROI Information */}
      {(funding.athRoi || funding.currentRoi) && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
          {funding.athRoi && (
            <div className="p-3 bg-slate-900 rounded-md border border-slate-700">
              <div className="text-xs text-slate-400">ATH ROI</div>
              <div className="text-md font-semibold text-emerald-400">{funding.athRoi}x</div>
            </div>
          )}
          {funding.currentRoi && (
            <div className="p-3 bg-slate-900 rounded-md border border-slate-700">
              <div className="text-xs text-slate-400">Current ROI</div>
              <div className="text-md font-semibold text-blue-400">{funding.currentRoi}x</div>
            </div>
          )}
        </div>
      )}

      {/* Sale Dates */}
      {(funding.saleStartDate || funding.saleEndDate) && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
          {funding.saleStartDate && (
            <div className="p-3 bg-slate-900 rounded-md border border-slate-700">
              <div className="text-xs text-slate-400">Sale Start Date</div>
              <div className="text-sm font-semibold">{formatDate(funding.saleStartDate)}</div>
            </div>
          )}
          {funding.saleEndDate && (
            <div className="p-3 bg-slate-900 rounded-md border border-slate-700">
              <div className="text-xs text-slate-400">Sale End Date</div>
              <div className="text-sm font-semibold">{formatDate(funding.saleEndDate)}</div>
            </div>
          )}
        </div>
      )}
      
      {/* Funding Rounds */}
      {funding.rounds && funding.rounds.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-3">Funding Rounds</h4>
          <div className="space-y-3">
            {funding.rounds.map((round, index) => (
              <div key={index} className="flex flex-col sm:flex-row sm:items-center justify-between p-3 bg-slate-900 rounded-md border border-slate-700">
                <div className="mb-2 sm:mb-0">
                  <div className="text-sm font-medium">{round.name}</div>
                  <div className="text-xs text-slate-400">{round.date}</div>
                </div>
                <div className="text-right">
                  <div className="text-md font-semibold">{formatCurrency(round.amount)}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Valuations */}
      {funding.valuations && Object.values(funding.valuations).some(val => val !== null && val !== undefined) && (
        <div>
          <h4 className="text-md font-medium mb-3 flex items-center">
            <TrendingUp className="w-4 h-4 mr-1" /> 
            Valuation Growth
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {funding.valuations?.seed !== null && funding.valuations?.seed !== undefined && (
              <div className="p-3 bg-slate-900 rounded-md border border-slate-700">
                <div className="text-xs text-slate-400">Seed Valuation</div>
                <div className="text-md font-semibold">{formatCurrency(funding.valuations.seed)}</div>
              </div>
            )}
            {funding.valuations?.private !== null && funding.valuations?.private !== undefined && (
              <div className="p-3 bg-slate-900 rounded-md border border-slate-700">
                <div className="text-xs text-slate-400">Private Sale</div>
                <div className="text-md font-semibold">{formatCurrency(funding.valuations.private)}</div>
              </div>
            )}
            {funding.valuations?.public !== null && funding.valuations?.public !== undefined && (
              <div className="p-3 bg-slate-900 rounded-md border border-slate-700">
                <div className="text-xs text-slate-400">Public Sale</div>
                <div className="text-md font-semibold">{formatCurrency(funding.valuations.public)}</div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FundingInsights;