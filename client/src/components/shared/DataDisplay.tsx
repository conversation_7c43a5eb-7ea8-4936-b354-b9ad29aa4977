import React from "react";
import {
  Toolt<PERSON>,
  Toolt<PERSON><PERSON>ontent,
  Toolt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useLanguage } from "@/contexts/LanguageContext";

interface DataDisplayProps {
  value: string | number | null | undefined;
  fallback?: string;
  tooltipMessage?: string;
  className?: string;
  children?: React.ReactNode;
  renderAsHtml?: boolean;
}

/**
 * Component that displays data values with "N/A" fallback and tooltip for missing data
 * If any field is null, undefined, or empty string, displays "N/A" with tooltip
 */
export function DataDisplay({
  value,
  fallback,
  tooltipMessage,
  className = "",
  children,
  renderAsHtml = false
}: DataDisplayProps) {
  const { t } = useLanguage();
  // Check if value is null, undefined, empty string, or just whitespace
  const isEmptyValue = value === null || value === undefined || 
    (typeof value === 'string' && (value.trim() === '' || value === '0' || value === '-1')) ||
    (typeof value === 'number' && (isNaN(value) || value === 0 || value === -1));

  // If we have children, render them with tooltip if value is empty
  if (children) {
    if (isEmptyValue) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className={`text-slate-400 cursor-help ${className}`}>
                {fallback || t('emptyState.noData')}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">{tooltipMessage || t('emptyState.noData')}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    return <>{children}</>;
  }

  // If value is empty, show N/A with tooltip
  if (isEmptyValue) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className={`text-slate-400 cursor-help ${className}`}>
              {fallback || t('emptyState.noData')}
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-sm">{tooltipMessage || t('emptyState.noData')}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Display the actual value as HTML if requested
  if (renderAsHtml && typeof value === 'string') {
    return (
      <div 
        className={className}
        dangerouslySetInnerHTML={{ __html: value }}
      />
    );
  }

  // Display the actual value
  return <span className={className}>{value}</span>;
}

/**
 * Hook to check if a value should be displayed as N/A
 */
export function useDataDisplay(value: string | number | null | undefined): {
  isEmpty: boolean;
  displayValue: string | number;
  renderWithTooltip: (className?: string, tooltipMessage?: string) => React.ReactNode;
} {
  const isEmptyValue = value === null || value === undefined || 
    (typeof value === 'string' && value.trim() === '') ||
    (typeof value === 'number' && (isNaN(value) || value === 0));

  const renderWithTooltip = (className = "", tooltipMessage = "Proje bu bilgiyi henüz açıklamadı") => (
    <DataDisplay 
      value={value} 
      className={className} 
      tooltipMessage={tooltipMessage} 
    />
  );

  return {
    isEmpty: isEmptyValue,
    displayValue: isEmptyValue ? "Proje açıklamadı" : value as string | number,
    renderWithTooltip
  };
}