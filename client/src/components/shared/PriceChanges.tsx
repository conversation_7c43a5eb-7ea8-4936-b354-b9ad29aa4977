import React from 'react';
import { cn } from '@/lib/utils';
import { Bar<PERSON><PERSON> } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface TimeframeData {
  label: string;
  value: number;
  timeRange?: string; // e.g., '1D', '1W', '1M'
}

interface PriceChangesProps {
  timeframes: TimeframeData[];
  className?: string;
  activeTimeframe?: string;
  onTimeframeChange?: (timeframe: string) => void;
}

export function PriceChanges({ 
  timeframes, 
  className, 
  activeTimeframe = '1M', 
  onTimeframeChange 
}: PriceChangesProps) {
  const { t } = useLanguage();
  // Helper function to determine the color for price change
  const getPriceChangeColor = (value: number): string => {
    if (value > 0) return 'bg-emerald-500/20 text-emerald-400';
    if (value < 0) return 'bg-red-500/20 text-red-400';
    return 'bg-slate-500/20 text-slate-400';
  };

  // Helper function to format the percentage value
  const formatPercentage = (value: number): string => {
    const prefix = value > 0 ? '+' : '';
    return `${prefix}${value.toFixed(2)}%`;
  };

  // Map from label to timeRange for default values
  const getLabelTimeRange = (label: string): string => {
    const mappings: Record<string, string> = {
      '1 day': '1D',
      '1 week': '1W',
      '1 month': '1M',
      '3 months': '3M',
      '6 months': '6M',
      '1 year': '1Y',
      'All time': 'ALL'
    };
    return mappings[label] || '1M';
  };

  // Handle timeframe button click
  const handleTimeframeClick = (timeframe: TimeframeData) => {
    const timeRange = timeframe.timeRange || getLabelTimeRange(timeframe.label);
    if (onTimeframeChange) {
      onTimeframeChange(timeRange);
    }
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="mb-3">
        <h3 className="text-white text-base font-medium flex items-center ml-[0.75rem]">
          <div className="bg-slate-800/80 p-1.5 rounded-md mr-2 flex items-center justify-center">
            <BarChart className="h-4 w-4 text-blue-400" />
          </div>
          {t('priceChanges.title')}
        </h3>
      </div>
      <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-2 mx-2">
        {timeframes.map((timeframe) => {
          const timeRange = timeframe.timeRange || getLabelTimeRange(timeframe.label);
          const isActive = timeRange === activeTimeframe;
          
          return (
            <div 
              key={timeframe.label}
              onClick={() => handleTimeframeClick(timeframe)}
              className={cn(
                "rounded-md px-3 py-2 flex flex-col border h-[68px] justify-between transition-all duration-200 cursor-pointer",
                isActive 
                  ? "bg-slate-700 border-blue-500" 
                  : "bg-slate-800/80 border-slate-700/20 hover:bg-slate-700/60"
              )}
            >
              <span className={cn("text-xs w-full text-center", isActive ? "text-white" : "text-blue-400")}>
                {(() => {
                  // Translate common time periods
                  const label = timeframe.label.toLowerCase();
                  if (label === '1 day') return t('priceChanges.1day');
                  if (label === '1 week') return t('priceChanges.1week');
                  if (label === '1 month') return t('priceChanges.1month');
                  if (label === '3 months') return t('priceChanges.3months');
                  if (label === '1 year') return t('priceChanges.1year');
                  return timeframe.label; // fallback to original label
                })()}
              </span>
              <div className="flex justify-center items-center">
                <span className={cn("text-xs px-2 py-0.5 rounded-full font-medium", getPriceChangeColor(timeframe.value))}>
                  {formatPercentage(timeframe.value)}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}