import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import {
  Bell,
  UserCircle2,
  LogIn,
  Settings,
  LogOut,
  BarChart2,
  Layers,
  Book,
  Menu,
  Search,
  MessageSquare,
  Home,
  Crown,
  MessageCircle,
} from "lucide-react";
import { Command, CommandInput } from "@/components/ui/command";
import { LanguageSelector } from "@/components/LanguageSelector";
import React, {
  useCallback,
  useMemo,
  useState,
  useEffect,
  useRef,
} from "react";
import { useAuth } from "@/hooks/use-auth";
import { useLanguage } from "@/contexts/LanguageContext"; // Import language context
import { useSubscription } from "@/contexts/SubscriptionContext"; // Import subscription context
import { getUserSubscription } from "@/lib/api";
import LogoImage from "@/assets/Primary.png";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { NotificationDropdown } from "@/components/notifications/NotificationDropdown";
import { getAvatarById } from "@shared/avatars";

const STYLES = {
  iconButton:
    "relative min-h-[40px] min-w-[40px] w-[40px] h-[40px] rounded-md bg-card/40 hover:bg-primary hover:text-white transition-colors duration-600 flex items-center justify-center shadow-sm border border-border/40 backdrop-blur-sm group",
  icon: "h-5 w-5 text-muted-foreground group-hover:text-white transition-colors duration-600",
};

// Importing the new HeaderSearch component
import { HeaderSearch } from "@/components/HeaderSearch";

// Old search bar implementation, keeping for reference
const SearchBar = React.memo(() => {
  const [searchQuery, setSearchQuery] = useState("");
  const [, setLocation] = useLocation();
  const { t } = useLanguage();

  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && searchQuery.trim()) {
      // Redirect to coin list page with search query
      setLocation(`/coinlist?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <Command className="rounded-lg border-none shadow-none">
      <div className="flex items-center">
        <CommandInput
          placeholder={t("searchPlaceholder", "navigation")}
          className="h-10 border-none focus-visible:ring-0 placeholder:text-muted-foreground/40 pl-3"
          value={searchQuery}
          onValueChange={setSearchQuery}
          onKeyDown={handleSearch}
        />
      </div>
    </Command>
  );
});

const Logo = React.memo(() => {
  const { t } = useLanguage();
  return (
    <Link
      href="/"
      className="flex items-center gap-2 transition-all duration-600"
      aria-label={t("goToHomepage", "navigation")}
    >
      <img
        src={LogoImage}
        alt={t("coinScoutAlt", "navigation")}
        className="h-8 w-auto transition-all duration-600 max-w-[120px] sm:max-w-[150px]"
        style={{ objectFit: "contain" }}
      />
    </Link>
  );
});

const NavigationButton = React.memo(
  ({
    icon: Icon,
    onClick,
    label,
    ariaLabel,
  }: {
    icon: React.ElementType;
    onClick?: () => void;
    label?: string;
    ariaLabel: string;
  }) => (
    <button
      className={STYLES.iconButton}
      onClick={onClick}
      aria-label={ariaLabel}
    >
      <Icon className={STYLES.icon} aria-hidden="true" />
      {label && <span className="sr-only">{label}</span>}
    </button>
  ),
);

export function Navigation() {
  const [profileOpen, setProfileOpen] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);
  const { t } = useLanguage(); // Get translation function
  const { subscription, subscriptionLevel, hasPaidSubscription } =
    useSubscription(); // Get subscription data
  const [apiSubscription, setApiSubscription] = useState<any>(null);

  // Close profile popup when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        profileRef.current &&
        !profileRef.current.contains(event.target as Node)
      ) {
        setProfileOpen(false);
      }
    }

    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [profileRef]);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, logout } = useAuth();
  const [location, setLocation] = useLocation();

  // Fetch subscription data when user is logged in
  useEffect(() => {
    const fetchSubscription = async () => {
      if (user) {
        try {
          const response = await getUserSubscription();
          if (response.success && response.data?.subscriptions?.length > 0) {
            setApiSubscription(response.data.subscriptions[0]);
          } else {
            // If no subscription found, clear the state
            setApiSubscription(null);
          }
        } catch (error) {
          console.error('Failed to fetch subscription in Navigation:', error);
          setApiSubscription(null);
        }
      } else {
        // Clear subscription data when user is not logged in
        setApiSubscription(null);
      }
    };

    fetchSubscription();
  }, [user]);

  const handleLogout = useCallback(async () => {
    try {
      // Use our auth hook for logging out
      await logout();
      // Close profile dropdown after logout
      setProfileOpen(false);
      console.log("User logged out successfully");
    } catch (error) {
      console.error("Logout error:", error);
    }
  }, [logout]);

  // Community butonu gizlendiği için bu fonksiyon şu an kullanılmıyor
  /*
  const handleCommunityClick = useCallback(() => {
    setLocation("/forum");
  }, [setLocation]);
  */

  // Navigate to login page with the login tab shown by default
  const handleLoginClick = useCallback(() => {
    // Ensure we remove any stored tab preference
    sessionStorage.removeItem("loginPageTab");
    console.log("Login button clicked, navigating to /login with returnTo:", location);
    // Force login mode by adding a specific parameter and preserve current location
    setLocation(`/login?mode=login&returnTo=${encodeURIComponent(location)}`);
  }, [setLocation, location]);

  // Navigate to login page with the register tab shown
  const handleSignupClick = useCallback(() => {
    console.log("Sign Up button clicked, navigating to /login?mode=register with returnTo:", location);

    // Force a full page navigation to ensure parameters are retained
    // This bypasses the SPA router entirely and loads the page fresh
    window.location.href = `/login?mode=register&returnTo=${encodeURIComponent(location)}`;

    // Prevent any further code execution
    return false;
  }, [location]);

  const getInitials = (name: string) => {
    if (!name) return "CS";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const navLinks = [
    // Community ve Forum butonları gizlendi - daha sonra tekrar aktifleştirilebilir
    /*
    {
      href: "/community",
      label: t("Community", "navigation"),
      icon: MessageSquare,
    },
    { href: "/forum", label: t("Forum", "navigation"), icon: BarChart2 },
    */
    { href: "/pricing", label: t("Pricing", "navigation"), icon: Layers },
    {
      href: "/docusaurus-docs",
      label: t("Documentation", "navigation"),
      icon: Book,
    },
  ];

  return (
    <nav
      className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur"
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="mx-auto max-w-[1600px]">
        <div className="flex h-16 items-center justify-between gap-4 px-6 pr-4 sm:px-16">
          {/* Logo Section */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden sm:mr-2 relative top-[3px]"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <Menu className="h-5 w-5" />
            </Button>
            <Logo />
          </div>

          {/* Desktop Navigation Links - Hidden on mobile - ARTIK KULLANILMIYOR */}
          {/* <div className="hidden md:flex items-center space-x-4">
            {navLinks.map((link) => {
              const isActive = location === link.href;
              return (
                <Link
                  key={link.href}
                  href={link.href}
                  className={cn(
                    "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 relative group",
                    isActive
                      ? "bg-primary/10 text-primary"
                      : "hover:bg-primary hover:text-white",
                  )}
                >
                  {link.label}
                  {isActive && (
                    <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full" />
                  )}
                </Link>
              );
            })}
          </div> */}

          {/* Search Bar - Center on desktop, hidden on mobile and homepage */}
          {location !== "/" && (
            <div className="hidden md:flex flex-1 justify-center max-w-[500px] px-4">
              <div className="w-full">
                <HeaderSearch className="w-full" />
              </div>
            </div>
          )}

          {/* Right Section - User Menu */}
          <div className="flex items-center justify-end gap-2">
            {user ? (
              <>
                {/* Notification dropdown - aktif */}
                {location !== "/" && <NotificationDropdown />}

                {/* Navigation butonları - sadece anasayfada göster */}
                {location === "/" && (
                  <div className="flex items-center sm:gap-2 sm:mr-2">
                    <Button
                      variant="ghost"
                      onClick={() => setLocation("/pricing")}
                      className="text-sm font-medium hidden sm:block"
                    >
                      {t("pricing", "navigation")}
                    </Button>
                    {/*<Button
                      variant="ghost"
                      onClick={() => setLocation("/docusaurus-docs")}
                      className="text-sm font-medium"
                    >
                      Documentation
                    </Button>*/}
                    <Button
                      variant="outline"
                      onClick={() => setLocation("/coinlist")}
                      className="bg-primary text-white hover:bg-primary/90 border-none shadow-sm max-sm:px-2"
                    >
                      {t("goToApp", "navigation")}
                    </Button>
                  </div>
                )}
                <LanguageSelector />
                {/* Modern Profile Popup with elevated design */}
                <div className="relative" ref={profileRef}>
                  <button
                    onClick={() => setProfileOpen(!profileOpen)}
                    className="relative focus:outline-none group"
                    aria-haspopup="true"
                    aria-expanded={profileOpen}
                  >
                    <div className="flex items-center space-x-2 rounded-full p-1 transition-all duration-200 cursor-pointer hover:bg-accent">
                      <Avatar className="h-8 w-8 border-2 border-transparent transition-all duration-200 group-hover:border-primary/30 group-focus:border-primary/30">
                        <AvatarImage
                          src={(() => {
                            const avatarUrl = user?.avatarUrl;
                            const avatarId = user?.avatarId;
                            const predefinedAvatar = avatarId ? getAvatarById(avatarId) : null;
                            const fallbackUrl = `https://ui-avatars.com/api/?name=${user?.username || "User"}&size=128`;

                            return avatarUrl || predefinedAvatar?.url || fallbackUrl;
                          })()}
                          alt={user.username}
                        />
                        <AvatarFallback className="text-xs bg-gradient-to-br from-primary/80 to-primary shadow-sm">
                          {getInitials(user.username)}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  </button>

                  {/* Enhanced Dropdown Panel */}
                  {profileOpen && (
                    <>
                      <div
                        className="fixed inset-0 z-40 bg-background/0"
                        onClick={() => setProfileOpen(false)}
                        aria-hidden="true"
                      />
                      <div
                        className="absolute right-0 mt-2 z-50 min-w-[280px] animate-in fade-in zoom-in-95 duration-100 origin-top-right"
                        role="menu"
                        tabIndex={-1}
                      >
                        <div className="rounded-xl shadow-lg ring-1 ring-black/5 dark:ring-white/10 overflow-hidden backdrop-blur-lg bg-background/95 border border-border">
                          {/* User info section with enhanced styling */}
                          <div className="p-4 bg-gradient-to-br from-primary/5 to-transparent dark:from-primary/10">
                            <div className="flex items-center gap-4">
                              <Avatar className="h-14 w-14 rounded-full border-4 border-background shadow-md">
                                <AvatarImage
                                  src={(() => {
                                    const avatarUrl = user?.avatarUrl;
                                    const avatarId = user?.avatarId;
                                    const predefinedAvatar = avatarId ? getAvatarById(avatarId) : null;
                                    const fallbackUrl = `https://ui-avatars.com/api/?name=${user?.username || "User"}&size=128`;

                                    return avatarUrl || predefinedAvatar?.url || fallbackUrl;
                                  })()}
                                  alt={user.username}
                                />
                                <AvatarFallback className="text-base bg-gradient-to-br from-primary/80 to-primary">
                                  {getInitials(user.username)}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <h3 className="font-medium text-base truncate">
                                  {user.username}
                                </h3>
                                {user.email && (
                                  <p className="text-sm text-muted-foreground truncate">
                                    {user.email}
                                  </p>
                                )}
                                <div
                                  className={`inline-flex items-center mt-1.5 px-2 py-0.5 rounded-full text-xs font-medium
                                  ${
                                    apiSubscription?.status === "active"
                                      ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                                      : "bg-primary/10 text-primary"
                                  }`}
                                >
                                  {/* Show Crown icon for active subscriptions */}
                                  {apiSubscription?.status === "active" && (
                                    <Crown className="h-3 w-3 mr-1" />
                                  )}

                                  {apiSubscription?.plan_name || t("membership.free", "navigation")}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Menu items with enhanced interaction */}
                          <div className="p-2">
                            <button
                              onClick={() => {
                                setProfileOpen(false);
                                setLocation("/profile");
                              }}
                              className="flex items-center w-full gap-2 px-3 py-2.5 text-sm rounded-lg transition-colors duration-600 hover:bg-accent focus:bg-accent focus:outline-none"
                              role="menuitem"
                            >
                              <UserCircle2
                                className="h-4 w-4 text-primary"
                                aria-hidden="true"
                              />
                              <span>{t("profile", "navigation")}</span>
                            </button>

                            <button
                              onClick={() => {
                                setProfileOpen(false);
                                setLocation("/profile/membership");
                              }}
                              className="flex items-center w-full gap-2 px-3 py-2.5 text-sm rounded-lg transition-colors duration-600 hover:bg-accent focus:bg-accent focus:outline-none"
                              role="menuitem"
                            >
                              <Crown
                                className="h-4 w-4 text-amber-500"
                                aria-hidden="true"
                              />
                              <span>
                                {t("membershipManagement", "navigation")}
                              </span>
                            </button>

                            <button
                              onClick={() => {
                                setProfileOpen(false);
                                // Open external feedback form in a new tab
                                window.open(
                                  "https://forms.gle/nRmGJUoMfdpaVZjVA",
                                  "_blank",
                                );
                              }}
                              className="flex items-center w-full gap-2 px-3 py-2.5 text-sm rounded-lg transition-colors duration-600 hover:bg-accent focus:bg-accent focus:outline-none"
                              role="menuitem"
                            >
                              <MessageCircle
                                className="h-4 w-4 text-blue-500"
                                aria-hidden="true"
                              />
                              <span>{t("feedback", "navigation")}</span>
                            </button>

                            {user.isAdmin && (
                              <button
                                onClick={() => {
                                  setProfileOpen(false);
                                  setLocation("/admin");
                                }}
                                className="flex items-center w-full gap-2 px-3 py-2.5 text-sm rounded-lg transition-colors duration-600 hover:bg-accent focus:bg-accent focus:outline-none"
                                role="menuitem"
                              >
                                <Settings
                                  className="h-4 w-4 text-muted-foreground"
                                  aria-hidden="true"
                                />
                                <span>{t("adminDashboard", "navigation")}</span>
                              </button>
                            )}

                            <div className="h-px bg-border my-2" />

                            {/* Feedback button removed to prevent duplication */}

                            <button
                              onClick={() => {
                                setProfileOpen(false);
                                handleLogout();
                              }}
                              className="flex items-center w-full gap-2 px-3 py-2.5 text-sm rounded-lg text-destructive hover:bg-destructive/10 focus:bg-destructive/10 transition-colors duration-600 focus:outline-none"
                              role="menuitem"
                            >
                              <LogOut className="h-4 w-4" aria-hidden="true" />
                              <span>{t("logout", "navigation")}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </>
            ) : (
              <>
                {/* Sadece ana sayfa değilse login/signup butonlarını göster */}
                {location !== "/" && (
                  <>
                    <div className="hidden md:flex gap-2">
                      <Button
                        variant="ghost"
                        onClick={handleLoginClick}
                        className="transition-all duration-600  h-10"
                      >
                        {t("login", "navigation")}
                      </Button>
                      <Button
                        variant="default"
                        onClick={handleSignupClick}
                        className="shadow-md transition-all duration-600 bg-gradient-to-r from-primary to-primary/80 h-10"
                      >
                        {t("signUp", "navigation")}
                      </Button>
                    </div>
                    <div className="flex md:hidden">
                      <NavigationButton
                        icon={UserCircle2}
                        onClick={handleLoginClick}
                        label="Login"
                        ariaLabel="Log in to your account"
                      />
                    </div>
                  </>
                )}
                {/* Navigation butonları - sadece anasayfada göster */}
                {location === "/" && (
                  <div className="flex items-center sm:gap-2 sm:mr-2">
                    <Button
                      variant="ghost"
                      onClick={() => setLocation("/pricing")}
                      className="text-sm font-medium hidden md:block"
                    >
                      {t("pricing", "navigation")}
                    </Button>
                    {/*<Button
                      variant="ghost"
                      onClick={() => setLocation("/docusaurus-docs")}
                      className="text-sm font-medium"
                    >
                      Documentation
                    </Button>*/}
                    <Button
                      variant="outline"
                      onClick={() => setLocation("/coinlist")}
                      className="bg-primary text-white hover:bg-primary/90 border-none shadow-sm max-sm:px-2"
                    >
                      {t("goToApp", "navigation")}
                    </Button>
                  </div>
                )}
                <LanguageSelector />
              </>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden p-4 border-t bg-background">
          {location !== "/" && (
            <div className="mb-4">
              <div className="w-full">
                <HeaderSearch className="w-full" />
              </div>
            </div>
          )}
          <div className="space-y-1">
            {navLinks.map((link) => {
              const isActive = location === link.href;
              return (
                <Link
                  key={link.href}
                  href={link.href}
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md relative group",
                    isActive
                      ? "bg-primary/10 text-primary font-medium"
                      : "hover:bg-primary hover:text-white",
                  )}
                >
                  <link.icon
                    className={cn(
                      "mr-2",
                      isActive
                        ? "h-5 w-5 text-primary"
                        : "h-5 w-5 text-muted-foreground",
                    )}
                  />
                  <span>{link.label}</span>
                  {isActive && (
                    <span className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-full" />
                  )}
                </Link>
              );
            })}
            {/* Mobil menüde Navigation butonları - sadece anasayfada göster */}
            {location === "/" && (
              <div className="mt-4 space-y-2">
                <Button
                  variant="outline"
                  onClick={() => setLocation("/pricing")}
                  className="w-full"
                >
                  {t("pricing", "navigation")}
                </Button>
                {/*
                <Button
                  variant="outline"
                  onClick={() => setLocation("/docusaurus-docs")}
                  className="w-full"
                >
                  Documentation
                </Button>*/}
                <Button
                  onClick={() => setLocation("/coinlist")}
                  className="w-full shadow-md hover:shadow-lg transition-all duration-600 bg-primary text-white"
                >
                  {t("goToApp", "navigation")}
                </Button>
              </div>
            )}

            {!user && location !== "/" && (
              <div className="grid grid-cols-2 gap-2 mt-6 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={handleLoginClick}
                  className="w-full transition-all duration-600 hover:bg-primary/10 hover:border-primary"
                >
                  {t("login", "navigation")}
                </Button>
                <Button
                  onClick={handleSignupClick}
                  className="w-full shadow-md hover:shadow-lg transition-all duration-600 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary"
                >
                  {t("signUp", "navigation")}
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}
