import React, { useState, useEffect, useCallback, useRef } from "react";
import { Check, ChevronDown, Search, X, Loader2 } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { ComparisonCoin } from "@/data/compareDemoData";
import SearchService, { SearchCoin } from "@/lib/services/SearchService";
import CoinDetailsService from "@/lib/services/CoinDetailsService";
import { useComparisonStore } from "@/stores/comparisonStore";
import { debounce } from "@/lib/utils";

interface CoinSelectorProps {
  onCoinAdded?: (coin: ComparisonCoin) => void;
  onCoinRemoved?: (coinId: string) => void;
  maxSelections?: number;
  className?: string;
}

// Utility function to convert SearchCoin to ComparisonCoin
const convertToComparisonCoin = (searchCoin: SearchCoin): ComparisonCoin => {
  console.log("Converting SearchCoin to ComparisonCoin", searchCoin);
  
  // Oluşturacağımız ID formatını API'de kullanılan formata dönüştür
  // API yanıtındaki ID bazen string format ("bitcoin") kullanabilir
  // SearchCoin.id genellikle "123456" gibi sayısal formatı kullanır
  let coinId = searchCoin.id; 
  
  // API'den gelecek verileri varsayılan değerlerle hazırla
  // Asıl veriler API'den alındığında güncellenecek
  return {
    id: coinId, // API compatibility: Keep original ID format
    name: searchCoin.name,
    symbol: searchCoin.symbol,
    logo: searchCoin.logo || "", // SearchCoin'de logo varsa kullan
    image: searchCoin.image || "", // API'den gelen yeni image alanı
    price: 0, // API will fill this
    priceChange24h: 0,
    priceChange7d: 0,
    priceChange30d: 0,
    marketCap: 0,
    volume24h: 0,
    category: "",
    rating: 0,
    status: "",
    description: "",
    // API yapısına uygun scores dizisi oluştur, API doldurulacak
    scores: [],
    // API yapısıyla uyumlu minimal objeler
    tokenomics: {
      circulatingSupply: 0,
      totalSupply: 0,
      maxSupply: null
    },
    riskMetrics: {
      volatility: 0,
      marketBeta: 0,
      sharpeRatio: 0,
      liquidityScore: 0,
      concentrationRisk: 0
    },
    socialMetrics: {
      twitter: {
        followers: 0,
        engagement: 0
      },
      reddit: {
        subscribers: 0,
        activity: 0
      },
      github: {
        stars: 0,
        commits: 0,
        contributors: 0
      },
      sentiment: {
        overall: 0,
        positive: 0,
        negative: 0,
        neutral: 0
      }
    },
    tags: []
  };
};

export function CoinSelector({
  onCoinAdded,
  onCoinRemoved,
  maxSelections = 3,
  className,
}: CoinSelectorProps) {
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [searchResults, setSearchResults] = useState<SearchCoin[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingCoin, setIsAddingCoin] = useState(false);
  
  // Use the comparison store
  const { selectedCoins, addCoin, removeCoin, clearCoins, isSelected } = useComparisonStore();

  // Function to perform the actual search
  const performSearch = useCallback(async (query: string) => {
    if (query.trim().length < 2) {
      setSearchResults([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    const results = await SearchService.searchCoinsForCompare({ query });
    setSearchResults(results.coins || []);
    setIsLoading(false);
  }, []);

  // Create debounced search function - prevents API calls until user stops typing for 300ms
  const debouncedSearch = useRef(
    debounce((query: string) => {
      performSearch(query);
    }, 300)
  ).current;

  // Effect to handle search input changes
  useEffect(() => {
    if (searchValue.trim().length >= 2) {
      // Only start loading indicator immediately, actual API call is debounced
      setIsLoading(true);
      debouncedSearch(searchValue);
    } else {
      setSearchResults([]);
      setIsLoading(false);
    }
  }, [searchValue, debouncedSearch]);

  const handleSelect = async (coin: SearchCoin) => {
    const isAlreadySelected = isSelected(coin.id);

    if (isAlreadySelected) {
      // Remove the coin from store (no API call needed)
      removeCoin(coin.id);
      onCoinRemoved?.(coin.id);
    } else if (selectedCoins.length < maxSelections) {
      // Show loading state
      setIsAddingCoin(true);
      
      try {
        // Fetch detailed information for this single coin
        const coinDetails = await CoinDetailsService.getSingleCoinDetails(coin.id);
        
        // Only proceed if we successfully got coin details
        if (coinDetails) {
          // First convert to ComparisonCoin with basic info
          const basicCoin = convertToComparisonCoin(coin);
          
          // Merge the detailed information
          const finalCoin = {
            ...basicCoin,
            price: coinDetails.price || 0,
            priceChange24h: coinDetails.priceChange24h || 0,
            priceChange7d: coinDetails.priceChange7d || 0,
            priceChange30d: coinDetails.priceChange30d || 0,
            marketCap: coinDetails.marketCap || 0,
            volume24h: coinDetails.volume24h || 0,
            category: coinDetails.category || "",
            rating: coinDetails.rating || CoinDetailsService.calculateOverallRating(coinDetails.scores),
            status: coinDetails.status || CoinDetailsService.getStatusForScore(coinDetails.rating || 0),
            description: coinDetails.description || "",
            image: coinDetails.image || basicCoin.image,
            logo: coinDetails.logo || basicCoin.logo,
            scores: coinDetails.scores || [],
            tokenomics: coinDetails.tokenomics || basicCoin.tokenomics,
            riskMetrics: coinDetails.riskMetrics || basicCoin.riskMetrics,
            socialMetrics: coinDetails.socialMetrics || basicCoin.socialMetrics,
            tags: coinDetails.tags || []
          };
          
          // Add to store only if we have valid data
          addCoin(finalCoin);
          onCoinAdded?.(finalCoin);
          
          console.log(`Successfully added coin ${coin.name} with details:`, finalCoin);
        } else {
          // API call failed or returned error - don't add the coin
          console.log(`Failed to fetch details for ${coin.name} - coin not added to comparison`);
        }
      } catch (error) {
        console.error("Error adding coin:", error);
        // Don't add anything on error - user will need to try again or upgrade plan
        console.log(`Error occurred while adding ${coin.name} - coin not added to comparison`);
      } finally {
        setIsAddingCoin(false);
      }
    }
  };

  const handleRemove = (coinId: string) => {
    removeCoin(coinId);
    onCoinRemoved?.(coinId);
  };

  // Close popover when max selections reached
  useEffect(() => {
    if (selectedCoins.length >= maxSelections) {
      setOpen(false);
    }
  }, [selectedCoins, maxSelections]);

  return (
    <div className={cn("space-y-4", className)}>
      {selectedCoins.length === 0 && (
        <div className="bg-blue-600/10 border border-blue-500/20 rounded-lg p-3 mb-3 text-sm text-blue-300">
          <div className="flex items-start gap-2">
            <div className="bg-blue-500/20 p-1 rounded-full mt-0.5">
              <ChevronDown className="h-4 w-4 text-blue-400" />
            </div>
            <div>
              <p className="font-medium">{t("gettingStarted", "compare", "Getting Started")}</p>
              <p className="text-slate-300 mt-1">
                {t("selectInstruction", "compare", `Select up to ${maxSelections} cryptocurrencies to compare their performance metrics and identify the strongest investment options.`)}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-wrap gap-2 mb-2">
        {selectedCoins.map((coin) => (
          <Badge
            key={coin.id}
            variant="secondary"
            className="flex items-center gap-1 py-1 pl-2 pr-1 bg-slate-800 hover:bg-slate-700 border border-slate-700/60 shadow-sm shadow-slate-900/30"
          >
            <div className="flex items-center gap-1.5">
              <div className="h-4 w-4 rounded-full bg-blue-600/20 flex items-center justify-center text-blue-400 text-xs font-bold overflow-hidden">
                {coin.image || coin.logo ? (
                  <img
                    src={coin.image || coin.logo}
                    alt={coin.name}
                    className="h-full w-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://via.placeholder.com/16x16/3b82f6/ffffff?text=${coin.symbol?.charAt(0) || 'C'}`;
                    }}
                  />
                ) : (
                  coin.symbol.slice(0, 2).toUpperCase()
                )}
              </div>
              <span>{coin.name}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 ml-1 rounded-full hover:bg-slate-600"
              onClick={() => handleRemove(coin.id)}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        ))}

        {selectedCoins.length < maxSelections && (
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={open}
                className="bg-slate-800 border-slate-700 hover:border-blue-500/50 hover:bg-slate-700 text-slate-300"
              >
                <Search className="mr-2 h-4 w-4" />
                {t("selectCoin", "compare", "Select coin")}
                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[300px] p-0 bg-slate-800/90 border border-slate-700/60 shadow-lg shadow-slate-900/50">
              <Command className="bg-transparent">
                <CommandInput
                  placeholder="Search coins..."
                  value={searchValue}
                  onValueChange={setSearchValue}
                  className="border-b border-slate-700 focus:ring-0 text-slate-300"
                />
                {isLoading ? (
                  <div className="py-6 text-center">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2 text-blue-500" />
                    <p className="text-sm text-slate-400">Searching coins...</p>
                  </div>
                ) : (
                  <>
                    <CommandEmpty className="py-6 text-center text-sm text-slate-400">
                      {searchValue.length >= 2 ? "No coins found." : "Type at least 2 characters to search"}
                    </CommandEmpty>
                    <CommandGroup className="max-h-[300px] overflow-auto">
                      {searchResults.map((coin) => {
                        const isAlreadySelected = isSelected(coin.id);
                        return (
                          <CommandItem
                            key={coin.id}
                            value={`${coin.name}-${coin.symbol}`}
                            onSelect={() => handleSelect(coin)}
                            className={cn(
                              "flex items-center gap-2 py-2 text-slate-300",
                              isAlreadySelected
                                ? "bg-blue-600/20 text-blue-100"
                                : "hover:bg-slate-700/70",
                              selectedCoins.length >= maxSelections && !isAlreadySelected
                                ? "opacity-50 cursor-not-allowed"
                                : "",
                            )}
                            disabled={
                              selectedCoins.length >= maxSelections && !isAlreadySelected
                            }
                          >
                            <div className="h-6 w-6 rounded-full bg-blue-600/20 flex items-center justify-center text-blue-400 text-xs font-bold overflow-hidden">
                              {coin.image || coin.logo ? (
                                <img
                                  src={coin.image || coin.logo}
                                  alt={coin.name}
                                  className="h-full w-full object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = `https://via.placeholder.com/24x24/3b82f6/ffffff?text=${coin.symbol?.charAt(0) || 'C'}`;
                                  }}
                                />
                              ) : (
                                coin.symbol.slice(0, 2).toUpperCase()
                              )}
                            </div>
                            <div className="flex-1">
                              <p className="text-sm font-medium">{coin.name}</p>
                              <p className="text-xs text-slate-400">
                                {coin.symbol}
                              </p>
                            </div>
                            {isAlreadySelected && (
                              <Check className="h-4 w-4 text-blue-500" />
                            )}
                          </CommandItem>
                        );
                      })}
                    </CommandGroup>
                  </>
                )}
              </Command>
            </PopoverContent>
          </Popover>
        )}
      </div>

      <div className="flex items-center justify-between text-sm text-slate-400">
        <div>
          {selectedCoins.length > 0 && (
            <div
              className={`flex items-center gap-1.5 ${
                selectedCoins.length === maxSelections
                  ? "text-amber-400"
                  : selectedCoins.length === maxSelections - 1
                    ? "text-blue-400"
                    : ""
              }`}
            >
              <span>
                {selectedCoins.length} of {maxSelections} selected
              </span>
              {selectedCoins.length === maxSelections && (
                <span className="rounded-full bg-amber-500/20 px-1.5 py-0.5 text-xs font-medium text-amber-400">
                  Max
                </span>
              )}
              {selectedCoins.length === maxSelections - 1 && (
                <span className="rounded-full bg-blue-500/20 px-1.5 py-0.5 text-xs font-medium text-blue-400">
                  1 left
                </span>
              )}
            </div>
          )}
        </div>

        <div>
          {selectedCoins.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 px-3 text-xs bg-slate-800 text-slate-400 hover:bg-slate-700 hover:text-blue-300 transition-colors"
              onClick={() => clearCoins()}
            >
              Reset selection
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
