import { useState, useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { Check, Mail, X, Eye, EyeOff, AlertCircle } from "lucide-react";
import api from "@/lib/api";
import { useAuth } from "@/hooks/use-auth";
import { queryClient } from "@/lib/queryClient";
import { useLanguage } from "@/contexts/LanguageContext";

// Add type declaration for reCAPTCHA v3
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
    };
  }
}

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";
import {
  Card,
  CardContent,
  CardFooter,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

function getRegisterSchema(t: (key: string, namespace?: string, fallback?: string) => string) {
  return z
    .object({
      username: z.string().min(3, t("auth:validation.username.min")),
      email: z.string().email(t("auth:validation.email.invalid")),
      password: z
        .string()
        .min(8, t("auth:validation.password.min"))
        .regex(/[A-Z]/, t("auth:validation.password.uppercase"))
        .regex(/[a-z]/, t("auth:validation.password.lowercase"))
        .regex(/[0-9]/, t("auth:validation.password.number"))
        .regex(
          /[^A-Za-z0-9]/,
          t("auth:validation.password.special"),
        ),
      confirmPassword: z.string(),
      agreeToTerms: z.boolean().refine((val) => val === true, {
        message: t("auth:validation.terms"),
      }),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("auth:validation.password.match"),
      path: ["confirmPassword"],
    });
}

type RegisterFormData = {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
};

interface RegisterFormProps {
  onToggleView: () => void;
  className?: string;
}

// Password strength criteria
function getPasswordCriteria(t: (key: string, namespace?: string, fallback?: string) => string) {
  return [
    { id: "length", label: t("password.criteria.length", "auth"), regex: /.{8,}/ },
    { id: "uppercase", label: t("password.criteria.uppercase", "auth"), regex: /[A-Z]/ },
    { id: "lowercase", label: t("password.criteria.lowercase", "auth"), regex: /[a-z]/ },
    { id: "number", label: t("password.criteria.number", "auth"), regex: /[0-9]/ },
    {
      id: "special",
      label: t("password.criteria.special", "auth"),
      regex: /[^A-Za-z0-9]/,
    },
  ];
};

export function RegisterForm({
  onToggleView,
  className = "",
}: RegisterFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [metCriteria, setMetCriteria] = useState<Record<string, boolean>>({});
  const [formError, setFormError] = useState<string | null>(null);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const { toast } = useToast();
  const { t } = useLanguage();
  const passwordCriteria = getPasswordCriteria(t);
  const registerSchema = getRegisterSchema(t);

  // Load reCAPTCHA v3 script
  useEffect(() => {
    // Load the reCAPTCHA script
    const loadRecaptchaScript = () => {
      const script = document.createElement('script');
      script.src = `https://www.google.com/recaptcha/api.js?render=6LeBrjQrAAAAAHJXkHe6VOGU74iNjCO75MCkOhcn`;
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
      
      return script;
    };

    const script = loadRecaptchaScript();
    
    return () => {
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  const form = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
    },
    mode: "onChange",
  });

  // Password strength checker
  useEffect(() => {
    const password = form.watch("password");
    if (!password) {
      setPasswordStrength(0);
      setMetCriteria({});
      return;
    }

    // Check which criteria are met
    const criteriaResults: Record<string, boolean> = {};
    passwordCriteria.forEach((criterion) => {
      criteriaResults[criterion.id] = criterion.regex.test(password);
    });
    setMetCriteria(criteriaResults);

    // Calculate strength percentage
    const metCount = Object.values(criteriaResults).filter(Boolean).length;
    setPasswordStrength((metCount / passwordCriteria.length) * 100);
  }, [form.watch("password")]);

  // Effect to clear form error when fields change
  useEffect(() => {
    // Clear form error when any field changes
    setFormError(null);
  }, [form.watch("email"), form.watch("username"), form.watch("password")]);

  const { register } = useAuth();

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true);
    

    
    try {
      // Execute reCAPTCHA v3 when the form is submitted
      if (window.grecaptcha) {
        window.grecaptcha.ready(async () => {
          try {
            const token = await window.grecaptcha.execute('6LeBrjQrAAAAAHJXkHe6VOGU74iNjCO75MCkOhcn', {action: 'register'});
            setCaptchaToken(token);

            
            // Continue with form submission after getting the token
            await processRegistration(data, token);
          } catch (error) {
            console.error("reCAPTCHA error:", error);
            handleRegistrationError(new Error("CAPTCHA verification failed. Please try again."));
          }
        });
      } else {
        console.error("reCAPTCHA not loaded");
        handleRegistrationError(new Error("CAPTCHA service not available. Please refresh the page and try again."));
      }
    } catch (error) {
      handleRegistrationError(error as Error);
    }
  };
  
  // Error handling function
  const handleRegistrationError = (error: Error) => {
    console.error("Registration error:", error);
    setFormError(error.message);
    toast({
      title: t("auth:register.failed", "auth", "Registration failed"),
      description: error.message,
      variant: "destructive",
    });
    setIsLoading(false);
  };

  // Separate the registration logic for clarity
  const processRegistration = async (data: RegisterFormData, captchaToken: string) => {

    
    try {
      console.log("Calling register function with data:", {
        email: data.email,
        username: data.username,
        captchaProvided: !!captchaToken
      });
      
      // Güvenlik kontrolü: try-catch bloğuna al
      let apiResponse;
      try {
        // Use the register function from auth context which already handles localStorage and token storage
        apiResponse = await register(
          data.email,
          data.password,
          data.username,
          data.agreeToTerms,
          captchaToken
        );
      } catch (registerError: any) {
        console.error("Error during register call:", registerError);
        // Hata durumunda güvenli bir cevap döndür
        apiResponse = {
          success: false,
          errormsg: registerError.message || "Registration failed. Please try again later.",
          error: "Registration error"
        };
      }
      
      // Log response details to console

      
      // Handle unsuccessful response
      if (!apiResponse.success) {

        
        // API'den gelen hata mesajını tercih ederek göster
        const errorMessage = apiResponse.errormsg || apiResponse.error || t("auth:register.failed");
        
        // IMPORTANT - API'den gelen hatayı form üzerinde belirgin şekilde göster
        setFormError(errorMessage);
        
        // Show toast notification
        toast({
          title: t("auth:register.failed"),
          description: errorMessage,
          variant: "destructive",
        });
        
        // End the process here
        setIsLoading(false);
        return;
      }
      
      // On successful response

      
      // Store token and user information
      if (apiResponse.output?.token) {
        // Token and user data are already stored by the register function in useAuth
        
        // Show success message to user
        toast({
          title: t("auth:register.success", "auth", "Registration successful"),
          description: t("auth:register.success.email_verify", "auth", "Registration successful. Please check your email to verify your account."),
        });
        
        // Redirect directly to CoinList page
        window.location.href = "/coinlist";
      } else {
        // If no token is received
        toast({
          title: t("auth:register.success", "auth", "Registration successful"),
          description: t("auth:register.success.login", "auth", "Your account has been created. Please log in."),
        });
        
        // Redirect back to login page
        onToggleView(); // Switch from Register page to Login page
      }
      
    } catch (error: any) {
      console.log("### REGISTER ERROR - EXCEPTION CAUGHT ###");
      console.error("Error details:", error);
      
      let errorMessage = t("auth:register.error.generic");
      
      // 1. Axios özel hata yanıtlarını kontrol et
      if (error.response) {
        console.log("Error response data:", error.response.data);
        console.log("Error response status:", error.response.status);
        
        // API'den dönen özel hata mesajı
        if (error.response.data?.errormsg) {
          errorMessage = error.response.data.errormsg;
        } else if (error.response.status === 403) {
          errorMessage = "Registration is currently limited to invited users only.";
        }
      }
      // 2. Normal Error mesajını al  
      else if (error instanceof Error) {
        errorMessage = error.message;
        console.log("Error message:", error.message);
      }
      
      // For reCAPTCHA v3, we don't need to reset as it's invisible and executed on form submission
      setCaptchaToken(null);
      
      // Update the form error message - bu satırın doğru çalıştığından emin olalım
      console.log("Setting form error to:", errorMessage);
      setFormError(errorMessage);
      
      // Toast notification
      toast({
        title: t("auth:register.failed"),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      console.log("### REGISTER FORM SUBMIT COMPLETED ###");
    }
  };

  // We removed all validation-related code because we don't need it anymore

  // We don't need these codes anymore - users will log in directly

  // Registration form
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`w-full ${className}`}
    >
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-bold tracking-tight">{t("auth:register.title")}</h1>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4">
          {/* Form error message - Updated for better visibility */}
          {formError && (
            <div className="p-4 mb-4 border-2 border-red-400 bg-red-50 dark:bg-red-950 dark:border-red-700 rounded-md flex items-start shadow-sm">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-sm font-medium text-red-600 dark:text-red-400">
                {formError}
              </div>
            </div>
          )}
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("auth:username")}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("auth:username.placeholder")}
                    disabled={isLoading}
                    autoComplete="username"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("auth:email")}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("auth:email.placeholder")}
                    type="email"
                    disabled={isLoading}
                    autoComplete="email"
                    {...field}
                  />
                </FormControl>
                <FormDescription className="text-xs">
                  {t("auth:email.description")}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("auth:password")}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder={t("auth:password.create")}
                      type={showPassword ? "text" : "password"}
                      disabled={isLoading}
                      autoComplete="new-password"
                      className="pr-10"
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                      <span className="sr-only">
                        {showPassword ? t("auth:password.hide") : t("auth:password.show")}
                      </span>
                    </Button>
                  </div>
                </FormControl>

                {/* Password strength meter */}
                {field.value && (
                  <div className="mt-2 space-y-2">
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>{t("auth:password.strength")}</span>
                        <span>
                          {passwordStrength < 40 && t("auth:password.strength.weak")}
                          {passwordStrength >= 40 &&
                            passwordStrength < 80 &&
                            t("auth:password.strength.good")}
                          {passwordStrength >= 80 && t("auth:password.strength.strong")}
                        </span>
                      </div>
                      <Progress
                        value={passwordStrength}
                        className="h-1"
                        style={{
                          color:
                            passwordStrength < 40
                              ? "var(--destructive)"
                              : passwordStrength < 80
                                ? "var(--warning)"
                                : "var(--success)",
                        }}
                      />
                    </div>

                    <div className="space-y-1">
                      {passwordCriteria.map((criterion) => (
                        <div
                          key={criterion.id}
                          className="flex items-center text-xs"
                        >
                          {metCriteria[criterion.id] ? (
                            <Check className="h-3 w-3 mr-2 text-green-500" />
                          ) : (
                            <X className="h-3 w-3 mr-2 text-destructive" />
                          )}
                          <span
                            className={
                              metCriteria[criterion.id]
                                ? "text-muted-foreground"
                                : "text-destructive"
                            }
                          >
                            {criterion.label}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("auth:password.confirm")}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder={t("auth:password.confirm.placeholder")}
                      type={showPassword ? "text" : "password"}
                      disabled={isLoading}
                      autoComplete="new-password"
                      className="pr-10"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="agreeToTerms"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4 shadow border">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isLoading}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    {t("terms.agree", "auth", "I agree to the")}{" "}
                    <Link
                      href="/terms"
                      className="text-primary hover:underline"
                    >
                      {t("terms.service", "auth", "Terms of Service")}
                    </Link>{" "}
                    {t("terms.and", "auth", "and")}{" "}
                    <Link
                      href="/privacy"
                      className="text-primary hover:underline"
                    >
                      {t("terms.privacy", "auth", "Privacy Policy")}
                    </Link>
                  </FormLabel>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />

          {/* reCAPTCHA v3 is invisible and executed on form submission */}
          <div className="flex justify-center my-4 text-xs text-muted-foreground">
            {t("captcha.protected", "auth", "This form is protected by reCAPTCHA")}
          </div>

          <div className="space-y-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isLoading || !form.formState.isValid}
                    >
                      {isLoading ? (
                        <span className="flex items-center gap-2">
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                          {t("register.creating", "auth", "Creating account...")}
                        </span>
                      ) : (
                        t("register.create", "auth", "Create account")
                      )}
                    </Button>
                  </div>
                </TooltipTrigger>
                {!form.formState.isValid && (
                  <TooltipContent className="max-w-[300px]">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-destructive" />
                      <span>{t("form.invalidFields", "auth", "Please fill in all required fields correctly")}</span>
                    </div>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>

            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={onToggleView}
              disabled={isLoading}
            >
              {t("register.haveAccount", "auth", "Already have an account?")}
            </Button>
          </div>
        </form>
      </Form>
    </motion.div>
  );
}
