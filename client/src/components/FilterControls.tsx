import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Coins, Star, Grid, Link2, Calendar, Lock } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import CoinService, { Category, Chain } from "@/lib/services/CoinService";
import { useAuth } from "@/hooks/use-auth";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { useLocation } from "wouter";
import { getUserSubscription } from "@/services/subscriptionService";

// Listing date options
interface ListingDateOption {
  value: string;
  label: string;
}

// Bu liste artık çeviri anahtarları ile oluşturulacak

export interface FilterControlsState {
  marketCapRange: [number, number];
  projectScoreRange: [number, number];
  categories: string[];
  chains: string[];
  listingDate?: string | null; // Listeleme tarihi filtresi eklendi
  isReset?: boolean; // Reset işlemlerini takip etmek için eklendi
}

interface FilterControlsProps {
  onFilterChange: (filters: FilterControlsState) => void;
  initialFilters?: FilterControlsState;
  onClose?: () => void;
  className?: string;
  showListingDateFilter?: boolean; // Listing Date filtresinin gösterilip gösterilmeyeceğini belirler
}

export function FilterControls({
  onFilterChange,
  initialFilters,
  onClose,
  className,
  showListingDateFilter = false, // Varsayılan olarak gösterme
}: FilterControlsProps) {
  // Authentication and subscription hooks
  const { isLoggedIn, user } = useAuth();
  const { canAccessFeature, subscriptionLevel } = useSubscription();
  const { toast } = useToast();
  const { t } = useLanguage();
  const [, setLocation] = useLocation();
  const [apiSubscription, setApiSubscription] = useState<any>(null);
  const [showSubscriptionDialog, setShowSubscriptionDialog] = useState(false);

  // Dinamik listing date options oluştur
  const getListingDateOptions = (): ListingDateOption[] => [
    { value: "1", label: t("newlyListed.last24Hours") },
    { value: "7", label: t("newlyListed.last7Days") },
    { value: "14", label: t("newlyListed.last14Days") },
    { value: "30", label: t("newlyListed.last30Days") },
    { value: "90", label: t("newlyListed.last90Days") }
  ];

  // Default values in actual units (not millions)
  const defaultMarketCapMin = 1000000; // $1M
  const defaultMarketCapMax = 100000000000; // $100B (değiştirildi: 10B -> 100B)

  // Convert from actual value to display value in millions with 2 decimal places
  const convertToDisplayValue = (actualValue: number): number => {
    return parseFloat((actualValue / 1000000).toFixed(2));
  };

  // Convert from display value (in millions) to actual value
  const convertToActualValue = (valueInMillions: number): number => {
    // Milyon değerini gerçek değere dönüştürüyoruz
    const result = valueInMillions * 1000000;
    console.log(`Converting display value ${valueInMillions}M to actual value: ${result}`);
    return result;
  };

  const [marketCapRange, setMarketCapRange] = React.useState<[number, number]>(
    initialFilters?.marketCapRange || [
      defaultMarketCapMin,
      defaultMarketCapMax,
    ],
  );

  // Display values for the inputs (in millions)
  const [minMarketCapDisplay, setMinMarketCapDisplay] = useState(
    convertToDisplayValue(marketCapRange[0]),
  );

  const [maxMarketCapDisplay, setMaxMarketCapDisplay] = useState(
    convertToDisplayValue(marketCapRange[1]),
  );

  const [projectScoreRange, setProjectScoreRange] = React.useState<
    [number, number]
  >(initialFilters?.projectScoreRange || [0, 100]);

  // Kategori ID'leri için state - kategori ismi yerine artık ID kullanıyoruz
  const [selectedCategories, setSelectedCategories] = React.useState<string[]>(
    initialFilters?.categories || [],
  );

  const [selectedChains, setSelectedChains] = React.useState<string[]>(
    initialFilters?.chains || [],
  );

  // API data states
  const [categories, setCategories] = useState<Category[]>([]);
  const [chains, setChains] = useState<Chain[]>([]);
  const [isLoading, setIsLoading] = useState({
    categories: false,
    chains: false,
  });

  // UI states for dropdowns
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
  const [isChainsOpen, setIsChainsOpen] = useState(false);
  
  // Arama sorguları için state'ler
  const [categoriesSearchQuery, setCategoriesSearchQuery] = useState('');
  const [chainsSearchQuery, setChainsSearchQuery] = useState('');
  
  // Listing date state
  const [selectedListingDate, setSelectedListingDate] = useState<string | null>(
    initialFilters?.listingDate || "30" // Default to 30 days
  );

  // Fetch subscription data when user is logged in (same as Navigation.tsx)
  useEffect(() => {
    const fetchSubscription = async () => {
      if (user) {
        try {
          const response = await getUserSubscription();
          const subscriptionData = response.data as any;
          if (response.success && subscriptionData?.subscriptions?.length > 0) {
            setApiSubscription(subscriptionData.subscriptions[0]);
            console.log("🔍 Filter - API Subscription fetched:", subscriptionData.subscriptions[0]);
          } else {
            // If no subscription found, clear the state
            setApiSubscription(null);
          }
        } catch (error) {
          console.error('Failed to fetch subscription in FilterControls:', error);
          setApiSubscription(null);
        }
      } else {
        // Clear subscription data when user is not logged in
        setApiSubscription(null);
      }
    };

    fetchSubscription();
  }, [user]);

  // Click outside handler to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const categoriesEl = document.getElementById("categories-dropdown");
      const chainsEl = document.getElementById("chains-dropdown");

      if (categoriesEl && !categoriesEl.contains(event.target as Node)) {
        setIsCategoriesOpen(false);
      }

      if (chainsEl && !chainsEl.contains(event.target as Node)) {
        setIsChainsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch categories and chains from API
  useEffect(() => {
    const fetchFilterData = async () => {
      try {
        setIsLoading((prev) => ({ ...prev, categories: true }));
        const categoriesData = await CoinService.getCategories();
        setCategories(categoriesData);
        setIsLoading((prev) => ({ ...prev, categories: false }));
      } catch (error) {
        console.error("Error fetching categories:", error);
        setIsLoading((prev) => ({ ...prev, categories: false }));
      }

      try {
        setIsLoading((prev) => ({ ...prev, chains: true }));
        const chainsData = await CoinService.getChains();
        setChains(chainsData);
        setIsLoading((prev) => ({ ...prev, chains: false }));
      } catch (error) {
        console.error("Error fetching chains:", error);
        setIsLoading((prev) => ({ ...prev, chains: false }));
      }
    };

    fetchFilterData();
  }, []);

  // Update state if initialFilters changes
  React.useEffect(() => {
    if (initialFilters) {
      // Ensure we have valid values and not extremely small numbers
      let minValue = initialFilters.marketCapRange[0];
      let maxValue = initialFilters.marketCapRange[1];

      // If values are suspiciously small, use defaults
      if (minValue < 100000) {
        // Less than $0.1M
        minValue = defaultMarketCapMin;
      }

      if (maxValue < 1000000) {
        // Less than $1M
        maxValue = defaultMarketCapMax;
      }

      console.log("Initializing filter with values:", {
        minValue,
        maxValue,
        minValueInM: convertToDisplayValue(minValue),
        maxValueInM: convertToDisplayValue(maxValue)
      });

      setMarketCapRange([minValue, maxValue]);

      // Update display values (convert to millions)
      setMinMarketCapDisplay(convertToDisplayValue(minValue));
      setMaxMarketCapDisplay(convertToDisplayValue(maxValue));

      setProjectScoreRange(initialFilters.projectScoreRange);
      setSelectedCategories(initialFilters.categories);
      setSelectedChains(initialFilters.chains);
    } else {
      // If no initial filters, explicitly set default values
      setMarketCapRange([defaultMarketCapMin, defaultMarketCapMax]);
      setMinMarketCapDisplay(1.00); // 1M
      setMaxMarketCapDisplay(100000.00); // 100,000M = 100B (değiştirildi: 10,000 -> 100,000)
    }
  }, [initialFilters]);

  // Handle manual input change for min market cap
  const handleMinMarketCapChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = parseFloat(e.target.value);
    if (isNaN(inputValue) || inputValue < 0) return;

    const actualValue = convertToActualValue(inputValue);

    // Ensure min doesn't exceed max
    if (actualValue < marketCapRange[1]) {
      setMarketCapRange([actualValue, marketCapRange[1]]);
      setMinMarketCapDisplay(inputValue);
    }
  };

  // Map market cap value to slider position (0-100)
  const mapMarketCapToSlider = (marketCap: number): number => {
    // Using logarithmic scale for better UX with large ranges
    // Min value: 1M (10^6), Max value: 100B (10^11)
    const minLog = Math.log10(1000000); // log10(1M)
    const maxLog = Math.log10(100000000000); // log10(100B)

    // Ensure the market cap isn't below our minimum before taking log
    const safeMarketCap = Math.max(marketCap, 1000000);
    const valueLog = Math.log10(safeMarketCap);

    // Map to 0-100 range
    return ((valueLog - minLog) / (maxLog - minLog)) * 100;
  };

  // Map slider position (0-100) to market cap value
  const mapSliderToMarketCap = (sliderValue: number): number => {
    // Convert slider position back to market cap using inverse log mapping
    const minLog = Math.log10(1000000); // log10(1M)
    const maxLog = Math.log10(100000000000); // log10(100B)

    const valueLog = minLog + (sliderValue / 100) * (maxLog - minLog);
    // Logaritmik hesaplama ile gerçek değeri elde ediyoruz
    const result = Math.round(Math.pow(10, valueLog));
    console.log(`Converting slider value ${sliderValue} to marketcap: ${result} (${result/1000000}M)`);
    return result;
  };

  // Handle manual input change for max market cap
  const handleMaxMarketCapChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = parseFloat(e.target.value);
    if (isNaN(inputValue) || inputValue < 0) return;

    const actualValue = convertToActualValue(inputValue);

    // Ensure max isn't below min
    if (actualValue > marketCapRange[0]) {
      setMarketCapRange([marketCapRange[0], actualValue]);
      setMaxMarketCapDisplay(inputValue);
    }
  };

  const handleApplyFilters = () => {
    // Debug: Log current authentication and subscription status
    console.log("🔍 Filter Debug - isLoggedIn:", isLoggedIn);
    console.log("🔍 Filter Debug - subscriptionLevel:", subscriptionLevel);
    console.log("🔍 Filter Debug - canAccessFeature(1):", canAccessFeature(1));
    console.log("🔍 Filter Debug - apiSubscription:", apiSubscription);

    // Check authentication and subscription status before applying filters
    if (!isLoggedIn) {
      console.log("❌ Filter blocked: User not logged in");
      setShowSubscriptionDialog(true);
      return;
    }

    // Use API subscription data if available, otherwise fallback to context
    // Check if user has paid subscription directly from API
    const hasValidSubscription = apiSubscription && 
      apiSubscription.status === 'active' && 
      (apiSubscription.plan_name === 'Basic' || 
       apiSubscription.plan_name === 'Advance' || 
       apiSubscription.plan_name === 'Premium');

    // If no valid subscription found, check subscription context as fallback
    const canUseFilters = hasValidSubscription || canAccessFeature(1);

    if (!canUseFilters) {
      console.log("❌ Filter blocked: Insufficient subscription level", {
        subscriptionLevel,
        apiSubscription: apiSubscription?.plan_name,
        hasValidSubscription
      });
      setShowSubscriptionDialog(true);
      return;
    }

    console.log("✅ Filter allowed: User has sufficient access", {
      hasValidSubscription,
      apiPlan: apiSubscription?.plan_name
    });

    // Ensure we're not sending extremely small values
    let [min, max] = marketCapRange;

    // Apply minimum thresholds
    if (min < 100000) min = defaultMarketCapMin;
    if (max < 1000000) max = defaultMarketCapMax;

    // Loglama ekleyelim
    console.log(`Applying filters with market cap range: ${min} to ${max}`);
    console.log(`Market cap in millions: ${min/1000000}M to ${max/1000000}M`);
    
    // Eğer tarih filtresi seçildi ise
    if (selectedListingDate) {
      console.log(`Filtering coins with listing_date: ${selectedListingDate} days`);
    }

    const filterState: FilterControlsState = {
      marketCapRange: [min, max],
      projectScoreRange,
      categories: selectedCategories,
      chains: selectedChains,
      listingDate: selectedListingDate, // Listeleme tarihini ekledik
    };

    onFilterChange(filterState);
    
    // Apply butonuna tıklandığında dialogu kapatmak için onClose varsa çağıralım
    if (onClose) {
      onClose();
    }
  };

  const handleResetFilters = () => {
    // Define default values
    const defaultMarketCapRange: [number, number] = [
      defaultMarketCapMin,
      defaultMarketCapMax,
    ]; // 1M to 100B (değiştirildi)
    const defaultFilters: FilterControlsState = {
      marketCapRange: defaultMarketCapRange,
      projectScoreRange: [0, 100],
      categories: [],
      chains: [],
      listingDate: "30", // Varsayılan olarak 30 gün
    };

    // Update visual state with default values
    setMarketCapRange(defaultMarketCapRange);
    setMinMarketCapDisplay(1.00); // 1M
    setMaxMarketCapDisplay(100000.00); // 100,000M = 100B (değiştirildi: 10,000 -> 100,000)

    setProjectScoreRange(defaultFilters.projectScoreRange);
    setSelectedCategories(defaultFilters.categories);
    setSelectedChains(defaultFilters.chains);
    setSelectedListingDate("30"); // Listeleme tarihini de sıfırlıyoruz

    // Create a special reset filter state with a flag to indicate this is a reset
    const resetFilterState: FilterControlsState & { isReset?: boolean } = {
      ...defaultFilters,
      isReset: true, // Add flag to indicate this is a reset action
    };

    // Notify parent that filters should be completely reset
    onFilterChange(resetFilterState);

    // Close dialog if onClose is provided
    if (onClose) {
      onClose();
    }
  };

  // İlk mount'ta verileri sıfırlamak için kullanılan useEffect'i tamamen kaldırıyoruz
  // Bu useEffect filtrelerin değerlerini başlangıç değerine ayarlıyor ve sorun yaratıyordu

  // CSS to hide input spinners
  const noSpinnerCSS = `
    /* Hide spinners for Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Hide spinners for Firefox */
    input[type=number] {
      -moz-appearance: textfield;
    }
  `;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
      className={cn(
        "w-full max-w-[95%] sm:max-w-3xl mx-auto px-4 sm:px-0 relative overflow-visible",
        className,
      )}
    >
      {/* Add the CSS to remove spinners */}
      <style>{noSpinnerCSS}</style>

      <Card className="bg-card/95 backdrop-blur-[2px] border-border/30 p-4 sm:p-6 space-y-6 sm:space-y-8 relative overflow-visible">
        <div className="space-y-6 sm:space-y-8">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Coins className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">
                {t("filters.marketCapRange")}
              </Label>
            </div>
            <div className="px-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
                {/* Min Market Cap Input */}
                <div className="relative flex">
                  <input
                    type="number"
                    value={minMarketCapDisplay}
                    onChange={handleMinMarketCapChange}
                    className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                    min="1"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                    M
                  </div>
                </div>

                {/* Max Market Cap Input */}
                <div className="relative flex">
                  <input
                    type="number"
                    value={maxMarketCapDisplay}
                    onChange={handleMaxMarketCapChange}
                    className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                    min="1"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                    M
                  </div>
                </div>
              </div>
              <div className="pt-4">
                <p className="text-xs text-muted-foreground mb-2">
                  {t("filters.moveSliderToAdjust")}
                </p>
                <Slider
                  value={[
                    mapMarketCapToSlider(marketCapRange[0]),
                    mapMarketCapToSlider(marketCapRange[1]),
                  ]}
                  min={0}
                  max={100}
                  step={1}
                  onValueChange={(value) => {
                    const [sliderMin, sliderMax] = value as [number, number];
                    const newMinMarketCap = mapSliderToMarketCap(sliderMin);
                    const newMaxMarketCap = mapSliderToMarketCap(sliderMax);

                    setMarketCapRange([newMinMarketCap, newMaxMarketCap]);
                    setMinMarketCapDisplay(
                      convertToDisplayValue(newMinMarketCap),
                    );
                    setMaxMarketCapDisplay(
                      convertToDisplayValue(newMaxMarketCap),
                    );
                  }}
                  className="my-2 h-0.5 mt-1"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">
                {t("filters.projectScoreRange")}
              </Label>
            </div>
            <div className="px-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
                <input
                  type="number"
                  value={projectScoreRange[0]}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 100 && value <= projectScoreRange[1]) {
                      setProjectScoreRange([value, projectScoreRange[1]]);
                    }
                  }}
                  min="0"
                  max="100"
                  className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                />
                <input
                  type="number"
                  value={projectScoreRange[1]}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 100 && value >= projectScoreRange[0]) {
                      setProjectScoreRange([projectScoreRange[0], value]);
                    }
                  }}
                  min="0"
                  max="100"
                  className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                />
              </div>
              <Slider
                value={projectScoreRange}
                min={0}
                max={100}
                step={1}
                onValueChange={(value) =>
                  setProjectScoreRange(value as [number, number])
                }
                className="my-2 h-0.5 mt-1"
              />
            </div>
          </div>

          {/* Categories section - unchanged */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Grid className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">{t("filters.categories")}</Label>
            </div>
            <div className="px-0">
              <div className="relative" id="categories-dropdown">
                <div
                  className={`w-full bg-background/50 border border-border/50 h-auto min-h-11 rounded-lg transition-standard p-2 px-3 flex flex-wrap gap-1 items-center ${isLoading.categories ? "opacity-70 cursor-not-allowed" : "cursor-pointer"}`}
                  onClick={() =>
                    !isLoading.categories &&
                    setIsCategoriesOpen(!isCategoriesOpen)
                  }
                >
                  {selectedCategories.length > 0 ? (
                    <div className="flex items-center flex-nowrap overflow-hidden">
                      {selectedCategories.slice(0, 2).map((categoryId, index) => {
                        // Kategori adını bulalım
                        const categoryObj = categories.find(cat => cat.id === categoryId);
                        const categoryName = categoryObj ? categoryObj.name : categoryId;
                        
                        return (
                          <span
                            key={index}
                            className="bg-primary/20 text-white rounded-md px-2 py-1 text-xs flex items-center gap-1 whitespace-nowrap mr-1"
                          >
                            {categoryName}
                            <button
                              className="text-primary-foreground/70 hover:text-primary-foreground ml-1 rounded-full focus:outline-none focus:ring-2 focus:ring-primary/30"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedCategories(
                                  selectedCategories.filter(
                                    (c) => c !== categoryId,
                                  ),
                                );
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="12"
                                height="12"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                              </svg>
                            </button>
                          </span>
                        );
                      })}
                      {selectedCategories.length > 2 && (
                        <span className="bg-primary/10 text-primary rounded-md px-2 py-1 text-xs whitespace-nowrap">
                          +{selectedCategories.length - 2} more
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">
                      {isLoading.categories
                        ? t("filters.loading")
                        : t("filters.selectCategories")}
                    </span>
                  )}
                </div>

                {isCategoriesOpen && (
                  <div className="absolute z-50 w-full mt-1 bg-background border border-border/50 rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                    <div className="p-2 border-b border-border/50 sticky top-0 bg-background z-10">
                      <div className="flex items-center relative">
                        <input
                          type="text"
                          placeholder="Search categories..."
                          className="w-full bg-background/80 border border-border/50 rounded-md px-3 py-1.5 text-sm"
                          value={categoriesSearchQuery || ""}
                          onChange={(e) => setCategoriesSearchQuery(e.target.value)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        {categoriesSearchQuery && (
                          <button
                            className="absolute right-2 text-muted-foreground hover:text-foreground"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCategoriesSearchQuery("");
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <line x1="18" y1="6" x2="6" y2="18"></line>
                              <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                          </button>
                        )}
                      </div>
                      <div className="flex justify-between mt-2 text-xs">
                        <span>
                          {selectedCategories.length} selected
                        </span>
                        <div className="flex gap-2">
                          <button 
                            className="text-xs text-primary hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (categoriesSearchQuery) {
                                // Sadece filtrelenmiş kategorileri seç
                                const filteredCategoryIds = categories
                                  .filter(cat => 
                                    cat.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                                  )
                                  .map(cat => cat.id);
                                
                                // Filtrelenmiş kategorileri mevcut seçimlere ekle
                                // Set kullanarak tekrar eden öğeleri kaldıralım
                                const uniqueIdsSet = new Set([...selectedCategories, ...filteredCategoryIds]);
                                // Set'ten diziye dönüştürelim
                                const uniqueIds = Array.from(uniqueIdsSet);
                                setSelectedCategories(uniqueIds);
                              } else {
                                // Tümünü seç - tüm kategori ID'lerini ekle
                                setSelectedCategories(categories.map(cat => cat.id));
                              }
                            }}
                          >
                            {categoriesSearchQuery ? "Select Filtered" : "Select All"}
                          </button>
                          <button 
                            className="text-xs text-primary hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (categoriesSearchQuery) {
                                // Eğer bir arama sorgusu varsa, yalnızca filtrelenmiş kategorileri kaldır
                                const filteredCategoryIds = categories
                                  .filter(cat => 
                                    cat.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                                  )
                                  .map(cat => cat.id);
                                
                                setSelectedCategories(selectedCategories.filter(id => !filteredCategoryIds.includes(id)));
                              } else {
                                // Arama sorgusu yoksa, tüm seçimleri temizle
                                setSelectedCategories([]);
                              }
                            }}
                          >
                            {categoriesSearchQuery ? "Deselect Filtered" : "Deselect All"}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="p-1">
                      {categories.length > 0 ? (
                        categories
                          .filter(category => 
                            !categoriesSearchQuery || 
                            category.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                          )
                          .map((category, index) => (
                          <div
                            key={index}
                            className={`p-2 rounded-md cursor-pointer flex items-center gap-2 ${selectedCategories.includes(category.id) ? "bg-primary/20 text-primary-foreground" : "hover:bg-accent/50"}`}
                            onClick={() => {
                              if (selectedCategories.includes(category.id)) {
                                setSelectedCategories(
                                  selectedCategories.filter(
                                    (c) => c !== category.id,
                                  ),
                                );
                              } else {
                                setSelectedCategories([
                                  ...selectedCategories,
                                  category.id,
                                ]);
                              }
                            }}
                          >
                            <div
                              className={`w-4 h-4 rounded border ${selectedCategories.includes(category.id) ? "bg-primary border-primary" : "border-border"} flex items-center justify-center`}
                            >
                              {selectedCategories.includes(category.id) && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="10"
                                  height="10"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              )}
                            </div>
                            <span>{category.name}</span>
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-muted-foreground">
                          {isLoading.categories
                            ? t("filters.loading")
                            : t("filters.noCategoriesFound")}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Listing Date section - sadece showListingDateFilter=true ise göster */}
          {showListingDateFilter && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-primary" />
                <Label className="text-base font-medium">{t("filters.listingDate")}</Label>
              </div>
              <div className="px-0">
                <Select
                  value={selectedListingDate || "30"}
                  onValueChange={(value) => setSelectedListingDate(value)}
                >
                  <SelectTrigger className="w-full bg-background/50 border border-border/50 rounded-lg transition-standard h-11">
                    <SelectValue placeholder={t("newlyListed.selectTimeframe")} />
                  </SelectTrigger>
                  <SelectContent className="bg-background border border-border/50 rounded-lg shadow-lg z-50">
                    {getListingDateOptions().map((option: ListingDateOption) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Chains section - unchanged */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Link2 className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">{t("filters.chainEcosystem")}</Label>
            </div>
            <div className="px-0">
              <div className="relative" id="chains-dropdown">
                <div
                  className={`w-full bg-background/50 border border-border/50 h-auto min-h-11 rounded-lg transition-standard p-2 px-3 flex flex-wrap gap-1 items-center ${isLoading.chains ? "opacity-70 cursor-not-allowed" : "cursor-pointer"}`}
                  onClick={() =>
                    !isLoading.chains && setIsChainsOpen(!isChainsOpen)
                  }
                >
                  {selectedChains.length > 0 ? (
                    <div className="flex items-center flex-nowrap overflow-hidden">
                      {selectedChains.slice(0, 2).map((chain, index) => (
                        <span
                          key={index}
                          className="bg-primary/20 text-white rounded-md px-2 py-1 text-xs flex items-center gap-1 whitespace-nowrap mr-1"
                        >
                          {chain}
                          <button
                            className="text-primary-foreground/70 hover:text-primary-foreground ml-1 rounded-full focus:outline-none focus:ring-2 focus:ring-primary/30"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedChains(
                                selectedChains.filter((c) => c !== chain),
                              );
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="12"
                              height="12"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <line x1="18" y1="6" x2="6" y2="18"></line>
                              <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                          </button>
                        </span>
                      ))}
                      {selectedChains.length > 2 && (
                        <span className="bg-primary/10 text-primary rounded-md px-2 py-1 text-xs whitespace-nowrap">
                          +{selectedChains.length - 2} more
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">
                      {isLoading.chains ? t("filters.loading") : t("filters.selectChains")}
                    </span>
                  )}
                </div>

                {isChainsOpen && (
                  <div className="absolute z-50 w-full mt-1 bg-background border border-border/50 rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                    <div className="p-2 border-b border-border/50 sticky top-0 bg-background z-10">
                      <div className="flex items-center relative">
                        <input
                          type="text"
                          placeholder="Search chains..."
                          className="w-full bg-background/80 border border-border/50 rounded-md px-3 py-1.5 text-sm"
                          value={chainsSearchQuery || ""}
                          onChange={(e) => setChainsSearchQuery(e.target.value)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        {chainsSearchQuery && (
                          <button
                            className="absolute right-2 text-muted-foreground hover:text-foreground"
                            onClick={(e) => {
                              e.stopPropagation();
                              setChainsSearchQuery("");
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <line x1="18" y1="6" x2="6" y2="18"></line>
                              <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                          </button>
                        )}
                      </div>
                      <div className="flex justify-between mt-2 text-xs">
                        <span>
                          {selectedChains.length} selected
                        </span>
                        <div className="flex gap-2">
                          <button 
                            className="text-xs text-primary hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (chainsSearchQuery) {
                                // Sadece filtrelenmiş zincirleri seç
                                const filteredChainNames = chains
                                  .filter(chain => 
                                    chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                                  )
                                  .map(chain => chain.name);
                                
                                // Filtrelenmiş zincirleri mevcut seçimlere ekle
                                // Set kullanarak tekrar eden öğeleri kaldıralım
                                const uniqueNamesSet = new Set([...selectedChains, ...filteredChainNames]);
                                // Set'ten diziye dönüştürelim
                                const uniqueNames = Array.from(uniqueNamesSet);
                                setSelectedChains(uniqueNames);
                              } else {
                                // Tümünü seç
                                setSelectedChains(chains.map(chain => chain.name));
                              }
                            }}
                          >
                            {chainsSearchQuery ? "Select Filtered" : "Select All"}
                          </button>
                          <button 
                            className="text-xs text-primary hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (chainsSearchQuery) {
                                // Eğer bir arama sorgusu varsa, yalnızca filtrelenmiş zincirleri kaldır
                                const filteredChainNames = chains
                                  .filter(chain => 
                                    chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                                  )
                                  .map(chain => chain.name);
                                
                                setSelectedChains(selectedChains.filter(name => !filteredChainNames.includes(name)));
                              } else {
                                // Arama sorgusu yoksa, tüm seçimleri temizle
                                setSelectedChains([]);
                              }
                            }}
                          >
                            {chainsSearchQuery ? "Deselect Filtered" : "Deselect All"}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="p-1">
                      {chains.length > 0 ? (
                        chains
                          .filter(chain => 
                            !chainsSearchQuery ||
                            chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                          )
                          .map((chain, index) => (
                          <div
                            key={index}
                            className={`p-2 rounded-md cursor-pointer flex items-center gap-2 ${selectedChains.includes(chain.name) ? "bg-primary/20 text-primary-foreground" : "hover:bg-accent/50"}`}
                            onClick={() => {
                              if (selectedChains.includes(chain.name)) {
                                setSelectedChains(
                                  selectedChains.filter(
                                    (c) => c !== chain.name,
                                  ),
                                );
                              } else {
                                setSelectedChains([
                                  ...selectedChains,
                                  chain.name,
                                ]);
                              }
                            }}
                          >
                            <div
                              className={`w-4 h-4 rounded border ${selectedChains.includes(chain.name) ? "bg-primary border-primary" : "border-border"} flex items-center justify-center`}
                            >
                              {selectedChains.includes(chain.name) && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="10"
                                  height="10"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              )}
                            </div>
                            <span>{chain.name}</span>
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-muted-foreground">
                          {isLoading.chains ? t("filters.loading") : t("filters.noChainsFound")}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 pt-6">
          <Button
            variant="outline"
            onClick={handleResetFilters}
            className="flex-1 bg-background/50 hover:bg-background/80 transition-standard h-11 rounded-lg text-base"
          >
            {t("filters.resetFilters")}
          </Button>
          <Button
            onClick={handleApplyFilters}
            className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground transition-standard hover:scale-[1.02] active:scale-[0.98] h-11 rounded-lg text-base"
          >
            {t("filters.applyFilters")}
          </Button>
        </div>
      </Card>

      {/* Subscription Required Dialog */}
      <AlertDialog open={showSubscriptionDialog} onOpenChange={setShowSubscriptionDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-amber-500" />
              {t("system.subscription.filterRestriction.title", "system", "Abonelik Gerekli")}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground">
              {!isLoggedIn ? (
                t("filters.loginRequired")
              ) : (
                t("filters.subscriptionRequired")
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("filters.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setShowSubscriptionDialog(false);
                if (!isLoggedIn) {
                  setLocation("/login");
                } else {
                  setLocation("/pricing");
                }
              }}
              className="bg-primary hover:bg-primary/90"
            >
              {!isLoggedIn ? t("filters.login") : t("filters.upgradePlan")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </motion.div>
  );
}
