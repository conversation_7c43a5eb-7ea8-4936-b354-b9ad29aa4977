import React from 'react';
import { Link as LinkIcon, ExternalLink } from 'lucide-react';
import { FaGithub, FaReddit, FaFacebook, FaTwitter, FaLinkedin, FaFileAlt, FaGlobe, FaSearch, FaComment, FaMedium, FaYoutube, FaDiscord } from 'react-icons/fa';
import { useLanguage } from '@/contexts/LanguageContext';

interface LinkItem {
  type: string;
  url: string;
}

interface LinksCardProps {
  socialLinks: LinkItem[];
  otherLinks: LinkItem[];
  coinName: string;
  className?: string;
}

/**
 * Component for displaying external links for a coin
 * Handles missing data and provides fallbacks
 */
const LinksCard: React.FC<LinksCardProps> = ({ 
  socialLinks = [], 
  otherLinks = [],
  coinName,
  className = '' 
}) => {
  const { t } = useLanguage();
  
  // Check if there are any links available
  const hasLinks = (socialLinks?.length > 0 || otherLinks?.length > 0);
  
  // Helper function to sanitize and validate URLs
  const validateUrl = (url: string): boolean => {
    if (!url) return false;
    
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch (e) {
      return false;
    }
  };
  
  // Helper function to format URL for display
  const formatUrlForDisplay = (url: string): string => {
    if (!url) return '';
    
    try {
      const urlObj = new URL(url);
      let domain = urlObj.hostname;
      
      // Remove www. prefix if present
      if (domain.startsWith('www.')) {
        domain = domain.substring(4);
      }
      
      // Include the first path segment if it exists
      const pathSegments = urlObj.pathname.split('/').filter(Boolean);
      if (pathSegments.length > 0) {
        return `${domain}/${pathSegments[0]}`;
      }
      
      return domain;
    } catch (e) {
      // If URL parsing fails, return a shorter version
      const parts = url.split('//');
      if (parts.length > 1) {
        return parts[1].split('/')[0];
      }
      return url;
    }
  };
  
  // Function to get icon for link type
  const getLinkIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'github':
        return <FaGithub className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'reddit':
        return <FaReddit className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'facebook':
        return <FaFacebook className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'twitter':
        return <FaTwitter className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'linkedin':
        return <FaLinkedin className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'whitepaper':
        return <FaFileAlt className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'web':
        return <FaGlobe className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'explorer':
        return <FaSearch className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'messageboard':
        return <FaComment className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'medium':
        return <FaMedium className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'youtube':
        return <FaYoutube className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      case 'discord':
        return <FaDiscord className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
      default:
        return <LinkIcon className="h-[18px] w-[18px] text-blue-400 group-hover:text-blue-300 transition-colors" />;
    }
  };
  
  // Helper function to get link category title
  const getLinkCategoryTitle = (type: string): string => {
    switch (type.toLowerCase()) {
      case 'github':
        return 'GitHub';
      case 'reddit':
        return 'Reddit';
      case 'facebook':
        return 'Facebook';
      case 'twitter':
        return 'Twitter';
      case 'linkedin':
        return 'LinkedIn';
      case 'whitepaper':
        return 'Whitepaper';
      case 'web':
        return 'Website';
      case 'explorer':
        return 'Blockchain Explorer';
      case 'messageboard':
        return 'Message Board';
      default:
        return 'Other';
    }
  };
  
  // Group links by type
  const groupLinksByType = (links: LinkItem[]) => {
    const grouped: Record<string, LinkItem[]> = {};
    
    links.forEach(link => {
      if (!grouped[link.type]) {
        grouped[link.type] = [];
      }
      if (validateUrl(link.url)) {
        grouped[link.type].push(link);
      }
    });
    
    return grouped;
  };
  
  const socialLinksByType = groupLinksByType(socialLinks);
  const otherLinksByType = groupLinksByType(otherLinks);
  
  // Helper function to render links
  const renderLinks = (links: LinkItem[], type: string) => {
    if (!links || links.length === 0) return null;
    
    return (
      <div className="mb-4 last:mb-0">
        <h4 className="text-[0.95rem] font-medium text-slate-300 mb-2.5">{getLinkCategoryTitle(type)}</h4>
        <div className="flex flex-wrap gap-2.5">
          {links.map((link, index) => (
            <a 
              key={`${type}-${index}-${link.url}`}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-slate-800/80 hover:bg-slate-700/80 px-4 py-3 rounded-md flex items-center gap-3 transition-all duration-200 hover:shadow-md hover:scale-[1.02] group text-slate-300 hover:text-white"
              title={link.url}
            >
              {getLinkIcon(link.type)}
              <span className="text-[0.95rem] font-medium truncate">{formatUrlForDisplay(link.url)}</span>
            </a>
          ))}
        </div>
      </div>
    );
  };
  
  return (
    <div className={`bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-lg border border-slate-700/20 p-6 hover:border-blue-500/30 transition-all duration-200 group relative overflow-hidden ${className}`}>
      {/* Background visual elements for styling */}
      <div className="absolute inset-0 bg-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
      <div className="absolute top-0 right-0 w-20 h-20 bg-blue-600/10 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>
      
      {/* Header */}
      <div className="flex items-center gap-3 mb-5">
        <div className="bg-blue-500/10 p-2 rounded-md group-hover:bg-blue-500/20 transition-colors duration-200">
          <ExternalLink className="h-5 w-5 text-blue-400 group-hover:text-blue-300 transition-colors duration-200" />
        </div>
        <h3 className="text-lg font-semibold text-white group-hover:text-blue-100 transition-colors duration-200">
          {t('externalLinks.title')}
        </h3>
      </div>
      
      {/* Link categories */}
      {hasLinks ? (
        <div className="space-y-4">
          {/* All Links in a compact grid */}
          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-3">
            {/* Combine and flatten all links into one view */}
            {[...socialLinks, ...otherLinks]
              .filter(link => validateUrl(link.url))
              .map((link, index) => (
                <a 
                  key={`link-${index}-${link.url}`}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-slate-800/80 hover:bg-slate-700/80 p-3 rounded-md flex flex-col items-center gap-2 transition-all duration-200 hover:shadow-md hover:scale-[1.05] group"
                  title={`${getLinkCategoryTitle(link.type)}: ${link.url}`}
                >
                  <div className="text-blue-400 group-hover:text-blue-300 transition-colors">
                    {getLinkIcon(link.type)}
                  </div>
                  <span className="text-[0.7rem] font-medium text-slate-400 group-hover:text-slate-300 transition-colors">
                    {getLinkCategoryTitle(link.type)}
                  </span>
                </a>
              ))}
          </div>
        </div>
      ) : (
        // No links fallback
        <div className="bg-slate-800/50 rounded-md p-4 text-center border border-slate-700/10">
          <p className="text-slate-400 text-sm">No external links available for {coinName}</p>
        </div>
      )}
    </div>
  );
};

export default LinksCard;