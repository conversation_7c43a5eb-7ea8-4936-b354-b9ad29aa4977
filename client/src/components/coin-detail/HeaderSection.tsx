import React, { useState } from 'react';
import { <PERSON> } from "wouter";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft } from 'lucide-react';
import { ProgressCircle } from "@/components/ProgressCircle";
import { useLanguage } from '@/contexts/LanguageContext';

interface HeaderSectionProps {
  id: string;
  name: string;
  symbol: string;
  rank: number;
  price: number;
  priceChanges: {
    '24h': number;
    '7d': number;
    '30d': number;
    '90d': number;
    '6m': number;
    '1y': number;
    'all': number;
  };
  overallScore: number;
  onAdvancedViewChange?: (isAdvanced: boolean) => void;
}

const HeaderSection: React.FC<HeaderSectionProps> = ({
  id,
  name,
  symbol,
  rank = 1,
  price = 20.05,
  priceChanges = {
    '24h': -8.16,
    '7d': -9.88,
    '30d': -25.14,
    '90d': -62.1,
    '6m': -58.3,
    '1y': 5.72,
    'all': 824,
  },
  overallScore,
  onAdvancedViewChange,
}) => {
  const { t } = useLanguage();
  const [advancedView, setAdvancedView] = useState<boolean>(false);

  // Helper function to get color based on score
  const getProgressColor = (score: number) => {
    if (score >= 80) return '#10b981'; // green-500
    if (score >= 70) return '#3b82f6'; // blue-500
    if (score >= 50) return '#eab308'; // yellow-500
    return '#ef4444'; // red-500
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 70) return 'text-blue-500';
    if (score >= 50) return 'text-yellow-500';
    return 'text-red-500';
  };

  // Toggle advanced view
  const toggleAdvancedView = () => {
    const newState = !advancedView;
    setAdvancedView(newState);
    if (onAdvancedViewChange) {
      onAdvancedViewChange(newState);
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-8">
      {/* Left Card: Coin Info & Price Data */}
      <Card className="bg-slate-900/80 border border-slate-800 shadow-lg overflow-hidden">
        <div className="flex items-center p-4">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center shadow-lg mr-4">
            <span className="text-lg font-bold text-white">{symbol.charAt(0)}</span>
          </div>
          <div>
            <div className="flex items-center">
              <h1 className="text-xl font-bold mr-2">{name}</h1>
              <Badge variant="outline" className="border-slate-700 text-slate-300">
                {symbol}
              </Badge>
            </div>
            <div className="flex items-center mt-0.5">
              <Badge variant="secondary" className="bg-slate-800 text-slate-300 mr-2">
                Rank #{rank}
              </Badge>
              <span className="text-xl font-bold">${price.toFixed(2)}</span>
            </div>
          </div>
        </div>
        
        <div className="px-4 pb-4">
          <div className="grid grid-cols-7 gap-2 mt-1">
            <div className="flex flex-col items-center">
              <span className="text-xs text-slate-400">1 day</span>
              <span className={`text-sm font-medium ${priceChanges['24h'] >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {priceChanges['24h'] >= 0 ? '+' : ''}{priceChanges['24h']}%
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-xs text-slate-400">{t('priceChanges.1week')}</span>
              <span className={`text-sm font-medium ${priceChanges['7d'] >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {priceChanges['7d'] >= 0 ? '+' : ''}{priceChanges['7d']}%
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-xs text-slate-400">{t('priceChanges.1month')}</span>
              <span className={`text-sm font-medium ${priceChanges['30d'] >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {priceChanges['30d'] >= 0 ? '+' : ''}{priceChanges['30d']}%
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-xs text-slate-400">{t('priceChanges.3months')}</span>
              <span className={`text-sm font-medium ${priceChanges['90d'] >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {priceChanges['90d'] >= 0 ? '+' : ''}{priceChanges['90d']}%
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-xs text-slate-400">{t('priceChanges.6months')}</span>
              <span className={`text-sm font-medium ${(priceChanges['6m'] || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {(priceChanges['6m'] || 0) >= 0 ? '+' : ''}{priceChanges['6m'] || -58.3}%
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-xs text-slate-400">{t('priceChanges.1year')}</span>
              <span className={`text-sm font-medium ${priceChanges['1y'] >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {priceChanges['1y'] >= 0 ? '+' : ''}{priceChanges['1y']}%
              </span>
            </div>

          </div>
        </div>
      </Card>
      
      {/* Right Card: Actions and Controls */}
      <Card className="bg-slate-900/80 border border-slate-800 shadow-lg overflow-hidden">
        <div className="px-5 py-4 flex flex-wrap gap-3 justify-center">
          <Link to="/coinlist">
            <button className="bg-slate-800/90 hover:bg-slate-700 text-slate-200 px-4 py-2 rounded-md text-sm flex items-center transition-colors border border-slate-700 shadow-md hover:shadow-lg">
              <ChevronLeft className="h-4 w-4 mr-1.5" />
              <span className="font-medium">Back to CoinList</span>
            </button>
          </Link>
          
          <button className="bg-slate-800/90 hover:bg-slate-700 text-slate-200 px-4 py-2 rounded-md text-sm flex items-center transition-colors border border-slate-700 shadow-md hover:shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
            </svg>
            <span className="font-medium">Alert</span>
          </button>
          
          <button className="bg-slate-800/90 hover:bg-slate-700 text-slate-200 px-4 py-2 rounded-md text-sm flex items-center transition-colors border border-slate-700 shadow-md hover:shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/>
            </svg>
            <span className="font-medium">Watchlist</span>
          </button>
          
          <button className="bg-slate-800/90 hover:bg-slate-700 text-slate-200 px-4 py-2 rounded-md text-sm flex items-center transition-colors border border-slate-700 shadow-md hover:shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
              <polyline points="17 21 17 13 7 13 7 21"/>
              <polyline points="7 3 7 8 15 8"/>
            </svg>
            <span className="font-medium">Portfolio</span>
          </button>
          
          <button className="bg-slate-800/90 hover:bg-slate-700 text-slate-200 px-4 py-2 rounded-md text-sm flex items-center transition-colors border border-slate-700 shadow-md hover:shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14 2 14 8 20 8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10 9 9 9 8 9"/>
            </svg>
            <span className="font-medium">Download PDF</span>
          </button>
          
          <button className="bg-slate-800/90 hover:bg-slate-700 text-slate-200 px-4 py-2 rounded-md text-sm flex items-center transition-colors border border-slate-700 shadow-md hover:shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
              <circle cx="18" cy="5" r="3"/>
              <circle cx="6" cy="12" r="3"/>
              <circle cx="18" cy="19" r="3"/>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
            </svg>
            <span className="font-medium">Share</span>
          </button>
        </div>
        
        <div className="px-5 pb-5 flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold">Coin Health Analysis</h2>
            <p className="text-sm text-slate-400">Detailed metrics and insights</p>
          </div>

          <div className="flex items-center gap-6">
            <div className="flex items-center">
              <span className="text-sm text-slate-300 font-medium mr-2">Advanced View</span>
              <div 
                className={`w-12 h-6 rounded-full ${advancedView ? 'bg-blue-700' : 'bg-slate-700'} flex items-center px-0.5 cursor-pointer transition-colors`}
                onClick={toggleAdvancedView}
              >
                <div className={`w-5 h-5 rounded-full ${advancedView ? 'bg-blue-300 translate-x-6' : 'bg-slate-400'} transform transition-all duration-200`}></div>
              </div>
            </div>
            
            <div className="relative">
              <div className="w-[75px] h-[75px]">
                <ProgressCircle 
                  value={overallScore} 
                  size={75} 
                  strokeWidth={6.4}
                  progressColor={getProgressColor(overallScore)}
                  textClass={`${getScoreColor(overallScore)} text-xl font-bold`}
                />
              </div>
              <div className="absolute -bottom-1 left-0 right-0 text-center">
                <span className="text-xs font-medium text-slate-400">OVERALL SCORE</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default HeaderSection;