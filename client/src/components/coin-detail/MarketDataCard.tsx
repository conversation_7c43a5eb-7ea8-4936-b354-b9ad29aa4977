import React from 'react';
import { motion } from 'framer-motion';
import { formatCurrencyValue } from '@/lib/coinDetailModel';
import { Building2, Bar<PERSON>hart4, LineChart } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Sparkline data for the price chart (this would come from API in production)
const mockSparklineData = [
  41235, 42546, 43100, 41823, 42901, 43925, 44271, 43988, 44350, 45102,
  44687, 45721, 46352, 45891, 46782, 47153, 48325, 47902, 48734, 49512
];

interface MarketDataCardProps {
  marketCap: number;
  volume24h: number;
  fullyDilutedVal?: number;
  priceChanges: {
    '24h': number;
    '7d': number;
    '30d': number;
    '90d': number;
    '1y': number;
  };
  currentPrice?: number;
  className?: string;
}

/**
 * A card component that displays market data including price changes
 */
export const MarketDataCard: React.FC<MarketDataCardProps> = ({
  marketCap,
  volume24h,
  fullyDilutedVal,
  priceChanges,
  currentPrice,
  className = ''
}) => {
  const { t } = useLanguage();
  // Helper to format percentages with +/- sign
  const formatPercentChange = (change: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  };
  
  // Get the appropriate color for a percentage change
  const getChangeColor = (change: number) => {
    return change >= 0 ? 'text-green-500' : 'text-red-500';
  };
  
  // Calculate the volume to market cap ratio (indicator of liquidity)
  const volumeToMcapRatio = (volume24h / marketCap * 100).toFixed(2);
  
  return (
    <Card className={`bg-slate-800/70 backdrop-blur-sm border-slate-700 ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <BarChart4 className="h-5 w-5 text-blue-400" />
          <CardTitle className="text-lg text-slate-200">Market Data</CardTitle>
        </div>
        <CardDescription className="text-slate-400">
          Market performance metrics
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {/* Market statistics */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-2">
          <div className="space-y-1">
            <p className="text-xs text-slate-400">{t('marketData.marketCap')}</p>
            <p className="text-sm font-semibold text-slate-200">{formatCurrencyValue(marketCap)}</p>
          </div>
          
          <div className="space-y-1">
            <p className="text-xs text-slate-400">{t('marketData.tradedVolume24')}</p>
            <p className="text-sm font-semibold text-slate-200">{formatCurrencyValue(volume24h)}</p>
          </div>
          
          <div className="space-y-1">
            <p className="text-xs text-slate-400">Volume/MCap</p>
            <p className="text-sm font-semibold text-slate-200">{volumeToMcapRatio}%</p>
          </div>
          
          {fullyDilutedVal && (
            <div className="space-y-1">
              <p className="text-xs text-slate-400">{t('marketData.fullyDiluted')}</p>
              <p className="text-sm font-semibold text-slate-200">{formatCurrencyValue(fullyDilutedVal)}</p>
            </div>
          )}
        </div>
        
        {/* Price change percentages */}
        <div className="mt-6">
          <p className="text-xs text-slate-400 mb-3">{t('marketData.priceChange')}</p>
          
          <div className="grid grid-cols-5 gap-2">
            {Object.entries(priceChanges).map(([period, change]) => (
              <motion.div 
                key={period}
                className="text-center py-2 px-1 rounded-lg bg-slate-700/40"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: parseInt(period) * 0.05 }}
              >
                <p className="text-[10px] text-slate-400 uppercase mb-1">{period}</p>
                <p className={`text-xs font-medium ${getChangeColor(change)}`}>
                  {formatPercentChange(change)}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Simple price trend chart */}
        <div className="mt-6">
          <p className="text-xs text-slate-400 mb-2">{t('marketData.priceMovement')} (7d)</p>
          <div className="h-12 w-full bg-slate-700/30 rounded-lg overflow-hidden">
            {/* Create a simple svg line representing price trends */}
            <svg 
              width="100%" 
              height="100%" 
              viewBox="0 0 100 30" 
              preserveAspectRatio="none"
              className="translate-y-[5px]"
            >
              <path
                d={`M0,15 ${mockSparklineData.map((d, i) => {
                  // Map data points to SVG path coordinates
                  const x = (i / (mockSparklineData.length - 1)) * 100;
                  const normalizedValue = ((d - Math.min(...mockSparklineData)) / 
                    (Math.max(...mockSparklineData) - Math.min(...mockSparklineData))) * 20;
                  const y = 20 - normalizedValue;
                  return `L${x},${y}`;
                }).join(' ')}`}
                fill="none"
                stroke="#38bdf8"
                strokeWidth="1.5"
              />
            </svg>
          </div>
          
          {currentPrice && (
            <div className="mt-2 text-right">
              <p className="text-sm font-semibold text-slate-200">
                {formatCurrencyValue(currentPrice)}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default MarketDataCard;