import React from 'react';
import { HeartPulse } from 'lucide-react';
import ScoreGauge from '@/components/coin-detail/ScoreGaugeNew';
import { useLanguage } from '@/contexts/LanguageContext';

interface CoinHealthScoreProps {
  totalScore: number;
}

/**
 * Kripto para sağlık puanını gösteren bileşen
 */
export const CoinHealthScore: React.FC<CoinHealthScoreProps> = ({ totalScore }) => {
  const { t } = useLanguage();
  
  return (
    <div className="bg-slate-800/70 rounded-xl min-w-[30%] p-5 min-h-[100%] border border-slate-700/40 shadow-md shadow-slate-900/40 flex flex-col justify-between transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden">
      <div className="flex justify items-center">
        <div className="bg-blue-500/10 p-1 rounded-md group-hover:bg-blue-500/20 transition-all duration-300 flex items-center justify-center">
          <HeartPulse className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
        </div>
        <span className="text-md ml-3 font-medium text-slate-300">
          {t('coinHealth.title')}
        </span>
      </div>
      <div className="flex items-center justify-between relative z-10">
        <div className="flex items-center justify-center">
          <div className="bg-slate-800/90 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 p-2.5 transition-all duration-300 hover:border-blue-500/30">
            <div
              className={`rounded-xl py-1.5 px-4 flex items-center whitespace-nowrap ${
                totalScore >= 85
                  ? "bg-emerald-500/20 text-emerald-400 border border-emerald-500/20"
                  : totalScore >= 70
                    ? "bg-emerald-500/20 text-emerald-400 border border-emerald-500/20"
                    : totalScore >= 50
                      ? "bg-amber-500/20 text-amber-400 border border-amber-500/20"
                      : "bg-red-500/20 text-red-400 border border-red-500/20"
              }`}
            >
              <HeartPulse className="h-4 w-4 mr-2" />
              <span className="text-sm font-medium">
                {totalScore >= 85
                  ? t('coinHealth.excellent')
                  : totalScore >= 70
                    ? t('coinHealth.positive')
                    : totalScore >= 50
                      ? t('coinHealth.average')
                      : t('coinHealth.critical')}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center">
          <ScoreGauge
            className="shadow-md shadow-slate-900/40 rounded-xl transition-all duration-300"
            score={totalScore}
            id="ido-health"
            showLabel={false}
            animated={false}
          />
        </div>
      </div>

      {/* Score Range Indicator */}
      <div className="mt-4 w-full bg-slate-700/30 h-1.5 rounded-full overflow-hidden">
        <div className="flex w-full h-full">
          <div className="bg-red-500 h-full" style={{ width: "20%" }}></div>
          <div className="bg-amber-500 h-full" style={{ width: "30%" }}></div>
          <div className="bg-emerald-500 h-full" style={{ width: "50%" }}></div>
        </div>
        <div
          className="relative bg-white h-3 w-3 rounded-full -mt-2 shadow-md shadow-slate-900/40"
          style={{
            left: `calc(${totalScore}% - 6px)`,
            background: `rgb(${
              totalScore >= 85
                ? "34, 197, 94"
                : totalScore >= 70
                ? "34, 197, 94"
                : totalScore >= 50
                ? "245, 158, 11"
                : "239, 68, 68"
            })`,
          }}
        ></div>
      </div>

      {/* Score Labels */}
      <div className="mt-2 flex justify-between w-full text-xs text-slate-400">
        <span>{t('coinHealth.critical')}</span>
        <span>{t('coinHealth.average')}</span>
        <span>{t('coinHealth.excellent')}</span>
      </div>
    </div>
  );
};

export default CoinHealthScore;