import React from 'react';
import { CalculationData, CalculationDataSingleValue, CalculationDataM36, CalculationDataM38, CalculationDataM40, CalculationDataM41, CalculationDataM42, CalculationDataM50, CalculationDataM52 } from '../../types/coinDetail';

interface CalculationDataDisplayProps {
  calculationData: CalculationData;
  coinSymbol?: string;
}

const CalculationDataDisplay: React.FC<CalculationDataDisplayProps> = ({ calculationData, coinSymbol = "TOKEN" }) => {
  // Determine which calculation type is present
  const getCalculationEntry = () => {
    // Single value metrics
    if (calculationData.m1) return { type: 'm1', data: calculationData.m1 };
    if (calculationData.m2) return { type: 'm2', data: calculationData.m2 };
    if (calculationData.m3) return { type: 'm3', data: calculationData.m3 };
    if (calculationData.m4) return { type: 'm4', data: calculationData.m4 };
    if (calculationData.m5) return { type: 'm5', data: calculationData.m5 };
    if (calculationData.m6) return { type: 'm6', data: calculationData.m6 };
    if (calculationData.m7) return { type: 'm7', data: calculationData.m7 };
    if (calculationData.m8) return { type: 'm8', data: calculationData.m8 };
    if (calculationData.m9) return { type: 'm9', data: calculationData.m9 };
    if (calculationData.m11) return { type: 'm11', data: calculationData.m11 };
    if (calculationData.m12) return { type: 'm12', data: calculationData.m12 };
    if (calculationData.m14) return { type: 'm14', data: calculationData.m14 };
    if (calculationData.m17) return { type: 'm17', data: calculationData.m17 };
    if (calculationData.m18) return { type: 'm18', data: calculationData.m18 };
    if (calculationData.m19) return { type: 'm19', data: calculationData.m19 };
    if (calculationData.m20) return { type: 'm20', data: calculationData.m20 };
    if (calculationData.m23) return { type: 'm23', data: calculationData.m23 };
    if (calculationData.m24) return { type: 'm24', data: calculationData.m24 };
    if (calculationData.m27) return { type: 'm27', data: calculationData.m27 };
    if (calculationData.m28) return { type: 'm28', data: calculationData.m28 };
    if (calculationData.m29) return { type: 'm29', data: calculationData.m29 };
    if (calculationData.m30) return { type: 'm30', data: calculationData.m30 };
    
    // Complex data metrics
    if (calculationData.m36) return { type: 'm36', data: calculationData.m36 };
    if (calculationData.m38) return { type: 'm38', data: calculationData.m38 };
    if (calculationData.m40) return { type: 'm40', data: calculationData.m40 };
    if (calculationData.m41) return { type: 'm41', data: calculationData.m41 };
    if (calculationData.m42) return { type: 'm42', data: calculationData.m42 };
    if (calculationData.m50) return { type: 'm50', data: calculationData.m50 };
    if (calculationData.m52) return { type: 'm52', data: calculationData.m52 };
    return null;
  };

  const calculationEntry = getCalculationEntry();
  
  if (!calculationEntry) {
    return null;
  }

  const { type, data } = calculationEntry;

  // Format large numbers with commas
  const formatNumber = (value: string | number): string => {
    if (!value || value === 'null' || value === null) return 'N/A';
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    if (isNaN(numValue)) return 'N/A';
    return numValue.toLocaleString();
  };

  // Format single value with proper display
  const formatSingleValue = (value: string): string => {
    if (!value || value === 'null' || value === null) return 'N/A';
    
    // Try to parse as number for formatting
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      // If it's a whole number, don't show decimals
      if (numValue === Math.floor(numValue)) {
        return numValue.toLocaleString();
      }
      // If it's a decimal, show up to 2 decimal places
      return numValue.toLocaleString(undefined, { maximumFractionDigits: 2 });
    }
    
    // Return as string if not a number
    return value;
  };

  // Get metric display name based on metric ID
  const getMetricDisplayName = (metricId: string): string => {
    const metricNames: Record<string, string> = {
      'm1': 'Market Cap / FDV Ratio',
      'm2': 'Max Supply',
      'm3': 'Code Security',
      'm4': 'Community Trust',
      'm5': 'Total Volume',
      'm6': 'Best CEX Rank',
      'm7': 'Best DEX Rank',
      'm8': 'CEX Count',
      'm9': 'DEX Count',
      'm11': 'Vesting Schedule',
      'm12': 'Emission Score (1Y)',
      'm14': 'Risk/Reward Rating',
      'm17': 'Fundamental Health',
      'm18': 'Governance Strength',
      'm19': 'Market Stability',
      'm20': 'Operational Resilience',
      'm23': 'Risk/Reward Rating',
      'm24': 'Alt Rank',
      'm27': 'Sentiment',
      'm28': 'Gecko Portfolio Count',
      'm29': 'Social Volume (24h)',
      'm30': 'Social Dominance'
    };
    return metricNames[metricId] || 'Metric Value';
  };

  // Render single value data
  const renderSingleValueData = (metricId: string, singleData: CalculationDataSingleValue) => {
    const metricName = getMetricDisplayName(metricId);
    
    // Get the key-value pair from the single data object
    const entries = Object.entries(singleData);
    if (entries.length === 0) return null;
    
    const [key, value] = entries[0]; // Take the first (and should be only) entry
    
    return (
      <div className="space-y-4">
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-1">{metricName}</div>
          <div className="text-lg font-semibold text-slate-200">{formatSingleValue(value)}</div>
        </div>
      </div>
    );
  };



  // Render M36 type (Token Use Case)
  const renderM36Data = (m36Data: CalculationDataM36) => {
    const { token_use_case } = m36Data;
    
    return (
      <div className="space-y-4">
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Summary</div>
          <div className="text-sm text-slate-300 mb-4">{token_use_case.summary}</div>
          
          <div className="text-sm text-slate-400 mb-2">
            Total Use Cases: {token_use_case.total_use_cases}
          </div>
        </div>

        <div className="space-y-3">
          {token_use_case.use_cases.map((useCase, index) => (
            <div key={index} className="bg-slate-700/30 rounded-lg p-4">
              <div className="text-sm font-medium text-slate-200 mb-2">{useCase.type}</div>
              <div className="text-xs text-slate-300 mb-2">{useCase.description}</div>
              {useCase.analysis && (
                <div className="text-xs text-slate-400">{useCase.analysis}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render M38 type (Deflationary/Inflationary)
  const renderM38Data = (m38Data: CalculationDataM38) => {
    const { def_or_inf } = m38Data;
    
    return (
      <div className="space-y-4">
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Overall Trend</div>
          <div className="text-lg font-semibold text-slate-200 mb-4">{def_or_inf.overall_trend}</div>
          
          <div className="text-sm text-slate-400 mb-2">Summary</div>
          <div className="text-sm text-slate-300 mb-4">{def_or_inf.summary}</div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="text-slate-400">Total Mechanisms</div>
              <div className="text-slate-200 font-medium">{def_or_inf.total_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Burn Mechanisms</div>
              <div className="text-slate-200 font-medium">{def_or_inf.burn_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Buyback Mechanisms</div>
              <div className="text-slate-200 font-medium">{def_or_inf.buyback_mechanisms}</div>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {def_or_inf.mechanisms.map((mechanism, index) => (
            <div key={index} className="bg-slate-700/30 rounded-lg p-4">
              <div className="text-sm font-medium text-slate-200 mb-2">{mechanism.type}</div>
              <div className="text-xs text-slate-300 mb-2">{mechanism.description}</div>
              {mechanism.analysis && (
                <div className="text-xs text-slate-400">{mechanism.analysis}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render M40 type (Token Redistribution)
  const renderM40Data = (m40Data: CalculationDataM40) => {
    const { token_redist_score } = m40Data;
    
    return (
      <div className="space-y-4">
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Redistribution Score</div>
          <div className="text-lg font-semibold text-slate-200 mb-4">{token_redist_score.redistribution_score}</div>
          
          <div className="text-sm text-slate-400 mb-2">Summary</div>
          <div className="text-sm text-slate-300 mb-4">{token_redist_score.summary}</div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-slate-400">Total Mechanisms</div>
              <div className="text-slate-200 font-medium">{token_redist_score.total_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Staking</div>
              <div className="text-slate-200 font-medium">{token_redist_score.staking_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Fee Sharing</div>
              <div className="text-slate-200 font-medium">{token_redist_score.fee_sharing_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Rewards</div>
              <div className="text-slate-200 font-medium">{token_redist_score.reward_mechanisms}</div>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {token_redist_score.redistribution_mechanisms.map((mechanism, index) => (
            <div key={index} className="bg-slate-700/30 rounded-lg p-4">
              <div className="text-sm font-medium text-slate-200 mb-2">{mechanism.type}</div>
              <div className="text-xs text-slate-300 mb-2">{mechanism.description}</div>
              {mechanism.analysis && (
                <div className="text-xs text-slate-400">{mechanism.analysis}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render M41 type (Buyback Mechanism)
  const renderM41Data = (m41Data: CalculationDataM41) => {
    const { token_buyback } = m41Data;
    
    return (
      <div className="space-y-4">
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Buyback Score</div>
          <div className="text-lg font-semibold text-slate-200 mb-4">{token_buyback.buyback_score}</div>
          
          <div className="text-sm text-slate-400 mb-2">Summary</div>
          <div className="text-sm text-slate-300 mb-4">{token_buyback.summary}</div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-slate-400">Total Mechanisms</div>
              <div className="text-slate-200 font-medium">{token_buyback.total_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Revenue Buybacks</div>
              <div className="text-slate-200 font-medium">{token_buyback.revenue_buybacks}</div>
            </div>
            <div>
              <div className="text-slate-400">Burn Buybacks</div>
              <div className="text-slate-200 font-medium">{token_buyback.burn_buybacks}</div>
            </div>
            <div>
              <div className="text-slate-400">Has Burn Component</div>
              <div className="text-slate-200 font-medium">{token_buyback.has_burn_component ? 'Yes' : 'No'}</div>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {token_buyback.buyback_mechanisms.map((mechanism, index) => (
            <div key={index} className="bg-slate-700/30 rounded-lg p-4">
              <div className="text-sm font-medium text-slate-200 mb-2">{mechanism.type}</div>
              <div className="text-xs text-slate-300 mb-2">{mechanism.description}</div>
              {mechanism.analysis && (
                <div className="text-xs text-slate-400">{mechanism.analysis}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render M42 type (Revenue Sharing)
  const renderM42Data = (m42Data: CalculationDataM42) => {
    const { revenue_sharing } = m42Data;
    
    return (
      <div className="space-y-4">
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Revenue Sharing Score</div>
          <div className="text-lg font-semibold text-slate-200 mb-4">{revenue_sharing.revenue_sharing_score}</div>
          
          <div className="text-sm text-slate-400 mb-2">Summary</div>
          <div className="text-sm text-slate-300 mb-4">{revenue_sharing.summary}</div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-slate-400">Total Mechanisms</div>
              <div className="text-slate-200 font-medium">{revenue_sharing.total_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Positive</div>
              <div className="text-slate-200 font-medium">{revenue_sharing.positive_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Fee Distribution</div>
              <div className="text-slate-200 font-medium">{revenue_sharing.fee_distribution_count}</div>
            </div>
            <div>
              <div className="text-slate-400">Passive Income</div>
              <div className="text-slate-200 font-medium">{revenue_sharing.passive_income_count}</div>
            </div>
          </div>
        </div>

        {/* Revenue Sharing Features */}
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-3">Revenue Sharing Features</div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className={`p-3 rounded-lg border ${revenue_sharing.has_profit_sharing ? 'bg-emerald-500/20 border-emerald-500/30' : 'bg-slate-600/30 border-slate-500/30'}`}>
              <div className="text-xs font-medium mb-1">
                <span className={revenue_sharing.has_profit_sharing ? 'text-emerald-400' : 'text-slate-400'}>
                  Profit Sharing
                </span>
              </div>
              <div className={`text-xs ${revenue_sharing.has_profit_sharing ? 'text-emerald-300' : 'text-slate-500'}`}>
                {revenue_sharing.has_profit_sharing ? 'Yes' : 'No'}
              </div>
            </div>

            <div className={`p-3 rounded-lg border ${revenue_sharing.has_fee_distribution ? 'bg-blue-500/20 border-blue-500/30' : 'bg-slate-600/30 border-slate-500/30'}`}>
              <div className="text-xs font-medium mb-1">
                <span className={revenue_sharing.has_fee_distribution ? 'text-blue-400' : 'text-slate-400'}>
                  Fee Distribution
                </span>
              </div>
              <div className={`text-xs ${revenue_sharing.has_fee_distribution ? 'text-blue-300' : 'text-slate-500'}`}>
                {revenue_sharing.has_fee_distribution ? 'Yes' : 'No'}
              </div>
            </div>

            <div className={`p-3 rounded-lg border ${revenue_sharing.has_passive_income ? 'bg-purple-500/20 border-purple-500/30' : 'bg-slate-600/30 border-slate-500/30'}`}>
              <div className="text-xs font-medium mb-1">
                <span className={revenue_sharing.has_passive_income ? 'text-purple-400' : 'text-slate-400'}>
                  Passive Income
                </span>
              </div>
              <div className={`text-xs ${revenue_sharing.has_passive_income ? 'text-purple-300' : 'text-slate-500'}`}>
                {revenue_sharing.has_passive_income ? 'Yes' : 'No'}
              </div>
            </div>

            <div className={`p-3 rounded-lg border ${!revenue_sharing.requires_staking ? 'bg-emerald-500/20 border-emerald-500/30' : 'bg-orange-500/20 border-orange-500/30'}`}>
              <div className="text-xs font-medium mb-1">
                <span className={!revenue_sharing.requires_staking ? 'text-emerald-400' : 'text-orange-400'}>
                  Staking Required
                </span>
              </div>
              <div className={`text-xs ${!revenue_sharing.requires_staking ? 'text-emerald-300' : 'text-orange-300'}`}>
                {revenue_sharing.requires_staking ? 'Yes' : 'No'}
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {revenue_sharing.revenue_sharing_mechanisms.map((mechanism, index) => (
            <div key={index} className="bg-slate-700/30 rounded-lg p-4">
              <div className="text-sm font-medium text-slate-200 mb-2">{mechanism.type}</div>
              <div className="text-xs text-slate-300 mb-2">{mechanism.description}</div>
              {mechanism.analysis && (
                <div className="text-xs text-slate-400">{mechanism.analysis}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render M50 type (Team Anonymous or Public)
  const renderM50Data = (m50Data: CalculationDataM50) => {
    const { team_anonymity } = m50Data;
    
    return (
      <div className="space-y-4">
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Team Status</div>
          <div className="text-lg font-semibold text-slate-200 mb-4">{team_anonymity.status}</div>
          
          <div className="text-sm text-slate-400 mb-2">
            Total Team Members: {team_anonymity.total_members}
          </div>
        </div>

        {team_anonymity.team_members && team_anonymity.team_members.length > 0 && (
          <div className="space-y-3">
            {team_anonymity.team_members.map((member, index) => (
              <div key={index} className="bg-slate-700/30 rounded-lg p-4">
                <div className="text-sm font-medium text-slate-200">{member.name}</div>
                <div className="text-xs text-slate-400">{member.title}</div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render M52 type (DAO Governance)
  const renderM52Data = (m52Data: CalculationDataM52) => {
    const { dao_governance } = m52Data;
    
    return (
      <div className="space-y-4">
        <div className="bg-slate-700/30 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Governance Score</div>
          <div className="text-lg font-semibold text-slate-200 mb-4">{dao_governance.governance_score}</div>
          
          <div className="text-sm text-slate-400 mb-2">Summary</div>
          <div className="text-sm text-slate-300 mb-4">{dao_governance.summary}</div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-slate-400">Total Mechanisms</div>
              <div className="text-slate-200 font-medium">{dao_governance.total_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Positive</div>
              <div className="text-slate-200 font-medium">{dao_governance.positive_mechanisms}</div>
            </div>
            <div>
              <div className="text-slate-400">Token Voting</div>
              <div className="text-slate-200 font-medium">{dao_governance.has_token_voting ? 'Yes' : 'No'}</div>
            </div>
            <div>
              <div className="text-slate-400">Decentralized</div>
              <div className="text-slate-200 font-medium">{dao_governance.is_decentralized ? 'Yes' : 'No'}</div>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {dao_governance.governance_mechanisms.map((mechanism, index) => (
            <div key={index} className="bg-slate-700/30 rounded-lg p-4">
              <div className="text-sm font-medium text-slate-200 mb-2">{mechanism.type}</div>
              <div className="text-xs text-slate-300 mb-2">{mechanism.description}</div>
              {mechanism.analysis && (
                <div className="text-xs text-slate-400">{mechanism.analysis}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render generic data for other types
  const renderGenericData = (data: any) => {
    return (
      <div className="bg-slate-700/30 rounded-lg p-4">
        <pre className="text-xs text-slate-300 whitespace-pre-wrap">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    );
  };

  // Get dynamic title based on calculation type
  const getTitle = () => {
    switch (type) {
      // Single value metrics
      case 'm1':
      case 'm2':
      case 'm3':
      case 'm4':
      case 'm5':
      case 'm6':
      case 'm7':
      case 'm8':
      case 'm9':
      case 'm11':
      case 'm12':
      case 'm14':
      case 'm17':
      case 'm18':
      case 'm19':
      case 'm20':
      case 'm23':
      case 'm24':
      case 'm27':
      case 'm28':
      case 'm29':
      case 'm30':
        return getMetricDisplayName(type);
      // Complex data metrics
      case 'm36':
        return 'Token Use Case';
      case 'm38':
        return 'Deflationary or Inflationary Token';
      case 'm40':
        return 'Token Redistribution';
      case 'm41':
        return 'Buyback Mechanism';
      case 'm42':
        return 'Revenue Sharing';
      case 'm50':
        return 'Team Anonymous or Public';
      case 'm52':
        return 'DAO Governance';
      default:
        return 'Calculation Data';
    }
  };

  // Get dynamic icon based on calculation type
  const getIcon = () => {
    switch (type) {
      case 'm1':
        return '💹';
      case 'm2':
        return '📊';
      case 'm3':
        return '🔐';
      case 'm4':
        return '🤝';
      case 'm5':
        return '📈';
      case 'm6':
      case 'm7':
        return '🏆';
      case 'm8':
      case 'm9':
        return '🔢';
      case 'm11':
        return '🔒';
      case 'm12':
        return '⚡';
      case 'm14':
      case 'm23':
        return '⚖️';
      case 'm17':
        return '💪';
      case 'm18':
        return '🏛️';
      case 'm19':
        return '📊';
      case 'm20':
        return '🛡️';
      case 'm24':
        return '🥇';
      case 'm27':
        return '😊';
      case 'm28':
        return '👥';
      case 'm29':
      case 'm30':
        return '📱';
      case 'm36':
        return '🎯';
      case 'm38':
        return '🔥';
      case 'm40':
        return '🔄';
      case 'm41':
        return '💰';
      case 'm42':
        return '💸';
      case 'm50':
        return '👥';
      case 'm52':
        return '🏛️';
      default:
        return '📈';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-lg font-semibold text-slate-200">
        <span className="mr-2">{getIcon()}</span>
        {getTitle()}
      </div>
      
      <div className="bg-slate-800/80 rounded-lg p-6 border border-slate-700/40">
        {/* Single value metrics */}
        {(type === 'm1' || type === 'm2' || type === 'm3' || type === 'm4' || type === 'm5' || 
          type === 'm6' || type === 'm7' || type === 'm8' || type === 'm9' || type === 'm11' || 
          type === 'm12' || type === 'm14' || type === 'm17' || type === 'm18' || type === 'm19' || 
          type === 'm20' || type === 'm23' || type === 'm24' || type === 'm27' || type === 'm28' || 
          type === 'm29' || type === 'm30') && renderSingleValueData(type, data as CalculationDataSingleValue)}
        
        {/* Complex data metrics */}
        {type === 'm36' && renderM36Data(data as CalculationDataM36)}
        {type === 'm38' && renderM38Data(data as CalculationDataM38)}
        {type === 'm40' && renderM40Data(data as CalculationDataM40)}
        {type === 'm41' && renderM41Data(data as CalculationDataM41)}
        {type === 'm42' && renderM42Data(data as CalculationDataM42)}
        {type === 'm50' && renderM50Data(data as CalculationDataM50)}
        {type === 'm52' && renderM52Data(data as CalculationDataM52)}
      </div>
    </div>
  );
};

export default CalculationDataDisplay;