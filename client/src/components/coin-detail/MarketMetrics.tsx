import React from 'react';
import {
  <PERSON><PERSON><PERSON>3,
  Bar<PERSON>hart4,
  DollarSign,
  Building2,
  Clock,
  Database,
  Layers
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useLanguage } from '@/contexts/LanguageContext';

interface MarketMetricsProps {
  marketCap: number | string;
  volume24h: number | string;
  circulatingSupply: number | string;
  totalSupply: number | string;
  maxSupply: number | string;
  formatCurrencyValue: (value: number | string, options?: { currency?: string; compact?: boolean; showZero?: boolean }) => string;
}

const MarketMetrics: React.FC<MarketMetricsProps> = ({
  marketCap,
  volume24h,
  circulatingSupply,
  totalSupply,
  maxSupply,
  formatCurrencyValue,
}) => {
  const { t } = useLanguage();
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
      {/* Market Cap Card */}
      <div
        className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg py-3 px-4 shadow-md flex flex-col border border-slate-700/20 hover:border-green-500/20 hover:bg-slate-800/80 transition-all duration-300 group relative"
        aria-label="Market capitalization"
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-1.5">
            <div className="bg-green-500/10 p-1.5 rounded-md group-hover:bg-green-500/20 transition-all duration-300 flex items-center justify-center">
              <BarChart3 className="h-4 w-4 text-green-400 group-hover:text-green-300 transition-colors duration-300" />
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-sm font-medium text-slate-400 group-hover:text-green-400 transition-colors duration-300">
                    Market Cap
                  </span>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                >
                  <p className="font-medium text-green-400 mb-1">
                    Market Capitalization
                  </p>
                  <p>
                    Total value of all circulating tokens calculated as
                    current price × circulating supply.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className="mt-2">
          <div className="flex flex-wrap items-baseline gap-2">
            <span className="text-xl font-bold text-white group-hover:text-green-50 transition-colors duration-300">
              {formatCurrencyValue(marketCap, { currency: "$", compact: true })}
            </span>
          </div>
        </div>
      </div>

      {/* Trading Volume Card */}
      <div
        className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg py-3 px-4 shadow-md flex flex-col border border-slate-700/20 hover:border-purple-500/20 hover:bg-slate-800/80 transition-all duration-300 group relative"
        aria-label="24-hour trading volume"
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-1.5">
            <div className="bg-purple-500/10 p-1.5 rounded-md group-hover:bg-purple-500/20 transition-all duration-300 flex items-center justify-center">
              <BarChart4 className="h-4 w-4 text-purple-400 group-hover:text-purple-300 transition-colors duration-300" />
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-sm font-medium text-slate-400 group-hover:text-purple-400 transition-colors duration-300">
                    {t('marketData.tradedVolume24')}
                  </span>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                >
                  <p className="font-medium text-purple-400 mb-1">
                    24h Trading Volume
                  </p>
                  <p>
                    Total trading volume across all markets in the last 24
                    hours.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className="mt-2">
          <div className="flex flex-wrap items-baseline gap-2">
            <span className="text-xl font-bold text-white group-hover:text-purple-50 transition-colors duration-300">
              {formatCurrencyValue(volume24h, {
                currency: "$",
                compact: true,
              })}
            </span>
          </div>
        </div>
      </div>

      {/* Circulating Supply Card */}
      <div
        className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg py-3 px-4 shadow-md flex flex-col border border-slate-700/20 hover:border-amber-500/20 hover:bg-slate-800/80 transition-all duration-300 group relative"
        aria-label="Circulating supply"
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-1.5">
            <div className="bg-amber-500/10 p-1.5 rounded-md group-hover:bg-amber-500/20 transition-all duration-300 flex items-center justify-center">
              <Database className="h-4 w-4 text-amber-400 group-hover:text-amber-300 transition-colors duration-300" />
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-sm font-medium text-slate-400 group-hover:text-amber-400 transition-colors duration-300">
                    Circulating Supply
                  </span>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                >
                  <p className="font-medium text-amber-400 mb-1">
                    Circulating Supply
                  </p>
                  <p>
                    Number of tokens currently available and circulating in
                    the market.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className="mt-2">
          <div className="flex flex-wrap items-baseline gap-2">
            <span className="text-xl font-bold text-white group-hover:text-amber-50 transition-colors duration-300">
              {formatCurrencyValue(circulatingSupply, {
                currency: "",
                compact: true,
              })}
            </span>
          </div>
        </div>
      </div>

      {/* Total Supply Card */}
      <div
        className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg py-3 px-4 shadow-md flex flex-col border border-slate-700/20 hover:border-blue-500/20 hover:bg-slate-800/80 transition-all duration-300 group relative"
        aria-label="Total supply"
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-1.5">
            <div className="bg-blue-500/10 p-1.5 rounded-md group-hover:bg-blue-500/20 transition-all duration-300 flex items-center justify-center">
              <Layers className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-sm font-medium text-slate-400 group-hover:text-blue-400 transition-colors duration-300">
                    Total Supply
                  </span>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                >
                  <p className="font-medium text-blue-400 mb-1">
                    Total Supply
                  </p>
                  <p>
                    Total amount of tokens created, minus any that have been
                    verifiably burned.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className="mt-2">
          <div className="flex flex-wrap items-baseline gap-2">
            <span className="text-xl font-bold text-white group-hover:text-blue-50 transition-colors duration-300">
              {formatCurrencyValue(totalSupply, {
                currency: "",
                compact: true,
              })}
            </span>
          </div>
        </div>
      </div>

      {/* Max Supply Card */}
      {maxSupply && maxSupply !== "0" && (
        <div
          className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg py-3 px-4 shadow-md flex flex-col border border-slate-700/20 hover:border-indigo-500/20 hover:bg-slate-800/80 transition-all duration-300 group relative"
          aria-label="Maximum supply"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-1.5">
              <div className="bg-indigo-500/10 p-1.5 rounded-md group-hover:bg-indigo-500/20 transition-all duration-300 flex items-center justify-center">
                <DollarSign className="h-4 w-4 text-indigo-400 group-hover:text-indigo-300 transition-colors duration-300" />
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="text-sm font-medium text-slate-400 group-hover:text-indigo-400 transition-colors duration-300">
                      Max Supply
                    </span>
                  </TooltipTrigger>
                  <TooltipContent
                    side="top"
                    className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                  >
                    <p className="font-medium text-indigo-400 mb-1">
                      Maximum Supply
                    </p>
                    <p>
                      The maximum amount of tokens that will ever exist,
                      including future issuance.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <div className="mt-2">
            <div className="flex flex-wrap items-baseline gap-2">
              <span className="text-xl font-bold text-white group-hover:text-indigo-50 transition-colors duration-300">
                {formatCurrencyValue(maxSupply, {
                  currency: "",
                  compact: true,
                })}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketMetrics;