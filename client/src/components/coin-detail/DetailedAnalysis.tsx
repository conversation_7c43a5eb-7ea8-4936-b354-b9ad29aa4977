import React, { useState, useEffect } from "react";
import {
  Shield,
  Users,
  BarChart4,
  Lightbulb,
  ChevronRight,
  ChevronDown,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  XCircle,
  Lightbulb as LightbulbIcon,
  Flag,
} from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { CoinStatus } from "@/types/CoinStatus";
import { CoinDetailService } from "@/lib/services/CoinDetailService";
import { MetricTextResponse } from "@/types/coinDetail";
import CalculationDataDisplay from "./CalculationDataDisplay";

// Category icons mapping
const categoryIcons: Record<string, React.ReactNode> = {
  Tokenomics: <BarChart4 className="h-5 w-5 flex-shrink-0 text-blue-400" />, // Changed to BarChart4 from Coins
  Security: <Shield className="h-5 w-5 flex-shrink-0 text-blue-400" />,
  Social: <Users className="h-5 w-5 flex-shrink-0 text-blue-400" />,
  "Social & Community": (
    <Users className="h-5 w-5 flex-shrink-0 text-blue-400" />
  ),
  Market: <BarChart4 className="h-5 w-5 flex-shrink-0 text-blue-400" />,
  "Market Performance": (
    <BarChart4 className="h-5 w-5 flex-shrink-0 text-blue-400" />
  ),
  Insight: <Lightbulb className="h-5 w-5 flex-shrink-0 text-blue-400" />,
};

// Status icon mapping
const statusIcons: Record<string, React.ReactNode> = {
  Excellent: <CheckCircle className="h-[18px] w-[18px] text-emerald-400" />,
  Positive: <CheckCircle className="h-[18px] w-[18px] text-blue-400" />, 
  Average: <AlertCircle className="h-[18px] w-[18px] text-amber-400" />, 
  Weak: <AlertTriangle className="h-[18px] w-[18px] text-red-400" />, 
  Critical: <XCircle className="h-[18px] w-[18px] text-rose-400" />, 
};

interface MetricProps {
  name: string;
  value?: number | string;
  score: number;
  label: string;
  status: CoinStatus;
  description: string;
  category: string;
  dataPoints?: string[];
  id: string; // API'nin beklediği gerçek subscore.id değeri - artık zorunlu alan
  uniqueKey?: string; // Added for React key uniqueness
}

interface CategoryProps {
  name: string;
  originalName?: string; // Optional original category name for preserving API keys
  score: number;
  status: CoinStatus;
  description: string;
  weight: number;
  metrics: MetricProps[];
}

interface DetailedAnalysisProps {
  categories: CategoryProps[];
  activeCategoryIndex: number;
  onCategoryChange: (index: number) => void;
  overallScore: number;
  coinId?: string; // Coin ID for fetching metric details from API
  coinName?: string; // Coin name for error reporting
  coinSymbol?: string; // Coin symbol for display in calculation data
}

/**
 * Component for displaying detailed analysis of coin metrics
 * Provides interactive category selection and metric details
 */
export const DetailedAnalysis: React.FC<DetailedAnalysisProps> = ({
  categories,
  activeCategoryIndex,
  onCategoryChange,
  overallScore,
  coinId,
  coinName,
  coinSymbol,
}) => {
  // Get translation function from language context
  const { t } = useLanguage();
  
  // Selected metric within the active category
  const [activeMetricIndex, setActiveMetricIndex] = useState<number | null>(
    null,
  );

  // State for metric HTML content from API
  const [metricContent, setMetricContent] = useState<MetricTextResponse>({
    conclusion_text: "",
    what_are_we_scoring: "",
    why_is_this_important: "",
    calculation_data: undefined,
  });
  const [isMetricContentLoading, setIsMetricContentLoading] =
    useState<boolean>(false);
  const [metricContentError, setMetricContentError] = useState<string | null>(
    null,
  );

  // State for modal dialogs
  const [isFeatureModalOpen, setIsFeatureModalOpen] = useState<boolean>(false);
  const [isErrorModalOpen, setIsErrorModalOpen] = useState<boolean>(false);
  const [featureRequest, setFeatureRequest] = useState<string>("");
  const [errorReport, setErrorReport] = useState<string>("");

  // Toast hook for notifications
  const { toast } = useToast();

  // Handle form submissions
  const handleFeatureSubmit = async () => {
    if (!featureRequest.trim()) {
      toast({
        title: "Hata",
        description: "Lütfen özellik önerinizi girin",
        variant: "destructive",
      });
      return;
    }

    try {
      // API'ye özellik önerisi gönder
      const response = await CoinDetailService.requestFeature({
        detail: featureRequest,
      });

      console.log("Feature request API response:", response);

      // Show success toast
      toast({
        title: "Özellik Önerisi Gönderildi!",
        description:
          "Geri bildiriminiz için teşekkür ederiz! Öneriniz ekibimiz tarafından değerlendirilecektir.",
        variant: "default",
        className: "bg-blue-900 border-blue-700 text-white shadow-lg border-2",
        duration: 5000, // Show for 5 seconds
      });

      // Create a visual confirmation overlay
      const confirmationElement = document.createElement("div");
      confirmationElement.className =
        "fixed inset-0 flex items-center justify-center z-[1000] bg-black/50";
      confirmationElement.innerHTML = `
        <div class="bg-blue-900 border-2 border-blue-600 p-6 rounded-lg max-w-md text-center animate-in slide-in-from-bottom-10 duration-300">
          <div class="text-blue-300 mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="m9 12 2 2 4-4"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-2">Feature Request Submitted!</h3>
          <p class="text-blue-100 mb-4">Thank you for your feedback. We'll review your suggestion and consider it for future updates.</p>
          <button class="bg-blue-700 hover:bg-blue-800 text-white py-2 px-5 rounded-md transition-colors duration-200">
            Close
          </button>
        </div>
      `;

      document.body.appendChild(confirmationElement);

      // Add event listener to close button
      confirmationElement
        .querySelector("button")
        ?.addEventListener("click", () => {
          document.body.removeChild(confirmationElement);
        });

      // Auto remove after 3 seconds
      setTimeout(() => {
        if (document.body.contains(confirmationElement)) {
          document.body.removeChild(confirmationElement);
        }
      }, 3000);

      // Reset form and close modal
      setFeatureRequest("");
      setIsFeatureModalOpen(false);
    } catch (error) {
      console.error("Error submitting feature request:", error);
      toast({
        title: "Hata",
        description:
          "Öneriniz gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.",
        variant: "destructive",
      });
    }
  };

  const handleErrorSubmit = async () => {
    if (!errorReport.trim()) {
      toast({
        title: "Hata",
        description: "Lütfen hata detaylarını girin",
        variant: "destructive",
      });
      return;
    }

    try {
      // API'ye hata raporunu gönder
      const response = await CoinDetailService.reportError({
        coin_id: coinId || "",
        coin_name: coinName || "",
        detail: errorReport,
      });

      console.log("Error report API response:", response);

      // Show success toast
      toast({
        title: "Hata Raporu Gönderildi!",
        description:
          "Hata bildiriminiz için teşekkür ederiz. Ekibimiz en kısa sürede inceleyecektir.",
        variant: "default",
        className:
          "bg-slate-900 border-slate-700 text-white shadow-lg border-2",
        duration: 5000, // Show for 5 seconds
      });

      // Create a visual confirmation overlay
      const confirmationElement = document.createElement("div");
      confirmationElement.className =
        "fixed inset-0 flex items-center justify-center z-[1000] bg-black/50";
      confirmationElement.innerHTML = `
        <div class="bg-slate-900 border-2 border-slate-700 p-6 rounded-lg max-w-md text-center animate-in slide-in-from-bottom-10 duration-300">
          <div class="text-red-400 mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto">
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
              <path d="M14 2v6h6"></path>
              <path d="M9 13h6"></path>
              <path d="M9 17h6"></path>
              <path d="M9 9h1"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-2">Error Report Received!</h3>
          <p class="text-slate-300 mb-4">Thank you for helping us improve. Our team will investigate the issue you reported.</p>
          <button class="bg-slate-700 hover:bg-slate-800 text-white py-2 px-5 rounded-md transition-colors duration-200">
            Close
          </button>
        </div>
      `;

      document.body.appendChild(confirmationElement);

      // Add event listener to close button
      confirmationElement
        .querySelector("button")
        ?.addEventListener("click", () => {
          document.body.removeChild(confirmationElement);
        });

      // Auto remove after 3 seconds
      setTimeout(() => {
        if (document.body.contains(confirmationElement)) {
          document.body.removeChild(confirmationElement);
        }
      }, 3000);

      // Reset form and close modal
      setErrorReport("");
      setIsErrorModalOpen(false);
    } catch (error) {
      console.error("Error submitting report:", error);
      toast({
        title: "Hata",
        description:
          "Rapor gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.",
        variant: "destructive",
      });
    }
  };

  // Error handling for empty categories
  if (!categories || categories.length === 0) {
    return (
      <div className="bg-slate-800/60 rounded-lg p-6 text-center border border-slate-700/20">
        <AlertCircle className="h-8 w-8 text-amber-400 mx-auto mb-4" />
        <h3 className="text-xl font-bold text-white mb-2">
          Analysis Data Unavailable
        </h3>
        <p className="text-slate-400">
          Detailed category metrics are not available for this coin.
        </p>
      </div>
    );
  }

  // Ensure we have a valid index (if activeCategoryIndex is out of bounds)
  const safeActiveCategoryIndex =
    activeCategoryIndex >= 0 && activeCategoryIndex < categories.length
      ? activeCategoryIndex
      : 0;

  // Get the active category
  const activeCategory = categories[safeActiveCategoryIndex];

  // Fetch metric HTML content when activeMetricIndex changes
  useEffect(() => {
    // If no active metric or no coinId, reset content and return
    if (
      activeMetricIndex === null ||
      !coinId ||
      !activeCategory?.metrics?.[activeMetricIndex]
    ) {
      console.log("Skipping metric fetch due to missing data:", {
        activeMetricIndex,
        coinId,
        hasMetrics: !!activeCategory?.metrics,
      });
      setMetricContent({
        conclusion_text: "",
        what_are_we_scoring: "",
        why_is_this_important: "",
        calculation_data: undefined,
      });
      setIsMetricContentLoading(false);
      setMetricContentError(null);
      return;
    }

    const fetchMetricContent = async () => {
      const metric = activeCategory.metrics[activeMetricIndex];

      // Subscore.id değerini kullan (API'nin beklediği gerçek id)
      // Eğer id yoksa fallback olarak label veya name kullan
      const metricId = metric.id || metric.label || metric.name;
      console.log(
        "Fetching metric content for metric:",
        JSON.stringify(activeCategory),
      );

      console.log("METRIC DATA USED FOR API CALL:", {
        metric,
        metricId,
        coinId,
        id: metric.id, // Log the actual id if it exists
        label: metric.label,
        name: metric.name,
      });

      try {
        setIsMetricContentLoading(true);
        setMetricContentError(null);

        console.log(
          `⏳ Fetching metric text for coin: ${coinId}, metric ID: ${metricId}`,
        );
        const metricTextData = await CoinDetailService.getMetricTextById(
          coinId,
          metricId,
        );
        console.log(
          `✅ Received metric text data:`,
          metricTextData
            ? `what_are_we_scoring: ${metricTextData.what_are_we_scoring?.substring(0, 30)}..., why_is_this_important: ${metricTextData.why_is_this_important?.substring(0, 30)}..., conclusion_text: ${metricTextData.conclusion_text?.substring(0, 30)}...`
            : "EMPTY",
        );

        // Set the metric content from API
        setMetricContent({
          conclusion_text: metricTextData.conclusion_text || "",
          what_are_we_scoring: metricTextData.what_are_we_scoring || "",
          why_is_this_important: metricTextData.why_is_this_important || "",
          calculation_data: metricTextData.calculation_data || undefined,
        });
      } catch (error) {
        console.error("❌ Error fetching metric content:", error);
        setMetricContentError(
          "Failed to load metric details. Please try again later.",
        );

        // If API call fails, use fallback data from props
        setMetricContent({
          conclusion_text: "",
          what_are_we_scoring: "",
          why_is_this_important: "",
          calculation_data: undefined,
        });
      } finally {
        setIsMetricContentLoading(false);
      }
    };

    fetchMetricContent();
  }, [activeMetricIndex, coinId, activeCategory]);

  return (
    <div className="space-y-5">
      {/* Removed duplicate header - main header is now in parent component */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-5 px-3">
        {/* Left side - Category Selection */}
        <div className="lg:col-span-5">
          <div className="bg-slate-800/60 min-h-[100%] rounded-lg border border-slate-700/20 overflow-hidden">
            <div className="p-3 border-b border-slate-700/20 bg-slate-800/80">
              <h3 className="text-base font-semibold text-white">{t('coinDetail.categories')}</h3>
            </div>

            <div className="divide-y divide-slate-700/20">
              {categories.map((category, index) => (
                <button
                  key={`${category.name}-${index}`}
                  className={`w-full text-left py-2.5 px-3 flex items-center justify-between transition-colors ${
                    index === safeActiveCategoryIndex
                      ? "bg-slate-700/40 text-white"
                      : "text-slate-300 hover:bg-slate-700/20"
                  }`}
                  onClick={() => {
                    onCategoryChange(index);
                    setActiveMetricIndex(null); // Reset active metric when changing category
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className={`p-1 rounded-md ${
                        index === safeActiveCategoryIndex
                          ? "bg-blue-500/20 text-blue-400"
                          : "bg-slate-700/50 text-slate-400"
                      }`}
                    >
                      {categoryIcons[category.name] || (
                        <Shield className="h-4 w-4" />
                      )}
                    </div>
                    <span className="font-medium text-sm">{category.name}</span>
                  </div>

                  <div className="flex items-center">
                    <div className="w-16 text-right">
                      <span className="text-xs font-medium text-white">
                        {category.score}
                        <span className="text-slate-400">/100</span>
                      </span>
                    </div>
                    <div
                      className={`ml-2 px-2 py-0.5 text-xs rounded-full min-w-[70px] text-center ${
                        category.score >= 90
                          ? "bg-[#00D88A]/20 text-[#00D88A]" // Excellent
                          : category.score >= 75
                            ? "bg-[#00B8D9]/20 text-[#00B8D9]" // Good/Positive
                            : category.score >= 65
                              ? "bg-[#FFAB00]/20 text-[#FFAB00]" // Fair/Average
                              : category.score >= 50
                                ? "bg-[#FF5630]/20 text-[#FF5630]" // Poor/Weak
                                : "bg-[#FF3B3B]/20 text-[#FF3B3B]" // Bad/Critical
                      }`}
                    >
                      {(() => {
                        if (category.score >= 90) return t("score:excellent", "Excellent");
                        if (category.score >= 75) return t("score:positive", "Positive");
                        if (category.score >= 65) return t("score:average", "Average");
                        if (category.score >= 50) return t("score:weak", "Weak");
                        return t("score:critical", "Critical");
                      })()}
                    </div>
                    <ChevronRight
                      className={`h-3.5 w-3.5 ml-2 transition-transform ${
                        index === safeActiveCategoryIndex ? "rotate-90" : ""
                      }`}
                    />
                  </div>
                </button>
              ))}

              {/* Feedback buttons */}
              <div className="flex justify-center mt-auto gap-2 p-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-slate-800 border-slate-700 border-[0.5px] hover:bg-slate-700 text-slate-300 hover:text-white flex items-center gap-1.5 py-1 px-2.5"
                  onClick={() => setIsFeatureModalOpen(true)}
                >
                  <LightbulbIcon className="h-3.5 w-3.5 text-blue-400" />
                  <span className="text-xs font-medium">{t('tokenomicsNamespace.requestFeature')}</span>
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="bg-slate-800 border-slate-700 border-[0.5px] hover:bg-slate-700 text-slate-300 hover:text-white flex items-center gap-1.5 py-1 px-2.5"
                  onClick={() => setIsErrorModalOpen(true)}
                >
                  <Flag className="h-3.5 w-3.5 text-red-400" />
                  <span className="text-xs font-medium">{t('tokenomicsNamespace.reportError')}</span>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Detailed metrics */}
        <div className="lg:col-span-7">
          <div className="bg-slate-800/60 rounded-lg border border-slate-700/20 overflow-hidden">
            {/* Header with category info */}
            <div className="px-5 py-4 border-b border-slate-700/20 bg-slate-800/80">
              <div className="flex items-center justify-between w-full mb-3">
                <div className="flex items-center gap-3">
                  <div className="bg-blue-500/20 p-3 rounded-md text-blue-400 flex-shrink-0">
                    {categoryIcons[activeCategory.name] || (
                      <Shield className="h-5 w-5" />
                    )}
                  </div>
                  <h2 className="text-lg font-bold text-white flex items-center">
                    {(() => {
                      const categoryName = activeCategory.name.toLowerCase();
                      if (categoryName.includes('tokenomics') || categoryName.includes('tokenomik')) {
                        return t('tokenomicsNamespace.metricsBreakdown');
                      }
                      // For other categories, use generic breakdown translation
                      return `${activeCategory.name} ${t('tokenomicsNamespace.metricsBreakdownGeneric')}`;
                    })()}
                    <span className="ml-4 text-xs text-slate-400 font-normal whitespace-nowrap bg-slate-700/30 px-2 py-0.5 rounded">
                      {t('tokenomicsNamespace.weight')}:{" "}
                      {activeCategory.weight ? activeCategory.weight : 0}%
                    </span>
                  </h2>
                </div>
              </div>
            </div>

            {/* Metrics list */}
            <div className="divide-y divide-slate-700/10">
              {activeCategory.metrics.map((metric, index) => (
                <div
                  key={metric.uniqueKey || `${metric.name}-${index}`}
                  className="overflow-hidden"
                >
                  {/* Metric header - always visible */}
                  <button
                    className={`w-full text-left px-5 py-3.5 flex items-center justify-between transition-colors ${
                      activeMetricIndex === index
                        ? "bg-slate-700/30"
                        : "hover:bg-slate-700/10"
                    }`}
                    onClick={() =>
                      setActiveMetricIndex(
                        activeMetricIndex === index ? null : index,
                      )
                    }
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex-shrink-0">
                        {statusIcons[metric.status] || statusIcons["Fair"]}
                      </div>
                      <span className="font-medium text-white text-sm">
                        {metric.name}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        <div className="text-right w-16">
                          <span className="text-xs font-semibold text-white">
                            {metric.value}
                            <span className="text-slate-400">/100</span>
                          </span>
                        </div>
                        <div
                          className={`ml-2 px-2 py-0.5 text-xs rounded-full min-w-[70px] text-center ${
                            metric.score >= 90
                              ? "bg-[#00D88A]/20 text-[#00D88A]" // Excellent
                              : metric.score >= 75
                                ? "bg-[#00B8D9]/20 text-[#00B8D9]" // Good/Positive
                                : metric.score >= 65
                                  ? "bg-[#FFAB00]/20 text-[#FFAB00]" // Fair/Average
                                  : metric.score >= 50
                                    ? "bg-[#FF5630]/20 text-[#FF5630]" // Poor/Weak
                                    : "bg-[#FF3B3B]/20 text-[#FF3B3B]" // Bad/Critical
                          }`}
                        >
                          {(() => {
                            if (metric.score >= 90) return t("score:excellent", "Excellent");
                            if (metric.score >= 75) return t("score:positive", "Positive");
                            if (metric.score >= 65) return t("score:average", "Average");
                            if (metric.score >= 50) return t("score:weak", "Weak");
                            return t("score:critical", "Critical");
                          })()}
                        </div>
                      </div>
                      <ChevronDown
                        className={`h-4 w-4 ml-2 text-slate-400 transition-transform ${
                          activeMetricIndex === index ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                  </button>

                  {/* Expandable metric details */}
                  {activeMetricIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="bg-slate-800/50 border-t border-slate-700/10 p-5"
                    >
                      <div className="space-y-4">
                        {/* API HTML Content Section - Displayed when API data is available */}
                        {isMetricContentLoading ? (
                          <div className="bg-slate-800/80 rounded-lg px-4 py-6 border border-slate-700/40 flex items-center justify-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
                            <span className="ml-3 text-sm text-slate-300">
                              Loading metric details...
                            </span>
                          </div>
                        ) : metricContentError ? (
                          <div className="bg-slate-800/80 rounded-lg px-4 py-3.5 border border-slate-700/40 p-4 text-center">
                            <AlertTriangle className="h-6 w-6 text-amber-400 mx-auto mb-2" />
                            <p className="text-sm text-slate-300">
                              {metricContentError}
                            </p>
                          </div>
                        ) : metricContent.what_are_we_scoring ||
                          metricContent.why_is_this_important ||
                          metricContent.conclusion_text ||
                          metricContent.calculation_data ? (
                          <div className="space-y-5">
                            {/* Calculation Data Section - Display at the top when available */}
                            {metricContent.calculation_data && (
                              <CalculationDataDisplay 
                                calculationData={metricContent.calculation_data} 
                                coinSymbol={coinSymbol}
                              />
                            )}

                            {/* What Are We Scoring Section */}
                            {metricContent.what_are_we_scoring && (
                              <div>
                                <div className="text-base font-semibold text-blue-400 flex items-center mb-3">
                                  <span className="mr-2">📄</span>
                                  {t("methodology.whatAreWeScoring", "What Are We Scoring?")}
                                </div>
                                <div className="bg-slate-800/80 rounded-lg px-4 py-3.5 border border-slate-700/40">
                                  <div
                                    className="metric-html-content text-[13px] leading-relaxed text-slate-300"
                                    dangerouslySetInnerHTML={{
                                      __html: metricContent.what_are_we_scoring,
                                    }}
                                  />
                                </div>
                              </div>
                            )}

                            {/* Why Is This Important Section */}
                            {metricContent.why_is_this_important && (
                              <div>
                                <div className="text-base font-semibold text-blue-400 flex items-center mb-3">
                                  <span className="mr-2">💡</span>
                                  {t("methodology.whyIsThisImportant", "Why Is This Important?")}
                                </div>
                                <div className="bg-slate-800/80 rounded-lg px-4 py-3.5 border border-slate-700/40">
                                  <div
                                    className="metric-html-content text-[13px] leading-relaxed text-slate-300"
                                    dangerouslySetInnerHTML={{
                                      __html:
                                        metricContent.why_is_this_important,
                                    }}
                                  />
                                </div>
                              </div>
                            )}

                            {/* Scoring Levels Section (previously named Conclusion) */}
                            {metricContent.conclusion_text && (
                              <div>
                                <div className="text-base font-semibold text-blue-400 flex items-center mb-3">
                                  <span className="mr-2">📊</span>
                                  {t("methodology.scoringLevels", "Scoring Levels")}
                                </div>
                                <div className="bg-slate-800/80 rounded-lg px-4 py-3.5 border border-slate-700/40">
                                  <div
                                    className="metric-html-content text-[13px] leading-relaxed text-slate-300"
                                    dangerouslySetInnerHTML={{
                                      __html: metricContent.conclusion_text,
                                    }}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          // No fallback content, just show empty state if no API data is available
                          <div className="py-4 text-center">
                            <p className="text-sm text-slate-400">
                              No metric content available.
                            </p>
                          </div>
                        )}

                        {/* Key Data Points section has been removed as this data is not provided by the API */}
                      </div>
                    </motion.div>
                  )}
                </div>
              ))}
            </div>

            {/* Empty state if no metrics */}
            {activeCategory.metrics.length === 0 && (
              <div className="p-6 text-center">
                <AlertCircle className="h-6 w-6 text-amber-400 mx-auto mb-3" />
                <h3 className="text-base font-semibold text-white mb-1">
                  No Metrics Available
                </h3>
                <p className="text-sm text-slate-400">
                  Detailed metrics are not available for this category.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Feature Request Modal */}
      <Dialog open={isFeatureModalOpen} onOpenChange={setIsFeatureModalOpen}>
        <DialogContent className="sm:max-w-[425px] bg-slate-900 text-white border-slate-700">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold flex items-center gap-2">
              <LightbulbIcon className="h-5 w-5 text-blue-400" />
              Request a Feature
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Tell us about a feature you'd like to see added to this platform.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Textarea
              className="bg-slate-800 border-slate-700 text-white min-h-[120px]"
              placeholder="Describe the feature you'd like to request..."
              value={featureRequest}
              onChange={(e) => setFeatureRequest(e.target.value)}
            />
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              className="border-slate-700 hover:bg-slate-800 text-slate-300"
              onClick={() => setIsFeatureModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white"
              onClick={handleFeatureSubmit}
              disabled={!featureRequest.trim()}
            >
              Submit Request
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Error Report Modal */}
      <Dialog open={isErrorModalOpen} onOpenChange={setIsErrorModalOpen}>
        <DialogContent className="sm:max-w-[425px] bg-slate-900 text-white border-slate-700">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold flex items-center gap-2">
              <Flag className="h-5 w-5 text-red-400" />
              Report an Issue
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Let us know about any errors or issues you've encountered.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Textarea
              className="bg-slate-800 border-slate-700 text-white min-h-[120px]"
              placeholder="Describe the issue you've encountered..."
              value={errorReport}
              onChange={(e) => setErrorReport(e.target.value)}
            />
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              className="border-slate-700 hover:bg-slate-800 text-slate-300"
              onClick={() => setIsErrorModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={handleErrorSubmit}
              disabled={!errorReport.trim()}
            >
              Submit Report
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DetailedAnalysis;
