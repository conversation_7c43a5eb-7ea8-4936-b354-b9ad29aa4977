import React from 'react';
import { Link } from 'wouter';
import { 
  Shield, 
  Building2, 
  Link as LinkIcon, 
  Coins,
  Award,
  Users,
  Bell,
  FileText,
  Share2,
  AlertCircle
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import CoinImage from '@/components/coin-detail/CoinImage';
import { ContextAwareWatchlistButton } from '@/components/watchlists/ContextAwareWatchlistButton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useLanguage } from '@/contexts/LanguageContext';

interface CoinOverviewProps {
  coinId: string;
  name: string;
  symbol: string;
  image: string;
  category: string;
  rank: number;
  watchlistCount: number;
  launchDate: string;
  website: string;
  explorer: string;
  formatDateDDMM: (dateString: string) => string;
}

const CoinOverview: React.FC<CoinOverviewProps> = ({
  coinId,
  name,
  symbol,
  image,
  category,
  rank,
  watchlistCount,
  launchDate,
  website,
  explorer,
  formatDateDDMM,
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="flex flex-col gap-4">
      {/* Coin Header with Actions */}
      <div className="flex flex-col md:flex-row justify-between gap-4 md:items-center bg-slate-800/40 p-4 rounded-lg border border-slate-700/50">
        <div className="flex items-center gap-3">
          <CoinImage src={image} alt={name} symbol={symbol} size="md" />
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-xl font-bold text-white">{name}</h1>
              <span className="text-sm font-medium text-slate-400">
                {symbol.toUpperCase()}
              </span>
              {rank && (
                <Badge variant="outline" className="text-amber-300 border-amber-500/30">
                  Rank #{rank}
                </Badge>
              )}
            </div>
            <div className="flex gap-2 mt-1 flex-wrap">
              {category && (
                <Badge variant="secondary" className="bg-slate-700/70 hover:bg-slate-700 text-slate-300">
                  {category}
                </Badge>
              )}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge variant="secondary" className="bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {watchlistCount || 0}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent className="bg-slate-800 text-slate-200 border-slate-700">
                    {watchlistCount || 0} users are watching this coin
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {launchDate && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="secondary" className="bg-green-500/10 text-green-400 hover:bg-green-500/20 flex items-center gap-1">
                        <Shield className="h-3 w-3" />
                        {formatDateDDMM(launchDate)}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent className="bg-slate-800 text-slate-200 border-slate-700">
                      Launch date: {new Date(launchDate).toLocaleDateString()}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2 justify-start md:justify-end">
          <ContextAwareWatchlistButton
            coinId={coinId}
            pageType="coindetail"
            variant="default"
            className="bg-slate-800 hover:bg-slate-700 text-slate-200"
          />
          <Button
            variant="outline"
            className="bg-slate-800 hover:bg-slate-700 text-slate-200 gap-2"
            onClick={() => {
              // Alert button functionality would go here
              console.log(`Set alert for ${coinId}`);
            }}
          >
            <Bell className="h-4 w-4" />
            <span>{t('watchlist.alerts')}</span>
          </Button>
          <Button 
            variant="outline"
            className="bg-slate-800 hover:bg-slate-700 text-slate-200 gap-2"
            onClick={() => {
              // Download PDF functionality
              console.log(`Download PDF for ${name} (${symbol})`);
            }}
          >
            <FileText className="h-4 w-4" />
            <span>PDF</span>
          </Button>
          <Button
            variant="outline"
            className="bg-slate-800 hover:bg-slate-700 text-slate-200 gap-2"
            onClick={() => {
              // Share functionality
              const shareUrl = window.location.origin + `/coin/${coinId}`;
              const shareText = `Check out ${name} (${symbol}) on CoinScout`;
              
              if (navigator.share) {
                navigator.share({
                  title: `${name} (${symbol}) on CoinScout`,
                  text: shareText,
                  url: shareUrl
                }).catch(err => console.error('Error sharing:', err));
              } else {
                // Fallback copy to clipboard
                navigator.clipboard.writeText(`${shareText} ${shareUrl}`)
                  .then(() => alert('Link copied to clipboard!'))
                  .catch(err => console.error('Error copying to clipboard:', err));
              }
            }}
          >
            <Share2 className="h-4 w-4" />
            <span>{t('watchlist.share')}</span>
          </Button>
          <Button
            variant="outline"
            className="bg-slate-800 hover:bg-slate-700 text-slate-200 gap-2"
            onClick={() => {
              // Report error functionality
              console.log(`Report error for ${name} (${coinId})`);
            }}
          >
            <AlertCircle className="h-4 w-4" />
            <span>Report</span>
          </Button>
        </div>
      </div>
      
      {/* Links Section */}
      <div className="flex flex-wrap gap-3">
        {website && (
          <Link
            href={website.startsWith('http') ? website : `https://${website}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1.5 text-sm bg-slate-800 hover:bg-slate-700 text-slate-200 px-3 py-1.5 rounded-md transition-colors"
          >
            <LinkIcon className="h-3.5 w-3.5 text-blue-400" />
            Website
          </Link>
        )}
        {explorer && (
          <Link
            href={explorer.startsWith('http') ? explorer : `https://${explorer}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1.5 text-sm bg-slate-800 hover:bg-slate-700 text-slate-200 px-3 py-1.5 rounded-md transition-colors"
          >
            <Coins className="h-3.5 w-3.5 text-amber-400" />
            Explorer
          </Link>
        )}
        <Link
          href={`/coin/${coinId}/forecast`}
          className="inline-flex items-center gap-1.5 text-sm bg-slate-800 hover:bg-slate-700 text-slate-200 px-3 py-1.5 rounded-md transition-colors"
        >
          <Award className="h-3.5 w-3.5 text-purple-400" />
          Price Forecast
        </Link>
        <Link
          href={`/coin/${coinId}/team`}
          className="inline-flex items-center gap-1.5 text-sm bg-slate-800 hover:bg-slate-700 text-slate-200 px-3 py-1.5 rounded-md transition-colors"
        >
          <Building2 className="h-3.5 w-3.5 text-green-400" />
          Team Analysis
        </Link>
      </div>
    </div>
  );
};

export default CoinOverview;

export default CoinOverview;