import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Star, Grid, Link2, Calendar, Lock } from "lucide-react";
import { cn } from "@/lib/utils";
import CoinService, { Category, Chain } from "@/lib/services/CoinService";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/hooks/use-auth";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";

// Filter option interface
interface FilterOption {
  value: string;
  label: string;
}

// Listing date options - these will be transformed with translations in the component
const LISTING_DATE_VALUES = [
  { value: "1", key: "upcoming.filters.last24Hours" },
  { value: "7", key: "upcoming.filters.last7Days" },
  { value: "14", key: "upcoming.filters.last14Days" },
  { value: "30", key: "upcoming.filters.last30Days" },
  { value: "90", key: "upcoming.filters.last90Days" }
];

// Token Sale Type options - these will be transformed with translations in the component
const SALE_TYPE_VALUES = [
  { value: "all", key: "upcoming.filters.allTypes" },
  { value: "IDO", key: "saleType.IDO" },
  { value: "IEO", key: "saleType.IEO" }, 
  { value: "ICO", key: "saleType.ICO" },
  { value: "SHO", key: "saleType.SHO" },
  { value: "Seed", key: "saleType.Seed" },
  { value: "IGO", key: "saleType.IGO" },
  { value: "ISO", key: "saleType.ISO" }
];

export interface UpcomingFiltersState {
  projectScoreRange: [number, number];
  categories: string[];
  chains: string[];
  saleType?: string | null; // Token Sale Type filtresi eklendi (IDO, IEO, ICO vb.)
  listingDate?: string | null; // Listeleme tarihi filtresi eklendi
  isReset?: boolean; // Reset işlemlerini takip etmek için eklendi
}

interface UpcomingFiltersProps {
  onFilterChange: (filters: UpcomingFiltersState) => void;
  initialFilters?: UpcomingFiltersState;
  onClose?: () => void;
  className?: string;
  showListingDateFilter?: boolean; // Listing Date filtresinin gösterilip gösterilmeyeceğini belirler
}

export function UpcomingFilters({
  onFilterChange,
  initialFilters,
  onClose,
  className,
  showListingDateFilter = false, // Varsayılan olarak gösterme
}: UpcomingFiltersProps) {
  // Authentication and subscription hooks
  const { isLoggedIn } = useAuth();
  const { canAccessFeature } = useSubscription();
  const { toast } = useToast();
  const { t } = useLanguage();
  const [, setLocation] = useLocation();

  const [projectScoreRange, setProjectScoreRange] = React.useState<
    [number, number]
  >(initialFilters?.projectScoreRange || [0, 100]);

  // Kategori ID'leri için state - kategori ismi yerine artık ID kullanıyoruz
  const [selectedCategories, setSelectedCategories] = React.useState<string[]>(
    initialFilters?.categories || [],
  );

  const [selectedChains, setSelectedChains] = React.useState<string[]>(
    initialFilters?.chains || [],
  );

  // API data states
  const [categories, setCategories] = useState<Category[]>([]);
  const [chains, setChains] = useState<Chain[]>([]);
  const [isLoading, setIsLoading] = useState({
    categories: false,
    chains: false,
  });

  // UI states for dropdowns
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
  const [isChainsOpen, setIsChainsOpen] = useState(false);
  
  // Arama sorguları için state'ler
  const [categoriesSearchQuery, setCategoriesSearchQuery] = useState('');
  const [chainsSearchQuery, setChainsSearchQuery] = useState('');
  
  // Listing date state
  const [selectedListingDate, setSelectedListingDate] = useState<string | null>(
    initialFilters?.listingDate || "30" // Default to 30 days
  );
  
  // Token Sale Type state
  const [selectedSaleType, setSelectedSaleType] = useState<string>(
    initialFilters?.saleType || "all" // Default to "all"
  );

  // Click outside handler to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const categoriesEl = document.getElementById("categories-dropdown");
      const chainsEl = document.getElementById("chains-dropdown");

      if (categoriesEl && !categoriesEl.contains(event.target as Node)) {
        setIsCategoriesOpen(false);
      }

      if (chainsEl && !chainsEl.contains(event.target as Node)) {
        setIsChainsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch categories and chains from API
  useEffect(() => {
    const fetchFilterData = async () => {
      try {
        setIsLoading((prev) => ({ ...prev, categories: true }));
        const categoriesData = await CoinService.getCategories();
        setCategories(categoriesData);
        setIsLoading((prev) => ({ ...prev, categories: false }));
      } catch (error) {
        console.error("Error fetching categories:", error);
        setIsLoading((prev) => ({ ...prev, categories: false }));
      }

      try {
        setIsLoading((prev) => ({ ...prev, chains: true }));
        const chainsData = await CoinService.getChains();
        setChains(chainsData);
        setIsLoading((prev) => ({ ...prev, chains: false }));
      } catch (error) {
        console.error("Error fetching chains:", error);
        setIsLoading((prev) => ({ ...prev, chains: false }));
      }
    };

    fetchFilterData();
  }, []);

  // Update state if initialFilters changes
  React.useEffect(() => {
    if (initialFilters) {
      setProjectScoreRange(initialFilters.projectScoreRange);
      setSelectedCategories(initialFilters.categories);
      setSelectedChains(initialFilters.chains);
      if (initialFilters.listingDate) {
        setSelectedListingDate(initialFilters.listingDate);
      }
      if (initialFilters.saleType) {
        setSelectedSaleType(initialFilters.saleType);
      }
    }
  }, [initialFilters]);

  const handleApplyFilters = () => {
    // Check authentication and subscription status before applying filters
    if (!isLoggedIn) {
      toast({
        title: t("system.subscription.filterRestriction.title", "system", "Abonelik Gerekli"),
        description: "Filtreleri kullanmak için giriş yapmanız gerekiyor.",
        variant: "destructive",
      });
      setLocation("/login");
      return;
    }

    // Check if user has at least basic subscription (level 1 or higher)
    // Level 0 = Free, Level 1 = Basic, Level 2 = Pro, etc.
    if (!canAccessFeature(1)) {
      toast({
        title: t("system.subscription.filterRestriction.title", "system", "Abonelik Gerekli"),
        description: t("system.subscription.filterRestriction.message", "system", "Filtreleri kullanmak için en az Basic pakete sahip olmanız gerekiyor. Lütfen abonelik planınızı yükseltin."),
        variant: "destructive",
      });
      setLocation("/pricing");
      return;
    }

    // Eğer tarih filtresi seçildi ise
    if (selectedListingDate) {
      console.log(`Filtering coins with listing_date: ${selectedListingDate} days`);
    }

    const filterState: UpcomingFiltersState = {
      projectScoreRange,
      categories: selectedCategories,
      chains: selectedChains,
      saleType: selectedSaleType, // Sale Type filtresi ekledik
      listingDate: selectedListingDate, // Listeleme tarihini ekledik
    };

    onFilterChange(filterState);
    
    // Apply butonuna tıklandığında dialogu kapatmak için onClose varsa çağıralım
    if (onClose) {
      onClose();
    }
  };

  const handleResetFilters = () => {
    // Define default values
    const defaultFilters: UpcomingFiltersState = {
      projectScoreRange: [0, 100],
      categories: [],
      chains: [],
      saleType: "all", // Varsayılan olarak "all"
      listingDate: "30", // Varsayılan olarak 30 gün
    };

    // Update visual state with default values
    setProjectScoreRange(defaultFilters.projectScoreRange);
    setSelectedCategories(defaultFilters.categories);
    setSelectedChains(defaultFilters.chains);
    setSelectedSaleType("all"); // Sale Type'ı da sıfırlıyoruz
    setSelectedListingDate("30"); // Listeleme tarihini de sıfırlıyoruz

    // Create a special reset filter state with a flag to indicate this is a reset
    const resetFilterState: UpcomingFiltersState & { isReset?: boolean } = {
      ...defaultFilters,
      isReset: true, // Add flag to indicate this is a reset action
    };

    // Notify parent that filters should be completely reset
    onFilterChange(resetFilterState);
  };


  // CSS to hide input spinners
  const noSpinnerCSS = `
    /* Hide spinners for Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Hide spinners for Firefox */
    input[type=number] {
      -moz-appearance: textfield;
    }
  `;

  // Translation function is already available from the hook above

  // Create options with translated labels
  const SALE_TYPE_OPTIONS = SALE_TYPE_VALUES.map(option => ({
    value: option.value,
    label: t(option.key)
  }));
  
  const LISTING_DATE_OPTIONS = LISTING_DATE_VALUES.map(option => ({
    value: option.value,
    label: t(option.key)
  }));

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
      className={cn(
        "w-full max-w-[95%] sm:max-w-3xl mx-auto px-4 sm:px-0 relative overflow-visible",
        className,
      )}
    >
      {/* Add the CSS to remove spinners */}
      <style>{noSpinnerCSS}</style>

      <Card className="bg-card/95 backdrop-blur-[2px] border-border/30 p-4 sm:p-6 space-y-6 sm:space-y-8 relative overflow-visible">
        <div className="space-y-6 sm:space-y-8">

          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">
                {t("upcoming.filters.projectScore")}
              </Label>
            </div>
            <div className="px-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
                <input
                  type="number"
                  value={projectScoreRange[0]}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 100 && value <= projectScoreRange[1]) {
                      setProjectScoreRange([value, projectScoreRange[1]]);
                    }
                  }}
                  min="0"
                  max="100"
                  className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                />
                <input
                  type="number"
                  value={projectScoreRange[1]}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 100 && value >= projectScoreRange[0]) {
                      setProjectScoreRange([projectScoreRange[0], value]);
                    }
                  }}
                  min="0"
                  max="100"
                  className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                />
              </div>
              <Slider
                value={projectScoreRange}
                min={0}
                max={100}
                step={1}
                onValueChange={(value) =>
                  setProjectScoreRange(value as [number, number])
                }
                className="my-2 h-0.5 mt-1"
              />
            </div>
          </div>

          {/* Token Sale Type section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5 text-primary">
                <path d="M21 8v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2Z"/>
                <line x1="12" x2="12" y1="6" y2="18"/>
                <path d="M3.5 12h17"/>
              </svg>
              <Label className="text-base font-medium">{t("upcoming.filters.saleType")}</Label>
            </div>
            <div className="px-0">
              <Select value={selectedSaleType} onValueChange={setSelectedSaleType}>
                <SelectTrigger className="w-full bg-background/50 border border-border/50 rounded-lg h-11">
                  <SelectValue placeholder={t("upcoming.filters.saleType")} />
                </SelectTrigger>
                <SelectContent>
                  {SALE_TYPE_VALUES.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {t(option.key)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Categories section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Grid className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">{t("upcoming.filters.category")}</Label>
            </div>
            <div className="px-0">
              <div className="relative" id="categories-dropdown">
                <div
                  className={`w-full bg-background/50 border border-border/50 h-auto min-h-11 rounded-lg transition-standard p-2 px-3 flex flex-wrap gap-1 items-center ${isLoading.categories ? "opacity-70 cursor-not-allowed" : "cursor-pointer"}`}
                  onClick={() =>
                    !isLoading.categories &&
                    setIsCategoriesOpen(!isCategoriesOpen)
                  }
                >
                  {selectedCategories.length > 0 ? (
                    <div className="flex items-center flex-nowrap overflow-hidden">
                      {selectedCategories.slice(0, 2).map((categoryId, index) => {
                        // Find category name
                        const categoryObj = categories.find(cat => cat.id === categoryId);
                        const categoryName = categoryObj ? categoryObj.name : categoryId;
                        
                        return (
                          <span
                            key={index}
                            className="bg-primary/20 text-primary-foreground rounded-md px-2 py-1 text-xs flex items-center gap-1 whitespace-nowrap mr-1"
                          >
                            {categoryName}
                            <button
                              className="text-primary-foreground/70 hover:text-primary-foreground ml-1 rounded-full focus:outline-none focus:ring-2 focus:ring-primary/30"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedCategories(
                                  selectedCategories.filter(
                                    (c) => c !== categoryId,
                                  ),
                                );
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="12"
                                height="12"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                              </svg>
                            </button>
                          </span>
                        );
                      })}
                      {selectedCategories.length > 2 && (
                        <span className="bg-primary/10 text-primary rounded-md px-2 py-1 text-xs whitespace-nowrap">
                          +{selectedCategories.length - 2} {t("common.more")}
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">
                      {isLoading.categories
                        ? t("common.loading")
                        : t("upcoming.filters.allCategories")}
                    </span>
                  )}
                </div>

                {isCategoriesOpen && (
                  <div className="absolute z-50 w-full mt-1 bg-background border border-border/50 rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                    <div className="p-2 border-b border-border/50 sticky top-0 bg-background z-10">
                      <div className="flex items-center relative">
                        <input
                          type="text"
                          placeholder={t("upcoming.filters.searchCategories")}
                          className="w-full bg-background/80 border border-border/50 rounded-md px-3 py-1.5 text-sm"
                          value={categoriesSearchQuery || ""}
                          onChange={(e) => setCategoriesSearchQuery(e.target.value)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        {categoriesSearchQuery && (
                          <button
                            className="absolute right-2 text-muted-foreground hover:text-foreground"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCategoriesSearchQuery("");
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <line x1="18" y1="6" x2="6" y2="18"></line>
                              <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                          </button>
                        )}
                      </div>
                      <div className="flex justify-between mt-2 text-xs">
                        <span>
                          {selectedCategories.length} {t("common.selected")}
                        </span>
                        <div className="flex gap-2">
                          <button 
                            className="text-xs text-primary hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (categoriesSearchQuery) {
                                // Sadece filtrelenmiş kategorileri seç
                                const filteredCategoryIds = categories
                                  .filter(cat => 
                                    cat.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                                  )
                                  .map(cat => cat.id);
                                
                                // Filtrelenmiş kategorileri mevcut seçimlere ekle
                                // Set kullanarak tekrar eden öğeleri kaldıralım
                                const uniqueIdsSet = new Set([...selectedCategories, ...filteredCategoryIds]);
                                // Set'ten diziye dönüştürelim
                                const uniqueIds = Array.from(uniqueIdsSet);
                                setSelectedCategories(uniqueIds);
                              } else {
                                // Tümünü seç - tüm kategori ID'lerini ekle
                                setSelectedCategories(categories.map(cat => cat.id));
                              }
                            }}
                          >
                            {categoriesSearchQuery ? "Select Filtered" : "Select All"}
                          </button>
                          <button 
                            className="text-xs text-primary hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (categoriesSearchQuery) {
                                // Eğer bir arama sorgusu varsa, yalnızca filtrelenmiş kategorileri kaldır
                                const filteredCategoryIds = categories
                                  .filter(cat => 
                                    cat.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                                  )
                                  .map(cat => cat.id);
                                
                                setSelectedCategories(selectedCategories.filter(id => !filteredCategoryIds.includes(id)));
                              } else {
                                // Arama sorgusu yoksa, tüm seçimleri temizle
                                setSelectedCategories([]);
                              }
                            }}
                          >
                            {categoriesSearchQuery ? "Deselect Filtered" : "Deselect All"}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="p-1">
                      {categories.length > 0 ? (
                        categories
                          .filter(category => 
                            !categoriesSearchQuery || 
                            category.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                          )
                          .map((category, index) => (
                          <div
                            key={index}
                            className={`p-2 rounded-md cursor-pointer flex items-center gap-2 ${selectedCategories.includes(category.id) ? "bg-primary/20 text-primary-foreground" : "hover:bg-accent/50"}`}
                            onClick={() => {
                              if (selectedCategories.includes(category.id)) {
                                setSelectedCategories(
                                  selectedCategories.filter(
                                    (c) => c !== category.id,
                                  ),
                                );
                              } else {
                                setSelectedCategories([
                                  ...selectedCategories,
                                  category.id,
                                ]);
                              }
                            }}
                          >
                            <div
                              className={`w-4 h-4 rounded border ${selectedCategories.includes(category.id) ? "bg-primary border-primary" : "border-border"} flex items-center justify-center`}
                            >
                              {selectedCategories.includes(category.id) && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="10"
                                  height="10"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              )}
                            </div>
                            <span>{category.name}</span>
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-muted-foreground">
                          {isLoading.categories
                            ? "Loading..."
                            : "No categories found"}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Token Sale Type section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">Token Sale Type</Label>
            </div>
            <div className="px-0">
              <Select
                value={selectedSaleType || "all"}
                onValueChange={(value) => setSelectedSaleType(value)}
              >
                <SelectTrigger className="w-full bg-background/50 border border-border/50 rounded-lg transition-standard h-11">
                  <SelectValue placeholder="Select sale type" />
                </SelectTrigger>
                <SelectContent className="bg-background border border-border/50 rounded-lg shadow-lg z-50">
                  {SALE_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Listing Date section - sadece showListingDateFilter=true ise göster */}
          {showListingDateFilter && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-primary" />
                <Label className="text-base font-medium">Listing Date</Label>
              </div>
              <div className="px-0">
                <Select
                  value={selectedListingDate || "30"}
                  onValueChange={(value) => setSelectedListingDate(value)}
                >
                  <SelectTrigger className="w-full bg-background/50 border border-border/50 rounded-lg transition-standard h-11">
                    <SelectValue placeholder={t("upcoming.filters.selectDateRange")} />
                  </SelectTrigger>
                  <SelectContent className="bg-background border border-border/50 rounded-lg shadow-lg z-50">
                    {LISTING_DATE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Chains section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Link2 className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">{t("upcoming.filters.blockchain")}</Label>
            </div>
            <div className="px-0">
              <div className="relative" id="chains-dropdown">
                <div
                  className={`w-full bg-background/50 border border-border/50 h-auto min-h-11 rounded-lg transition-standard p-2 px-3 flex flex-wrap gap-1 items-center ${isLoading.chains ? "opacity-70 cursor-not-allowed" : "cursor-pointer"}`}
                  onClick={() =>
                    !isLoading.chains && setIsChainsOpen(!isChainsOpen)
                  }
                >
                  {selectedChains.length > 0 ? (
                    <div className="flex items-center flex-nowrap overflow-hidden">
                      {selectedChains.slice(0, 2).map((chain, index) => (
                        <span
                          key={index}
                          className="bg-primary/20 text-primary-foreground rounded-md px-2 py-1 text-xs flex items-center gap-1 whitespace-nowrap mr-1"
                        >
                          {chain}
                          <button
                            className="text-primary-foreground/70 hover:text-primary-foreground ml-1 rounded-full focus:outline-none focus:ring-2 focus:ring-primary/30"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedChains(
                                selectedChains.filter((c) => c !== chain),
                              );
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="12"
                              height="12"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <line x1="18" y1="6" x2="6" y2="18"></line>
                              <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                          </button>
                        </span>
                      ))}
                      {selectedChains.length > 2 && (
                        <span className="bg-primary/10 text-primary rounded-md px-2 py-1 text-xs whitespace-nowrap">
                          +{selectedChains.length - 2} {t("common.more")}
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">
                      {isLoading.chains ? t("common.loading") : t("upcoming.filters.allBlockchains")}
                    </span>
                  )}
                </div>

                {isChainsOpen && (
                  <div className="absolute z-50 w-full mt-1 bg-background border border-border/50 rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                    <div className="p-2 border-b border-border/50 sticky top-0 bg-background z-10">
                      <div className="flex items-center relative">
                        <input
                          type="text"
                          placeholder={t("upcoming.filters.searchChains")}
                          className="w-full bg-background/80 border border-border/50 rounded-md px-3 py-1.5 text-sm"
                          value={chainsSearchQuery || ""}
                          onChange={(e) => setChainsSearchQuery(e.target.value)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        {chainsSearchQuery && (
                          <button
                            className="absolute right-2 text-muted-foreground hover:text-foreground"
                            onClick={(e) => {
                              e.stopPropagation();
                              setChainsSearchQuery("");
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <line x1="18" y1="6" x2="6" y2="18"></line>
                              <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                          </button>
                        )}
                      </div>
                      <div className="flex justify-between mt-2 text-xs">
                        <span>
                          {selectedChains.length} selected
                        </span>
                        <div className="flex gap-2">
                          <button 
                            className="text-xs text-primary hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (chainsSearchQuery) {
                                // Sadece filtrelenmiş zincirleri seç
                                const filteredChainNames = chains
                                  .filter(chain => 
                                    chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                                  )
                                  .map(chain => chain.name);
                                
                                // Filtrelenmiş zincirleri mevcut seçimlere ekle
                                // Set kullanarak tekrar eden öğeleri kaldıralım
                                const uniqueNamesSet = new Set([...selectedChains, ...filteredChainNames]);
                                // Set'ten diziye dönüştürelim
                                const uniqueNames = Array.from(uniqueNamesSet);
                                setSelectedChains(uniqueNames);
                              } else {
                                // Tümünü seç
                                setSelectedChains(chains.map(chain => chain.name));
                              }
                            }}
                          >
                            {chainsSearchQuery ? "Select Filtered" : "Select All"}
                          </button>
                          <button 
                            className="text-xs text-primary hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (chainsSearchQuery) {
                                // Eğer bir arama sorgusu varsa, yalnızca filtrelenmiş zincirleri kaldır
                                const filteredChainNames = chains
                                  .filter(chain => 
                                    chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                                  )
                                  .map(chain => chain.name);
                                
                                setSelectedChains(selectedChains.filter(name => !filteredChainNames.includes(name)));
                              } else {
                                // Arama sorgusu yoksa, tüm seçimleri temizle
                                setSelectedChains([]);
                              }
                            }}
                          >
                            {chainsSearchQuery ? "Deselect Filtered" : "Deselect All"}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="p-1">
                      {chains.length > 0 ? (
                        chains
                          .filter(chain => 
                            !chainsSearchQuery ||
                            chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                          )
                          .map((chain, index) => (
                          <div
                            key={index}
                            className={`p-2 rounded-md cursor-pointer flex items-center gap-2 ${selectedChains.includes(chain.name) ? "bg-primary/20 text-primary-foreground" : "hover:bg-accent/50"}`}
                            onClick={() => {
                              if (selectedChains.includes(chain.name)) {
                                setSelectedChains(
                                  selectedChains.filter(
                                    (c) => c !== chain.name,
                                  ),
                                );
                              } else {
                                setSelectedChains([
                                  ...selectedChains,
                                  chain.name,
                                ]);
                              }
                            }}
                          >
                            <div
                              className={`w-4 h-4 rounded border ${selectedChains.includes(chain.name) ? "bg-primary border-primary" : "border-border"} flex items-center justify-center`}
                            >
                              {selectedChains.includes(chain.name) && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="10"
                                  height="10"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              )}
                            </div>
                            <span>{chain.name}</span>
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-muted-foreground">
                          {isLoading.chains ? "Loading..." : "No chains found"}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-between gap-3 pt-6">
          <Button
            variant="outline"
            onClick={handleResetFilters}
            className="w-full sm:w-auto px-8 bg-background/50 hover:bg-background/80 transition-standard h-11 rounded-lg text-base"
          >
            {t("upcoming.filters.reset")}
          </Button>
          <Button
            onClick={handleApplyFilters}
            className="w-full sm:w-auto px-8 bg-primary hover:bg-primary/90 text-primary-foreground transition-standard hover:scale-[1.02] active:scale-[0.98] h-11 rounded-lg text-base"
          >
            {t("upcoming.filters.apply")}
          </Button>
        </div>
      </Card>
    </motion.div>
  );
}
