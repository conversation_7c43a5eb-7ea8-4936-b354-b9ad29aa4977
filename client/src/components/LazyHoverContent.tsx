import React, { useState, useEffect } from "react";
import { CoinLogo } from "@/components/CoinLogo";
import { Loader2, Lock, LogIn } from "lucide-react";
import { cn } from "@/lib/utils";
import { useFormattedCurrency } from "@/lib/formatters";
import { Button } from "@/components/ui/button";
import { useLocation } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/hooks/use-auth";
import CoinHoverService, {
  CoinHoverData,
  CoinHoverStatus,
  CoinHoverResult,
} from "@/lib/services/CoinHoverService";

interface LazyHoverContentProps {
  coinId: string;
  initialData: {
    name: string;
    symbol: string;
    rank?: number;
    image?: string; // Logo URL'si
  };
}

/**
 * Hover yapıldığında coin bilgilerini lazy olarak yükleyen bileşen
 */
export function LazyHoverContent({
  coinId,
  initialData,
}: LazyHoverContentProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [status, setStatus] = useState<CoinHoverStatus | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [coinData, setCoinData] = useState<CoinHoverData | null>(null);
  const [, setLocation] = useLocation();
  const { t } = useLanguage();
  const { isLoggedIn, isLoading: authLoading } = useAuth();
  const formatCurrency = useFormattedCurrency();

  // Debug için logo url konsola yazdır
  useEffect(() => {
    console.log(
      `LazyHoverContent: Logo URL for ${initialData.symbol}:`,
      initialData.image,
    );
  }, [initialData.symbol, initialData.image]);

  // Authentication ve coin ID kontrolü
  useEffect(() => {
    let isMounted = true;

    const initializeComponent = async () => {
      console.log(
        `LazyHoverContent: Initializing for coin ${coinId}, auth status: ${isLoggedIn}`,
      );

      // Auth loading durumunu bekle
      if (authLoading) {
        return;
      }

      // Eğer kullanıcı giriş yapmamışsa, API çağrısı yapma
      if (!isLoggedIn) {
        if (isMounted) {
          setStatus(CoinHoverStatus.AUTH_REQUIRED);
          setIsLoading(false);
          console.log(`LazyHoverContent: User not logged in, showing auth prompt for coin ${coinId}`);
        }
        return;
      }

      // Kullanıcı giriş yaptıysa, API çağrısını yap
      const fetchCoinData = async () => {
        console.log(
          `LazyHoverContent: User logged in, fetching data for coin ${coinId}`,
        );

        setIsLoading(true);
        setError(null);

        try {
          console.time(`API Call - Coin Summary ${coinId}`);

          const result = await CoinHoverService.getCoinSummary(coinId);
          console.timeEnd(`API Call - Coin Summary ${coinId}`);

          console.log(`LazyHoverContent: API returned for ${coinId}`, result);

          if (isMounted) {
            setStatus(result.status);
            
            if (result.status === CoinHoverStatus.SUCCESS && result.data) {
              setCoinData(result.data);
            } else if (result.status === CoinHoverStatus.AUTH_REQUIRED) {
              setCoinData(null);
              setError(result.message || "Bu özelliği kullanmak için giriş yapmalısınız.");
            } else {
              setCoinData(null);
              setError(result.message || "Coin bilgileri yüklenirken bir hata oluştu.");
            }
            
            setIsLoading(false);
          }
        } catch (err) {
          console.timeEnd(`API Call - Coin Summary ${coinId}`);
          if (isMounted) {
            console.error("Error fetching coin summary data:", err);
            setStatus(CoinHoverStatus.ERROR);
            setError("Coin bilgileri yüklenirken bir hata oluştu.");
            setIsLoading(false);
          }
        }
      };

      if (coinId) {
        await fetchCoinData();
      } else {
        console.warn("LazyHoverContent: No coinId provided, skipping API call");
      }
    };

    initializeComponent();

    return () => {
      isMounted = false;
    };
  }, [coinId, isLoggedIn, authLoading]);

  // Yükleme durumu
  if (isLoading) {
    return (
      <div className="flex flex-col space-y-3 p-4 max-w-[320px] min-h-[180px]">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CoinLogo
              symbol={initialData.symbol}
              size="lg"
              imageUrl={initialData.image}
              className="opacity-90 hover:opacity-100 transition-opacity w-6 h-6"
            />
            <div>
              <div className="font-semibold text-base text-[#E7EBF0]">
                {initialData.name}
              </div>
              <div className="text-[#E7EBF0]/70 text-sm font-medium">
                {initialData.symbol}
              </div>
            </div>
          </div>
          {initialData.rank && (
            <div>
              <div className="px-2 py-1 rounded-md bg-[#132F4C]/90 border border-[#1E4976]/40 text-xs font-semibold text-[#66B2FF]">
                Market Cap
              </div>
              <div className="px-2 py-1 rounded-md bg-[#132F4C]/90 border border-[#1E4976]/40 text-sm font-semibold text-[#66B2FF]">
                Rank #{initialData.rank}
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col items-center justify-center h-[120px]">
          <Loader2 className="h-8 w-8 text-[#66B2FF] animate-spin mb-2" />
          <p className="text-[#E7EBF0]/70 text-sm">Bilgiler yükleniyor...</p>
        </div>
      </div>
    );
  }

  // Hata durumu veya authentication gerekli durumu
  if (error || status === CoinHoverStatus.AUTH_REQUIRED) {
    const isAuthRequired = status === CoinHoverStatus.AUTH_REQUIRED;
    
    return (
      <div className="flex flex-col space-y-3 p-4 max-w-[320px] min-h-[180px]">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CoinLogo
              symbol={initialData.symbol}
              size="lg"
              imageUrl={initialData.image}
              className="opacity-90 hover:opacity-100 transition-opacity w-6 h-6"
            />
            <div>
              <div className="font-semibold text-base text-[#E7EBF0]">
                {initialData.name}
              </div>
              <div className="text-[#E7EBF0]/70 text-sm font-medium">
                {initialData.symbol}
              </div>
            </div>
          </div>
          {initialData.rank && (
            <div>
              <div className="px-2 py-1 rounded-md bg-[#132F4C]/90 border border-[#1E4976]/40 text-xs font-semibold text-[#66B2FF]">
                Market Cap
              </div>
              <div className="px-2 py-1 rounded-md bg-[#132F4C]/90 border border-[#1E4976]/40 text-sm font-semibold text-[#66B2FF]">
                Rank #{initialData.rank}
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col items-center justify-center h-[120px] text-center">
          {isAuthRequired ? (
            <>
              <div className="p-3 rounded-full bg-[#66B2FF]/10 mb-3">
                <Lock className="w-6 h-6 text-[#66B2FF]" />
              </div>
              <p className="text-[#E7EBF0] text-sm mb-3 leading-relaxed">
                {t("system:auth.required", "system", "Login required to view coin summary")}
              </p>
              <Button
                size="sm"
                onClick={() => setLocation('/login')}
                className="bg-[#66B2FF] hover:bg-[#5ba3ff] text-white text-xs px-4 py-1.5 h-auto"
              >
                <LogIn className="w-3 h-3 mr-1.5" />
                {t("system:auth.loginButton", "system", "Login")}
              </Button>
            </>
          ) : (
            <>
              <div className="p-2 rounded-full bg-red-500/10 mb-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-red-400"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="12" y1="8" x2="12" y2="12" />
                  <line x1="12" y1="16" x2="12.01" y2="16" />
                </svg>
              </div>
              <p className="text-red-400 text-sm">{error}</p>
            </>
          )}
        </div>
      </div>
    );
  }

  // Veri geldi ama null ise
  if (!coinData) {
    return (
      <div className="flex flex-col space-y-3 p-4 max-w-[320px] min-h-[180px]">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CoinLogo
              symbol={initialData.symbol}
              size="lg"
              imageUrl={initialData.image}
              className="opacity-90 hover:opacity-100 transition-opacity w-6 h-6"
            />
            <div>
              <div className="font-semibold text-base text-[#E7EBF0]">
                {initialData.name}
              </div>
              <div className="text-[#E7EBF0]/70 text-sm font-medium">
                {initialData.symbol}
              </div>
            </div>
          </div>
          {initialData.rank && (
            <div>
              <div className="px-2 py-1 rounded-md bg-[#132F4C]/90 border border-[#1E4976]/40 text-xs font-semibold text-[#66B2FF]">
                Market Cap
              </div>
              <div className="px-2 py-1 rounded-md bg-[#132F4C]/90 border border-[#1E4976]/40 text-sm font-semibold text-[#66B2FF]">
                Rank #{initialData.rank}
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col items-center justify-center h-[120px] text-center">
          <p className="text-[#E7EBF0]/70 text-sm">Veriler bulunamadı.</p>
        </div>
      </div>
    );
  }

  // Başarılı durum - veri geldi
  return (
    <div className="flex flex-col space-y-3 p-4 z-[9999] max-w-[320px]">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {/* API'den gelen logo yerine row'dan aktarılan logoyu kullan */}
          <CoinLogo
            symbol={coinData.symbol}
            size="lg"
            imageUrl={initialData.image}
            className="opacity-90 hover:opacity-100 transition-opacity w-6 h-6"
          />
          <div>
            <div className="font-semibold text-base text-[#E7EBF0]">
              {coinData.name}
            </div>
            <div className="text-[#E7EBF0]/70 text-sm font-medium">
              {coinData.symbol}
            </div>
          </div>
        </div>
        <div>
          <div className="px-2 py-1 rounded-md bg-[#132F4C]/90 border border-[#1E4976]/40 text-xs font-semibold text-[#66B2FF] flex flex-col items-start">
            <p className="text-[8px]"> Market Cap Rank</p>
            <p className="text-[8px]"> </p>
            <p className=" ml-auto"> #{initialData.rank}</p>
          </div>
        </div>
      </div>

      <div className="pt-3 border-t border-[#1E4976]/30">
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex flex-col gap-1 bg-[#132F4C]/50 p-2.5 rounded-md border border-[#1E4976]/20">
            <span className="text-xs text-[#E7EBF0]/70 font-semibold">
              Current Price
            </span>
            <span className="text-base font-semibold text-[#E7EBF0] truncate">
              {formatCurrency(coinData.price)}
            </span>
          </div>
          <div className="flex flex-col gap-1 bg-[#132F4C]/50 p-2.5 rounded-md border border-[#1E4976]/20">
            <span className="text-xs text-[#E7EBF0]/70 font-semibold">
              Market Cap
            </span>
            <span className="text-base font-semibold text-[#E7EBF0] truncate">
              {formatCurrency(coinData.marketcap)}
            </span>
          </div>
        </div>
        
        {/* Watchlist Count Information */}
        <div className="flex items-center justify-between mb-3 bg-[#132F4C]/30 p-2 rounded-md border border-[#1E4976]/20">
          <span className="text-xs text-[#E7EBF0]/70 font-semibold">
            Watchlist Count
          </span>
          <div className="flex items-center gap-1.5">
            <span className="text-sm font-semibold text-[#66B2FF]">
              {coinData.watchlist_count || 0}
            </span>
            <span className="text-xs text-[#E7EBF0]/50">
              users watching
            </span>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-2 w-full">
          {Object.entries(coinData.priceChanges || {}).map(
            ([period, change]) => (
              <div key={period} className="flex flex-col items-center">
                <div className="text-[10px] text-[#E7EBF0]/70 font-medium">
                  {period === "24h"
                    ? "1D"
                    : period === "7d"
                      ? "1W"
                      : period === "30d"
                        ? "1M"
                        : period === "90d"
                          ? "3M"
                          : "1Y"}
                </div>
                <div
                  className={cn(
                    "text-xs font-semibold whitespace-nowrap",
                    change >= 0 ? "text-[#00D88A]" : "text-[#FF3B3B]",
                  )}
                >
                  {change >= 0 ? "+" : ""}
                  {change.toFixed(1)}%
                </div>
              </div>
            ),
          )}
        </div>
      </div>
    </div>
  );
}

export default LazyHoverContent;
