import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Coins, Star, BarChart, TrendingUp, Lock } from "lucide-react";
import { cn } from "@/lib/utils";
import CoinService, { Category, Chain } from "@/lib/services/CoinService";
import { useAuth } from "@/hooks/use-auth";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { useLocation } from "wouter";

export interface TopMoversFiltersState {
  marketCapRange: [number, number];
  projectScoreRange: [number, number];
  categories: string[];
  chains: string[];
  minimumGain: string;
}

interface TopMoversFiltersProps {
  onFilterChange: (filters: TopMoversFiltersState) => void;
  initialFilters?: TopMoversFiltersState;
  onClose?: () => void;
  className?: string;
}

export function TopMoversFilters({
  onFilterChange,
  initialFilters,
  onClose,
  className,
}: TopMoversFiltersProps) {
  // Authentication and subscription hooks
  const { isLoggedIn } = useAuth();
  const { canAccessFeature } = useSubscription();
  const { toast } = useToast();
  const { t } = useLanguage();
  const [, setLocation] = useLocation();

  // Default values in actual units (not millions)
  const defaultMarketCapMin = 1000000; // $1M
  const defaultMarketCapMax = 100000000000; // $100B

  // Convert from actual value to display value in millions with 2 decimal places
  const convertToDisplayValue = (actualValue: number): number => {
    return parseFloat((actualValue / 1000000).toFixed(2));
  };

  // Convert from display value (in millions) to actual value
  const convertToActualValue = (valueInMillions: number): number => {
    const result = valueInMillions * 1000000;
    console.log(`Converting display value ${valueInMillions}M to actual value: ${result}`);
    return result;
  };

  const [marketCapRange, setMarketCapRange] = React.useState<[number, number]>(
    initialFilters?.marketCapRange || [
      defaultMarketCapMin,
      defaultMarketCapMax,
    ],
  );

  // Display values for the inputs (in millions)
  const [minMarketCapDisplay, setMinMarketCapDisplay] = useState(
    convertToDisplayValue(marketCapRange[0]),
  );

  const [maxMarketCapDisplay, setMaxMarketCapDisplay] = useState(
    convertToDisplayValue(marketCapRange[1]),
  );

  const [projectScoreRange, setProjectScoreRange] = React.useState<
    [number, number]
  >(initialFilters?.projectScoreRange || [0, 100]);

  // Category IDs state
  const [selectedCategories, setSelectedCategories] = React.useState<string[]>(
    initialFilters?.categories || [],
  );

  const [selectedChains, setSelectedChains] = React.useState<string[]>(
    initialFilters?.chains || [],
  );

  // Minimum Gain filter state
  const [minimumGain, setMinimumGain] = React.useState<string>(
    initialFilters?.minimumGain || "Any"
  );

  // API data states
  const [categories, setCategories] = useState<Category[]>([]);
  const [chains, setChains] = useState<Chain[]>([]);
  const [isLoading, setIsLoading] = useState({
    categories: false,
    chains: false,
  });

  // UI states for dropdowns
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
  const [isChainsOpen, setIsChainsOpen] = useState(false);
  
  // Search states
  const [chainsSearchQuery, setChainsSearchQuery] = useState("");
  const [categoriesSearchQuery, setCategoriesSearchQuery] = useState("");

  // Click outside handler to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const categoriesEl = document.getElementById("categories-dropdown");
      const chainsEl = document.getElementById("chains-dropdown");

      if (categoriesEl && !categoriesEl.contains(event.target as Node)) {
        setIsCategoriesOpen(false);
      }

      if (chainsEl && !chainsEl.contains(event.target as Node)) {
        setIsChainsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch categories and chains from API
  useEffect(() => {
    const fetchFilterData = async () => {
      try {
        setIsLoading((prev) => ({ ...prev, categories: true }));
        const categoriesData = await CoinService.getCategories();
        setCategories(categoriesData);
        setIsLoading((prev) => ({ ...prev, categories: false }));
      } catch (error) {
        console.error("Error fetching categories:", error);
        setIsLoading((prev) => ({ ...prev, categories: false }));
      }

      try {
        setIsLoading((prev) => ({ ...prev, chains: true }));
        const chainsData = await CoinService.getChains();
        setChains(chainsData);
        setIsLoading((prev) => ({ ...prev, chains: false }));
      } catch (error) {
        console.error("Error fetching chains:", error);
        setIsLoading((prev) => ({ ...prev, chains: false }));
      }
    };

    fetchFilterData();
  }, []);

  // Update state if initialFilters changes
  React.useEffect(() => {
    if (initialFilters) {
      // Ensure we have valid values and not extremely small numbers
      let minValue = initialFilters.marketCapRange[0];
      let maxValue = initialFilters.marketCapRange[1];

      // If values are suspiciously small, use defaults
      if (minValue < 100000) {
        // Less than $0.1M
        minValue = defaultMarketCapMin;
      }

      if (maxValue < 1000000) {
        // Less than $1M
        maxValue = defaultMarketCapMax;
      }

      setMarketCapRange([minValue, maxValue]);

      // Update display values (convert to millions)
      setMinMarketCapDisplay(convertToDisplayValue(minValue));
      setMaxMarketCapDisplay(convertToDisplayValue(maxValue));

      setProjectScoreRange(initialFilters.projectScoreRange);
      setSelectedCategories(initialFilters.categories);
      setSelectedChains(initialFilters.chains);
      setMinimumGain(initialFilters.minimumGain);
    } else {
      // If no initial filters, explicitly set default values
      setMarketCapRange([defaultMarketCapMin, defaultMarketCapMax]);
      setMinMarketCapDisplay(1.00); // 1M
      setMaxMarketCapDisplay(100000.00); // 100,000M = 100B
      setMinimumGain("Any");
    }
  }, [initialFilters]);

  // Handle manual input change for min market cap
  const handleMinMarketCapChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = parseFloat(e.target.value);
    if (isNaN(inputValue) || inputValue < 0) return;

    const actualValue = convertToActualValue(inputValue);

    // Ensure min doesn't exceed max
    if (actualValue < marketCapRange[1]) {
      setMarketCapRange([actualValue, marketCapRange[1]]);
      setMinMarketCapDisplay(inputValue);
    }
  };

  // Map market cap value to slider position (0-100)
  const mapMarketCapToSlider = (marketCap: number): number => {
    // Using logarithmic scale for better UX with large ranges
    // Min value: 1M (10^6), Max value: 100B (10^11)
    const minLog = Math.log10(1000000); // log10(1M)
    const maxLog = Math.log10(100000000000); // log10(100B)

    // Ensure the market cap isn't below our minimum before taking log
    const safeMarketCap = Math.max(marketCap, 1000000);
    const valueLog = Math.log10(safeMarketCap);

    // Map to 0-100 range
    return ((valueLog - minLog) / (maxLog - minLog)) * 100;
  };

  // Map slider position (0-100) to market cap value
  const mapSliderToMarketCap = (sliderValue: number): number => {
    // Convert slider position back to market cap using inverse log mapping
    const minLog = Math.log10(1000000); // log10(1M)
    const maxLog = Math.log10(100000000000); // log10(100B)

    const valueLog = minLog + (sliderValue / 100) * (maxLog - minLog);
    const result = Math.round(Math.pow(10, valueLog));
    console.log(`Converting slider value ${sliderValue} to marketcap: ${result} (${result/1000000}M)`);
    return result;
  };

  // Handle manual input change for max market cap
  const handleMaxMarketCapChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = parseFloat(e.target.value);
    if (isNaN(inputValue) || inputValue < 0) return;

    const actualValue = convertToActualValue(inputValue);

    // Ensure max isn't below min
    if (actualValue > marketCapRange[0]) {
      setMarketCapRange([marketCapRange[0], actualValue]);
      setMaxMarketCapDisplay(inputValue);
    }
  };

  const handleApplyFilters = () => {
    // Check authentication and subscription status before applying filters
    if (!isLoggedIn) {
      toast({
        title: t("system.subscription.filterRestriction.title", "system", "Abonelik Gerekli"),
        description: "Filtreleri kullanmak için giriş yapmanız gerekiyor.",
        variant: "destructive",
      });
      setLocation("/login");
      return;
    }

    // Check if user has at least basic subscription (level 1 or higher)
    // Level 0 = Free, Level 1 = Basic, Level 2 = Pro, etc.
    if (!canAccessFeature(1)) {
      toast({
        title: t("system.subscription.filterRestriction.title", "system", "Abonelik Gerekli"),
        description: t("system.subscription.filterRestriction.message", "system", "Filtreleri kullanmak için en az Basic pakete sahip olmanız gerekiyor. Lütfen abonelik planınızı yükseltin."),
        variant: "destructive",
      });
      setLocation("/pricing");
      return;
    }

    // Ensure we're not sending extremely small values
    let [min, max] = marketCapRange;

    // Apply minimum thresholds
    if (min < 100000) min = defaultMarketCapMin;
    if (max < 1000000) max = defaultMarketCapMax;

    // Loglama ekleyelim
    console.log(`Applying filters with market cap range: ${min} to ${max}`);
    console.log(`Market cap in millions: ${min/1000000}M to ${max/1000000}M`);
    console.log(`Minimum gain: ${minimumGain}`);

    const filterState: TopMoversFiltersState = {
      marketCapRange: [min, max],
      projectScoreRange,
      categories: selectedCategories,
      chains: selectedChains,
      minimumGain,
    };

    onFilterChange(filterState);
  };

  const handleResetFilters = () => {
    // Define default values
    const defaultMarketCapRange: [number, number] = [
      defaultMarketCapMin,
      defaultMarketCapMax,
    ]; // 1M to 100B
    const defaultFilters: TopMoversFiltersState = {
      marketCapRange: defaultMarketCapRange,
      projectScoreRange: [0, 100],
      categories: [],
      chains: [],
      minimumGain: "Any",
    };

    // Update visual state with default values
    setMarketCapRange(defaultMarketCapRange);
    setMinMarketCapDisplay(1.00); // 1M
    setMaxMarketCapDisplay(100000.00); // 100,000M = 100B

    setProjectScoreRange(defaultFilters.projectScoreRange);
    setSelectedCategories(defaultFilters.categories);
    setSelectedChains(defaultFilters.chains);
    setMinimumGain(defaultFilters.minimumGain);

    // Create a special reset filter state with a flag to indicate this is a reset
    const resetFilterState: TopMoversFiltersState & { isReset?: boolean } = {
      ...defaultFilters,
      isReset: true, // Add flag to indicate this is a reset action
    };

    // Notify parent that filters should be completely reset
    onFilterChange(resetFilterState);

    // Close dialog if onClose is provided
    if (onClose) {
      onClose();
    }
  };

  // Run on initial component mount to ensure correct display values
  useEffect(() => {
    // Force initialize with proper values
    setMarketCapRange([defaultMarketCapMin, defaultMarketCapMax]);
    setMinMarketCapDisplay(1.00); // 1M
    setMaxMarketCapDisplay(100000.00); // 100,000M = 100B
  }, []);

  // CSS to hide input spinners
  const noSpinnerCSS = `
    /* Hide spinners for Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Hide spinners for Firefox */
    input[type=number] {
      -moz-appearance: textfield;
    }
  `;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
      className={cn(
        "w-full max-w-[95%] sm:max-w-3xl mx-auto px-4 sm:px-0 relative overflow-visible",
        className,
      )}
    >
      {/* Add the CSS to remove spinners */}
      <style>{noSpinnerCSS}</style>

      <Card className="bg-card/95 backdrop-blur-[2px] border-border/30 p-4 sm:p-6 space-y-6 sm:space-y-8 relative overflow-visible">
        <div className="space-y-6 sm:space-y-8">
          {/* Market Cap Range Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Coins className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">
{t("filters.marketCapRange", "", "Market Cap Range (Million)")}
              </Label>
            </div>
            <div className="px-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
                {/* Min Market Cap Input */}
                <div className="relative flex">
                  <input
                    type="number"
                    value={minMarketCapDisplay}
                    onChange={handleMinMarketCapChange}
                    className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                    min="1"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                    M
                  </div>
                </div>

                {/* Max Market Cap Input */}
                <div className="relative flex">
                  <input
                    type="number"
                    value={maxMarketCapDisplay}
                    onChange={handleMaxMarketCapChange}
                    className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                    min="1"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                    M
                  </div>
                </div>
              </div>
              <div className="pt-4">
                <p className="text-xs text-muted-foreground mb-2">
                  Move the slider to adjust the range:
                </p>
                <Slider
                  value={[
                    mapMarketCapToSlider(marketCapRange[0]),
                    mapMarketCapToSlider(marketCapRange[1]),
                  ]}
                  min={0}
                  max={100}
                  step={1}
                  onValueChange={(value) => {
                    const [sliderMin, sliderMax] = value as [number, number];
                    const newMinMarketCap = mapSliderToMarketCap(sliderMin);
                    const newMaxMarketCap = mapSliderToMarketCap(sliderMax);

                    setMarketCapRange([newMinMarketCap, newMaxMarketCap]);
                    setMinMarketCapDisplay(
                      convertToDisplayValue(newMinMarketCap),
                    );
                    setMaxMarketCapDisplay(
                      convertToDisplayValue(newMaxMarketCap),
                    );
                  }}
                  className="my-2 h-0.5 mt-1"
                />
              </div>
            </div>
          </div>

          {/* Project Score Range Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">
{t("filters.projectScoreRange", "", "Project Score Range")}
              </Label>
            </div>
            <div className="px-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
                <input
                  type="number"
                  value={projectScoreRange[0]}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 100 && value <= projectScoreRange[1]) {
                      setProjectScoreRange([value, projectScoreRange[1]]);
                    }
                  }}
                  min="0"
                  max="100"
                  className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                />
                <input
                  type="number"
                  value={projectScoreRange[1]}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 100 && value >= projectScoreRange[0]) {
                      setProjectScoreRange([projectScoreRange[0], value]);
                    }
                  }}
                  min="0"
                  max="100"
                  className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm transition-standard"
                />
              </div>
              <Slider
                value={projectScoreRange}
                min={0}
                max={100}
                step={1}
                onValueChange={(value) => setProjectScoreRange(value as [number, number])}
                className="my-2 h-0.5 mt-4"
              />
            </div>
          </div>

          {/* Categories Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <BarChart className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">{t("filters.categories", "", "Categories")}</Label>
            </div>
            <div className="px-0 relative">
              <div
                id="categories-dropdown"
                className="w-full relative cursor-pointer"
              >
                <div
                  className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm flex items-center justify-between min-h-[40px] transition-standard"
                  onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
                >
                  <div className="flex-1 overflow-hidden text-ellipsis">
                    {selectedCategories.length === 0 ? (
                      <span className="text-muted-foreground">{t("filters.selectCategories", "", "Select categories (You can choose multiple options)")}</span>
                    ) : (
                      <div className="flex flex-wrap gap-1">
                        {selectedCategories.map((catId) => {
                          const category = categories.find((c) => c.id === catId);
                          return (
                            <div
                              key={catId}
                              className="bg-primary/10 text-primary px-2 py-0.5 rounded-md text-xs flex items-center"
                            >
                              {category ? category.name : catId}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                  <div className="ml-2 text-muted-foreground">▼</div>
                </div>

                {isCategoriesOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.15 }}
                    className="absolute top-full left-0 right-0 mt-1 bg-background/95 backdrop-blur-sm border border-border/50 rounded-md shadow-lg z-50 max-h-[320px] overflow-y-auto scrollbar-custom"
                  >
                    {/* Search input for categories */}
                    <div className="p-2 sticky top-0 bg-background/95 backdrop-blur-sm border-b border-border/30 z-10">
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Search categories..."
                          value={categoriesSearchQuery}
                          onChange={(e) => setCategoriesSearchQuery(e.target.value)}
                          className="w-full bg-background border border-border/50 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary/50 focus:border-primary/50 pr-8"
                          onClick={(e) => e.stopPropagation()}
                        />
                        {categoriesSearchQuery && (
                          <button
                            type="button"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCategoriesSearchQuery('');
                            }}
                          >
                            ✕
                          </button>
                        )}
                      </div>
                    </div>
                    
                    {/* Select/Deselect All buttons */}
                    <div className="p-2 flex justify-between items-center border-b border-border/30">
                      <span className="text-xs text-muted-foreground">
                        {selectedCategories.length} selected
                      </span>
                      <div className="flex gap-2">
                        <button
                          type="button"
                          className="text-xs text-primary hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Get filtered categories based on search
                            const filteredCategoryIds = categories
                              .filter(category => 
                                category.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                              )
                              .map(category => category.id);
                            
                            // Add all filtered categories to selection (without duplicates)
                            const combinedCategories = [...selectedCategories, ...filteredCategoryIds];
                            const uniqueCategories = Array.from(new Set(combinedCategories));
                            setSelectedCategories(uniqueCategories);
                          }}
                        >
                          Select All
                        </button>
                        <span className="text-xs text-muted-foreground">|</span>
                        <button
                          type="button"
                          className="text-xs text-primary hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (categoriesSearchQuery) {
                              // If there's a search query, only deselect the filtered categories
                              const filteredCategoryIds = categories
                                .filter(category => 
                                  category.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                                )
                                .map(category => category.id);
                              
                              setSelectedCategories(selectedCategories.filter(id => !filteredCategoryIds.includes(id)));
                            } else {
                              // If no search query, clear all selections
                              setSelectedCategories([]);
                            }
                          }}
                        >
                          {categoriesSearchQuery ? "Deselect Filtered" : "Deselect All"}
                        </button>
                      </div>
                    </div>
                    
                    {isLoading.categories ? (
                      <div className="p-3 text-center text-sm">
                        <div className="animate-spin h-4 w-4 border-2 border-primary/80 border-t-transparent rounded-full inline-block mr-2"></div>
                        Loading categories...
                      </div>
                    ) : categories.length === 0 ? (
                      <div className="p-3 text-center text-sm text-muted-foreground">
                        No categories available
                      </div>
                    ) : (
                      <div className="p-1">
                        {/* Filter categories based on search query */}
                        {categories
                          .filter(category => 
                            category.name.toLowerCase().includes(categoriesSearchQuery.toLowerCase())
                          )
                          .map((category) => (
                            <div
                              key={category.id}
                              className={cn(
                                "px-3 py-2 text-sm rounded-md cursor-pointer transition-colors",
                                selectedCategories.includes(category.id)
                                  ? "bg-primary/10 text-primary"
                                  : "hover:bg-muted/80 text-foreground"
                              )}
                              onClick={() => {
                                const isSelected = selectedCategories.includes(category.id);
                                if (isSelected) {
                                  setSelectedCategories(selectedCategories.filter(id => id !== category.id));
                                } else {
                                  setSelectedCategories([...selectedCategories, category.id]);
                                }
                              }}
                            >
                              {category.name}
                            </div>
                          ))}
                      </div>
                    )}
                  </motion.div>
                )}
              </div>
            </div>
          </div>

          {/* Chains Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Coins className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">{t("filters.chains", "", "Chains")}</Label>
            </div>
            <div className="px-0 relative">
              <div
                id="chains-dropdown"
                className="w-full relative cursor-pointer"
              >
                <div
                  className="w-full bg-background/50 border border-border/50 rounded-lg px-4 py-2 text-sm flex items-center justify-between min-h-[40px] transition-standard"
                  onClick={() => setIsChainsOpen(!isChainsOpen)}
                >
                  <div className="flex-1 overflow-hidden text-ellipsis">
                    {selectedChains.length === 0 ? (
                      <span className="text-muted-foreground">Select chains (You can choose multiple options)</span>
                    ) : (
                      <div className="flex flex-wrap gap-1">
                        {selectedChains.map((chain) => (
                          <div
                            key={chain}
                            className="bg-primary/10 text-primary px-2 py-0.5 rounded-md text-xs flex items-center"
                          >
                            {chain}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="ml-2 text-muted-foreground">▼</div>
                </div>

                {isChainsOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.15 }}
                    className="absolute top-full left-0 right-0 mt-1 bg-background/95 backdrop-blur-sm border border-border/50 rounded-md shadow-lg z-50 max-h-[320px] overflow-y-auto scrollbar-custom"
                  >
                    {/* Search input for chains */}
                    <div className="p-2 sticky top-0 bg-background/95 backdrop-blur-sm border-b border-border/30 z-10">
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Search chains..."
                          value={chainsSearchQuery}
                          onChange={(e) => setChainsSearchQuery(e.target.value)}
                          className="w-full bg-background border border-border/50 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary/50 focus:border-primary/50 pr-8"
                          onClick={(e) => e.stopPropagation()}
                        />
                        {chainsSearchQuery && (
                          <button
                            type="button"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                            onClick={(e) => {
                              e.stopPropagation();
                              setChainsSearchQuery('');
                            }}
                          >
                            ✕
                          </button>
                        )}
                      </div>
                    </div>
                    
                    {/* Select/Deselect All buttons */}
                    <div className="p-2 flex justify-between items-center border-b border-border/30">
                      <span className="text-xs text-muted-foreground">
                        {selectedChains.length} selected
                      </span>
                      <div className="flex gap-2">
                        <button
                          type="button"
                          className="text-xs text-primary hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Get filtered chains based on search
                            const filteredChains = chains
                              .filter(chain => 
                                chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                              )
                              .map(chain => chain.name);
                            
                            // Add all filtered chains to selection (without duplicates)
                            const combinedChains = [...selectedChains, ...filteredChains];
                            const uniqueChains = Array.from(new Set(combinedChains));
                            setSelectedChains(uniqueChains);
                          }}
                        >
                          Select All
                        </button>
                        <span className="text-xs text-muted-foreground">|</span>
                        <button
                          type="button"
                          className="text-xs text-primary hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (chainsSearchQuery) {
                              // If there's a search query, only deselect the filtered chains
                              const filteredChains = chains
                                .filter(chain => 
                                  chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                                )
                                .map(chain => chain.name);
                              
                              setSelectedChains(selectedChains.filter(name => !filteredChains.includes(name)));
                            } else {
                              // If no search query, clear all selections
                              setSelectedChains([]);
                            }
                          }}
                        >
                          {chainsSearchQuery ? "Deselect Filtered" : "Deselect All"}
                        </button>
                      </div>
                    </div>
                    
                    {isLoading.chains ? (
                      <div className="p-3 text-center text-sm">
                        <div className="animate-spin h-4 w-4 border-2 border-primary/80 border-t-transparent rounded-full inline-block mr-2"></div>
                        Loading chains...
                      </div>
                    ) : chains.length === 0 ? (
                      <div className="p-3 text-center text-sm text-muted-foreground">
                        No chains available
                      </div>
                    ) : (
                      <div className="p-1">
                        {/* Filter chains based on search query */}
                        {chains
                          .filter(chain => 
                            chain.name.toLowerCase().includes(chainsSearchQuery.toLowerCase())
                          )
                          .map((chain) => (
                            <div
                              key={chain.name}
                              className={cn(
                                "px-3 py-2 text-sm rounded-md cursor-pointer transition-colors",
                                selectedChains.includes(chain.name)
                                  ? "bg-primary/10 text-primary"
                                  : "hover:bg-muted/80 text-foreground"
                              )}
                              onClick={() => {
                                const isSelected = selectedChains.includes(chain.name);
                                if (isSelected) {
                                  setSelectedChains(selectedChains.filter(name => name !== chain.name));
                                } else {
                                  setSelectedChains([...selectedChains, chain.name]);
                                }
                              }}
                            >
                              {chain.name}
                            </div>
                          ))}
                      </div>
                    )}
                  </motion.div>
                )}
              </div>
            </div>
          </div>

          {/* Minimum Gain % Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-primary" />
              <Label className="text-base font-medium">
                Minimum Gain %
              </Label>
            </div>
            <div className="px-0">
              <Select
                value={minimumGain}
                onValueChange={(value) => setMinimumGain(value)}
              >
                <SelectTrigger className="w-full bg-background/50 border border-border/50 rounded-lg text-sm">
                  <SelectValue placeholder="Select minimum gain" />
                </SelectTrigger>
                <SelectContent>
                  {["Any", "> 5%", "> 10%", "> 20%", "> 50%", "> 100%"].map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="pt-4 flex gap-4 justify-end">
            <Button
              variant="outline"
              onClick={handleResetFilters}
              className="px-6 py-2 border-border/50 text-foreground/80 hover:bg-background/80"
            >
{t("filters.resetFilters", "", "Reset Filters")}
            </Button>
            <Button
              onClick={handleApplyFilters}
              className="px-6 py-2 bg-primary/80 hover:bg-primary text-primary-foreground"
            >
{t("filters.applyFilters", "", "Apply Filters")}
            </Button>
          </div>
        </div>
      </Card>
    </motion.div>
  );
}