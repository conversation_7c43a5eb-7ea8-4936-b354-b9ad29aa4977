import React, { useState, useEffect, useRef, createRef } from 'react';
import { useLocation } from 'wouter';
import { Button } from './button';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bot,
  X,
  Minimize2,
  Maximize2,
  MessageSquareText,
  ChevronRight,
  Volume2,
  VolumeX,
  PlusCircle,
  Image as ImageIcon,
  HelpCircle,
  Settings,
  Mic,
  Zap,
  Code,
  BarChart4
} from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from './avatar';
import { useIsMobile } from '@/hooks/use-mobile';
import { useLanguage } from '@/contexts/LanguageContext';
import { useToast } from '@/hooks/use-toast';

interface PageContextType {
  title: string;
  description?: string;
  suggestedQuestions: string[];
  hints?: string[];
  route?: string;
}

interface FloatingAssistantProps {
  className?: string;
  context?: PageContextType;
}

interface Message {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: Date;
  image?: string; // Base64 encoded image data
  responseType?: 'text' | 'chart' | 'code' | 'image';
}

interface PageContext {
  [key: string]: {
    title: string;
    suggestedQuestions: string[];
    hints: string[];
    route?: string;
  }
}

export function FloatingAssistant({ className, context }: FloatingAssistantProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentLocation] = useLocation();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [responseFormat, setResponseFormat] = useState<'text' | 'chart' | 'code'>('text');
  const [userPreferences, setUserPreferences] = useState({
    preferShortAnswers: false,
    expertiseLevel: 'beginner' as 'beginner' | 'intermediate' | 'expert',
    language: 'en' as string,
  });
  const [isRecording, setIsRecording] = useState(false);
  const [memoryEnabled, setMemoryEnabled] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();
  const { t } = useLanguage();
  const { toast } = useToast();

  // Context-aware suggestions for different pages
  const pageContextMap: PageContext = {
    '/coins': {
      title: t('assistant.contextCoins.title', 'assistant', 'Coin List Assistant'),
      suggestedQuestions: [
        t('assistant.contextCoins.q1', 'assistant', 'How to filter coins by score?'),
        t('assistant.contextCoins.q2', 'assistant', 'What does the total score mean?'),
        t('assistant.contextCoins.q3', 'assistant', 'How to add coins to my watchlist?'),
        t('assistant.contextCoins.q4', 'assistant', 'Show me a guided tour'),
      ],
      hints: [
        t('assistant.contextCoins.hint1', 'assistant', 'Try sorting by different metrics'),
        t('assistant.contextCoins.hint2', 'assistant', 'You can compare coins by clicking "Compare"'),
      ]
    },
    '/compare': {
      title: t('assistant.contextCompare.title', 'assistant', 'Cryptocurrency Comparison Assistant'),
      suggestedQuestions: [
        t('assistant.contextCompare.q1', 'assistant', 'How to add more cryptocurrencies to compare?'),
        t('assistant.contextCompare.q2', 'assistant', 'What do the category scores mean?'),
        t('assistant.contextCompare.q3', 'assistant', 'How to interpret the metric rankings?'),
        t('assistant.contextCompare.q4', 'assistant', 'Show me a guided tour'),
      ],
      hints: [
        t('assistant.contextCompare.hint1', 'assistant', 'You can compare up to 4 cryptocurrencies at once'),
        t('assistant.contextCompare.hint2', 'assistant', 'Click on a category in the sidebar to focus on specific metrics'),
      ]
    },
    '/upcoming': {
      title: t('assistant.contextUpcoming.title', 'assistant', 'Upcoming IDOs Assistant'),
      suggestedQuestions: [
        t('assistant.contextUpcoming.q1', 'assistant', 'How to filter upcoming projects?'),
        t('assistant.contextUpcoming.q2', 'assistant', 'What do the score badges mean?'),
        t('assistant.contextUpcoming.q3', 'assistant', 'How to get notified about new IDOs?'),
        t('assistant.contextUpcoming.q4', 'assistant', 'Show me a guided tour'),
      ],
      hints: [
        t('assistant.contextUpcoming.hint1', 'assistant', 'Filter by sale type to find specific IDO types'),
        t('assistant.contextUpcoming.hint2', 'assistant', 'Sort by score to find the most promising projects'),
      ]
    },
    '/watchlist': {
      title: t('assistant.contextWatchlist.title', 'assistant', 'Watchlist Assistant'),
      suggestedQuestions: [
        t('assistant.contextWatchlist.q1', 'assistant', 'How to create a custom watchlist?'),
        t('assistant.contextWatchlist.q2', 'assistant', 'How to share my watchlist?'),
        t('assistant.contextWatchlist.q3', 'assistant', 'Can I set price alerts?'),
        t('assistant.contextWatchlist.q4', 'assistant', 'Help me understand this page'),
      ],
      hints: [
        t('assistant.contextWatchlist.hint1', 'assistant', 'Create multiple watchlists for different strategies'),
        t('assistant.contextWatchlist.hint2', 'assistant', 'Regular reviews of your watchlist can improve performance'),
      ]
    },
    // Add more pages as needed
  };

  // Get current page context or default
  const getCurrentPageContext = () => {
    // If custom context is provided, use that instead
    if (context) {
      return {
        title: context.title,
        suggestedQuestions: context.suggestedQuestions,
        hints: context.hints || [
          t('assistant.contextDefault.hint1', 'assistant', 'Explore our tools using the navigation menu'),
          t('assistant.contextDefault.hint2', 'assistant', 'Check out trending coins in the home page'),
        ],
        route: context.route
      };
    }
    
    // Otherwise find the matching context for the current path
    const matchingPath = Object.keys(pageContextMap).find(path => 
      currentLocation.startsWith(path)
    );
    
    return matchingPath 
      ? { ...pageContextMap[matchingPath], route: matchingPath }
      : {
          title: t('assistant.contextDefault.title', 'assistant', 'AI Assistant'),
          suggestedQuestions: [
            t('assistant.contextDefault.q1', 'assistant', 'What features are available?'),
            t('assistant.contextDefault.q2', 'assistant', 'How to get started?'),
            t('assistant.contextDefault.q3', 'assistant', 'Can you explain how scores work?'),
            t('assistant.contextDefault.q4', 'assistant', 'Show me a guided tour'),
          ],
          hints: [
            t('assistant.contextDefault.hint1', 'assistant', 'Explore our tools using the navigation menu'),
            t('assistant.contextDefault.hint2', 'assistant', 'Check out trending coins in the home page'),
          ],
          route: currentLocation
        };
  };

  const currentContext = getCurrentPageContext();

  // Welcome message based on current page context
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage = {
        id: 'welcome-' + Date.now(),
        content: `${t('assistant.welcome', 'assistant', 'Welcome to')} ${currentContext.title}! ${t('assistant.howCanIHelp', 'assistant', 'How can I help you today?')}`,
        type: 'assistant' as const,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [currentLocation]);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isOpen]);

  // Focus input when assistant opens
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      type: 'user',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    
    // Simulate AI thinking
    setTimeout(() => {
      // AI response logic would go here
      // For now, we'll simulate a generic response
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: generateResponse(inputValue, currentContext),
        type: 'assistant',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
      
      // Play sound if not muted
      if (!isMuted) {
        playNotificationSound();
      }
    }, 1500);
  };

  // Generate a simulated response - in a real app, this would call your AI service
  const generateResponse = (query: string, context: typeof currentContext) => {
    // Simple keyword matching for demo purposes
    const lowercaseQuery = query.toLowerCase();
    
    // For compare page specific queries
    if (context.route?.startsWith('/compare')) {
      if (lowercaseQuery.includes('add') || lowercaseQuery.includes('select') || lowercaseQuery.includes('cryptocurrency')) {
        return t('assistant.responses.addCrypto', 'assistant', 'To add cryptocurrencies for comparison, use the selector at the top of the page. You can search for coins by name or symbol, and add up to 4 different cryptocurrencies to compare at once.');
      }
      
      if (lowercaseQuery.includes('category') || lowercaseQuery.includes('categories')) {
        return t('assistant.responses.categories', 'assistant', 'The comparison tool organizes metrics into 5 main categories: Tokenomics (supply and distribution), Security (code and vulnerabilities), Social Media (community metrics), Market (price action and liquidity), and Insights (analytical metrics). Click on any category in the sidebar to view related metrics.');
      }
      
      if (lowercaseQuery.includes('metrics') || lowercaseQuery.includes('rankings')) {
        return t('assistant.responses.metrics', 'assistant', 'Each metric card shows how the selected cryptocurrencies compare on that specific measure. The cryptocurrency with the highest score for each metric is highlighted at the top. The color coding (green to red) indicates performance from excellent to poor.');
      }
      
      if (lowercaseQuery.includes('maximum') || lowercaseQuery.includes('limit') || (lowercaseQuery.includes('how many') && lowercaseQuery.includes('coins'))) {
        return t('assistant.responses.maxCoins', 'assistant', 'You can compare up to 4 cryptocurrencies at once. This limit ensures that the comparison remains clear and visually comprehensible.');
      }
    }
    
    if (lowercaseQuery.includes('filter') || lowercaseQuery.includes('sort')) {
      return t('assistant.responses.filter', 'assistant', 'You can filter or sort coins using the controls at the top of the coin list. Click on the filter icon to open more advanced filtering options.');
    }
    
    if (lowercaseQuery.includes('score') || lowercaseQuery.includes('rating')) {
      return t('assistant.responses.score', 'assistant', 'Our proprietary scoring system evaluates cryptocurrencies across multiple categories: Tokenomics, Security, Social, Market, and Technology. Each contributes to the total score shown in the list.');
    }
    
    if (lowercaseQuery.includes('watchlist') || lowercaseQuery.includes('favorite')) {
      return t('assistant.responses.watchlist', 'assistant', 'To add a coin to your watchlist, click the star icon next to any coin in the list. You can manage your watchlists from the Watchlist page accessible from the main navigation.');
    }
    
    if (lowercaseQuery.includes('tour') || lowercaseQuery.includes('guide') || lowercaseQuery.includes('help me') || lowercaseQuery.includes('show me around')) {
      // Trigger the appropriate tour based on the current page or custom context
      setTimeout(() => {
        let eventName = "show-coinlist-tour"; // Default tour event
        
        // First check if we have a custom context with a specific route
        if (context?.route) {
          if (context.route.startsWith('/upcoming')) {
            eventName = "show-upcoming-ido-tour";
          } else if (context.route.startsWith('/coins')) {
            eventName = "show-coinlist-tour";
          } else if (context.route.startsWith('/compare')) {
            eventName = "show-compare-tour";
          }
        } 
        // Otherwise check current location
        else if (currentLocation.startsWith('/upcoming')) {
          eventName = "show-upcoming-ido-tour";
        } else if (currentLocation.startsWith('/coins')) {
          eventName = "show-coinlist-tour";
        } else if (currentLocation.startsWith('/compare')) {
          eventName = "show-compare-tour";
        }
        
        const event = new CustomEvent(eventName);
        document.dispatchEvent(event);
        

      }, 500);
      
      return t('assistant.responses.tour', 'assistant', 'I\'d be happy to guide you through the platform! I\'ve just started an interactive tour that will walk you through the main features. Follow along with the tour to learn more about what you can do here.');
    }

    // Default response
    return t('assistant.responses.default', 'assistant', 'That\'s a great question! Let me help you with that. Our platform offers comprehensive analysis of cryptocurrencies with detailed metrics and real-time data. Is there anything specific about this feature you\'d like to know more about?');
  };

  const playNotificationSound = () => {
    // In a real implementation, you would play a sound here
  };

  const handleSuggestedQuestion = (question: string) => {
    setInputValue(question);
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  const toggleAssistant = () => {
    if (!isOpen) {
      setIsOpen(true);
      setIsMinimized(false);
    } else {
      setIsMinimized(!isMinimized);
    }
  };

  const closeAssistant = () => {
    setIsOpen(false);
  };

  const toggleMute = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMuted(!isMuted);
  };
  
  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: t('assistant.imageTooBig', 'assistant', 'Image too large'),
        description: t('assistant.imageSizeLimit', 'assistant', 'Please upload an image smaller than 5MB.'),
        variant: 'destructive'
      });
      return;
    }
    
    // Convert to base64
    const reader = new FileReader();
    reader.onload = (e) => {
      const base64 = e.target?.result as string;
      setSelectedImage(base64);
      
      // Add image to message
      const userMessage: Message = {
        id: Date.now().toString(),
        content: t('assistant.imageSent', 'assistant', 'Image sent'),
        type: 'user',
        timestamp: new Date(),
        image: base64
      };
      
      setMessages(prev => [...prev, userMessage]);
      setIsTyping(true);
      
      // Simulate AI response to image
      setTimeout(() => {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: t('assistant.imageResponse', 'assistant', 'I see the image you shared. What would you like to know about it?'),
          type: 'assistant',
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, aiResponse]);
        setIsTyping(false);
        setSelectedImage(null);
        
        // Clear file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        
        // Play sound if not muted
        if (!isMuted) {
          playNotificationSound();
        }
      }, 1500);
    };
    reader.readAsDataURL(file);
  };
  
  // Toggle short answer mode
  const toggleShortAnswers = () => {
    setUserPreferences(prev => ({
      ...prev,
      preferShortAnswers: !prev.preferShortAnswers
    }));
    
    toast({
      title: userPreferences.preferShortAnswers 
        ? t('assistant.verboseMode', 'assistant', 'Verbose mode enabled') 
        : t('assistant.conciseMode', 'assistant', 'Concise mode enabled'),
      description: userPreferences.preferShortAnswers
        ? t('assistant.verboseModeDesc', 'assistant', 'I\'ll provide more detailed answers now.')
        : t('assistant.conciseModeDesc', 'assistant', 'I\'ll keep my answers brief and to the point.')
    });
  };
  
  // Handle voice input (simulate for now)
  const toggleVoiceInput = () => {
    setIsRecording(!isRecording);
    
    if (!isRecording) {
      toast({
        title: t('assistant.listeningStarted', 'assistant', 'Listening...'),
        description: t('assistant.speakNow', 'assistant', 'Speak now, I\'m listening.'),
      });
      
      // Simulate voice recognition after 3 seconds
      setTimeout(() => {
        setIsRecording(false);
        setInputValue("What are the top trending coins today?");
        
        toast({
          title: t('assistant.listeningComplete', 'assistant', 'Listening complete'),
          description: t('assistant.processingVoice', 'assistant', 'Processing your request.'),
        });
        
        // Submit the voice input
        setTimeout(() => {
          handleSendMessage();
        }, 500);
      }, 3000);
    } else {
      toast({
        title: t('assistant.listeningCancelled', 'assistant', 'Listening cancelled'),
      });
    }
  };
  
  // Set the preferred response format
  const handleResponseFormatChange = (format: 'text' | 'chart' | 'code') => {
    // Set the response format state
    setResponseFormat(format);
    
    // Create a simulated response to show the user the new format
    const formatMessages = {
      'text': t('assistant.responseFormatText', 'assistant', 'I\'ll provide text responses.'),
      'chart': t('assistant.responseFormatChart', 'assistant', 'I\'ll include charts and visualizations when possible.'),
      'code': t('assistant.responseFormatCode', 'assistant', 'I\'ll include code examples when relevant.')
    };
    
    // Show toast message
    toast({
      title: t('assistant.responseFormatChanged', 'assistant', 'Response format updated'),
      description: formatMessages[format]
    });
    
    // Add an example message if changing to chart or code
    if (format !== 'text') {
      // Create example message with proper typing
      const exampleMessage: Message = {
        id: Date.now().toString(),
        content: format === 'chart' 
          ? t('assistant.chartExample', 'assistant', 'Here\'s how chart responses will look:')
          : t('assistant.codeExample', 'assistant', 'Here\'s how code examples will look:'),
        type: 'assistant',
        timestamp: new Date(),
        responseType: format as 'chart' | 'code'
      };
      
      // Add to messages
      setMessages(prev => [...prev, exampleMessage]);
    }
  };

  return (
    <div className={cn("fixed bottom-4 right-4 z-50 flex flex-col items-end", className)}>
      {/* Floating Button */}
      <Button
        onClick={toggleAssistant}
        variant="default"
        size="icon"
        className={cn(
          "rounded-full shadow-lg h-12 w-12 bg-gradient-to-tr from-primary to-primary/80 transition-all duration-300",
          isOpen ? "rotate-0" : "rotate-12 hover:rotate-0",
          !isOpen && "animate-pulse"
        )}
      >
        {isOpen ? (
          isMinimized ? <Maximize2 className="h-5 w-5" /> : <Minimize2 className="h-5 w-5" />
        ) : (
          <Bot className="h-5 w-5" />
        )}
      </Button>

      {/* Assistant Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ 
              opacity: 1, 
              y: 0, 
              scale: 1,
              height: isMinimized ? 'auto' : isMobile ? '80vh' : '500px'
            }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "bg-card mt-2 rounded-lg shadow-lg overflow-hidden border border-border w-[350px]",
              isMinimized ? "max-h-16" : isMobile ? "max-h-[80vh]" : "max-h-[500px]"
            )}
          >
            {/* Header - completely redesigned */}
            <div className="bg-primary/10 border-b border-border">
              {/* First row with avatar, title, and status */}
              <div className="flex items-center px-3 pt-2.5 pb-1">
                <Avatar className="h-6 w-6 flex-shrink-0">
                  <AvatarImage src="/ai-assistant-avatar.png" alt="AI Assistant" />
                  <AvatarFallback className="bg-primary/20 flex items-center justify-center">
                    <Bot className="h-3 w-3 text-primary" />
                  </AvatarFallback>
                </Avatar>
                <div className="ml-2 overflow-hidden">
                  <h3 className="font-medium text-xs truncate">{currentContext.title}</h3>
                  <p className="text-[10px] text-muted-foreground truncate">
                    {isTyping ? 
                      t('assistant.typing', 'assistant', 'Typing...') : 
                      t('assistant.online', 'assistant', 'Online')}
                  </p>
                </div>
              </div>
              
              {/* Second row with buttons only */}
              <div className="flex justify-end px-2 py-1.5">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 rounded p-0"
                  onClick={toggleMute}
                >
                  {isMuted ? 
                    <VolumeX className="h-3.5 w-3.5" /> : 
                    <Volume2 className="h-3.5 w-3.5" />}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 rounded p-0 ml-2"
                  onClick={closeAssistant}
                >
                  <X className="h-3.5 w-3.5" />
                </Button>
              </div>
            </div>

            {/* Minimized view */}
            {isMinimized && (
              <div className="p-3 flex items-center">
                <p className="text-sm text-muted-foreground truncate">
                  {messages.length > 0 
                    ? messages[messages.length - 1].content
                    : t('assistant.minimizedDefault', 'assistant', 'How can I help you?')}
                </p>
              </div>
            )}

            {/* Full view */}
            {!isMinimized && (
              <>
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-3 space-y-4" style={{ maxHeight: 'calc(100% - 140px)', height: isMobile ? 'calc(80vh - 140px)' : '360px' }}>
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={cn(
                        "flex items-start gap-2 animate-in fade-in-0 zoom-in-95 duration-300",
                        message.type === 'user' ? "flex-row-reverse" : ""
                      )}
                    >
                      {message.type === 'assistant' && (
                        <Avatar className="h-8 w-8 bg-primary/10">
                          <AvatarFallback className="bg-primary/20 flex items-center justify-center">
                            <Bot className="h-4 w-4 text-primary" />
                          </AvatarFallback>
                        </Avatar>
                      )}
                      
                      <div
                        className={cn(
                          "rounded-lg px-3 py-2 max-w-[80%] text-sm",
                          message.type === 'user' 
                            ? "bg-primary text-primary-foreground ml-auto" 
                            : "bg-muted"
                        )}
                      >
                        {message.content}
                        {message.image && (
                          <div className="mt-2">
                            <img 
                              src={message.image} 
                              alt="Uploaded" 
                              className="rounded-md max-w-full max-h-[200px] object-contain"
                            />
                          </div>
                        )}
                        {message.responseType === 'chart' && (
                          <div className="mt-2 p-2 bg-background rounded-md">
                            <BarChart4 className="h-24 w-full text-muted-foreground opacity-70" />
                            <p className="text-xs text-center mt-1 text-muted-foreground">Sample Chart Visualization</p>
                          </div>
                        )}
                        {message.responseType === 'code' && (
                          <div className="mt-2 p-2 bg-background rounded-md font-mono text-xs overflow-x-auto">
                            <pre className="text-muted-foreground">
                              {`// Sample crypto data fetching code
const fetchCryptoData = async (symbol) => {
  const response = await fetch(
    \`https://api.example.com/v1/crypto/\${symbol}\`
  );
  return response.json();
};`}
                            </pre>
                          </div>
                        )}
                      </div>
                      
                      {message.type === 'user' && (
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="bg-secondary flex items-center justify-center">
                            <span className="text-xs text-secondary-foreground">U</span>
                          </AvatarFallback>
                        </Avatar>
                      )}
                    </div>
                  ))}
                  
                  {isTyping && (
                    <div className="flex items-start gap-2">
                      <Avatar className="h-8 w-8 bg-primary/10">
                        <AvatarFallback className="bg-primary/20 flex items-center justify-center">
                          <Bot className="h-4 w-4 text-primary" />
                        </AvatarFallback>
                      </Avatar>
                      <div className="bg-muted rounded-lg px-3 py-2 text-sm">
                        <span className="inline-block w-10">
                          <span className="animate-pulse">••••</span>
                        </span>
                      </div>
                    </div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>
                
                {/* Suggested questions */}
                {messages.length < 3 && (
                  <div className="px-3 py-2 border-t border-border">
                    <p className="text-xs font-medium text-muted-foreground mb-2">
                      {t('assistant.suggested', 'assistant', 'Suggested questions:')}
                    </p>
                    <div className="flex flex-col w-full space-y-1">
                      {currentContext.suggestedQuestions.map((question, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestedQuestion(question)}
                          className="text-xs bg-muted hover:bg-muted/80 text-foreground rounded-md px-3 py-1.5 flex items-start gap-1 transition-colors w-full break-words whitespace-normal text-left"
                        >
                          <HelpCircle className="h-3 w-3 mt-0.5 shrink-0" />
                          <span className="inline-block">{question}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Input */}
                <div className="border-t border-border p-3">
                  {/* Additional features toolbar */}
                  <div className="flex items-center justify-between px-1 py-1.5 border-b border-border">
                    <div className="flex items-center gap-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs rounded-full flex items-center gap-1"
                        onClick={toggleShortAnswers}
                        title={userPreferences.preferShortAnswers ? "Switch to detailed responses" : "Switch to concise responses"}
                      >
                        <Zap className="h-3 w-3" />
                        <span>{userPreferences.preferShortAnswers ? "Concise" : "Detailed"}</span>
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 rounded-full"
                        onClick={() => handleResponseFormatChange('chart')}
                        title="Prefer chart visualizations when possible"
                      >
                        <BarChart4 className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 rounded-full"
                        onClick={() => handleResponseFormatChange('code')}
                        title="Prefer code examples"
                      >
                        <Code className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="flex items-center">
                      <Button
                        type="button"
                        variant={isRecording ? "destructive" : "ghost"}
                        size="sm"
                        className={cn("h-6 w-6 rounded-full", isRecording && "animate-pulse")}
                        onClick={toggleVoiceInput}
                        title="Voice input"
                      >
                        <Mic className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 rounded-full"
                        onClick={() => {
                          setMemoryEnabled(!memoryEnabled);
                          toast({
                            title: memoryEnabled ? "Context memory disabled" : "Context memory enabled",
                            description: memoryEnabled 
                              ? "I won't remember previous messages in this conversation." 
                              : "I'll remember our conversation to provide better assistance."
                          });
                        }}
                        title={memoryEnabled ? "Disable conversation memory" : "Enable conversation memory"}
                      >
                        {memoryEnabled ? (
                          <Settings className="h-3 w-3" />
                        ) : (
                          <Settings className="h-3 w-3 text-muted-foreground" />
                        )}
                      </Button>
                    </div>
                  </div>
                  
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      handleSendMessage();
                    }}
                    className="flex items-center gap-2 mt-2"
                  >
                    <input
                      type="text"
                      ref={inputRef}
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder={t('assistant.inputPlaceholder', 'assistant', 'Type your question...')}
                      className="flex-1 bg-background rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary border border-input"
                    />
                    <div className="flex items-center gap-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-full"
                        title={t('assistant.attachImage', 'assistant', 'Attach image')}
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <ImageIcon className="h-4 w-4" />
                      </Button>
                      <input 
                        type="file"
                        ref={fileInputRef}
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageUpload}
                      />
                      <Button 
                        type="submit" 
                        size="icon" 
                        className="h-8 w-8 rounded-full"
                        disabled={!inputValue.trim()}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </form>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}