import React from "react";
import { cn } from "@/lib/utils";
import { CircleDollarSign, Shield, Rocket, Users2 } from "lucide-react";
import { BadgeType } from "@/components/EnhancedVariationBadge";
import { useLanguage } from "@/contexts/LanguageContext";
import { getStatusFromScore, getScoreRatingStyles, getStatusTranslationInComponent } from "@/utils/scoreUtils";
import { CoinStatus } from "@/types/CoinStatus";

/**
 * UpcomingIdoBadgeRenderer creates a high-visibility version of EnhancedVariationBadge
 * with full opacity colors for the CoinScout UpcomingIDO page.
 * Displays score indicators with hover effects but no dialogs/modals.
 */
interface UpcomingIdoBadgeRendererProps {
  score: number;
  value: string | number;
  badgeType?: BadgeType;
  className?: string;
}

// Not: getScoreRating fonksiyonunu kaldırıp, merkezi scoreUtils içindeki getScoreRatingStyles fonksiyonunu kullanıyoruz

// Get icon based on badge type
const getBadgeIcon = (badgeType: BadgeType) => {
  switch (badgeType) {
    case "initialCap":
      return <CircleDollarSign className="w-4 h-4" />;
    case "raised":
      return <Shield className="w-4 h-4" />;
    case "launchpad":
      return <Rocket className="w-4 h-4" />;
    case "investors":
      return <Users2 className="w-4 h-4" />;
    default:
      return null;
  }
};

export const UpcomingIdoBadgeRenderer: React.FC<
  UpcomingIdoBadgeRendererProps
> = ({ score, value, badgeType, className }) => {
  // Merkezi hesaplamayı kullan
  const styles = getScoreRatingStyles(score); 
  const { t } = useLanguage();

  // İç sistem CoinStatus tipini hesapla
  const statusValue: CoinStatus = getStatusFromScore(score);
  
  // Direkt çeviri fonksiyonunu kullan
  const getTranslatedStatus = () => getStatusTranslationInComponent(statusValue, t);

  // Convert value to string (handle both string and number types)
  const valueStr = typeof value === "string" ? value : String(value);

  return (
    <div
      className={cn(
        "flex items-center gap-1.5 transition-all duration-300 w-full",
        className,
      )}
      role="status"
      aria-label={
        badgeType
          ? `${badgeType} score: ${score}, value: ${valueStr}`
          : `Score: ${score}`
      }
    >
      {/* Score box with FULL opacity - no opacity reduction - no borders */}
      <div
        className={cn(
          "w-10 h-10 rounded-md flex items-center justify-center transition-all duration-300 flex-shrink-0",
          styles.bgWithOpacity20
        )}
      >
        <span
          className={cn(
            "text-sm font-bold transition-colors duration-300",
            styles.colorWithOpacity80
          )}
        >
          {score}
        </span>
      </div>

      <div className="flex flex-col items-start transition-all duration-300">
        {/* Rating text with 80% opacity for non-hover and full opacity on hover */}
        <span
          className={cn(
            "text-xs font-medium transition-colors duration-300 w-[90px] text-left",
            styles.colorWithOpacity80
          )}
        >
          {getTranslatedStatus()}
        </span>

        {/* Value text with 60% opacity for non-hover and full opacity on hover */}
        <span
          className={cn(
            "text-xs transition-colors duration-300 text-left",
            styles.colorWithOpacity60
          )}
        >
          {valueStr}
        </span>
      </div>
    </div>
  );
};

export default UpcomingIdoBadgeRenderer;
