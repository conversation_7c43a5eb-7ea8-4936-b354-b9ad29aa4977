import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { getProxyImageUrl, getFallbackImageUrl } from "@/utils/imageProxy";

// Tanımlanmış kripto para logoları - <PERSON><PERSON> kaynaklar için sabit logoları burada tutuyoruz
const CRYPTO_LOGOS: Record<string, string> = {
  // Ana kripto paralar
  BTC: "https://cryptologos.cc/logos/bitcoin-btc-logo.svg",
  ETH: "https://cryptologos.cc/logos/ethereum-eth-logo.svg",
  SOL: "https://cryptologos.cc/logos/solana-sol-logo.svg",
  BNB: "https://cryptologos.cc/logos/bnb-bnb-logo.svg",
  ADA: "https://cryptologos.cc/logos/cardano-ada-logo.svg",
  DOT: "https://cryptologos.cc/logos/polkadot-new-dot-logo.svg",
  XRP: "https://cryptologos.cc/logos/xrp-xrp-logo.svg",
  DOGE: "https://cryptologos.cc/logos/dogecoin-doge-logo.svg",
  SHIB: "https://cryptologos.cc/logos/shiba-inu-shib-logo.svg",
  AVAX: "https://cryptologos.cc/logos/avalanche-avax-logo.svg",
  TRX: "https://cryptologos.cc/logos/tron-trx-logo.svg",
  LINK: "https://cryptologos.cc/logos/chainlink-link-logo.svg",
  ATOM: "https://cryptologos.cc/logos/cosmos-atom-logo.svg",
  MATIC: "https://cryptologos.cc/logos/polygon-matic-logo.svg",
  LTC: "https://cryptologos.cc/logos/litecoin-ltc-logo.svg",
  UNI: "https://cryptologos.cc/logos/uniswap-uni-logo.svg",
  NEAR: "https://cryptologos.cc/logos/near-protocol-near-logo.svg",
  XLM: "https://cryptologos.cc/logos/stellar-xlm-logo.svg",
  FIL: "https://cryptologos.cc/logos/filecoin-fil-logo.svg",
  // DEFI ve popüler tokenler
  AAVE: "https://cryptologos.cc/logos/aave-aave-logo.svg",
  MKR: "https://cryptologos.cc/logos/maker-mkr-logo.svg",
  SNX: "https://cryptologos.cc/logos/synthetix-network-token-snx-logo.svg",
  COMP: "https://cryptologos.cc/logos/compound-comp-logo.svg",
  YFI: "https://cryptologos.cc/logos/yearn-finance-yfi-logo.svg",
};

// Ekstra coin logo kaynakları - daha fazla coin için yedek kaynak
const getCoinLogoFromCDN = (symbol: string): string => {
  const lowerSymbol = symbol.toLowerCase();
  return `https://cdn.jsdelivr.net/gh/atomiclabs/cryptocurrency-icons@bea1507f8c/128/color/${lowerSymbol}.png`;
};

const getBackupCoinLogo = (symbol: string): string => {
  const lowerSymbol = symbol.toLowerCase();
  return `https://raw.githubusercontent.com/spothq/cryptocurrency-icons/master/128/color/${lowerSymbol}.png`;
};

export interface CoinLogoProps {
  symbol: string;
  size?: "sm" | "md" | "lg" | "highlight";
  className?: string;
  imageUrl?: string; // Optional image URL from the API
  hideText?: boolean; // Optional flag to hide text and only show icon
}

export function CoinLogo({
  symbol,
  size = "md",
  className,
  imageUrl,
  hideText = false,
}: CoinLogoProps) {
  const [useFallbackText, setUseFallbackText] = useState(false);

  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-7 h-7",
    lg: "w-10 h-10",
    highlight: "w-8 h-8",
  };

  const fallbackClasses = {
    sm: "text-[10px]",
    md: "text-[14px]",
    lg: "text-[18px]",
    highlight: "text-[14px]",
  };

  const bgColors = [
    "bg-gradient-to-br from-[#132F4C] to-[#173A5E]",
    "bg-gradient-to-br from-[#143456] to-[#1D4071]",
    "bg-gradient-to-br from-[#1B3B58] to-[#214A7D]",
  ];

  // Generate a stable index from the symbol for consistent coloring
  const symbolIndex = symbol.charCodeAt(0) % bgColors.length;
  const bgColor = bgColors[symbolIndex];

  // Eğer API'den URL varsa direkt onu kullan, yoksa fallback sistemi
  let logoUrl = "";

  if (imageUrl) {
    // API'den gelen URL'i hiç değiştirmeden kullan
    logoUrl = imageUrl;
  } else {
    // Sadece API URL'i yoksa fallback sistemi
    const upperSymbol = symbol.toUpperCase();
    if (upperSymbol in CRYPTO_LOGOS) {
      logoUrl = getProxyImageUrl(CRYPTO_LOGOS[upperSymbol]);
    } else {
      logoUrl = getCoinLogoFromCDN(symbol);
    }
    console.log(`Fallback image URL for ${symbol}:`, logoUrl);
  }

  // Metin gösterimini kullan - Tüm resim kaynaklarını denedikten sonra çalışıyorsa
  if (useFallbackText) {
    return (
      <div
        className={cn(
          "rounded-full flex items-center justify-center flex-shrink-0 font-semibold text-[#66B2FF] overflow-hidden",
          "transition-all duration-300 hover:shadow-[0_0_10px_rgba(102,178,255,0.25)] group",
          "cursor-pointer",
          sizeClasses[size],
          bgColor,
          className,
        )}
      >
        <span className={cn("font-bold text-white", fallbackClasses[size])}>
          {symbol.substring(0, 3)}
        </span>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "rounded-full flex items-center justify-center flex-shrink-0 font-semibold text-[#66B2FF] overflow-hidden",
        "transition-all duration-300 hover:shadow-[0_0_10px_rgba(102,178,255,0.25)] group",
        "cursor-pointer",
        sizeClasses[size],
        bgColor,
        className,
      )}
    >
      {logoUrl ? (
        <img
          src={logoUrl}
          alt={`${symbol} logo`}
          className="w-full h-full object-cover transition-all duration-300 group-hover:brightness-110"
          onError={(e) => {
            if (imageUrl) {
              // API'den gelen resim için sadece console log, fallback yok
              console.log(`Using API image for ${symbol}: ${imageUrl}`);
            } else {
              // Sadece API URL'i yoksa fallback sistemi kullan
              const target = e.target as HTMLImageElement;
              const backupLogoUrl = getBackupCoinLogo(symbol);
              target.src = backupLogoUrl;

              target.onerror = () => {
                const fallbackSvg = getFallbackImageUrl(symbol);
                target.src = fallbackSvg;

                target.onerror = () => {
                  setUseFallbackText(true);
                };
              };
            }
          }}
          loading="lazy"
        />
      ) : (
        <span className={cn("font-bold text-white", fallbackClasses[size])}>
          {symbol.substring(0, 3)}
        </span>
      )}
    </div>
  );
}
