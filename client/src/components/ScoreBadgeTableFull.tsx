import React, { useState, useMemo } from "react";
import { Card } from "@/components/ui/card";
import { Star, ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";
import { ContextAwareWatchlistButton } from "@/components/watchlists/ContextAwareWatchlistButton";
import TableScoreGauge from "./TableScoreGauge";
import {
  EnhancedTableHeader,
  HeaderTooltipContent,
} from "@/components/EnhancedTableHeader";
import { getProxyImageUrl, getFallbackImageUrl } from "@/utils/imageProxy";
import {
  coinRowBaseClasses,
  coinRowEvenClass,
  coinRowOddClass,
} from "../styles/coinRowStyles";
import { cn } from "@/lib/utils";

// Define the types for badge data
export interface BadgeData {
  score: number;
  value: string | number;
}

// Define types for the project/coin data
export interface ProjectData {
  id: string;
  name: string;
  symbol: string;
  image: string;
  launchDate: string; // ISO 8601 formatında tarih (örnek: "2025-06-09")
  launchType: string;

  imcScore: BadgeData;
  financingScore: BadgeData;
  launchpadScore: BadgeData;
  investorScore: BadgeData;
  totalAiScore: BadgeData;
  socialScore: BadgeData;

  // Ek alanlar
  rank?: number;
  icon?: React.ElementType | React.ReactNode;
  demo?: boolean; // Demo proje işaretleyici
}

interface ScoreBadgeTableFullProps {
  projects: ProjectData[];
  // This component will accept a custom badge renderer from the parent page
  badgeRenderer: React.ComponentType<{
    score: number;
    value: string | number;
    badgeType?:
      | "initialCap"
      | "raised"
      | "launchpad"
      | "investors"
      | "fundamentals";
    className?: string;
    variant?: "default" | "compact" | "tableCell";
  }>;
  onViewDetails?: (project: ProjectData) => void;
  // Added handler for Total AI Score clicks
  onTotalAIScoreClick?: (id: string, score: number) => void;
  tableClassName?: string;
  headerClassName?: string;
  rowClassName?: string;
  cellClassName?: string;
  // Add watchlist functionality
  isFavorite?: (id: string | number) => boolean;
  toggleFavorite?: (id: string | number) => void;
  showWatchlist?: boolean;
  // Add header tooltip configuration
  useEnhancedHeaders?: boolean;
  // Column opacity configurations
  addColumnOpacity?: boolean; // Flag to enable additional opacity for specific columns
  dimmedColumnWrapperClassName?: string; // Class for columns that should be dimmed
  // Flag to control whether to show Details column
  showDetailsColumn?: boolean;
  // Pagination props for continuous ranking
  currentPage?: number;
  pageSize?: number;
  allProjects?: ProjectData[]; // Tüm projeler listesi (sıralama için)
  // External sorting control
  externalSortField?: SortField;
  externalSortDirection?: SortDirection;
  onSortChange?: (field: SortField, direction: SortDirection) => void;
}

// Score rating function - used for totalAiScore display
import { getStatusFromScore, getStatusTranslation } from "@/utils/scoreUtils";
import { useLanguage } from "@/contexts/LanguageContext";
import { CoinStatus } from "@/types/CoinStatus";

const getScoreRating = (score: number) => {
  // Get the status value
  const statusValue: CoinStatus = getStatusFromScore(score);

  // Base style configurations based on score thresholds
  if (score >= 85)
    return {
      text: statusValue, // Will be translated at usage point
      colorClass: "text-[#00D88A]",
      bgClass: "bg-[#00D88A]",
    };
  if (score >= 75)
    return {
      text: statusValue, // Will be translated at usage point
      colorClass: "text-[#00B8D9]",
      bgClass: "bg-[#00B8D9]",
    };
  if (score >= 65)
    return {
      text: statusValue, // Will be translated at usage point
      colorClass: "text-[#FFAB00]",
      bgClass: "bg-[#FFAB00]",
    };
  if (score >= 50)
    return {
      text: statusValue, // Will be translated at usage point
      colorClass: "text-[#FF5630]",
      bgClass: "bg-[#FF5630]",
    };
  return {
    text: statusValue, // Will be translated at usage point
    colorClass: "text-[#FF3B3B]",
    bgClass: "bg-[#FF3B3B]",
  };
};

// Sorting types
type SortField =
  | "name"
  | "launchDate"
  | "imcScore"
  | "financingScore"
  | "launchpadScore"
  | "investorScore"
  | "socialScore"
  | "totalAiScore";
type SortDirection = "asc" | "desc";

export const ScoreBadgeTableFull: React.FC<ScoreBadgeTableFullProps> = ({
  projects,
  badgeRenderer: BadgeComponent,
  onViewDetails,
  onTotalAIScoreClick,
  tableClassName = "w-full",
  headerClassName = "border-b border-gray-800 h-12",
  rowClassName = "border-b border-gray-800 h-16",
  cellClassName = "py-2 px-2", // Reduced padding from p-3
  isFavorite,
  toggleFavorite,
  showWatchlist = false,
  useEnhancedHeaders = false,
  addColumnOpacity = false,
  dimmedColumnWrapperClassName = "",
  showDetailsColumn = true,
  currentPage = 1,
  pageSize = 10,
  allProjects = [],
  externalSortField,
  externalSortDirection,
  onSortChange,
}) => {
  // Get language translation utility
  const { t } = useLanguage();

  // Sorting state - use external if provided, otherwise internal
  const [internalSortField, setInternalSortField] = useState<SortField>("totalAiScore");
  const [internalSortDirection, setInternalSortDirection] = useState<SortDirection>("desc");

  const sortField = externalSortField || internalSortField;
  const sortDirection = externalSortDirection || internalSortDirection;

  // Handle header click for sorting
  const handleSort = (field: SortField) => {
    let newDirection: SortDirection;

    if (field === sortField) {
      // Toggle direction if same field
      newDirection = sortDirection === "asc" ? "desc" : "asc";
    } else {
      // Set new field and default to descending for scores (higher is better)
      if (field === "name" || field === "launchDate") {
        newDirection = "asc";
      } else {
        newDirection = "desc";
      }
    }

    // Use external handler if provided, otherwise update internal state
    if (onSortChange) {
      onSortChange(field, newDirection);
    } else {
      setInternalSortField(field);
      setInternalSortDirection(newDirection);
    }
  };

  // Sort the projects - simplified since external sorting is now handled by parent
  const sortedProjects = useMemo(() => {
    // Null check - projects gelmediğinde boş array döndür
    if (!projects || !Array.isArray(projects) || projects.length === 0) {
      console.error(
        "⚠️ projects prop is undefined, null, empty or not an array:",
        projects,
      );
      console.log(
        "🔴 ScoreBadgeTableFull - projects verisinin detayları:",
        JSON.stringify(projects, null, 2),
      );
      return [];
    }

    console.log(
      "✅ ScoreBadgeTableFull - projects verisi alındı, uzunluk:",
      projects.length,
    );

    // If external sorting is being used, just return projects as-is (already sorted by parent)
    if (onSortChange) {
      return projects;
    }

    // Otherwise, use internal sorting (legacy behavior)
    // Önce demo ve gerçek projeleri ayır
    const realProjects = projects.filter(project => !project.demo);
    const demoProjects = projects.filter(project => project.demo);

    // Gerçek projeleri sırala
    const sortedRealProjects = [...realProjects].sort((a, b) => {
      // Handle different field types
      if (sortField === "name") {
        return sortDirection === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      }

      if (sortField === "launchDate") {
        // Assuming launchDate is a string in a format that can be compared
        return sortDirection === "asc"
          ? a.launchDate.localeCompare(b.launchDate)
          : b.launchDate.localeCompare(a.launchDate);
      }

      if (sortField === "totalAiScore") {
        return sortDirection === "asc"
          ? a.totalAiScore.score - b.totalAiScore.score
          : b.totalAiScore.score - a.totalAiScore.score;
      }

      // Handle badge scores (initialCap, raised, launchpad, investors)
      const aScore = a[sortField]?.score || 0;
      const bScore = b[sortField]?.score || 0;

      return sortDirection === "asc" ? aScore - bScore : bScore - aScore;
    });

    // Demo projeleri de sırala (aynı mantıkla)
    const sortedDemoProjects = [...demoProjects].sort((a, b) => {
      // Handle different field types
      if (sortField === "name") {
        return sortDirection === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      }

      if (sortField === "launchDate") {
        return sortDirection === "asc"
          ? a.launchDate.localeCompare(b.launchDate)
          : b.launchDate.localeCompare(a.launchDate);
      }

      if (sortField === "totalAiScore") {
        return sortDirection === "asc"
          ? a.totalAiScore.score - b.totalAiScore.score
          : b.totalAiScore.score - a.totalAiScore.score;
      }

      // Handle badge scores (initialCap, raised, launchpad, investors)
      const aScore = a[sortField]?.score || 0;
      const bScore = b[sortField]?.score || 0;

      return sortDirection === "asc" ? aScore - bScore : bScore - aScore;
    });

    // Gerçek projeler önce, demo projeler sonra
    return [...sortedRealProjects, ...sortedDemoProjects];
  }, [projects, sortField, sortDirection, onSortChange]);

  // Function to render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown size={12} className="ml-1 opacity-50" />;
    }
    return sortDirection === "asc" ? (
      <ArrowUp size={12} className="ml-1" />
    ) : (
      <ArrowDown size={12} className="ml-1" />
    );
  };

  return (
    <Card className="overflow-hidden border-gray-800">
      <div className="w-full overflow-auto">
        <table
          className={`${tableClassName} border-collapse`}
          style={{
            height: "auto",
            width: "100%",
            tableLayout: "fixed",
            borderSpacing: "0",
            borderCollapse: "collapse",
          }}
        >
          <colgroup>
            <col style={{ width: "40px" }} /> {/* Rank - reduced from 48px */}
            {showWatchlist && <col style={{ width: "40px" }} />}{" "}
            {/* Watchlist - reduced from 48px */}
            <col style={{ width: "140px" }} /> {/* Name - reduced from 160px */}
            <col style={{ width: "90px" }} />{" "}
            {/* Launch Date - fixed width instead of auto */}
            <col style={{ width: "110px" }} />{" "}
            {/* Tokenomics - fixed width for badge */}
            <col style={{ width: "110px" }} />{" "}
            {/* Allocation - fixed width for badge */}
            <col style={{ width: "110px" }} />{" "}
            {/* Vesting - fixed width for badge */}
            <col style={{ width: "110px" }} />{" "}
            {/* Social - fixed width for badge */}
            <col style={{ width: "110px" }} />{" "}
            {/* Fundamentals - fixed width for badge */}
            <col style={{ width: "140px" }} />{" "}
            {/* Total AI Score - wider for gauge + rating */}
            {showDetailsColumn && <col style={{ width: "112px" }} />}{" "}
            {/* Details */}
          </colgroup>
          <thead>
            <tr className={headerClassName}>
              <th className="py-2 px-2 text-center text-xs font-medium text-gray-400 w-12">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label="#"
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.rank.title")}
                        description={t("upcoming.tooltips.rank.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="center"
                  />
                ) : (
                  <div className="flex items-center justify-center">#</div>
                )}
              </th>
              {showWatchlist && (
                <th className="py-2 px-2 text-center text-xs font-medium text-gray-400 w-12">
                  {useEnhancedHeaders ? (
                    <EnhancedTableHeader
                      label={
                        (<Star size={14} className="text-gray-400" />) as any
                      }
                      tooltipContent={
                        <HeaderTooltipContent
                          title={t("upcoming.tooltips.watchlist.title")}
                          description={t("upcoming.tooltips.watchlist.description")}
                        />
                      }
                      showInfoIcon={false}
                      align="center"
                    />
                  ) : (
                    <span className="sr-only">Watchlist</span>
                  )}
                </th>
              )}
              <th className="py-2 px-4 text-left text-xs font-medium text-gray-400">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label={t("upcoming.table.name")}
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.projectName.title")}
                        description={t("upcoming.tooltips.projectName.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="left"
                    sortable={true}
                    isSorted={sortField === "name" ? sortDirection : false}
                    onSort={() => handleSort("name")}
                  />
                ) : (
                  <button
                    onClick={() => handleSort("name")}
                    className="flex items-center pl-4 w-full hover:text-gray-300 transition-colors duration-300 cursor-pointer"
                  >
                    <span>{t("upcoming.table.name")}</span>
                    {renderSortIcon("name")}
                  </button>
                )}
              </th>
              <th className="py-2 px-2 text-center text-xs font-medium text-gray-400">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label={t("upcoming.table.date")}
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.launchDate.title")}
                        description={t("upcoming.tooltips.launchDate.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="center"
                    sortable={true}
                    isSorted={
                      sortField === "launchDate" ? sortDirection : false
                    }
                    onSort={() => handleSort("launchDate")}
                  />
                ) : (
                  <button
                    onClick={() => handleSort("launchDate")}
                    className="flex items-center justify-center w-full hover:text-gray-300 transition-colors duration-300 cursor-pointer"
                  >
                    <span>{t("upcoming.table.date")}</span>
                    {renderSortIcon("launchDate")}
                  </button>
                )}
              </th>
              <th className="py-2 px-2 text-center text-xs font-medium text-gray-400">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label={t("upcoming.table.imcScore")}
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.initialMarketCap.title")}
                        description={t("upcoming.tooltips.initialMarketCap.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="center"
                    sortable={true}
                    isSorted={
                      sortField === "imcScore" ? sortDirection : false
                    }
                    onSort={() => handleSort("imcScore")}
                  />
                ) : (
                  <button
                    onClick={() => handleSort("imcScore")}
                    className="flex items-center justify-center w-full hover:text-gray-300 transition-colors duration-300 cursor-pointer"
                  >
                    <span>{t("upcoming.table.imcScore")}</span>
                    {renderSortIcon("imcScore")}
                  </button>
                )}
              </th>
              <th className="py-2 px-2 text-center text-xs font-medium text-gray-400">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label={t("upcoming.table.fundingScore")}
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.financing.title")}
                        description={t("upcoming.tooltips.financing.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="center"
                    sortable={true}
                    isSorted={sortField === "financingScore" ? sortDirection : false}
                    onSort={() => handleSort("financingScore")}
                  />
                ) : (
                  <button
                    onClick={() => handleSort("financingScore")}
                    className="flex items-center justify-center w-full hover:text-gray-300 transition-colors duration-300 cursor-pointer"
                  >
                    <span>{t("upcoming.table.fundingScore")}</span>
                    {renderSortIcon("financingScore")}
                  </button>
                )}
              </th>
              <th className="py-2 px-2 text-center text-xs font-medium text-gray-400">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label={t("upcoming.table.launchpadScore")}
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.launchpad.title")}
                        description={t("upcoming.tooltips.launchpad.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="center"
                    sortable={true}
                    isSorted={sortField === "launchpadScore" ? sortDirection : false}
                    onSort={() => handleSort("launchpadScore")}
                  />
                ) : (
                  <button
                    onClick={() => handleSort("launchpadScore")}
                    className="flex items-center justify-center w-full hover:text-gray-300 transition-colors duration-300 cursor-pointer"
                  >
                    <span>{t("upcoming.table.launchpadScore")}</span>
                    {renderSortIcon("launchpadScore")}
                  </button>
                )}
              </th>
              <th className="py-2 px-2 text-center text-xs font-medium text-gray-400">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label={t("upcoming.table.investorScore")}
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.investors.title")}
                        description={t("upcoming.tooltips.investors.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="center"
                    sortable={true}
                    isSorted={sortField === "investorScore" ? sortDirection : false}
                    onSort={() => handleSort("investorScore")}
                  />
                ) : (
                  <button
                    onClick={() => handleSort("investorScore")}
                    className="flex items-center justify-center w-full hover:text-gray-300 transition-colors duration-300 cursor-pointer"
                  >
                    <span>{t("upcoming.table.investorScore")}</span>
                    {renderSortIcon("investorScore")}
                  </button>
                )}
              </th>
              <th className="py-2 px-2 text-center text-xs font-medium text-gray-400">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label={t("upcoming.table.socialScore")}
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.socialMedia.title")}
                        description={t("upcoming.tooltips.socialMedia.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="center"
                    sortable={true}
                    isSorted={
                      sortField === "socialScore" ? sortDirection : false
                    }
                    onSort={() => handleSort("socialScore")}
                  />
                ) : (
                  <button
                    onClick={() => handleSort("socialScore")}
                    className="flex items-center justify-center w-full hover:text-gray-300 transition-colors duration-300 cursor-pointer"
                  >
                    <span>{t("upcoming.table.socialScore")}</span>
                    {renderSortIcon("socialScore")}
                  </button>
                )}
              </th>
              <th className="py-2 px-2 text-center text-xs font-medium text-gray-400 bg-black/5">
                {useEnhancedHeaders ? (
                  <EnhancedTableHeader
                    label={t("upcoming.table.totalAiScore")}
                    tooltipContent={
                      <HeaderTooltipContent
                        title={t("upcoming.tooltips.coinScoutAiScore.title")}
                        description={t("upcoming.tooltips.coinScoutAiScore.description")}
                      />
                    }
                    showInfoIcon={false}
                    align="center"
                    sortable={true}
                    isSorted={
                      sortField === "totalAiScore" ? sortDirection : false
                    }
                    onSort={() => handleSort("totalAiScore")}
                  />
                ) : (
                  <button
                    onClick={() => handleSort("totalAiScore")}
                    className="flex items-center justify-center w-full hover:text-gray-300 transition-colors duration-300 cursor-pointer"
                  >
                    <span>{t("upcoming.table.totalAiScore")}</span>
                    {renderSortIcon("totalAiScore")}
                  </button>
                )}
              </th>
              {showDetailsColumn && (
                <th className="py-2 px-2 text-center text-xs font-medium text-gray-400 w-28">
                  {useEnhancedHeaders ? (
                    <EnhancedTableHeader
                      label={t("upcoming.tooltips.projectDetails.title")}
                      tooltipContent={
                        <HeaderTooltipContent
                          title={t("upcoming.tooltips.projectDetails.title")}
                          description={t("upcoming.tooltips.projectDetails.description")}
                        />
                      }
                      showInfoIcon={false}
                      align="center"
                    />
                  ) : (
                    <div className="flex items-center justify-center">
                      Details
                    </div>
                  )}
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {sortedProjects.map((project, index) => (


              <tr
                key={project.id}
                className={cn(
                  coinRowBaseClasses,
                  index % 2 === 0 ? coinRowEvenClass : coinRowOddClass,
                  project.demo && "blur-sm opacity-60 pointer-events-none", // Demo projeler için blur efekti
                )}
                onClick={() => !project.demo && onViewDetails && onViewDetails(project)} // Demo projelerin tıklanmasını engelle
              >
                <td className={`${cellClassName} text-center text-sm  w-12`}>
                  <div className="flex items-center justify-center h-full">
                    {(() => {
                      // Eğer allProjects varsa, tüm projeler içindeki gerçek sırayı hesapla
                      if (allProjects && allProjects.length > 0) {
                        // Önce tüm projeleri aynı sıralama mantığıyla sırala
                        const realProjects = allProjects.filter(p => !p.demo);
                        const demoProjects = allProjects.filter(p => p.demo);

                        // Gerçek projeleri sırala
                        const sortedRealProjects = [...realProjects].sort((a, b) => {
                          if (sortField === "name") {
                            return sortDirection === "asc"
                              ? a.name.localeCompare(b.name)
                              : b.name.localeCompare(a.name);
                          }
                          if (sortField === "launchDate") {
                            return sortDirection === "asc"
                              ? a.launchDate.localeCompare(b.launchDate)
                              : b.launchDate.localeCompare(a.launchDate);
                          }
                          if (sortField === "totalAiScore") {
                            return sortDirection === "asc"
                              ? a.totalAiScore.score - b.totalAiScore.score
                              : b.totalAiScore.score - a.totalAiScore.score;
                          }
                          const aScore = a[sortField]?.score || 0;
                          const bScore = b[sortField]?.score || 0;
                          return sortDirection === "asc" ? aScore - bScore : bScore - aScore;
                        });

                        // Demo projeleri sırala
                        const sortedDemoProjects = [...demoProjects].sort((a, b) => {
                          if (sortField === "name") {
                            return sortDirection === "asc"
                              ? a.name.localeCompare(b.name)
                              : b.name.localeCompare(a.name);
                          }
                          if (sortField === "launchDate") {
                            return sortDirection === "asc"
                              ? a.launchDate.localeCompare(b.launchDate)
                              : b.launchDate.localeCompare(a.launchDate);
                          }
                          if (sortField === "totalAiScore") {
                            return sortDirection === "asc"
                              ? a.totalAiScore.score - b.totalAiScore.score
                              : b.totalAiScore.score - a.totalAiScore.score;
                          }
                          const aScore = a[sortField]?.score || 0;
                          const bScore = b[sortField]?.score || 0;
                          return sortDirection === "asc" ? aScore - bScore : bScore - aScore;
                        });

                        // Tüm sıralı listeyi oluştur
                        const allSortedProjects = [...sortedRealProjects, ...sortedDemoProjects];

                        // Bu projenin tüm listede kaçıncı sırada olduğunu bul
                        const globalIndex = allSortedProjects.findIndex(p => p.id === project.id);
                        return globalIndex !== -1 ? globalIndex + 1 : sortedProjects.indexOf(project) + 1;
                      }

                      // Fallback: Sadece mevcut sayfa içindeki sıra
                      return (currentPage - 1) * pageSize + sortedProjects.indexOf(project) + 1;
                    })()}
                  </div>
                </td>
                {showWatchlist && (
                  <td className={`${cellClassName} text-center px-0 w-12`}>
                    <div
                      className="flex items-center justify-center h-full"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {/* Use ContextAwareWatchlistButton when available, fallback to original button */}
                      <ContextAwareWatchlistButton
                        coinId={project.id.toString()}
                        variant="icon"
                        pageType="upcoming"
                        className="flex items-center justify-center"
                        isUpcoming={true}
                        colorOverride={
                          isFavorite && isFavorite(project.id)
                            ? "yellow-400"
                            : undefined
                        }
                      />
                    </div>
                  </td>
                )}
                <td className={`${cellClassName} text-left px-0`}>
                  <div className="flex items-center h-full">
                    <div className="flex items-center space-x-3 pl-4 w-full">
                      <div className="w-7 h-7 rounded-full bg-gray-800 p-0.5 flex items-center justify-center flex-shrink-0">
                        {project.image ? (
                          <img
                            src={project.image}
                            alt="Project Image"
                            className="w-full h-full object-contain rounded-full"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-xs font-bold text-white">
                            {project.symbol.substring(0, 3)}
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col justify-center">
                        <span className="text-xs font-medium text-gray-300">
                          {project.name}
                        </span>
                        <span className="text-[11px] text-gray-500 text-left">
                          {project.symbol}
                        </span>
                      </div>
                    </div>
                  </div>
                </td>
                <td className={`${cellClassName} text-center`}>
                  <div className="flex items-center justify-center h-full">
                    {addColumnOpacity ? (
                      <div
                        className={`${dimmedColumnWrapperClassName} flex items-center justify-center h-full`}
                      >
                        <div className="flex flex-col items-center">
                          <>
                            <span className="text-xs font-medium text-gray-300">
                              {project.launchDate}
                            </span>
                            <span className="text-xs text-gray-500">
                              {project.launchType}
                            </span>
                          </>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center">
                        <>
                          <span className="text-xs font-medium text-gray-300">
                            {project.launchDate}
                          </span>
                          <span className="text-xs text-gray-500">
                            {project.launchType}
                          </span>
                        </>
                      </div>
                    )}
                  </div>
                </td>
                <td className={`${cellClassName} text-center`}>
                  <div className="flex items-center justify-center h-full">
                    <div className={`w-full flex items-center justify-center`}>
                      <div className="my-auto flex items-center justify-center ">
                        <div className={`relative  rounded-lg p-1`}>
                          <BadgeComponent
                            score={project.imcScore?.score || 0}
                            value={project.imcScore?.value || "N/A"}
                            badgeType="initialCap"
                            className=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
                <td className={`${cellClassName} text-center`}>
                  <div className="flex items-center justify-center h-full">
                    <div className={`w-full flex items-center justify-center`}>
                      <div className="my-auto flex items-center justify-center ">
                        <div className="relative rounded-lg p-1 ">
                          <BadgeComponent
                            score={project.financingScore?.score || 0}
                            value={project.financingScore?.value || ''}
                            badgeType="raised"
                            variant="tableCell"
                            className=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
                <td className={`${cellClassName} text-center`}>
                  <div className="flex items-center justify-center h-full">
                    <div className={` w-full flex items-center justify-center`}>
                      <div className="my-auto flex items-center justify-center ">
                        <div className={`relative rounded-lg p-1 `}>
                          <BadgeComponent
                            score={project.launchpadScore?.score || 0}
                            value={project.launchpadScore?.value || ''}
                            badgeType="launchpad"
                            variant="tableCell"
                            className=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
                <td className={`${cellClassName} text-center`}>
                  <div className="flex items-center justify-center h-full">
                    <div className={` w-full flex items-center justify-center`}>
                      <div className="my-auto flex items-center justify-center ">
                        <div className="relative rounded-lg p-1">
                          <BadgeComponent
                            score={project.investorScore?.score || 0}
                            value={project.investorScore?.value || ''}
                            badgeType="investors"
                            variant="tableCell"
                            className=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
                <td className={`${cellClassName} text-center`}>
                  <div className="flex items-center justify-center h-full">
                    <div className={` w-full flex items-center justify-center`}>
                      <div className="my-auto flex items-center justify-center ">
                        <div className="relative rounded-lg p-1 ">
                          <BadgeComponent
                            score={project.socialScore?.score || 0}
                            value={project.socialScore?.value || ''}
                            badgeType="fundamentals"
                            variant="tableCell"
                            className=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </td>

                <td
                  className={`${cellClassName} text-center py-1.5 px-2 relative total-score-col`}
                  style={{ width: "144px", minWidth: "140px" }}
                >
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center justify-center gap-1.5 my-auto">
                      <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5 ">
                        <div className="flex-shrink-0">
                          <div className="relative flex items-center justify-center w-10 h-10 flex items-center">
                            <svg
                              className="w-10 h-10 rotate-[-90deg]"
                              viewBox="0 0 36 36"
                            >
                              <circle
                                cx="18"
                                cy="18"
                                r="16"
                                fill="none"
                                stroke="#1E4976"
                                strokeWidth="2.1"
                                strokeOpacity="0.3"
                              ></circle>
                              <circle
                                cx="18"
                                cy="18"
                                r="16"
                                fill="none"
                                stroke={
                                  (project.totalAiScore?.score || 0) >= 85
                                    ? "#00D88A"
                                    : (project.totalAiScore?.score || 0) >= 75
                                      ? "#00B8D9"
                                      : (project.totalAiScore?.score || 0) >= 65
                                        ? "#FFAB00"
                                        : (project.totalAiScore?.score || 0) >= 50
                                          ? "#FF5630"
                                          : "#FF3B3B"
                                }
                                strokeWidth="2.8"
                                strokeLinecap="round"
                                strokeDasharray="100 100"
                                strokeDashoffset={100 - (project.totalAiScore?.score || 0)}
                              ></circle>
                            </svg>
                            <div
                              className={`absolute text-sm font-bold ${
                                (project.totalAiScore?.score || 0) >= 85
                                  ? "text-[#00D88A]"
                                  : (project.totalAiScore?.score || 0) >= 75
                                    ? "text-[#00B8D9]"
                                    : (project.totalAiScore?.score || 0) >= 65
                                      ? "text-[#FFAB00]"
                                      : (project.totalAiScore?.score || 0) >= 50
                                        ? "text-[#FF5630]"
                                        : "text-[#FF3B3B]"
                              }`}
                            >
                              <span>{project.totalAiScore?.score || 0}</span>
                            </div>
                          </div>
                        </div>
                        {(() => {
                          const score = project.totalAiScore?.score || 0;
                          // Use the centralized status calculation and translation
                          const statusValue = getStatusFromScore(score);
                          const statusText = getStatusTranslation(
                            statusValue,
                            t,
                          );

                          // Get the styling based on the score
                          const getRating = (score: number) => {
                            if (score >= 85)
                              return {
                                text: statusText, // Use translated status text
                                bgClass: "bg-[#00D88A]",
                                colorClass: "text-[#00D88A]",
                              };
                            if (score >= 75)
                              return {
                                text: statusText, // Use translated status text
                                bgClass: "bg-[#00B8D9]",
                                colorClass: "text-[#00B8D9]",
                              };
                            if (score >= 65)
                              return {
                                text: statusText, // Use translated status text
                                bgClass: "bg-[#FFAB00]",
                                colorClass: "text-[#FFAB00]",
                              };
                            if (score >= 50)
                              return {
                                text: statusText, // Use translated status text
                                bgClass: "bg-[#FF5630]",
                                colorClass: "text-[#FF5630]",
                              };
                            return {
                              text: statusText, // Use translated status text
                              bgClass: "bg-[#FF3B3B]",
                              colorClass: "text-[#FF3B3B]",
                            };
                          };
                          const rating = getRating(score);
                          return (
                            <div
                              className={`w-[90px] h-10 flex items-center justify-center px-3 rounded-md text-xs font-medium ${rating.bgClass}/10 ${rating.colorClass} pointer-events-none`}
                            >
                              {rating.text}
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                </td>
                {showDetailsColumn && (
                  <td className={`${cellClassName} text-center w-28`}>
                    <div className="flex items-center justify-center h-full">
                      <button
                        className="px-3 py-1 text-xs text-gray-500"
                        onClick={() => onViewDetails && onViewDetails(project)}
                      >
                        View Details
                      </button>
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
};

export default ScoreBadgeTableFull;
