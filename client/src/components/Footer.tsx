import { Link, useLocation } from "wouter";
import {
  Twitter,
  MessageSquare,
  Mail,
  Shield,
  Heart,
  BarChart2,
  Youtube,
  Facebook,
  Linkedin,
  Send,
  Video,
  Image,
  Pencil,
  Package,
  Users,
  BookOpen,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/contexts/LanguageContext";

export default function Footer() {
  const [location] = useLocation();
  const { t } = useLanguage();

  const socialLinks = [
    { name: "Twitter", icon: Twitter, href: "https://x.com/CoinScout_app" },
    {
      name: "Discord",
      icon: MessageSquare,
      href: "https://discord.gg/qTtJBhXY",
    },
    {
      name: "YouTube",
      icon: Youtube,
      href: "https://www.youtube.com/@coinscout_app",
    },
    { name: "Telegram", icon: Send, href: "https://t.me/+XJPP0Ktz559iZmFk " },
    {
      name: "TikTok",
      icon: Video,
      href: "https://www.tiktok.com/@coinscout_app",
    },
    {
      name: "Instagram",
      icon: Image,
      href: "https://www.instagram.com/coinscout.app/ ",
    },
    {
      name: "Medium",
      icon: Pencil,
      href: "https://medium.com/@coinscout.app ",
    },
    {
      name: "Facebook",
      icon: Facebook,
      href: "https://facebook.com/coinscout",
    },
    {
      name: "LinkedIn",
      icon: Linkedin,
      href: "https://www.linkedin.com/company/coinscout/",
    },
    { name: "Email", icon: Mail, href: "mailto:<EMAIL>" },
  ];

  const productLinks = [
    // Features page removed as requested
    { name: t("links.cryptoRatings", "footer"), href: "/coinlist#top" },
    { name: t("links.idoRatings", "footer"), href: "/upcoming#top" },
    { name: t("links.aiPortfolioStrategist", "footer"), href: "/generator#top", comingSoon: true },
    {
      name: t("links.aiPortfolioCheckup", "footer"),
      href: "/portfolio-analysis#top",
      comingSoon: true,
    },
    { name: t("links.compareCoins", "footer"), href: "/compare#top" },
    { name: t("links.recentListings", "footer"), href: "/new-coins#top" },
    { name: t("links.topMovers", "footer"), href: "/top-gained-coins#top" },
    { name: t("links.airdropsHub", "footer"), href: "/airdropscore#top", comingSoon: true },
    { name: t("links.scoutAI", "footer"), href: "/gemscout#top" },
    { name: t("links.aiAssistant", "footer"), href: "/aiassistant#top" },
    { name: t("links.pricing", "footer"), href: "/pricing#top" },
  ];

  const learnLinks = [
    { name: t("links.academy", "footer"), href: "/academy", comingSoon: true },
    { name: t("links.documentation", "footer"), href: "/documentation", comingSoon: true },
    { name: t("links.blog", "footer"), href: "/blog", comingSoon: true },
    // Removing /guide as it's an orphaned link with no corresponding route
    { name: t("links.faq", "footer"), href: "/faq#top" },
  ];

  const communityLinks = [
    { name: t("links.forum", "footer"), href: "/forum", comingSoon: true },
    { name: t("links.telegram", "footer"), href: "https://t.me/+YTFnfQnA_L5lMmE0" },
    { name: t("links.discord", "footer"), href: "https://discord.gg/qTtJBhXY " },
    { name: t("links.twitter", "footer"), href: "https://x.com/CoinScout_app" },
    { name: t("links.publicPortfolios", "footer"), href: "/public-portfolios", comingSoon: true },
    {
      name: t("links.communityGuidelines", "footer"),
      href: "/community-guidelines",
      comingSoon: true,
    },
    { name: t("links.userTestimonials", "footer"), href: "/testimonials", comingSoon: true },
  ];

  const legalLinks = [
    { name: t("links.privacyPolicy", "footer"), href: "/privacy#top" },
    { name: t("links.termsOfService", "footer"), href: "/terms#top" },
    { name: t("links.cookiePolicy", "footer"), href: "/cookie-policy#top" },
    { name: t("links.disclaimer", "footer"), href: "/disclaimer#top" },
    { name: t("links.advertisingPolicy", "footer"), href: "/ad-policy#top" },
    { name: t("links.careers", "footer"), href: "/careers#top" },
  ];

  const noSidebarPages = [
    "/",
    "/pricing",
    "/docusaurus-docs",
    "/features",
    "/faq",
    "/public-portfolios",
    "/privacy",
    "/terms",
    "/cookie-policy",
    "/disclaimer",
    "/login",
  ];

  const isNoSidebarPage =
    noSidebarPages.includes(location) ||
    location.startsWith("/public-portfolios/");

  return (
    <footer className="bg-slate-900/95 backdrop-blur-sm border-t border-slate-800/60">
      {/* Premium horizontal footer design with visual enhancements */}
      <div className="w-full py-12 ml-[36px]">
        <div
          className={cn(
            "w-full max-w-[1600px] mx-auto pr-8 sm:px-12 lg:px-16",
            !isNoSidebarPage && "pl-8",
          )}
        >
          {/* Main footer with enhanced visuals */}
          <div className="flex flex-col space-y-12">
            {/* Top section with logo and navigation categories */}
            <div className="flex flex-col lg:grid lg:grid-cols-12 gap-10 lg:gap-4">
              {/* Logo and description with enhanced styling */}
              <div className="lg:col-start-1 lg:col-end-5">
                <Link href="/">
                  <div className="flex items-center gap-2 cursor-pointer mb-3 group">
                    <div className="bg-primary/10 p-1.5 rounded-md group-hover:bg-primary/20 transition-all duration-300">
                      <BarChart2 className="h-5 w-5 text-primary" />
                    </div>
                    <span className="text-sm font-semibold text-foreground group-hover:text-primary/90 transition-colors duration-300">
                      CoinScout
                    </span>
                  </div>
                </Link>
                <p className="text-xs leading-relaxed text-slate-400 max-w-[280px] pb-6">
                  {t("description", "footer")}
                </p>
                <div className="flex justify-start pt-6 border-t border-slate-800/40">
                  {/* Social icons in a row with enhanced hover effects */}
                  <div className="flex items-center gap-7">
                    {socialLinks.slice(0, 7).map((social) => (
                      <TooltipProvider key={social.name}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <a
                              href={social.href}
                              className="text-slate-500 hover:text-primary hover:scale-110 transition-all duration-300"
                              target="_blank"
                              rel="noopener noreferrer"
                              aria-label={`Follow us on ${social.name}`}
                              onClick={(e) => {
                                e.preventDefault();
                                window.open(social.href, "_blank");
                              }}
                            >
                              <social.icon className="h-4 w-4" />
                            </a>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{social.name}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                  </div>
                </div>
              </div>

              {/* Navigation links in horizontal groups with enhanced styling */}
              <div className="lg:col-start-5 lg:col-end-13 grid grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-8">
                {/* Product category */}
                <div>
                  <h3 className="text-xs font-semibold text-primary mb-4 tracking-wide uppercase">
                    {t("categories.product", "footer")}
                  </h3>
                  <ul className="space-y-2.5">
                    {productLinks.slice(0, 5).map((link) => (
                      <li key={link.name} className="group">
                        {"comingSoon" in link && link.comingSoon ? (
                          <div className="flex flex-col">
                            <span className="text-xs text-slate-400 opacity-70 cursor-not-allowed flex items-center">
                              {link.name}
                              <span className="ml-2 inline-flex items-center rounded-full bg-primary/10 px-1.5 py-0.5 text-[9px] text-primary/90">
                                {t("links.soon", "footer")}
                              </span>
                            </span>
                          </div>
                        ) : link.href.startsWith("http") ? (
                          <a
                            href={link.href}
                            className="text-xs text-slate-400 group-hover:text-primary/90 transition-colors duration-300 cursor-pointer flex items-center"
                            target="_blank"
                            rel="noopener noreferrer"
                            onClick={(e) => {
                              e.preventDefault();
                              window.open(link.href, "_blank");
                            }}
                          >
                            {link.name}
                          </a>
                        ) : (
                          <Link
                            href={link.href}
                            onClick={() => {
                              window.scrollTo({ top: 0, behavior: "smooth" });
                            }}
                          >
                            <div className="flex flex-col">
                              <span className="text-xs text-slate-400 group-hover:text-primary/90 transition-colors duration-300 cursor-pointer flex items-center">
                                {link.name}
                              </span>
                            </div>
                          </Link>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Learn category */}
                <div>
                  <h3 className="text-xs font-semibold text-primary mb-4 tracking-wide uppercase">
                    {t("categories.learn", "footer")}
                  </h3>
                  <ul className="space-y-2.5">
                    {learnLinks.map((link) => (
                      <li key={link.name} className="group">
                        {"comingSoon" in link && link.comingSoon ? (
                          <div className="flex flex-col">
                            <span className="text-xs text-slate-400 opacity-70 cursor-not-allowed flex items-center">
                              {link.name}
                              <span className="ml-2 inline-flex items-center rounded-full bg-primary/10 px-1.5 py-0.5 text-[9px] text-primary/90">
                                {t("links.soon", "footer")}
                              </span>
                            </span>
                          </div>
                        ) : link.href.startsWith("http") ? (
                          <a
                            href={link.href}
                            className="text-xs text-slate-400 group-hover:text-primary/90 transition-colors duration-300 cursor-pointer flex items-center"
                            target="_blank"
                            rel="noopener noreferrer"
                            onClick={(e) => {
                              e.preventDefault();
                              window.open(link.href, "_blank");
                            }}
                          >
                            {link.name}
                          </a>
                        ) : (
                          <Link href={link.href}>
                            <div className="flex flex-col">
                              <span className="text-xs text-slate-400 group-hover:text-primary/90 transition-colors duration-300 cursor-pointer flex items-center">
                                {link.name}
                              </span>
                            </div>
                          </Link>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Community category */}
                <div>
                  <h3 className="text-xs font-semibold text-primary mb-4 tracking-wide uppercase">
                    {t("categories.community", "footer")}
                  </h3>
                  <ul className="space-y-2.5">
                    {communityLinks.slice(0, 5).map((link) => (
                      <li key={link.name} className="group">
                        {"comingSoon" in link && link.comingSoon ? (
                          <div className="flex flex-col">
                            <span className="text-xs text-slate-400 opacity-70 cursor-not-allowed flex items-center">
                              {link.name}
                              <span className="ml-2 inline-flex items-center rounded-full bg-primary/10 px-1.5 py-0.5 text-[9px] text-primary/90">
                                {t("links.soon", "footer")}
                              </span>
                            </span>
                          </div>
                        ) : link.href.startsWith("http") ? (
                          <a
                            href={link.href}
                            className="text-xs text-slate-400 group-hover:text-primary/90 transition-colors duration-300 cursor-pointer flex items-center"
                            target="_blank"
                            rel="noopener noreferrer"
                            onClick={(e) => {
                              e.preventDefault();
                              window.open(link.href, "_blank");
                            }}
                          >
                            {link.name}
                          </a>
                        ) : (
                          <Link href={link.href}>
                            <div className="flex flex-col">
                              <span className="text-xs text-slate-400 group-hover:text-primary/90 transition-colors duration-300 cursor-pointer flex items-center">
                                {link.name}
                              </span>
                            </div>
                          </Link>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Legal category */}
                <div>
                  <h3 className="text-xs font-semibold text-primary mb-4 tracking-wide uppercase">
                    {t("categories.legal", "footer")}
                  </h3>
                  <ul className="space-y-2.5">
                    {legalLinks.slice(0, 5).map((link) => (
                      <li key={link.name} className="group">
                        {"comingSoon" in link && link.comingSoon ? (
                          <div className="flex flex-col">
                            <span className="text-xs text-slate-400 opacity-70 cursor-not-allowed flex items-center">
                              {link.name}
                              <span className="ml-2 inline-flex items-center rounded-full bg-primary/10 px-1.5 py-0.5 text-[9px] text-primary/90">
                                {t("links.soon", "footer")}
                              </span>
                            </span>
                          </div>
                        ) : link.href.startsWith("http") ? (
                          <a
                            href={link.href}
                            className="text-xs text-slate-400 group-hover:text-primary/90 transition-colors duration-300 cursor-pointer"
                            target="_blank"
                            rel="noopener noreferrer"
                            onClick={(e) => {
                              e.preventDefault();
                              window.open(link.href, "_blank");
                            }}
                          >
                            {link.name}
                          </a>
                        ) : (
                          <Link href={link.href}>
                            <div className="flex flex-col">
                              <span className="text-xs text-slate-400 group-hover:text-primary/90 transition-colors duration-300 cursor-pointer">
                                {link.name}
                              </span>
                            </div>
                          </Link>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Middle section with social icons only */}

            {/* Copyright section with enhanced styling */}
            <div className="flex flex-col md:flex-row items-center justify-between gap-4 pt-6 border-t border-slate-800/40">
              <p className="text-xs text-slate-500">
                © {new Date().getFullYear()} CoinScout.{" "}
                {t("allRightsReserved", "footer")}
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
