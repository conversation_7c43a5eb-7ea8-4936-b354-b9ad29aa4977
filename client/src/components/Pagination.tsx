import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ArrowLeftRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useLanguage } from "@/contexts/LanguageContext";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  siblingsCount?: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  pageSizeOptions?: number[];
  className?: string;
}

export function Pagination({
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  siblingsCount = 1,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  className,
}: PaginationProps) {
  const { t } = useLanguage();
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(startItem + pageSize - 1, totalItems);

  return (
    <div
      className={cn(
        "flex flex-col md:flex-row md:items-center justify-between gap-4",
        className,
      )}
    >
      <div
        className="text-sm text-[#E7EBF0]/70 font-medium bg-[#132F4C]/50 px-4 py-2 rounded-md border border-[#1E4976]/30 backdrop-blur-sm h-9 flex items-center"
        aria-live="polite"
      >
        <span className="hidden md:inline">{t("showing", "pagination", "Showing")}&nbsp;</span> {startItem}-
        {endItem} <span className="hidden md:inline">&nbsp;{t("of", "pagination", "of")}&nbsp; </span>{" "}
        {totalItems} <span className="hidden md:inline">&nbsp;{t("rows", "pagination", "rows")}</span>
      </div>

      <div className="flex items-center gap-4">
        <nav
          aria-label="Pagination"
          className="flex items-center bg-[#132F4C]/50 backdrop-blur-sm rounded-md p-1.5 gap-0.5 sm:gap-1 md:gap-1.5 border border-[#1E4976]/30 h-[38px] w-auto min-w-[180px] md:min-w-[220px]"
          role="navigation"
        >
          {totalPages > 3 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 p-0 text-[#E7EBF0]/70 hover:bg-[#0A1929]/70 hover:text-[#66B2FF] transition-colors duration-200 disabled:opacity-50"
                  disabled={currentPage <= 1}
                  onClick={() => onPageChange(1)}
                  aria-label="First Page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                className="bg-[#132F4C]/95 border-[#1E4976]/60 text-[#E7EBF0] text-xs"
              >
                First Page
              </TooltipContent>
            </Tooltip>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 p-0 text-[#E7EBF0]/70 hover:bg-[#0A1929]/70 hover:text-[#66B2FF] transition-colors duration-200 disabled:opacity-50"
                disabled={currentPage <= 1}
                onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
                aria-label="Previous Page"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              className="bg-[#132F4C]/95 border-[#1E4976]/60 text-[#E7EBF0] text-xs"
            >
              Previous Page
            </TooltipContent>
          </Tooltip>

          {/* Page buttons */}
          {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
            let pageNum;

            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }

            if (pageNum <= 0 || pageNum > totalPages) return null;

            return (
              <Button
                key={pageNum}
                variant="ghost"
                size="sm"
                className={cn(
                  "h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 p-0 text-xs sm:text-sm hover:bg-[#0A1929]/70 transition-all duration-200",
                  currentPage === pageNum
                    ? "bg-[#132F4C]/90 text-[#66B2FF] font-semibold border border-[#1E4976]/50 ring-1 ring-[#66B2FF]/30"
                    : "text-[#E7EBF0]/70 hover:text-[#66B2FF]",
                )}
                onClick={() => onPageChange(pageNum)}
                aria-label={`Page ${pageNum}`}
                aria-current={currentPage === pageNum ? "page" : undefined}
              >
                {pageNum}
              </Button>
            );
          })}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 p-0 text-[#E7EBF0]/70 hover:bg-[#0A1929]/70 hover:text-[#66B2FF] transition-colors duration-200 disabled:opacity-50"
                disabled={currentPage >= totalPages}
                onClick={() =>
                  onPageChange(Math.min(currentPage + 1, totalPages))
                }
                aria-label="Next Page"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              className="bg-[#132F4C]/95 border-[#1E4976]/60 text-[#E7EBF0] text-xs"
            >
              Next Page
            </TooltipContent>
          </Tooltip>

          {totalPages > 3 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 p-0 text-[#E7EBF0]/70 hover:bg-[#0A1929]/70 hover:text-[#66B2FF] transition-colors duration-200 disabled:opacity-50"
                  disabled={currentPage >= totalPages}
                  onClick={() => onPageChange(totalPages)}
                  aria-label="Last Page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                className="bg-[#132F4C]/95 border-[#1E4976]/60 text-[#E7EBF0] text-xs"
              >
                Last Page
              </TooltipContent>
            </Tooltip>
          )}
        </nav>

        <div className="relative w-[150px] inline-block h-9">
          <select
            className="h-9 w-full rounded-md border border-[#1E4976]/40 bg-[#0A1929]/80 pl-3 pr-9 py-0 text-sm font-medium text-[#E7EBF0] 
                      hover:bg-[#132F4C]/90 hover:border-[#1E4976]/60 transition-all duration-200
                     appearance-none cursor-pointer backdrop-blur-sm shadow-md"
            value={pageSize}
            onChange={(e) => onPageSizeChange(Number(e.target.value))}
            aria-label="Items per page"
          >
            {pageSizeOptions.map((size) => (
              <option
                key={size}
                value={size}
                className="bg-[#0A1929] text-[#E7EBF0] font-medium py-2"
              >
                {size} {t("rows", "pagination", "rows")}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2.5 pointer-events-none">
            <ArrowLeftRight className="h-4 w-4 text-[#66B2FF]" />
          </div>
        </div>
      </div>
    </div>
  );
}
