/**
 * CoinScout WebSocket Client
 *
 * Enhanced WebSocket client for real-time crypto alert notifications
 * Compatible with the CoinScout notification system
 */

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'alert' | 'update' | 'system' | 'security' | 'price';
  priority: 'high' | 'medium' | 'low';
  status: 'unread' | 'read';
  timestamp: Date;
  link?: string;
  coinId?: string;
  coinName?: string;
  coinSymbol?: string;
  data?: Record<string, any>;
}

export interface CoinScoutWebSocketOptions {
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  debug?: boolean;
  autoConnect?: boolean;
}

export type EventType = 'connect' | 'disconnect' | 'auth_success' | 'auth_error' | 'notification' | 'unread_notifications' | 'notification_read_confirmed' | 'unread_count_update' | 'error' | 'token_refreshed' | 'token_refresh_failed';

// Global WebSocket connection counter for debugging
let globalConnectionCount = 0;

export class CoinScoutWebSocketClient {
  private wsUrl: string;
  private authToken: string;
  private options: Required<CoinScoutWebSocketOptions>;
  private socket: WebSocket | null = null;
  private isConnected: boolean = false;
  public isAuthenticated: boolean = false;
  private reconnectAttempts: number = 0;
  private eventListeners: Record<EventType, Function[]> = {
    'connect': [],
    'disconnect': [],
    'auth_success': [],
    'auth_error': [],
    'notification': [],
    'unread_notifications': [],
    'notification_read_confirmed': [],
    'unread_count_update': [],
    'error': [],
    'token_refreshed': [],
    'token_refresh_failed': []
  };
  private tokenRefreshInterval: NodeJS.Timeout | null = null;
  private tokenRefreshCallback: (() => Promise<string>) | null = null;
  private tokenRefreshIntervalMs: number = 4 * 60 * 1000; // 4 minutes default

  constructor(wsUrl: string, authToken: string, options: CoinScoutWebSocketOptions = {}) {
    globalConnectionCount++;
    console.log(`🔢 [WebSocket] Creating new WebSocket client instance #${globalConnectionCount}`);
    
    this.wsUrl = wsUrl;
    this.authToken = authToken;
    this.options = {
      reconnectInterval: options.reconnectInterval ?? 3000,
      maxReconnectAttempts: options.maxReconnectAttempts ?? 5,
      debug: options.debug ?? false,
      autoConnect: options.autoConnect ?? true
    };

    if (this.options.autoConnect) {
      this.connect();
    }
  }

  /**
   * Connect to WebSocket server
   */
  connect(): void {
    if (this.socket) {
      this.disconnect();
    }

    try {
      this.log('Connecting to WebSocket server:', this.wsUrl);
      this.socket = new WebSocket(this.wsUrl);

      this.socket.onopen = () => {
        this.log('WebSocket connection opened successfully');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.emit('connect');
        this.log('Emitted connect event, starting authentication...');
        this.authenticate();
      };

      this.socket.onmessage = (event) => {
        this.handleMessage(event);
      };

      this.socket.onclose = () => {
        this.log('WebSocket connection closed');
        this.isConnected = false;
        this.isAuthenticated = false;
        this.emit('disconnect');
        this.log('Emitted disconnect event, attempting reconnection...');
        this._reconnect();
      };

      this.socket.onerror = (error) => {
        this.log('WebSocket error:', error);
        this.emit('error', error);
      };
    } catch (error) {
      this.log('Connection error:', error);
      this.emit('error', error);
      this._reconnect();
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.isConnected = false;
    this.isAuthenticated = false;
    
    // Stop token refresh when disconnecting
    this.stopTokenRefresh();
    
    globalConnectionCount = Math.max(0, globalConnectionCount - 1);
    console.log(`🔢 [WebSocket] Disconnected WebSocket client, remaining connections: ${globalConnectionCount}`);
  }

  /**
   * Authenticate with the server
   */
  authenticate(): void {
    if (!this.isConnected || !this.socket) {
      this.log('Authentication failed: No connection');
      return;
    }

    this.log('Sending authentication request with token:', this.authToken);
    this.socket.send(JSON.stringify({
      type: 'auth',
      token: this.authToken
    }));
    this.log('Authentication request sent, waiting for response...');
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);

      // Debug: Tüm gelen mesajları logla
      console.log('🔍 [DEBUG] Received WebSocket message:', data);
      console.log('🔍 [DEBUG] Message type:', data.type);
      console.log('🔍 [DEBUG] isAuthenticated:', this.isAuthenticated);

      // Authentication kontrol edilmesi gereken mesaj tipleri
      const authRequiredTypes = ['new_notification', 'unread_notifications', 'notification_read_confirmed', 'unread_count_update'];
      
      // Eğer authentication gerektiren bir mesaj türü ise ve authenticated değilse, mesajı reddet
      if (authRequiredTypes.includes(data.type) && !this.isAuthenticated) {
        console.log('❌ [WebSocket] Blocking message - user not authenticated:', data.type);
        this.log('❌ Blocking message - user not authenticated:', data.type);
        return;
      }

      switch (data.type) {
        case 'new_notification':
          this.log('🔔 New notification received');
          this.emit('notification', data.notification);
          break;

        case 'unread_notifications':
          this.log(`📬 ${data.count} unread notifications received`);
          this.emit('unread_notifications', data);
          break;

        case 'notification_read_confirmed':
          this.log('✅ Notification marked as read confirmed:', data.notificationId);
          this.emit('notification_read_confirmed', data);
          break;

        case 'unread_count_update':
          this.log(`📊 Unread count updated: ${data.count}`);
          this.emit('unread_count_update', data);
          break;

        case 'auth_response':
          if (data.status === 'success') {
            this.isAuthenticated = true;
            this.log('Authentication successful');
            this.emit('auth_success', data);
          } else {
            this.isAuthenticated = false;
            this.log('Authentication failed:', data.message);
            this.emit('auth_error', data);
          }
          break;

        case 'auth_success':
          this.isAuthenticated = true;
          this.log('Authentication successful - auth_success received');
          this.emit('auth_success', data);
          break;

        case 'auth_error':
          this.isAuthenticated = false;
          this.log('Authentication failed - auth_error received:', data.message);
          this.emit('auth_error', data);
          break;

        default:
          this.log('Unknown message type:', data.type);
      }
    } catch (error) {
      this.log('Message processing error:', error);
      this.emit('error', error);
    }
  }

  /**
   * Add event listener
   */
  on(event: EventType, callback: Function): this {
    if (this.eventListeners[event]) {
      this.eventListeners[event].push(callback);
    }
    return this;
  }

  /**
   * Remove event listener
   */
  off(event: EventType, callback: Function): this {
    if (this.eventListeners[event]) {
      this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
    }
    return this;
  }

  /**
   * Mark notification as read
   */
  markNotificationAsRead(notificationId: string): void {
    if (!this.isConnected || !this.socket || !this.isAuthenticated) {
      this.log('Cannot mark notification as read: No authenticated connection');
      console.log('❌ [NotificationRead] WebSocket not connected/authenticated');
      return;
    }

    this.log('Marking notification as read:', notificationId);
    console.log('📤 [NotificationRead] Sending WebSocket message:', {
      type: 'mark_notification_read',
      notificationId: notificationId
    });
    
    this.socket.send(JSON.stringify({
      type: 'mark_notification_read',
      notificationId: notificationId
    }));
  }

  /**
   * Start token refresh mechanism
   */
  startTokenRefresh(tokenRefreshCallback: () => Promise<string>, intervalMs: number = 4 * 60 * 1000): void {
    this.tokenRefreshCallback = tokenRefreshCallback;
    this.tokenRefreshIntervalMs = intervalMs;
    
    // Clear existing interval if any
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
    }
    
    this.log(`🔄 Starting token refresh mechanism (every ${intervalMs / 1000} seconds)`);
    
    this.tokenRefreshInterval = setInterval(async () => {
      await this.refreshToken();
    }, intervalMs);
  }

  /**
   * Stop token refresh mechanism
   */
  stopTokenRefresh(): void {
    if (this.tokenRefreshInterval) {
      this.log('🛑 Stopping token refresh mechanism');
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }

  /**
   * Refresh token
   */
  private async refreshToken(): Promise<void> {
    if (!this.tokenRefreshCallback) {
      this.log('⚠️ No token refresh callback set');
      return;
    }

    try {
      this.log('🔄 Refreshing authentication token...');
      const newToken = await this.tokenRefreshCallback();
      
      if (newToken && newToken !== this.authToken) {
        this.authToken = newToken;
        this.log('✅ Token refreshed successfully');
        this.emit('token_refreshed');
        
        // Re-authenticate with new token if connected
        if (this.isConnected && this.socket) {
          this.authenticate();
        }
      } else {
        this.log('⚠️ Token refresh returned same token or empty token');
      }
    } catch (error) {
      this.log('❌ Token refresh failed:', error);
      this.emit('token_refresh_failed', error);
      
      // Stop token refresh on failure to prevent spam
      this.stopTokenRefresh();
    }
  }

  /**
   * Update auth token manually
   */
  updateAuthToken(newToken: string): void {
    if (newToken !== this.authToken) {
      this.authToken = newToken;
      this.log('🔑 Auth token updated manually');
      
      // Re-authenticate if connected
      if (this.isConnected && this.socket) {
        this.authenticate();
      }
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): { isConnected: boolean, isAuthenticated: boolean } {
    return {
      isConnected: this.isConnected,
      isAuthenticated: this.isAuthenticated
    };
  }

  /**
   * Debug logging
   */
  log(...args: any[]): void {
    if (this.options.debug) {
      console.log('🔗 [CoinScoutWebSocket]', ...args);
    }
  }

  /**
   * Emit event to all listeners
   */
  emit(event: EventType, data?: any): void {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Event listener error:', error);
        }
      });
    }
  }

  /**
   * Reconnect logic
   */
  private _reconnect(): void {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.log('Maximum reconnection attempts exceeded');
      return;
    }

    this.reconnectAttempts++;
    this.log(`Reconnecting (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})...`);

    setTimeout(() => {
      this.connect();
    }, this.options.reconnectInterval);
  }
}

// Utility to create WebSocket URL with correct protocol
export const getWebSocketUrl = (path: string = '/ws'): string => {
  // Always use the remote WebSocket server
  const wsUrl = `wss://api.coinscout.app/socket${path}`;
  console.log('🔗 Using WebSocket URL:', wsUrl);
  return wsUrl;
};

export default CoinScoutWebSocketClient;