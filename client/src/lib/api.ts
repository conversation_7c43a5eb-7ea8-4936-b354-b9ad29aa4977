import axios, { AxiosError } from "axios";

// Custom API error class
export class ApiError extends Error {
  status?: number;
  data?: any;

  constructor(message: string, status?: number, data?: any) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.data = data;
  }

  static fromAxiosError(error: AxiosError): ApiError {
    const status = error.response?.status;
    const data = error.response?.data;
    const message = error.message || "Unknown API Error";

    return new ApiError(message, status, data);
  }
}

// API service helper function
export const apiService = {
  get: async <T>(url: string, params?: any): Promise<T> => {
    try {
      const response = await api.get<T>(url, { params });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw ApiError.fromAxiosError(error);
      }
      throw new ApiError("Unknown error during API request");
    }
  },

  post: async <T>(url: string, data?: any): Promise<T> => {
    try {
      const response = await api.post<T>(url, data);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw ApiError.fromAxiosError(error);
      }
      throw new ApiError("Unknown error during API request");
    }
  },

  // Watchlist API Methods
  getUserWatchlists: async () => {
    console.log("Calling getUserWatchlists API method");
    try {
      const response = await api.post("/client.php", {
        f: "get_user_watchlists",
      });
      console.log("getUserWatchlists response:", response.data);
      return response.data;
    } catch (error) {
      console.error("getUserWatchlists error:", error);
      return { success: false, errormsg: "Failed to fetch watchlists" };
    }
  },

  // IDO Watchlist API Methods
  getUserIDOWatchlists: async () => {
    console.log("Calling getUserIDOWatchlists API method");
    try {
      const response = await api.post("/client.php", {
        f: "get_user_ido_watchlists",
      });
      console.log("getUserIDOWatchlists response:", response.data);
      return response.data;
    } catch (error) {
      console.error("getUserIDOWatchlists error:", error);
      return { success: false, errormsg: "Failed to fetch IDO watchlists" };
    }
  },

  // Bu metod Watchlist edit formundan yapılan düzenlemeleri gönderir
  // Verdiğiniz örnek: {"f":"edit_watchlist","id":"48","name":"48","description":"test name","icon_id":1}
  editWatchlist: async (
    watchlistId: string,
    newName: string,
    newDescription: string = "",
    icon_id: number = 1,
  ) => {
    // Show detailed debugging information
    console.log(
      "%c EDITING WATCHLIST - START OF DEBUG INFO",
      "background: #4b5563; color: white; padding: 4px; border-radius: 4px;",
    );
    console.log("Input parameters received:", {
      watchlistId,
      newName,
      newDescription,
      icon_id,
    });

    try {
      // Create the payload with the correct parameters
      const payload = {
        f: "edit_watchlist",
        id: watchlistId, // id = watchlist ID (correct)
        name: newName, // name = new name (correct)
        description: newDescription, // description = user provided description (fixed)
        icon_id: icon_id, // icon_id = selected icon ID (correct)
      };

      // Show exactly what is being sent
      console.log("API Request Payload (BEFORE sending):", payload);

      // Send the request
      const response = await api.post("/client.php", payload);

      console.log(
        "Actual Request Data sent (from axios):",
        response.config.data,
      );
      console.log("API Response:", response.data);
      console.log(
        "%c EDITING WATCHLIST - END OF DEBUG INFO",
        "background: #4b5563; color: white; padding: 4px; border-radius: 4px;",
      );

      return response.data;
    } catch (error) {
      console.error("editWatchlist error:", error);
      return { success: false, errormsg: "Failed to update watchlist" };
    }
  },

  addToWatchlist: async (
    watchlistId: string,
    coinId: string,
    description: string = "",
  ) => {
    console.log("Calling addToWatchlist API method", { watchlistId, coinId });
    try {
      const response = await api.post("/client.php", {
        f: "add_to_watchlist_v2",
        listId: watchlistId,
        coinId,
        description,
      });
      console.log("addToWatchlist response:", response.data);
      return response.data;
    } catch (error) {
      console.error("addToWatchlist error:", error);
      return { success: false, errormsg: "Failed to add to watchlist" };
    }
  },

  removeFromWatchlist: async (watchlistId: string, coinId: string) => {
    console.log("Calling removeFromWatchlist API method", {
      watchlistId,
      coinId,
    });
    try {
      const response = await api.post("/client.php", {
        f: "remove_from_watchlist_v2",
        listId: watchlistId,
        coinId,
      });
      console.log("removeFromWatchlist response:", response.data);
      return response.data;
    } catch (error) {
      console.error("removeFromWatchlist error:", error);
      return { success: false, errormsg: "Failed to remove from watchlist" };
    }
  },

  // Stripe Checkout API Methods
  getSubscriptionPlans: async () => {
    console.log("Fetching subscription plans");
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "get-subscription-plans",
      });
      console.log("getSubscriptionPlans response:", response.data);

      // API yanıt formatı: { success: true, output: [plan1, plan2, ...] }
      // plans array'ini response.data.output'tan alıyoruz
      if (response.data.success && Array.isArray(response.data.output)) {
        return {
          success: true,
          data: response.data.output,
        };
      } else {
        return {
          success: false,
          errormsg: "Plan verisi beklenen formatta değil",
        };
      }
    } catch (error) {
      console.error("getSubscriptionPlans error:", error);
      return { success: false, errormsg: "Failed to fetch subscription plans" };
    }
  },

  createCheckoutSession: async (priceId: string, userId: string | number) => {
    console.log("Creating checkout session", { priceId, userId });
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "create-checkout-session",
        priceId: priceId,
        userId: userId,
        successUrl: window.location.origin + "/payment-success",
        cancelUrl: window.location.origin + "/payment-cancel",
      });
      console.log("createCheckoutSession response:", response.data);

      // API yanıt formatı: { success: true, output: { sessionId: "...", url: "..." } }
      if (response.data.success && response.data.output) {
        // Burada output içeriğinde sessionId ve url bilgilerini alıyoruz
        return {
          success: true,
          sessionId: response.data.output.sessionId,
          url: response.data.output.url,
        };
      } else {
        return {
          success: false,
          errormsg: response.data.error || "Ödeme oturumu oluşturulamadı",
        };
      }
    } catch (error) {
      console.error("createCheckoutSession error:", error);
      return { success: false, errormsg: "Failed to create checkout session" };
    }
  },

  verifyPayment: async (
    paymentIntent: string,
    paymentIntentClientSecret: string,
  ) => {
    console.log("Verifying payment", { paymentIntent });
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "verify-payment",
        paymentIntent: paymentIntent,
        paymentIntentClientSecret: paymentIntentClientSecret,
      });
      console.log("verifyPayment response:", response.data);

      // API yanıt formatı kontrol edildi ve uygun şekilde işlenecek
      if (response.data.success) {
        return {
          success: true,
          // Varsa output'taki diğer bilgileri de burada işleyebiliriz
          data: response.data.output,
        };
      } else {
        return {
          success: false,
          errormsg: response.data.error || "Ödeme doğrulanamadı",
        };
      }
    } catch (error) {
      console.error("verifyPayment error:", error);
      return { success: false, errormsg: "Failed to verify payment" };
    }
  },

  // Abonelik bilgilerini getir
  getUserSubscription: async () => {
    console.log("Fetching user subscription");
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "get-user-subscription",
      });
      console.log("getUserSubscription response:", response.data);

      if (response.data.success && response.data.output) {
        return {
          success: true,
          data: response.data.output,
        };
      } else {
        return {
          success: false,
          errormsg: response.data.error || "Abonelik bilgileri alınamadı",
        };
      }
    } catch (error) {
      console.error("getUserSubscription error:", error);
      return { success: false, errormsg: "Abonelik bilgileri alınamadı" };
    }
  },

  // Kullanıcının fatura geçmişini getir
  getUserInvoices: async () => {
    console.log("Fetching user invoices");
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "get-user-invoices",
      });
      console.log("getUserInvoices response:", response.data);

      if (response.data.success && response.data.output) {
        return {
          success: true,
          data: response.data.output,
        };
      } else {
        return {
          success: false,
          errormsg: response.data.error || "Fatura geçmişi alınamadı",
        };
      }
    } catch (error) {
      console.error("getUserInvoices error:", error);
      return { success: false, errormsg: "Fatura geçmişi alınamadı" };
    }
  },

  // Kullanıcının ödeme yöntemini getir
  getUserPaymentMethod: async () => {
    console.log("Fetching user payment method");
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "get-user-payment-method",
      });
      console.log("getUserPaymentMethod response:", response.data);

      if (response.data.success && response.data.output) {
        return {
          success: true,
          data: response.data.output,
        };
      } else {
        return {
          success: false,
          errormsg: response.data.error || "Ödeme yöntemi bilgileri alınamadı",
        };
      }
    } catch (error) {
      console.error("getUserPaymentMethod error:", error);
      return { success: false, errormsg: "Ödeme yöntemi bilgileri alınamadı" };
    }
  },

  // Aboneliği iptal et
  cancelSubscription: async (subscriptionId: string) => {
    console.log("Canceling subscription", { subscriptionId });
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "cancel-subscription",
        subscriptionId: subscriptionId,
      });
      console.log("cancelSubscription response:", response.data);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.output,
        };
      } else {
        return {
          success: false,
          errormsg: response.data.error || "Abonelik iptal edilemedi",
        };
      }
    } catch (error) {
      console.error("cancelSubscription error:", error);
      return { success: false, errormsg: "Abonelik iptal edilemedi" };
    }
  },

  // İptal edilmiş aboneliği yeniden aktifleştir
  reactivateSubscription: async (subscriptionId: string) => {
    console.log("Reactivating subscription", { subscriptionId });
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "reactivate-subscription",
        subscriptionId: subscriptionId,
      });
      console.log("reactivateSubscription response:", response.data);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.output,
        };
      } else {
        return {
          success: false,
          errormsg:
            response.data.error || "Abonelik yeniden aktifleştirilemedi",
        };
      }
    } catch (error) {
      console.error("reactivateSubscription error:", error);
      return {
        success: false,
        errormsg: "Abonelik yeniden aktifleştirilemedi",
      };
    }
  },

  // Ödeme yöntemi güncelleme oturumu oluştur
  createPaymentUpdateSession: async (subscriptionId: string) => {
    console.log("Creating payment update session", { subscriptionId });
    try {
      const response = await api.post("/stripe_checkout.php", {
        f: "create-payment-update-session",
        subscriptionId: subscriptionId,
        successUrl: window.location.origin + "/profile/membership",
        cancelUrl: window.location.origin + "/profile/membership",
      });
      console.log("createPaymentUpdateSession response:", response.data);

      if (response.data.success && response.data.output) {
        return {
          success: true,
          url: response.data.output.url,
          sessionId: response.data.output.sessionId,
        };
      } else {
        return {
          success: false,
          errormsg:
            response.data.error ||
            "Ödeme yöntemi güncelleme oturumu oluşturulamadı",
        };
      }
    } catch (error) {
      console.error("createPaymentUpdateSession error:", error);
      return {
        success: false,
        errormsg: "Ödeme yöntemi güncelleme oturumu oluşturulamadı",
      };
    }
  },

  // Duplicate fonksiyon, kaldırıldı

  // Tüm abonelik seçeneklerini getir
  getSubscriptionOptions: async () => {
    console.log("Fetching subscription options");
    try {
      const userToken = localStorage.getItem("auth_token") || "";
      const response = await api.post(
        "/stripe_checkout.php",
        {
          f: "get-subscription-options",
        },
        {
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
        },
      );
      console.log("getSubscriptionOptions response:", response.data);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data || response.data.output,
        };
      } else {
        return {
          success: false,
          errormsg: response.data.error || "Abonelik seçenekleri alınamadı",
        };
      }
    } catch (error) {
      console.error("getSubscriptionOptions error:", error);
      return { success: false, errormsg: "Abonelik seçenekleri alınamadı" };
    }
  },

  // Abonelik yükseltme önizleme
  previewUpgradeSubscription: async (
    subscriptionId: string,
    newPriceId: string,
  ) => {
    console.log("Previewing subscription upgrade", {
      subscriptionId,
      newPriceId,
    });
    try {
      const userToken = localStorage.getItem("auth_token") || "";
      const response = await api.post(
        "/stripe_checkout.php",
        {
          f: "preview-upgrade-subscription",
          subscriptionId: subscriptionId,
          newPriceId: newPriceId,
        },
        {
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
        },
      );
      console.log("previewUpgradeSubscription response:", response.data);

      if (response.data.success) {
        const responseData = response.data.output || response.data.data;
        return {
          success: true,
          data: responseData,
          message: responseData?.message || null,
        };
      } else {
        return {
          success: false,
          errormsg:
            response.data.error || "Abonelik yükseltme önizlemesi alınamadı",
        };
      }
    } catch (error) {
      console.error("previewUpgradeSubscription error:", error);
      return {
        success: false,
        errormsg: "Abonelik yükseltme önizlemesi alınamadı",
      };
    }
  },

  // Abonelik yükseltme
  upgradeSubscription: async (subscriptionId: string, newPriceId: string) => {
    console.log("Upgrading subscription", { subscriptionId, newPriceId });
    try {
      const userToken = localStorage.getItem("auth_token") || "";
      const response = await api.post(
        "/stripe_checkout.php",
        {
          f: "upgrade-subscription",
          subscriptionId: subscriptionId,
          newPriceId: newPriceId,
        },
        {
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
        },
      );
      console.log("upgradeSubscription response:", response.data);

      if (response.data.success) {
        const responseData = response.data.output || response.data.data;
        return {
          success: true,
          data: responseData,
          message: responseData?.message || null,
        };
      } else {
        return {
          success: false,
          errormsg: response.data.errormsg || "Abonelik yükseltilemedi",
        };
      }
    } catch (error) {
      console.error("upgradeSubscription error:", error);
      return { success: false, errormsg: "Abonelik yükseltilemedi" };
    }
  },

  // Abonelik düşürme önizleme
  previewDowngradeSubscription: async (
    subscriptionId: string,
    newPriceId: string,
  ) => {
    console.log("Previewing subscription downgrade", {
      subscriptionId,
      newPriceId,
    });
    try {
      const userToken = localStorage.getItem("auth_token") || "";
      const response = await api.post(
        "/stripe_checkout.php",
        {
          f: "preview-downgrade-subscription",
          subscriptionId: subscriptionId,
          newPriceId: newPriceId,
        },
        {
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
        },
      );
      console.log("previewDowngradeSubscription response:", response.data);

      if (response.data.success) {
        const output = response.data.output || response.data.data;
        // Get message from the API response in the expected format from the example
        const message =
          output?.message ||
          response.data.output?.message ||
          response.data.message ||
          null;
        return {
          success: true,
          data: output,
          message: message,
        };
      } else {
        return {
          success: false,
          errormsg:
            response.data.error || "Abonelik düşürme önizlemesi alınamadı",
        };
      }
    } catch (error) {
      console.error("previewDowngradeSubscription error:", error);
      return {
        success: false,
        errormsg: "Abonelik düşürme önizlemesi alınamadı",
      };
    }
  },

  // Abonelik düşürme
  downgradeSubscription: async (subscriptionId: string, newPriceId: string) => {
    console.log("Downgrading subscription", { subscriptionId, newPriceId });
    try {
      const userToken = localStorage.getItem("auth_token") || "";
      const response = await api.post(
        "/stripe_checkout.php",
        {
          f: "downgrade-subscription",
          subscriptionId: subscriptionId,
          newPriceId: newPriceId,
        },
        {
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
        },
      );
      console.log("downgradeSubscription response:", response.data);

      if (response.data.success) {
        const output = response.data.output || response.data.data;
        return {
          success: true,
          data: output,
          message:
            output?.message ||
            response.data.output?.message ||
            response.data.message ||
            "Aboneliğiniz dönem sonunda düşürülecektir.",
        };
      } else {
        return {
          success: false,
          errormsg: response.data.errormsg || response.data.error || "Abonelik düşürülemedi",
        };
      }
    } catch (error) {
      console.error("downgradeSubscription error:", error);
      return { success: false, errormsg: "Abonelik düşürülemedi g" };
    }
  },
};

const api = axios.create({
  // API base URL hardcoded to ensure all requests go to the external API
  baseURL: "https://api.coinscout.app",
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Required for session cookies if used
});

// Development ortamında Replit baseURL'ini geçersiz kılma sorunu için önlem
// Force the baseURL to be always the correct one
const forceBaseUrl = (config: any) => {
  // API isteklerinin her zaman doğru URL'ye gitmesini sağla
  if (config && config.url) {
    // Göreli URL'leri mutlak URL'ye dönüştür
    if (config.url.startsWith('/')) {
      config.url = `https://api.coinscout.app${config.url}`;
    }
    // baseURL'i doğrudan atayalım
    config.baseURL = "https://api.coinscout.app";
  }
  return config;
};

// Force baseURL to be the correct API URL for all requests
api.interceptors.request.use(forceBaseUrl);

// API isteklerini loglama
api.interceptors.request.use((request) => {
  console.log("API Request:", {
    url: request.url,
    method: request.method,
    data: request.data,
    baseURL: request.baseURL,
  });
  return request;
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Get auth token from localStorage if it exists
    const token = localStorage.getItem("auth_token");
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add language headers from localStorage or browser language
    const languageCode =
      localStorage.getItem("language_code") ||
      localStorage.getItem("preferredLanguage") ||
      navigator.language.split("-")[0] ||
      "en";
    if (config.headers) {
      config.headers["Accept-Language"] = languageCode;
      config.headers["Content-Language"] = languageCode;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Track ongoing refresh attempts to prevent multiple simultaneous refresh calls
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}> = [];

// Process failed queue after refresh
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Refresh token function
const refreshToken = async (): Promise<string> => {
  try {
    console.log("Attempting to refresh token...");

    // Create a separate axios instance for refresh to avoid interceptor loops
    const refreshInstance = axios.create({
      baseURL: "https://api.coinscout.app",
      withCredentials: true, // Important: This sends HTTP-only cookies
    });

    // Call refresh endpoint - no need to send current token,
    // refresh token is sent automatically via HTTP-only cookie
    const response = await refreshInstance.post("/authentication.php", {
      f: "refresh"
    }, {
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
      },
    });

    if (response.status === 200 && response.data?.output?.token) {
      const newToken = response.data.output.token;
      localStorage.setItem("auth_token", newToken);
      console.log("Token refreshed successfully");
      return newToken;
    } else {
      throw new Error("Token refresh failed: " + (response.data?.errormsg || "No new token received"));
    }

  } catch (error: any) {
    console.error("Token refresh error:", error);

    // Check if it's a 401/403 from refresh endpoint - means refresh token is also expired
    if (error.response?.status === 401 || error.response?.status === 403) {
      throw new Error("Refresh token expired");
    }

    throw error;
  }
};

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle common errors (401, 403, etc.)
    if (error.response) {
      // Auth error handling - 401 Unauthorized
      if (error.response.status === 401 && !originalRequest._retry) {
        if (isRefreshing) {
          // If already refreshing, queue this request
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return api(originalRequest);
          }).catch(err => {
            return Promise.reject(err);
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          const newToken = await refreshToken();
          processQueue(null, newToken);

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return api(originalRequest);
        } catch (refreshError) {
          processQueue(refreshError, null);

          console.error("Token refresh failed, logging out user");

          // Clear auth data
          localStorage.removeItem("auth_token");
          localStorage.removeItem("user_email");

          // Trigger logout event for auth context
          window.dispatchEvent(new CustomEvent('auth:logout', {
            detail: { reason: 'token_refresh_failed', error: refreshError }
          }));

          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }

      // Global subscription upgrade handling for 403 errors
      if (error.response.status === 403 && error.response.data) {
        const errorData = error.response.data;
        if (errorData.success === false && errorData.errormsg) {
          // Check for subscription-related error messages
          const subscriptionKeywords = [
            "subscription", "upgrade", "Basic", "limit", "requires",
            "plan", "tier", "premium", "feature"
          ];

          const isSubscriptionError = subscriptionKeywords.some(keyword =>
            errorData.errormsg.toLowerCase().includes(keyword.toLowerCase())
          );

          if (isSubscriptionError) {
            // Trigger global subscription upgrade modal
            window.dispatchEvent(new CustomEvent('subscription-upgrade-required', {
              detail: {
                message: errorData.errormsg,
                originalError: error
              }
            }));

            // Mark error as handled to prevent Vite error overlay
            error.handled = true;

            // Create a new error object that won't trigger Vite overlay
            const handledError = new Error('Subscription upgrade required');
            handledError.name = 'SubscriptionError';
            (handledError as any).handled = true;
            (handledError as any).originalError = error;

            return Promise.reject(handledError);
          }
        }
      }
    }
    return Promise.reject(error);
  },
);

// Avatar API functions - using predefined avatars with backend integration

// Update user's selected avatar - sends avatar ID to backend
export const updateUserAvatar = async (avatarId: number) => {
  try {
    console.log('Updating user avatar with ID:', avatarId);

    const response = await api.post('/client.php', {
      f: 'update_user_avatar',
      avatar_id: avatarId
    });

    console.log('Update avatar response:', response.data);

    if (response.data && response.data.success) {
      return {
        success: response.data.success,
        data: response.data.output,
        message: response.data.output?.message || 'Avatar başarıyla güncellendi!'
      };
    } else {
      throw new Error(response.data?.output?.message || response.data?.message || 'Avatar güncellenemedi');
    }
  } catch (error: any) {
    console.error('Update user avatar error:', error);

    if (error.response?.data?.output?.message) {
      throw new Error(error.response.data.output.message);
    }

    if (error.response?.data?.message) {
      throw new Error(error.response.data.message);
    }

    throw new Error('Avatar güncellenirken bir hata oluştu');
  }
};

// Remove user's avatar - sets avatar_id to null
export const removeUserAvatar = async () => {
  try {
    console.log('Removing user avatar');

    const response = await api.post('/client.php', {
      f: 'remove_user_avatar'
    });

    console.log('Remove avatar response:', response.data);

    if (response.data && response.data.success) {
      return {
        success: response.data.success,
        data: response.data.output,
        message: response.data.output?.message || 'Avatar başarıyla kaldırıldı!'
      };
    } else {
      throw new Error(response.data?.output?.message || response.data?.message || 'Avatar kaldırılamadı');
    }
  } catch (error: any) {
    console.error('Remove user avatar error:', error);

    if (error.response?.data?.output?.message) {
      throw new Error(error.response.data.output.message);
    }

    if (error.response?.data?.message) {
      throw new Error(error.response.data.message);
    }

    throw new Error('Avatar kaldırılırken bir hata oluştu');
  }
};

// Get all notifications from API
export const getAllNotifications = async () => {
  try {
    console.log('🔔 [API] Fetching all notifications from server');
    
    const response = await api.post('/client.php', {
      f: 'get_notifications'
    });

    console.log('🔔 [API] All notifications response:', response.data);

    if (response.data && response.data.success) {
      return {
        success: response.data.success,
        notifications: response.data.output || [],
        message: 'Notifications fetched successfully'
      };
    } else {
      throw new Error(response.data?.message || 'Failed to fetch notifications');
    }
  } catch (error: any) {
    console.error('🔔 [API] Get all notifications error:', error);

    if (error.response?.data?.message) {
      throw new Error(error.response.data.message);
    }

    throw new Error('Bildirimler alınırken bir hata oluştu');
  }
};

export default api;

// Export refresh token function
export { refreshToken };

// Abonelik yönetimi fonksiyonlarını ayrıca export ediyoruz
export const getUserSubscription = apiService.getUserSubscription;
export const getUserInvoices = apiService.getUserInvoices;
export const getUserPaymentMethod = apiService.getUserPaymentMethod;
export const cancelSubscription = apiService.cancelSubscription;
export const reactivateSubscription = apiService.reactivateSubscription;
export const createPaymentUpdateSession = apiService.createPaymentUpdateSession;
