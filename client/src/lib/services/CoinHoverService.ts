import api from "../api";

// Coin hover detayları için tip tanımı
export interface CoinHoverData {
  id: string;
  rank: number;
  name: string;
  symbol: string;
  price: number;
  priceChanges: {
    "24h": number;
    "7d": number;
    "30d": number;
    "90d": number;
    "1y": number;
  };
  marketcap: number; // API'den "marketcap" olarak geliyor (camelCase değil)
  watchlist_count: number; // Coin'in kaç watchlist içinde olduğu bilgisi
  // Opsiyonel alanlar (API'den gelebilir veya gelmeyebilir)
  tokenomics?: {
    score: number;
    status: string;
  };
  security?: {
    score: number;
    status: string;
  };
  social?: {
    score: number;
    status: string;
  };
}

// API'den gelecek yanıt tipi
export interface CoinHoverResponse {
  success: boolean;
  output?: CoinHoverData;
  error?: string;
  errormsg?: string;
}

// API sonuç durumları
export enum CoinHoverStatus {
  SUCCESS = 'success',
  AUTH_REQUIRED = 'auth_required',
  ERROR = 'error'
}

// API yanıt result tipi
export interface CoinHoverResult {
  status: CoinHoverStatus;
  data?: CoinHoverData;
  message?: string;
}

export class CoinHoverService {
  /**
   * Belirli bir coin için özet bilgilerini getirir
   * @param coinId - API'de kullanılacak coin ID'si
   * @returns Promise with status and data
   */
  static async getCoinSummary(coinId: string): Promise<CoinHoverResult> {
    try {
      console.log(`Fetching coin summary for ID: ${coinId}`);

      // API'ye istek atılıyor
      const response = await api.post("/client.php", {
        f: "get_coin_summary",
        id: coinId,
      });

      // Log API yanıtı (debug için)
      console.log("API Response for coin summary:", response.data);

      // Başarılı cevap kontrolü
      if (response.data.success && response.data.output) {
        return {
          status: CoinHoverStatus.SUCCESS,
          data: response.data.output
        };
      } else {
        // API yanıtında hata mesajını kontrol et
        const errorMsg = response.data.errormsg || response.data.error || "Unknown error";
        
        // Eğer authorization hatası varsa AUTH_REQUIRED döndür
        if (errorMsg.toLowerCase().includes('auth') || 
            errorMsg.toLowerCase().includes('login') || 
            errorMsg.toLowerCase().includes('permission') ||
            errorMsg.toLowerCase().includes('unauthorized')) {
          return {
            status: CoinHoverStatus.AUTH_REQUIRED,
            message: "Bu özelliği kullanmak için giriş yapmalısınız."
          };
        }
        
        console.warn("API returned unsuccessful response:", errorMsg);
        return {
          status: CoinHoverStatus.ERROR,
          message: errorMsg
        };
      }
    } catch (error: any) {
      console.error("API error when fetching coin summary:", error);
      
      // HTTP status koduna göre auth hatası kontrolü
      if (error.response?.status === 401 || error.response?.status === 403) {
        return {
          status: CoinHoverStatus.AUTH_REQUIRED,
          message: "Bu özelliği kullanmak için giriş yapmalısınız."
        };
      }
      
      return {
        status: CoinHoverStatus.ERROR,
        message: "Coin bilgileri yüklenirken bir hata oluştu."
      };
    }
  }
}

export default CoinHoverService;
