import api from "../api";
import { Coin } from "../../types/coin";

/**
 * Search coin interface
 */
export interface SearchCoin {
  id: string;
  name: string;
  symbol: string;
  logo?: string;  // API bazen logo döndürebilir
  image?: string; // API'den gelen yeni image alanı
}

/**
 * Search IDO interface
 */
export interface SearchIDO {
  id: string;
  name: string;
  symbol: string;
  image?: string; // API'den gelen yeni image alanı
}

/**
 * Search results interface
 */
export interface SearchResults {
  coins: SearchCoin[];
  idos: SearchIDO[];
  hasCoinResults: boolean;
  hasIdoResults: boolean;
}

/**
 * Search params interface
 */
export interface SearchParams {
  query: string;
}

/**
 * SearchService
 * Handles search functionality for the application
 */
class SearchService {
  /**
   * Search for assets (coins and IDOs) by query string
   * Only called after user stops typing for a specific delay
   *
   * @param params - Search parameters
   * @returns Promise with search results
   */
  static async searchCoins(params: SearchParams): Promise<SearchResults> {
    try {
      console.log("Performing search API request for:", params.query);

      const response = await api.post("/client.php", {
        f: "search_assets",
        query: params.query,
      });

      if (response?.data?.success && response?.data?.output) {
        const { coins = [], idos = [] } = response.data.output;

        return {
          coins,
          idos,
          hasCoinResults: coins.length > 0,
          hasIdoResults: idos.length > 0
        };
      }

      return {
        coins: [],
        idos: [],
        hasCoinResults: false,
        hasIdoResults: false
      };
    } catch (error: any) {
      console.error("Error searching assets:", error);

      // Let the interceptor handle subscription errors globally
      // Just return empty results for any error
      return {
        coins: [],
        idos: [],
        hasCoinResults: false,
        hasIdoResults: false
      };
    }
  }

  /**
   * Search specifically for coins (used in compare page)
   * Uses "search_coins" API endpoint instead of "search_assets"
   *
   * @param params - Search parameters
   * @returns Promise with search results
   */
  static async searchCoinsForCompare(params: SearchParams): Promise<SearchResults> {
    try {
      console.log("Performing compare search API request for:", params.query);

      const response = await api.post("/client.php", {
        f: "search_coins",
        query: params.query,
      });

      if (response?.data?.success && response?.data?.output) {
        const { coins = [], idos = [] } = response.data.output;

        return {
          coins,
          idos,
          hasCoinResults: coins.length > 0,
          hasIdoResults: idos.length > 0
        };
      }

      return {
        coins: [],
        idos: [],
        hasCoinResults: false,
        hasIdoResults: false
      };
    } catch (error: any) {
      console.error("Error searching coins for compare:", error);

      // Let the interceptor handle subscription errors globally
      // Just return empty results for any error
      return {
        coins: [],
        idos: [],
        hasCoinResults: false,
        hasIdoResults: false
      };
    }
  }
}

export default SearchService;