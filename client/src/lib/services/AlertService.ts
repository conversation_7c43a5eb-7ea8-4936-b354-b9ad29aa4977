import api from "../api";
import { AlertFormValues, Alert } from "../../api/alertsApi";
import { toast } from "../../hooks/use-toast";
import { queryClient } from "../queryClient";

// API yanıt modeli
export interface AlertsResponse {
  success: boolean;
  data: any[]; // API'den gelen data yapısı için esnek tip
  output?: Alert[]; // Uyumluluk için eski yapı
  errormsg?: string;
}

export interface AlertResponse {
  success: boolean;
  data?: any; // API'den gelen data yapısı için esnek tip
  output?: Alert; // Uyumluluk için eski yapı
  errormsg?: string;
  message?: string;
}

class AlertService {
  /**
   * Kullanıcıya ait tüm alertleri getirir
   */
  static async getUserAlerts(): Promise<Alert[]> {
    try {
      const response = await api.post("/client.php", {
        f: "get_user_alerts"
      });
      
      console.log("Get user alerts response:", response.data);
      
      if (response?.data?.success) {
        // API yanıtı output içinde bir dizi olarak geliyor
        if (Array.isArray(response.data.output)) {
          // API yanıtını Alert formatına dönüştür
          return response.data.output.map((item: any) => {
            // Conditions dizisini düzenle
            const conditionAbove = item.conditions?.some((c: any) => c.type === 'above') || false;
            const conditionBelow = item.conditions?.some((c: any) => c.type === 'below') || false;
            
            // Above ve below değerlerini bul
            const aboveCondition = item.conditions?.find((c: any) => c.type === 'above');
            const belowCondition = item.conditions?.find((c: any) => c.type === 'below');
            
            // Alert nesnesini oluştur
            const alert: Alert = {
              id: parseInt(item.id),
              coin: item.coinName || item.coinSymbol, 
              coinId: item.coinId,
              coinSymbol: item.coinSymbol,
              coinImage: item.coinImage,
              type: item.type,
              notificationType: item.notificationType,
              conditionAbove: conditionAbove,
              conditionBelow: conditionBelow,
              status: 'active', // Varsayılan değer
              createdAt: item.createdAt,
              updatedAt: item.createdAt
            };
            
            // Price tipi için ekstra alanlar
            if (item.type === 'price') {
              alert.priceAbove = aboveCondition?.value?.toString();
              alert.priceBelow = belowCondition?.value?.toString();
            } else {
              // AI ve social score için
              alert.thresholdAbove = aboveCondition?.value?.toString();
              alert.thresholdBelow = belowCondition?.value?.toString();
            }
            
            return alert;
          });
        }
        // Eski data formatı kullanıldığında - data içinde bir dizi var
        else if (Array.isArray(response.data.data)) {
          return response.data.data.map((item: any) => {
            // Aynı işlemler
            const conditionAbove = item.conditions?.some((c: any) => c.type === 'above') || false;
            const conditionBelow = item.conditions?.some((c: any) => c.type === 'below') || false;
            
            const aboveCondition = item.conditions?.find((c: any) => c.type === 'above');
            const belowCondition = item.conditions?.find((c: any) => c.type === 'below');
            
            const alert: Alert = {
              id: parseInt(item.id),
              coin: item.coinName || item.coinSymbol, 
              coinId: item.coinId,
              coinSymbol: item.coinSymbol,
              coinImage: item.coinImage,
              type: item.type,
              notificationType: item.notificationType,
              conditionAbove: conditionAbove,
              conditionBelow: conditionBelow,
              status: 'active',
              createdAt: item.createdAt,
              updatedAt: item.createdAt
            };
            
            if (item.type === 'price') {
              alert.priceAbove = aboveCondition?.value?.toString();
              alert.priceBelow = belowCondition?.value?.toString();
            } else {
              alert.thresholdAbove = aboveCondition?.value?.toString();
              alert.thresholdBelow = belowCondition?.value?.toString();
            }
            
            return alert;
          });
        }
        
        return [];
      } else {
        console.error("Error fetching user alerts:", response?.data?.errormsg);
        return [];
      }
    } catch (error) {
      console.error("Error fetching user alerts:", error);
      return [];
    }
  }
  


  /**
   * Belirli bir coin için oluşturulmuş alertleri getirir
   */
  static async getCoinAlerts(coinSymbol: string): Promise<Alert[]> {
    try {
      const response = await api.post("/client.php", {
        f: "get_coin_alerts",
        coin: coinSymbol
      });
      
      if (response?.data?.success) {
        return response.data.output || [];
      } else {
        console.error("Error fetching coin alerts:", response?.data?.errormsg);
        return [];
      }
    } catch (error) {
      console.error("Error fetching coin alerts:", error);
      return [];
    }
  }

  /**
   * Yeni bir alert oluşturur
   */
  static async createAlert(alertData: AlertFormValues): Promise<Alert | null> {
    try {
      // Form verilerinden API için veri hazırla
      const requestData = {
        f: "create_alert",
        coin: alertData.coin,
        type: alertData.type,
        notificationType: alertData.notificationType,
        conditionAbove: alertData.conditions.above,
        conditionBelow: alertData.conditions.below,
      };

      // Price alert için ekstra alanlar
      if (alertData.type === 'price') {
        Object.assign(requestData, {
          priceAbove: alertData.priceAbove,
          priceBelow: alertData.priceBelow,
          currentPrice: alertData.currentPrice
        });
      } 
      // AI Score veya Social Score için threshold değerleri
      else {
        Object.assign(requestData, {
          thresholdAbove: alertData.thresholdAbove,
          thresholdBelow: alertData.thresholdBelow
        });
      }

      console.log("Creating alert with data:", requestData);
      
      const response = await api.post("/client.php", requestData);
      
      console.log("Create alert response:", response.data);
      
      if (response?.data?.success) {
        // Başarılı alert oluşturmadan sonra userAlerts query'sini geçersiz kıl
        // Böylece React Query yeni bir istek yaparak taze verileri getirecek
        queryClient.invalidateQueries({ queryKey: ['userAlerts'] });
        
        // Show success notification
        toast({
          title: "Alert başarıyla oluşturuldu",
          description: "Koşullar karşılandığında bildirim alacaksınız.",
          variant: "default"
        });
        
        // Return the alert data
        return response.data.output;
      } else {
        console.error("Error creating alert:", response?.data?.errormsg);
        
        // Show error notification
        toast({
          title: "Alert oluşturulamadı",
          description: response?.data?.errormsg || "Bir hata oluştu. Lütfen tekrar deneyin.",
          variant: "destructive"
        });
        
        return null;
      }
    } catch (error) {
      console.error("Error creating alert:", error);
      
      // Show error notification on exceptions
      toast({
        title: "Alert oluşturulamadı",
        description: "Bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive"
      });
      
      return null;
    }
  }

  /**
   * Mevcut bir alerti günceller
   */
  static async updateAlert(alertId: number, alertData: Partial<AlertFormValues>): Promise<Alert | null> {
    try {
      // Base request data
      const requestData: any = {
        f: "update_alert",
        alertId: alertId
      };
      
      // Copy all fields from alertData to requestData
      if (alertData.coin) requestData.coin = alertData.coin;
      if (alertData.type) requestData.type = alertData.type;
      if (alertData.notificationType) requestData.notificationType = alertData.notificationType;
      
      // Handle conditions object if it exists
      if (alertData.conditions) {
        requestData.conditionAbove = alertData.conditions.above;
        requestData.conditionBelow = alertData.conditions.below;
      }
      
      // Handle type-specific fields
      if (alertData.type === 'price') {
        if (alertData.priceAbove) requestData.priceAbove = alertData.priceAbove;
        if (alertData.priceBelow) requestData.priceBelow = alertData.priceBelow;
        if (alertData.currentPrice) requestData.currentPrice = alertData.currentPrice;
      } else {
        if (alertData.thresholdAbove) requestData.thresholdAbove = alertData.thresholdAbove;
        if (alertData.thresholdBelow) requestData.thresholdBelow = alertData.thresholdBelow;
      }
      
      console.log("Updating alert with data:", requestData);
      
      const response = await api.post("/client.php", requestData);
      
      console.log("Update alert response:", response.data);
      
      if (response?.data?.success) {
        // Alert güncellemesi başarılı olduğunda userAlerts sorgusunu geçersiz kıl
        queryClient.invalidateQueries({ queryKey: ['userAlerts'] });
        
        return response.data.output;
      } else {
        console.error("Error updating alert:", response?.data?.errormsg);
        return null;
      }
    } catch (error) {
      console.error("Error updating alert:", error);
      return null;
    }
  }

  /**
   * Bir alerti siler
   */
  static async deleteAlert(alertId: number): Promise<boolean> {
    try {
      const response = await api.post("/client.php", {
        f: "delete_alert",
        alertId: alertId
      });
      
      console.log("Delete alert response:", response.data);
      
      if (response?.data?.success) {
        // Alert silme başarılı olduğunda userAlerts sorgusunu geçersiz kıl
        queryClient.invalidateQueries({ queryKey: ['userAlerts'] });
      }
      
      return response?.data?.success || false;
    } catch (error) {
      console.error("Error deleting alert:", error);
      return false;
    }
  }

  /**
   * Alert durumunu değiştirir (active/paused)
   */
  static async toggleAlertStatus(alertId: number, isActive: boolean): Promise<Alert | null> {
    try {
      const response = await api.post("/client.php", {
        f: "toggle_alert_status",
        alertId: alertId,
        status: isActive ? 'active' : 'paused'
      });
      
      console.log("Toggle alert status response:", response.data);
      
      if (response?.data?.success) {
        // Alert durumu değiştirildiğinde userAlerts sorgusunu geçersiz kıl
        queryClient.invalidateQueries({ queryKey: ['userAlerts'] });
        
        return response.data.output;
      } else {
        console.error("Error toggling alert status:", response?.data?.errormsg);
        return null;
      }
    } catch (error) {
      console.error("Error toggling alert status:", error);
      return null;
    }
  }

}

export default AlertService;