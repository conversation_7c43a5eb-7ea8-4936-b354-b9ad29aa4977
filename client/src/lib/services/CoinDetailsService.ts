import api from "../api";
import { ComparisonCoin } from "../../data/compareDemoData";
import { getStatusFromScore } from "../../utils/scoreUtils";

/**
 * Request parameters interface for fetching coin details
 */
export interface CoinDetailsParams {
  coinIds: string[];
}

/**
 * Coin details response interface from API
 */
export interface CoinDetailsResponse {
  success: boolean;
  coins: CoinDetail[];
}

/**
 * Score interface for API
 */
export interface ScoreDetail {
  name: string;
  total: number;
  subScores: {
    id: string;
    name: string;
    value: number;
  }[];
}

/**
 * Detailed coin information interface
 */
export interface CoinDetail {
  id: string;
  name: string;
  symbol: string;
  price: number;
  priceChange24h: number;
  priceChange7d: number;
  priceChange30d: number;
  marketCap: number;
  volume24h: number;
  category: string;
  status: string;
  description: string;
  image?: string;  // Coin resmi URL'si
  logo?: string;   // Geriye dönük uyumluluk için
  scores?: ScoreDetail[]; // API'den dönen skor bilgileri
  rating?: number; // Geriye dönük uyumluluk için

  // API yanıtındaki potansiyel diğer alanlar
  tokenomics?: {
    circulatingSupply: number;
    totalSupply: number;
    maxSupply: number | null;
    initialDistribution?: {
      team: number;
      publicSale: number;
      privateSale: number;
      marketing: number;
      ecosystem: number;
      reserve: number;
      other: number;
    };
  };
  riskMetrics?: {
    volatility: number;
    marketBeta: number;
    sharpeRatio: number;
    liquidityScore: number;
    concentrationRisk: number;
  };
  socialMetrics?: {
    twitter: {
      followers: number;
      engagement: number;
    };
    reddit: {
      subscribers: number;
      activity: number;
    };
    github: {
      stars: number;
      commits: number;
      contributors: number;
    };
    sentiment: {
      overall: number;
      positive: number;
      negative: number;
      neutral: number;
    };
  };
  tags?: string[];
}

/**
 * CoinDetailsService
 * Handles fetching detailed information for selected coins
 */
class CoinDetailsService {
  /**
   * Fetch detailed information for multiple coins
   * @param params - Request parameters containing coin IDs
   * @returns Promise with detailed coin information
   */
  static async getMultipleCoinDetails(params: CoinDetailsParams): Promise<CoinDetail[]> {
    try {
      console.log("Fetching details for coins:", params.coinIds);

      const response = await api.post("/client.php", {
        f: "compare_coins",
        coin_ids: params.coinIds,
      });

      console.log("API Response SUCCESS:", response.data.success);

      if (response?.data?.success && response?.data?.coins && Array.isArray(response.data.coins)) {
        const coins = response.data.coins;
        console.log(`Received ${coins.length} coins from API`);

        // İlk coin'in tam yanıtını kontrol et
        if (coins.length > 0) {
          const firstCoin = coins[0];
          console.log("FIRST COIN FULL STRUCTURE:", JSON.stringify(firstCoin, null, 2));

          // Scores dizisini kontrol et
          if (firstCoin.scores && Array.isArray(firstCoin.scores)) {
            console.log("SCORES ARRAY EXISTS with length:", firstCoin.scores.length);
            console.log("SCORES SAMPLE:", JSON.stringify(firstCoin.scores[0], null, 2));
          } else {
            console.error("NO SCORES FOUND in API response!");
          }
        }

        return response.data.coins;
      }

      console.error("Invalid API response structure:", response.data);
      return [];
    } catch (error) {
      console.error("Error fetching coin details:", error);
      return [];
    }
  }

  /**
   * Kategori skorunu API verisinden al
   * @param details - API'den dönen detaylı coin bilgisi
   * @param categoryName - Aranacak kategori adı
   * @returns Kategori skoru veya 0
   */
  private static getCategoryScore(details: CoinDetail, categoryName: string): number {
    if (!details.scores) return 0;

    const category = details.scores.find(s => s.name === categoryName);
    return category ? category.total : 0;
  }

  // convertScoresToCategories methodu kaldırıldı - artık doğrudan API'den gelen verileri kullanıyoruz

  /**
   * Puan için durum metni oluştur
   * @param score - Skor değeri
   * @returns Durum metni
   */
  static getStatusForScore(score: number): string {
    // Utils içindeki merkezi hesaplama fonksiyonunu kullan
    return getStatusFromScore(score);
  }

  /**
   * Genel puanı hesapla
   * @param scores - Kategori skorları
   * @returns Ortalama puan
   */
  static calculateOverallRating(scores?: ScoreDetail[]): number {
    if (!scores || scores.length === 0) return 0;

    const sum = scores.reduce((total, score) => total + score.total, 0);
    return Math.round(sum / scores.length);
  }

  /**
   * Fetch details for a single coin
   * @param coinId - ID of the coin to fetch
   * @returns Promise with detailed coin information
   */
  static async getSingleCoinDetails(coinId: string): Promise<CoinDetail | null> {
    try {
      console.log("Fetching details for single coin:", coinId);

      const response = await api.post("/client.php", {
        f: "compare_coins",
        coin_ids: [coinId], // Send as array but with single coin
      });

      // Check if API response indicates success
      if (response?.data?.success === true && response?.data?.coins && Array.isArray(response.data.coins)) {
        const coins = response.data.coins;
        console.log(`Received coin details for ID: ${coinId}`);
        
        if (coins.length > 0) {
          return coins[0]; // Return the first (and only) coin
        }
      }

      // Check if API returned an error message (like monthly limit exceeded)
      if (response?.data?.success === false) {
        console.warn("API returned error:", response.data.errormsg || "Unknown error");
        if (response.data.errormsg && response.data.errormsg.includes("monthly limit")) {
          console.warn("Monthly comparison limit reached - coin will not be added");
        }
        return null; // Return null so CoinSelector knows not to add the coin
      }

      console.error("Invalid API response for single coin:", response.data);
      return null;
    } catch (error) {
      console.error("Error fetching single coin details:", error);
      return null;
    }
  }

  /**
   * Update comparison coins with detailed information
   * @param selectedCoins - Currently selected coins with basic information
   * @returns Promise with updated coin information
   */
  static async updateComparisonCoins(selectedCoins: ComparisonCoin[]): Promise<ComparisonCoin[]> {
    try {
      if (selectedCoins.length === 0) {
        return [];
      }

      // Coin ID'lerini al
      const coinIds = selectedCoins.map(coin => coin.id);

      // Detaylı bilgileri al
      const coinDetails = await this.getMultipleCoinDetails({ coinIds });

      console.log("Coin details successfully fetched for IDs:", coinIds);

      if (coinDetails.length === 0) {
        console.log("No coin details were returned from API");
        return selectedCoins; // Veri alınamazsa orijinal verileri döndür
      }

      console.log("INCOMING COINDETAILS:", JSON.stringify(coinDetails, null, 2));
      console.log("SELECTED COINS:", JSON.stringify(selectedCoins, null, 2));

      // Detaylı verileri mevcut coinlere ekle
      return selectedCoins.map(coin => {
        // Detay verisi bul - ID'ler farklı formatta gelebilir, dikkatli karşılaştır
        // Birincil olarak ID ile eşleştir, alternatif olarak sembol ile eşleştir
        const details = coinDetails.find(detail => {
          // API'den gelen ID'yi kontrol et
          if (detail.id === coin.id) {
            return true;
          }

          // API response bazen string ID kullanırken, frontend numara olabilir
          // Sembol ve isimle de karşılaştıralım
          if (detail.symbol.toLowerCase() === coin.symbol.toLowerCase() &&
              detail.name.toLowerCase() === coin.name.toLowerCase()) {
            console.log(`Found match by symbol/name instead of ID for ${coin.name}`);
            return true;
          }

          return false;
        });

        if (!details) {
          console.log(`No details found for coin with ID: ${coin.id}, Symbol: ${coin.symbol}`);
          return coin; // Detay bulunamazsa orijinal coin'i döndür
        }

        console.log(`Processing details for coin: ${details.name} (${details.id})`);

        // API yanıtını inceleme
        console.log('API response for coin:', JSON.stringify(details, null, 2));

        // ÖNEMLI: API'den gelen scores dizisini incele ve logla
        if (details.scores) {
          console.log(`API returned ${details.scores.length} score categories for ${details.name}:`);
          details.scores.forEach(score => {
            console.log(`Category: ${score.name}, Total score: ${score.total}, SubScores: ${score.subScores.length}`);
          });
        } else {
          console.warn(`No scores array in API response for coin ${details.name}`);
        }

        // API'den scores dizisi gelmiyorsa, varsayılan API yapısına uygun skor dizisi oluştur
        let scores: ScoreDetail[] = [];

        // API yanıtında scores dizisi varsa kullan
        if (details.scores && Array.isArray(details.scores)) {
          scores = details.scores;
        } else {
          console.log("API did not return scores array for:", details.name);
          console.log("Using placeholder scores until API is updated");

          // Temel kategorileri oluştur
          scores = [
            {
              name: "Tokenomics",
              total: 75,
              subScores: [
                { id: "supply", name: "Supply Model", value: 75 },
                { id: "distribution", name: "Distribution", value: 75 },
              ]
            },
            {
              name: "Security",
              total: 80,
              subScores: [
                { id: "consensus", name: "Consensus", value: 80 },
                { id: "code", name: "Code Quality", value: 80 },
              ]
            },
            {
              name: "Social",
              total: 70,
              subScores: [
                { id: "community", name: "Community", value: 70 },
                { id: "engagement", name: "Engagement", value: 70 },
              ]
            }
          ];
        }

        // Hesaplanan genel puanlama
        const rating = this.calculateOverallRating(scores);

        // Detayları mevcut coin ile birleştir
        return {
          ...coin,
          price: details.price || 0,
          priceChange24h: details.priceChange24h || 0,
          priceChange7d: details.priceChange7d || 0,
          priceChange30d: details.priceChange30d || 0,
          marketCap: details.marketCap || 0,
          volume24h: details.volume24h || 0,
          category: details.category || "",
          rating: rating,
          status: details.status || this.getStatusForScore(rating),
          description: details.description || "",
          // API'den gelen image veya logo verisini logo'ya ata
          logo: details.image || details.logo || coin.logo,
          // Oluşturduğumuz scores dizisini kullan
          scores: scores,
        };
      });
    } catch (error) {
      console.error("Error updating comparison coins:", error);
      return selectedCoins; // Hata olursa orijinal verileri döndür
    }
  }
}

export default CoinDetailsService;