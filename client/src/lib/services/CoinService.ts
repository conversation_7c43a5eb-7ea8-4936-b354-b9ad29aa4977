import api from "../api";
import { Coin, CoinScore } from "../../types/CoinTypes";
import { mapToValidCoinStatus } from "../../types/CoinStatus";
import { getStatusFromScore, normalizeScoreObject } from "../../utils/scoreUtils";

// Define filter interface for coin listing
export interface CoinFilters {
  marketcap_min?: number | null;
  marketcap_max?: number | null;
  score_min?: number | null;
  score_max?: number | null;
  selectedcategories?: string[] | null;
  selectedchains?: string[] | null;
  change?: boolean | null;
  newlyListed?: boolean | null;
  listing_date?: string | null; // Kaç gün öncesine kadar listelenen coinlerin gösterileceği
  listing_date_start?: string | null; // API'ye Unix timestamp olarak gönderilecek başlangıç tarihi
  listing_date_end?: string | null; // API'ye Unix timestamp olarak gönderilecek bitiş tarihi
}

// Define API response interfaces
export interface CoinListResponse {
  success: boolean;
  output: Coin[];
  meta?: {
    isDemo: boolean;
    count: number;
  };
}

export interface Category {
  id: string;
  name: string;
}

export interface Chain {
  name: string;
}

export interface CategoriesResponse {
  success: boolean;
  output: Category[];
}

export interface ChainsResponse {
  success: boolean;
  output: Chain[];
}

class CoinService {
  // Generate demo coins for subscription limitations
  private static generateDemoCoins(count: number): Coin[] {
    const demoCoins: Coin[] = [];
    const demoNames = [
      "DemoToken", "TestCoin", "SampleCrypto", "MockToken", "PlaceholderCoin",
      "DummyCoin", "ExampleToken", "PrototypeToken", "PreviewCoin", "TrialToken"
    ];
    const demoSymbols = [
      "DMO", "TST", "SMP", "MCK", "PLC", "DMY", "EXM", "PTT", "PRV", "TRL"
    ];

    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * demoNames.length);
      const demoCoin: Coin = {
        id: `demo-${i + 1}`,
        rank: 0,
        name: `${demoNames[randomIndex]} ${i + 1}`,
        symbol: `${demoSymbols[randomIndex]}${i + 1}`,
        tokenomics: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        security: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        social: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        market: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        insights: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        totalScore: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        sevenDayChange: (Math.random() - 0.5) * 20, // Random change between -10% and +10%
        demo: true, // Mark as demo coin
        marketCap: (Math.floor(Math.random() * 1000000000)).toString(), // Random market cap as string
        price: Math.random() * 100, // Random price
        volume24h: (Math.floor(Math.random() * 10000000)).toString(), // Random volume as string
        coin_age: `${Math.floor(Math.random() * 365)}d${Math.floor(Math.random() * 24)}h` // Random age as string format
      };
      demoCoins.push(demoCoin);
    }

    return demoCoins;
  }
  static async getCoinsList(filters: CoinFilters): Promise<Coin[]> {
    try {
      // API isteği için parametreleri hazırla
      const requestParams: any = {
        f: "get_coins_list_v2",
        marketcap_min: filters.marketcap_min ?? null,
        marketcap_max: filters.marketcap_max ?? null,
        score_max: filters.score_max ?? null,
        score_min: filters.score_min ?? null,
        categories: filters.selectedcategories ?? null,
        chains: filters.selectedchains ?? null,
        change: filters.change ?? null,
        newly_listed: filters.newlyListed ?? null,
      };

      // Listing date parametrelerini ekle
      if (filters.listing_date) {
        requestParams.listing_date = filters.listing_date;
        console.log(`Filtering coins with listing_date: ${filters.listing_date} days`);
      }

      // Timestamp aralığı parametrelerini ekle
      if (filters.listing_date_start) {
        requestParams.listing_date_start = filters.listing_date_start;
      }

      if (filters.listing_date_end) {
        requestParams.listing_date_end = filters.listing_date_end;
      }

      // API çağrısını yap
      const response = await api.post("/client.php", requestParams);

      // Log API response to see if it contains category information
      console.log('API Response for Coins List:', response.data);

      let coinsList = [];

      if (response?.data?.output) {
        // Handle case when API returns data in topGainers/newListings format
        if (response.data.output.topGainers && filters.change) {
          // Log a sample coin to see if it contains category information
          if (response.data.output.topGainers.length > 0) {
            console.log('Sample Coin Data:', response.data.output.topGainers[0]);
          }

          coinsList = response.data.output.topGainers.map((coin: any) => {
            // API'den gelen verileri kullan, her kategori için ayrı skor
            const mappedCoin = {
              ...coin,
              // Ensure proper structure for CoinTableRow compatibility
              rank: parseInt(coin.id) || 0,
              image: coin.image || null,
              tokenomics: normalizeScoreObject({
                score: coin.tokenomics?.score || 0
              }),
              security: normalizeScoreObject({
                score: coin.security?.score || 0
              }),
              social: normalizeScoreObject({
                score: coin.social?.score || 0
              }),
              market: normalizeScoreObject({
                score: coin.market?.score || 0
              }),
              insights: normalizeScoreObject({
                score: coin.insights?.score || 0
              }),
              totalScore: normalizeScoreObject({
                score: parseInt(coin.totalScore?.score || coin.totalScore) || 0
              }),
              sevenDayChange: parseFloat(coin.priceChangePercentage24h) || 0,
            };
            return mappedCoin;
          });
        }
        // Handle case when API returns data in newListings format
        else if (response.data.output.newListings && filters.newlyListed) {
          coinsList = response.data.output.newListings.map((coin: any) => {
            // Use age or coin_age directly from the API response
            const ageValue = coin.coin_age || coin.age || "000d00";

            return {
              ...coin,
              // Ensure proper structure for CoinTableRow compatibility
              rank: parseInt(coin.id) || 0,
              image: coin.image || null,
              tokenomics: normalizeScoreObject({
                score: coin.tokenomics?.score || 0
              }),
              security: normalizeScoreObject({
                score: coin.security?.score || 0
              }),
              social: normalizeScoreObject({
                score: coin.social?.score || 0
              }),
              market: normalizeScoreObject({
                score: coin.market?.score || 0
              }),
              insights: normalizeScoreObject({
                score: coin.insights?.score || 0
              }),
              totalScore: normalizeScoreObject({
                score: parseInt(coin.totalScore?.score || coin.totalScore) || 0
              }),
              sevenDayChange: parseFloat(coin.priceChangePercentage24h) || 0,
              // Add age for showing coin age
              age: ageValue,
            };
          });
        }
        // Handle standard coins list response
        else if (Array.isArray(response.data.output)) {
          coinsList = response.data.output;
        }
      }

      // Check if meta field exists and handle demo coins
      if (response?.data?.meta?.isDemo && response?.data?.meta?.count > 0) {
        console.log(`Adding ${response.data.meta.count} demo coins for subscription limitation`);
        const demoCoins = this.generateDemoCoins(response.data.meta.count);
        coinsList = [...coinsList, ...demoCoins];
      }

      // TEMPORARY: For testing demo coins functionality
      // Remove this after testing - simulate meta response for demonstration
      if (coinsList.length > 0 && coinsList.length < 50) {
        console.log("DEMO: Adding 8 demo coins for testing purposes");
        const testDemoCoins = this.generateDemoCoins(8);
        coinsList = [...coinsList, ...testDemoCoins];
      }

      return coinsList;
    } catch (error) {
      console.error("Error fetching coins list:", error);
      return [];
    }
  }

  static async getCategories(): Promise<Category[]> {
    try {
      const response = await api.post("/client.php", {
        f: "get_categories",
      });

      return response?.data.output || [];
    } catch (error) {
      console.error("Error fetching categories:", error);
      return [];
    }
  }

  static async getChains(): Promise<Chain[]> {
    try {
      const response = await api.post("/client.php", {
        f: "get_chains",
      });

      return response?.data.output || [];
    } catch (error) {
      console.error("Error fetching chains:", error);
      return [];
    }
  }
}
export default CoinService;
