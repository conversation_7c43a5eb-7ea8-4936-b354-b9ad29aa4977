import { CoinData, CategoryScore } from './scoreMethodology';
import { allMethodologies, categoryMapping } from './methodologies';
import { CoinStatus } from '@/types/CoinStatus';
import { getScoreStatus } from './methodologyUtils';
import { Methodology } from '@/lib/types';

/**
 * Additional score information structure that provides more detailed
 * breakdown for the coin detail page
 */
export interface DetailedScore {
  name: string;
  total: number;
  weight?: number;
  status?: CoinStatus;
  description?: string;
  subScores: Array<{
    name: string;
    value: number;
    status?: CoinStatus;
    description?: string;
    dataPoints?: string[];
    methodology?: string;
    category?: string;
    id?: string; // API'nin beklediği gerçek subscore.id değeri
  }>;
}

/**
 * Market statistics specific to the coin detail page
 */
export interface DetailedMarketData {
  currentPrice?: number;
  Price?: number; // For the new API response format
  MarketCap: number;
  H24Volume: number;
  FullyDilutedValuation: number;
  volumeToMarketCapRatio?: number;
  priceChanges: Array<{
    name: string;
    value: number;
    displayName?: string;
  }>;
  // For backwards compatibility we also keep the old priceChanges structure
  priceChangesMap?: {
    '24h': number;
    '7d': number;
    '30d': number;
    '90d': number;
    '1y': number;
  };
}

/**
 * Social and project links structure
 */
export interface ProjectLinks {
  homepage: string[];
  blockchain_site: string[];
  official_forum_url: string[];
  chat_url: string[];
  announcement_url: string[];
  twitter_screen_name?: string;
  facebook_username?: string;
  telegram_channel_identifier?: string;
  subreddit_url?: string;
  repos_url: {
    github: string[];
    bitbucket: string[];
  };
}

/**
 * Structured social media links for the SocialsCard component
 */
export interface ProjectSocials {
  twitter_screen_name?: string;
  facebook_username?: string;
  telegram_channel_identifier?: string;
  subreddit_url?: string;
  discord_url?: string;
  github_url?: string;
  medium_url?: string;
  youtube_url?: string;
  linkedin_url?: string;
}

/**
 * Enhanced coin data for the detail page that works alongside the standard CoinData model
 * without interfering with existing implementations
 */
export interface LinkItem {
  type: string;
  url: string;
}

export interface CoinDetailData {
  // Basic information (can be derived from CoinData)
  id: string;
  name: string;
  symbol: string;
  rank: number;
  
  // API identifier
  geckoslug?: string;  // CoinGecko slug identifier for coin
  
  // Enhanced market data 
  marketData: DetailedMarketData;
  
  // Historical price data
  ath?: number;
  atl?: number;
  ath_change?: number;
  atl_change?: number;
  price?: number;
  
  // Visual assets
  image: string;
  sparkline7d?: string;
  
  // Price history data for charts (added for real-time charts)
  priceHistory?: {
    "7D": Array<{ date: string; value: number }>;
    "14D": Array<{ date: string; value: number }>;
    "30D": Array<{ date: string; value: number }>;
  };
  
  // Content
  description?: string;
  summary?: string;  // Short summary of the coin
  categoryNames: string[];  // List of categories the coin belongs to
  
  // External links - can be string (from API) or ProjectLinks (transformed)
  links?: ProjectLinks | string;
  
  // New API structure for links
  socialLinks?: LinkItem[];
  otherLinks?: LinkItem[];
  
  // Structured social links for the SocialsCard component
  socials?: ProjectSocials;
  
  // Score data 
  scores: DetailedScore[];
  total_score: number;
  
  // Historical score data for charts
  total_score_history?: Array<{
    date: string;
    score: number;
  }>;
  
  // Category scores for better typed PDF report generation
  categoryScores?: Record<string, number>;
  
  // Community & tracking data
  watchlist_count?: number;  // Kaç kullanıcının watchlist'inde olduğu bilgisi
  
  // Performance metrics for PDF reports
  metrics?: {
    priceStability?: string;
    volatilityIndex?: string;
    roi30d?: string;
    roi90d?: string;
    roi1y?: string;
  };
  
  // Risk analysis for PDF reports
  risk?: {
    marketRisk?: string;
    technologyRisk?: string;
    regulatoryExposure?: string;
    liquidityRisk?: string;
    centralizationFactor?: string;
  };
  
  // Supply metrics
  total_supply?: string | number | null;
  max_supply?: string | number | null;
  circulating_supply?: string | number | null;
  
  // Next unlock information
  unlock_amount?: string | null;
  nextunlock?: string | null;
  
  // Launchpad information as string array
  launchpad?: string[];
}

/**
 * Helper function to convert standard CoinData to enhanced CoinDetailData
 * This allows us to easily transform existing data to the enhanced format
 */
export const convertToCoinDetailData = (
  coinData: CoinData, 
  additionalData: Partial<CoinDetailData>
): CoinDetailData => {
  // Extract category scores into the detailed score format
  const scores: DetailedScore[] = Object.entries(coinData.categories).map(([key, category]) => {
    const categoryName = getCategoryDisplayName(key);
    return {
      name: categoryName,
      total: category.score,
      subScores: category.metrics.map(metric => ({
        name: metric.name,
        value: metric.value,
        description: metric.description
      }))
    };
  });
  
  // Extract social links for the SocialsCard component
  const links = additionalData.links || {
    homepage: [],
    blockchain_site: [],
    official_forum_url: [],
    chat_url: [],
    announcement_url: [],
    repos_url: {
      github: [],
      bitbucket: []
    }
  };
  
  // Map links to socials structure based on type (string or ProjectLinks)
  const socials: ProjectSocials = typeof links === 'string' ? {
    // Defaults if links is just a string
    twitter_screen_name: "",
    facebook_username: "",
    telegram_channel_identifier: "",
    subreddit_url: "",
    github_url: "",
    discord_url: "",
    medium_url: "",
    youtube_url: "",
    linkedin_url: ""
  } : {
    twitter_screen_name: links.twitter_screen_name,
    facebook_username: links.facebook_username,
    telegram_channel_identifier: links.telegram_channel_identifier,
    subreddit_url: links.subreddit_url,
    // Get GitHub URL from repos if available
    github_url: links.repos_url?.github?.[0] || "",
    // Other social media fields remain empty by default
    discord_url: "",
    medium_url: "",
    youtube_url: "",
    linkedin_url: ""
  };
  
  // Create the enhanced coin detail data object
  return {
    id: coinData.id,
    name: coinData.name,
    symbol: coinData.symbol,
    rank: coinData.rank,
    
    // Default market data structure derived from CoinData
    marketData: {
      currentPrice: additionalData.marketData?.currentPrice || coinData.marketData?.currentPrice || 0,
      MarketCap: additionalData.marketData?.MarketCap || 0,
      H24Volume: additionalData.marketData?.H24Volume || 0,
      FullyDilutedValuation: additionalData.marketData?.FullyDilutedValuation || 0,
      volumeToMarketCapRatio: additionalData.marketData?.volumeToMarketCapRatio,
      priceChanges: Array.isArray(coinData.marketData?.priceChanges) 
        ? coinData.marketData?.priceChanges 
        : [
          { name: '24h', value: 0, displayName: '24 Saat' },
          { name: '7d', value: 0, displayName: '7 Gün' },
          { name: '30d', value: 0, displayName: '30 Gün' },
          { name: '90d', value: 0, displayName: '90 Gün' },
          { name: '1y', value: 0, displayName: '1 Yıl' }
        ]
    },
    
    // Historical price data
    ath: additionalData.ath,
    atl: additionalData.atl,
    ath_change: additionalData.ath_change,
    atl_change: additionalData.atl_change,
    
    // Visual assets
    image: additionalData.image || `https://coinicons-api.vercel.app/api/icon/${coinData.symbol.toLowerCase()}`,
    sparkline7d: additionalData.sparkline7d,
    
    // Content
    description: additionalData.description || '',
    categoryNames: additionalData.categoryNames || ['Cryptocurrency'],
    
    // Links and socials
    links,
    socials: additionalData.socials || socials,
    
    // Score data
    scores,
    total_score: coinData.overallScore
  };
};

/**
 * Get a display friendly name for a category
 */
function getCategoryDisplayName(categoryKey: string): string {
  switch(categoryKey) {
    case 'tokenomics': return 'Tokenomics';
    case 'security': return 'Security';
    case 'social': return 'Social & Community';
    case 'market': return 'Market Performance';
    case 'technology': case 'insights': return 'Insights';
    default: return categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1);
  }
}

/**
 * Helper function to format currency values for display
 */
/**
 * Formats a currency value with proper denomination and precision
 * 
 * @param value - The numeric value to format
 * @param options - Formatting options
 * @returns Formatted string with appropriate denomination
 */
export const formatCurrencyValue = (
  value: number | undefined, 
  options: {
    currency?: string;
    prefix?: boolean;
    compact?: boolean;
    precision?: number;
    showZero?: boolean;
  } = {}
): string => {
  // Destructure options with defaults
  const { 
    currency = '$', 
    prefix = true,
    compact = true,
    precision,
    showZero = false
  } = options;
  
  // Handle undefined, null, or NaN
  if (value === undefined || value === null || Number.isNaN(value)) {
    return 'N/A';
  }
  
  // Handle zero case based on showZero option
  if (value === 0) {
    return showZero ? `${prefix ? currency : ''}0${!prefix ? ` ${currency}` : ''}` : 'N/A';
  }
  
  let formattedValue: string;
  
  // Compact notation (K, M, B, T)
  if (compact) {
    if (value >= 1e12) {
      formattedValue = `${(value / 1e12).toFixed(2)}T`;
    } else if (value >= 1e9) {
      formattedValue = `${(value / 1e9).toFixed(2)}B`;
    } else if (value >= 1e6) {
      formattedValue = `${(value / 1e6).toFixed(2)}M`;
    } else if (value >= 1e3) {
      formattedValue = `${(value / 1e3).toFixed(2)}K`;
    } else {
      // Variable precision for smaller values based on magnitude
      const dynamicPrecision = precision !== undefined ? precision : 
        value >= 1 ? 2 :
        value >= 0.1 ? 4 :
        value >= 0.001 ? 6 :
        value >= 0.00001 ? 8 : 10;
      
      formattedValue = value.toFixed(dynamicPrecision);
      
      // Trim trailing zeros, but keep at least 2 decimals for consistency
      if (dynamicPrecision > 2) {
        formattedValue = formattedValue.replace(/\.?0+$/, '');
        
        // Ensure at least 2 decimal places for values >= 1
        if (value >= 1 && !formattedValue.includes('.')) {
          formattedValue += '.00';
        } else if (value >= 1 && formattedValue.split('.')[1]?.length === 1) {
          formattedValue += '0';
        }
      }
    }
  } else {
    // Non-compact notation with locale string and fixed precision
    formattedValue = value.toLocaleString('en-US', {
      minimumFractionDigits: precision ?? 2,
      maximumFractionDigits: precision ?? 
        (value >= 1 ? 2 : value >= 0.1 ? 4 : value >= 0.001 ? 6 : 8)
    });
  }
  
  // Return with or without currency symbol based on prefix preference
  return prefix ? `${currency}${formattedValue}` : `${formattedValue} ${currency}`;
};

/**
 * Helper function to determine color based on score
 */
export const getScoreColor = (score: number): string => {
  if (score >= 90) return '#00D88A'; // Green
  if (score >= 75) return '#00B8D9'; // Blue
  if (score >= 65) return '#FFAB00'; // Amber
  if (score >= 50) return '#FF5630';
  return '#FF3B3B'; // Red
};

/**
 * Helper function to get descriptive name for score
 */
export const getScoreName = (score: number): string => {
  if (score >= 90) return 'Excellent';
  if (score >= 75) return 'Positive';
  if (score >= 65) return 'Average';
  if (score >= 50) return 'Weak';
  return 'Critical';
};

/**
 * Get default score values for each category in case they're missing in the data
 * Used as fallbacks to maintain a consistent UI when data is incomplete
 */
export const getDefaultScoreForCategory = (categoryName: string): number => {
  const defaultScores: Record<string, number> = {
    'tokenomics': 75,
    'security': 82,
    'social': 67,
    'market': 77,
    'insights': 70,
    'total': 74
  };
  
  return defaultScores[categoryName.toLowerCase()] || 70;
};

/**
 * Generates real methodology-based coin details data 
 * with authentic methodology content for each metric
 * while preserving the original metric structure
 */
export const generateRealMethodologyData = (baseCoinData: CoinDetailData): CoinDetailData => {
  // Preserve original category weights from API data instead of using hardcoded values
  const categoryWeights: Record<string, number> = {};
  
  // Create new scores array with real methodology data
  const newScores: DetailedScore[] = [];
  
  // Process each category from the original data to preserve structure
  baseCoinData.scores.forEach(originalCategory => {
    const categoryName = originalCategory.name;
    
    // Determine the category key for methodology mapping
    const categoryKey = getCategoryKeyFromName(categoryName);
    const methodologyKeys = categoryKey ? categoryMapping[categoryKey as keyof typeof categoryMapping] : [];
    
    // Create enhanced sub-scores by enriching original metrics with real methodology
    const enhancedSubScores = originalCategory.subScores.map(originalMetric => {
      // Find the most appropriate methodology for this metric
      const bestMethodologyKey = findBestMethodologyMatch(originalMetric.name, methodologyKeys || []);
      
      // Get the real methodology data if available
      const methodology = bestMethodologyKey ? 
        allMethodologies[bestMethodologyKey as keyof typeof allMethodologies] : 
        undefined;
      
      // Return enhanced metric with real methodology if available
      return {
        id: originalMetric.id || String(originalMetric.value),
        name: originalMetric.name,
        value: originalMetric.value,
        status: getScoreName(originalMetric.value) as CoinStatus,
        // Use real methodology data or fallback to original data, handling both classic and enhanced format
        description: ('description' in (methodology || {})) 
                      ? (methodology as any).description 
                      : ('whatAreWeScoring' in (methodology || {})) 
                        ? (methodology as any).whatAreWeScoring 
                        : originalMetric.description || `${originalMetric.name} analysis`,
        dataPoints: ('keyDataPoints' in (methodology || {}))
                      ? (methodology as any).keyDataPoints 
                      : [],
        // Store the methodology key for future reference
        methodology: bestMethodologyKey,
        category: categoryName
      };
    });
    
    // Create the category score with enhanced metrics & preserve original weights
    newScores.push({
      name: categoryName,
      total: originalCategory.total,
      weight: originalCategory.weight, // Orijinal API'den gelen weight değerini koruyoruz
      status: getScoreName(originalCategory.total) as CoinStatus,
      description: originalCategory.description || `${categoryName} analysis for ${baseCoinData.name}`,
      subScores: enhancedSubScores
    });
  });
  
  /**
   * Helper function to get category key from display name
   */
  function getCategoryKeyFromName(displayName: string): string | undefined {
    const normDisplayName = displayName.toLowerCase().replace(/&|\s+/g, '');
    
    // Map display names to category keys
    if (normDisplayName.includes('token')) return 'tokenomics';
    if (normDisplayName.includes('security')) return 'security';
    if (normDisplayName.includes('social') || normDisplayName.includes('community')) return 'socialMedia';
    if (normDisplayName.includes('market')) return 'market';
    if (normDisplayName.includes('insight')) return 'insights';
    
    return undefined;
  }
  
  /**
   * Helper function to find the best methodology match for a metric
   */
  function findBestMethodologyMatch(metricName: string | null | undefined, methodologyKeys: string[]): string | undefined {
    // Gelen metricName değerini kontrol et, null veya undefined ise boş string kullan
    const normalizedMetricName = (metricName || '').toLowerCase();
    
    // Check for direct matches first
    for (const key of methodologyKeys) {
      const normalizedKey = key.replace(/-/g, ' ');
      if (normalizedMetricName.includes(normalizedKey) || normalizedKey.includes(normalizedMetricName)) {
        return key;
      }
    }
    
    // Then check for partial matches
    for (const key of methodologyKeys) {
      const keyWords = key.split('-');
      for (const word of keyWords) {
        if (normalizedMetricName.includes(word) && word.length > 3) {
          return key;
        }
      }
    }
    
    // Specific mappings for common metrics that might not match directly
    if (normalizedMetricName.includes('inflation')) return 'inflation-rate';
    if (normalizedMetricName.includes('vesting')) return 'vesting-schedules';
    if (normalizedMetricName.includes('distribution')) return 'token-distribution';
    if (normalizedMetricName.includes('utility')) return 'token-utility';
    if (normalizedMetricName.includes('supply')) return 'token-supply';
    if (normalizedMetricName.includes('economics') || normalizedMetricName.includes('emission')) return 'token-economics';
    if (normalizedMetricName.includes('staking')) return 'staking-mechanics';
    if (normalizedMetricName.includes('burn')) return 'burn-mechanisms';
    if (normalizedMetricName.includes('transparency')) return 'tokenomics-transparency';
    if (normalizedMetricName.includes('security') || normalizedMetricName.includes('code')) return 'code-audits';
    if (normalizedMetricName.includes('community') || normalizedMetricName.includes('engagement')) return 'community-engagement';
    if (normalizedMetricName.includes('volume')) return 'market-volume';
    if (normalizedMetricName.includes('cap')) return 'market-cap';
    
    // Return undefined if no good match is found
    return undefined;
  }
  
  // Use the original total_score from the API instead of calculating it
  const totalScore = baseCoinData.total_score;
  
  // Aşağıdaki satırları artık kullanmıyoruz, ancak referans olarak bırakıyoruz:
  // const totalScore = Math.round(
  //   newScores.reduce((sum, category) => {
  //     const weight = category.weight || 0.2;
  //     return sum + (category.total * weight);
  //   }, 0)
  // );
  
  // Extract category scores into a flat object for PDF report generation
  const categoryScores: Record<string, number> = {};
  newScores.forEach(category => {
    categoryScores[category.name] = category.total;
  });
  
  // Generate performance metrics based on the coin's ID
  // (In a real-world scenario, these would come from API data)
  const performanceMetrics = {
    priceStability: baseCoinData.id === 'bitcoin' ? 'High' : 
                     baseCoinData.id === 'ethereum' ? 'Medium-High' : 'Medium',
    volatilityIndex: baseCoinData.id === 'bitcoin' ? '0.42 (Moderate)' : 
                      baseCoinData.id === 'ethereum' ? '0.58 (Moderate-High)' : '0.65 (High)',
    roi30d: `${Math.round((Math.random() * 20) - 5)}%`,
    roi90d: `${Math.round((Math.random() * 40) - 10)}%`,
    roi1y: `${Math.round((Math.random() * 150) - 20)}%`
  };
  
  // Generate risk analysis based on the coin's total score
  // (In a real-world scenario, these would come from API data)
  const riskAnalysis = {
    marketRisk: totalScore > 85 ? 'Low' : totalScore > 70 ? 'Medium-Low' : 'Medium',
    technologyRisk: totalScore > 85 ? 'Low' : totalScore > 70 ? 'Medium-Low' : 'Medium',
    regulatoryExposure: 'Medium', // Most cryptocurrencies have medium regulatory exposure
    liquidityRisk: baseCoinData.rank <= 5 ? 'Very Low' : 
                   baseCoinData.rank <= 20 ? 'Low' : 
                   baseCoinData.rank <= 50 ? 'Medium' : 'High',
    centralizationFactor: baseCoinData.id === 'bitcoin' ? 'Low' : 
                          baseCoinData.id === 'ethereum' ? 'Medium-Low' : 'Medium'
  };
  
  // Return updated coin data with real methodology
  console.log("generateRealMethodologyData supply values:", {
    total_supply: baseCoinData.total_supply,
    max_supply: baseCoinData.max_supply,
    circulating_supply: baseCoinData.circulating_supply
  });
  
  return {
    ...baseCoinData,
    scores: newScores,
    total_score: totalScore,
    price: baseCoinData.marketData?.currentPrice || baseCoinData.price,
    categoryScores,
    metrics: performanceMetrics,
    risk: riskAnalysis,
    // Ensure supply fields are explicitly preserved from the original data
    total_supply: baseCoinData.total_supply,
    max_supply: baseCoinData.max_supply,
    circulating_supply: baseCoinData.circulating_supply,
    // Preserve total_score_history from API data
    total_score_history: baseCoinData.total_score_history
  };
};

/**
 * Icons and colors for different project link types
 */
export const projectLinkIcons = {
  website: { color: '#22d3ee', name: 'globe' },
  explorer: { color: '#818cf8', name: 'external-link' },
  forum: { color: '#fb923c', name: 'message-square' },
  chat: { color: '#a78bfa', name: 'message-circle' },
  announcement: { color: '#f87171', name: 'bell' },
  github: { color: '#94a3b8', name: 'github' },
  twitter: { color: '#38bdf8', name: 'twitter' },
  facebook: { color: '#3b82f6', name: 'facebook' },
  telegram: { color: '#38bdf8', name: 'send' },
  reddit: { color: '#f97316', name: 'message-square' }
};

/**
 * Mock data for Bitcoin with enhanced detail information
 */
export const mockBitcoinDetailData: CoinDetailData = {
  id: "bitcoin",
  name: "Bitcoin",
  symbol: "BTC",
  rank: 1,
  marketData: {
    currentPrice: 65480.75,
    MarketCap: 1275849302748,
    H24Volume: 37586910294,
    FullyDilutedValuation: 1346789513511,
    volumeToMarketCapRatio: 0.0294,
    priceChanges: [
      { name: '24h', value: 2.5, displayName: '24 Saat' },
      { name: '7d', value: 5.8, displayName: '7 Gün' },
      { name: '30d', value: -3.2, displayName: '30 Gün' },
      { name: '90d', value: 15.7, displayName: '90 Gün' },
      { name: '1y', value: 45.3, displayName: '1 Yıl' }
    ]
  },
  price: 65480.75, // For PDF report generation
  ath: 69045,
  atl: 67.81,
  ath_change: -30.45,
  atl_change: 98765.43,
  image: "https://assets.coingecko.com/coins/images/1/large/bitcoin.png",
  sparkline7d: "https://www.coingecko.com/coins/1/sparkline.svg",
  description: "Bitcoin is the first successful internet money based on peer-to-peer technology; whereby no central bank or authority is involved in the transaction and production of the Bitcoin currency. It was created by an anonymous individual/group under the name, Satoshi Nakamoto. The source code is available publicly as an open source project, anybody can look at it and be part of the developmental process.\r\n\r\nBitcoin is changing the way we see money as we speak. The idea was to produce a means of exchange, independent of any central authority, that could be transferred electronically in a secure, verifiable and immutable way. It is a decentralized peer-to-peer internet currency making mobile payment easy, very low transaction fees, protects your identity, and it works anywhere all the time with no central authority and banks.\r\n\r\nBitcoin is designed to have only 21 million BTC ever created, thus making it a deflationary currency. Bitcoin uses the SHA-256 hashing algorithm with an average transaction confirmation time of 10 minutes. Miners today are mining Bitcoin using ASIC chip dedicated to only mining Bitcoin, and the hash rate has shot up to peta hashes.\r\n\r\nBeing the first successful online cryptography currency, Bitcoin has inspired other alternative currencies such as Litecoin, Peercoin, Primecoin, and so on.\r\n\r\nThe cryptocurrency then took off with the innovation of the turing-complete smart contract by Ethereum which led to the development of other amazing projects such as EOS, Tron, and even crypto-collectibles such as CryptoKitties.",
  categoryNames: ["Cryptocurrency", "Layer 1", "Store of Value", "Mineable", "PoW"],
  links: {
    homepage: ["https://bitcoin.org/", "https://bitcoin.com/"],
    blockchain_site: ["https://blockchain.info/", "https://btc.com/", "https://btc.tokenview.com/"],
    official_forum_url: ["https://bitcointalk.org/"],
    chat_url: [],
    announcement_url: ["https://bitcointalk.org/index.php?board=1.0"],
    twitter_screen_name: "bitcoin",
    facebook_username: "bitcoins",
    telegram_channel_identifier: "",
    subreddit_url: "https://reddit.com/r/bitcoin",
    repos_url: {
      github: ["https://github.com/bitcoin/bitcoin"],
      bitbucket: []
    }
  },
  socials: {
    twitter_screen_name: "bitcoin",
    facebook_username: "bitcoins",
    telegram_channel_identifier: "",
    subreddit_url: "https://reddit.com/r/bitcoin",
    github_url: "https://github.com/bitcoin/bitcoin",
    discord_url: "",
    medium_url: "",
    youtube_url: "",
    linkedin_url: ""
  },
  scores: [
    {
      name: "Tokenomics",
      total: 92,
      subScores: [
        { name: "Market Cap / FDV Ratio", value: 80, description: "Ratio between current market cap and fully diluted valuation" },
        { name: "Max Supply", value: 100, description: "Fixed maximum token supply limit of 21M BTC" },
        { name: "Inflation Rate", value: 90, description: "Low annual inflation rate with halving mechanism" },
        { name: "Token Vesting", value: 100, description: "No token vesting schedule as it was fairly launched" },
        { name: "Emission Score", value: 95, description: "Predictable emission schedule with decreasing inflation" }
      ]
    },
    {
      name: "Security",
      total: 95,
      subScores: [
        { name: "Code Security", value: 96, description: "Battle-tested codebase with minimal vulnerabilities" },
        { name: "Community Trust", value: 93, description: "Extremely high trust within the global crypto community" },
        { name: "Fundamental Health", value: 95, description: "Strong fundamentals with proven track record" },
        { name: "Governance Strength", value: 97, description: "Robust governance process with BIP implementation" },
        { name: "Market Stability", value: 97, description: "Excellent stability relative to market capitalization" }
      ]
    },
    {
      name: "Social & Community",
      total: 90,
      subScores: [
        { name: "Galaxy Score", value: 71, description: "Aggregate social media performance metric" },
        { name: "Engagement", value: 100, description: "Extremely high engagement across all social platforms" },
        { name: "Sentiment", value: 75, description: "Generally positive sentiment with some mixed opinions" },
        { name: "Mentions", value: 100, description: "Highest mention rate across social media platforms" },
        { name: "Density", value: 100, description: "High density of meaningful social interactions" }
      ]
    },
    {
      name: "Market Performance",
      total: 89,
      subScores: [
        { name: "Daily Trade Volume", value: 95, description: "Extremely high daily trading volume across exchanges" },
        { name: "CEX Tier Score", value: 92, description: "Listed on all top-tier centralized exchanges" },
        { name: "DEX Tier Score", value: 88, description: "Strong presence on decentralized exchanges" },
        { name: "CEX Coverage Score", value: 94, description: "Available on virtually all centralized exchanges" },
        { name: "DEX Coverage Score", value: 90, description: "Well represented on most decentralized exchanges" },
        { name: "TVL Score", value: 96, description: "Extremely high total value locked in protocols" },
        { name: "Risk Reward Rating", value: 85, description: "Favorable risk-to-reward ratio compared to alternatives" },
        { name: "TVL to Market Cap Score", value: 89, description: "Healthy ratio between TVL and market capitalization" },
        { name: "TVL Growth Score", value: 91, description: "Consistent growth in total value locked" }
      ]
    },
    {
      name: "Insights",
      total: 83,
      subScores: [
        { name: "Trending Category Score", value: 92, description: "High relevance within trending cryptocurrency categories" },
        { name: "VC Tier Score", value: 88, description: "Strong backing from top-tier venture capital firms" },
        { name: "Token Use Case", value: 95, description: "Clear and valuable use cases as store of value and medium of exchange" },
        { name: "Fundraising Score", value: 89, description: "No traditional fundraising, fair launch model" },
        { name: "Inflation / Deflation Rating", value: 94, description: "Deflationary design with halving mechanism" },
        { name: "Chain Interoperability Rating", value: 91, description: "Growing wrapped versions on other blockchains" },
        { name: "Token Redistribution Score", value: 87, description: "Fair and transparent mining-based distribution" },
        { name: "Token Buyback Mechanism", value: 86, description: "No formal buyback but adoption acts similarly" },
        { name: "Revenue Sharing", value: 85, description: "Mining rewards as indirect revenue sharing" },
        { name: "Project Development Status", value: 93, description: "Mature project with active development" },
        { name: "Real-World Applications", value: 90, description: "Growing adoption for payments, store of value, and remittances" }
      ]
    }
  ],
  total_score: 90,
  // Additional fields for PDF report generation
  categoryScores: {
    "Tokenomics": 92,
    "Security": 95,
    "Social & Community": 90,
    "Market Performance": 89,
    "Insights": 83
  },
  metrics: {
    priceStability: "High",
    volatilityIndex: "0.42 (Moderate)",
    roi30d: "+12.5%",
    roi90d: "+34.8%",
    roi1y: "+145.3%"
  },
  risk: {
    marketRisk: "Low",
    technologyRisk: "Low",
    regulatoryExposure: "Medium",
    liquidityRisk: "Very Low",
    centralizationFactor: "Low"
  }
};