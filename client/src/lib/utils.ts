import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

export function formatNumber(value: number): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

/**
 * Creates a debounced function that delays invoking the provided function
 * until after the specified wait time has elapsed since the last invocation.
 * @param func The function to debounce
 * @param wait The number of milliseconds to delay
 * @returns A debounced version of the provided function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function(...args: Parameters<T>): void {
    const later = () => {
      timeout = null;
      func(...args);
    };
    
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(later, wait);
  };
}

/**
 * Formats a coin age timestamp into a human readable format
 * This handles Unix timestamps in seconds format (e.g., 1268784000, 1375642260, 1620691200)
 * @param ageTimestamp Timestamp representing the coin's age
 * @param showAsRelative If true, show as "X days ago" or "Coming soon" instead of the date
 * @returns Formatted date string showing in format like "12 Jan 2016" or relative time
 */
export function formatCoinAge(ageTimestamp: string | undefined, showAsRelative: boolean = false): string {
  if (!ageTimestamp) return '';
  
  // Try parsing the timestamp as a number first
  const numericTimestamp = Number(ageTimestamp);
  if (!isNaN(numericTimestamp)) {
    // Convert Unix timestamp (seconds) to milliseconds
    const timestamp = numericTimestamp * 1000;
    const coinDate = new Date(timestamp);
    const currentDate = new Date();
    
    if (showAsRelative) {
      // Calculate difference in milliseconds between dates (without using Math.abs)
      const diffMs = currentDate.getTime() - coinDate.getTime();
      
      // Check if the date is in the future
      if (diffMs < 0) {
        // Future date - show as coming soon
        const futureDiffDays = Math.ceil(Math.abs(diffMs) / (1000 * 60 * 60 * 24));
        if (futureDiffDays <= 1) {
          return "Coming soon";
        } else {
          return `Coming in ${futureDiffDays} days`;
        }
      } else {
        // Past date - show as listed X days ago
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffDays < 1) {
          return "Listed today";
        } else if (diffDays === 1) {
          return "1 day ago";
        } else {
          return `${diffDays} days ago`;
        }
      }
    } else {
      // English month names
      const monthNames = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];
      
      // Format date as "12 Jan 2016"
      const day = coinDate.getDate();
      const month = monthNames[coinDate.getMonth()];
      const year = coinDate.getFullYear();
      
      return `${day} ${month} ${year}`;
    }
  }
  
  // If it's already a formatted string, return it as is
  return ageTimestamp;
}

/**
 * Formats a coin age timestamp with translation support
 * @param ageTimestamp Timestamp representing the coin's age
 * @param showAsRelative If true, show as "X days ago" or "Coming soon" instead of the date
 * @param t Translation function from useLanguage hook
 * @returns Formatted date string with translations
 */
export function formatCoinAgeWithTranslation(ageTimestamp: string | undefined, showAsRelative: boolean = false, t?: (key: string) => string): string {
  if (!ageTimestamp) return '';
  
  // Try parsing the timestamp as a number first
  const numericTimestamp = Number(ageTimestamp);
  if (!isNaN(numericTimestamp)) {
    // Convert Unix timestamp (seconds) to milliseconds
    const timestamp = numericTimestamp * 1000;
    const coinDate = new Date(timestamp);
    const currentDate = new Date();
    
    if (showAsRelative && t) {
      // Calculate difference in milliseconds between dates (without using Math.abs)
      const diffMs = currentDate.getTime() - coinDate.getTime();
      
      // Check if the date is in the future
      if (diffMs < 0) {
        // Future date - show as coming soon
        const futureDiffDays = Math.ceil(Math.abs(diffMs) / (1000 * 60 * 60 * 24));
        if (futureDiffDays <= 1) {
          return t("coinAge.comingSoon");
        } else {
          return t("coinAge.comingInDays").replace("{days}", futureDiffDays.toString());
        }
      } else {
        // Past date - show as listed X days ago
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffDays < 1) {
          return t("coinAge.listedToday");
        } else if (diffDays === 1) {
          return t("coinAge.oneDayAgo");
        } else {
          return t("coinAge.daysAgo").replace("{days}", diffDays.toString());
        }
      }
    } else {
      // Fallback to original formatting if no translation function
      return formatCoinAge(ageTimestamp, showAsRelative);
    }
  }
  
  // If it's already a formatted string, return it as is
  return ageTimestamp;
}

