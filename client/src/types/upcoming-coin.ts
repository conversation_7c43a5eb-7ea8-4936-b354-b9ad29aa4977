// Upcoming Coin Interface
// Bu interface, yaklaşan ICO, IDO, IEO vb. kripto projelerine ait verileri tanımlar

export interface SimpleScoreModel {
  score: number;
  status: "Excellent" | "Good" | "Fair" | "Poor" | "Bad";
}

// Eski sistem için uyumluluk
export interface ScoreModel {
  score: number;
  status: "Excellent" | "Good" | "Fair" | "Poor" | "Bad";
  valueText: string;
}

// Yeni API yanıt formatı - güncellendi
export interface UpcomingCoin {
  id: string;
  name: string;
  symbol: string;
  image: string; // Değişti: "logo" -> "image"
  launchDate: string;
  launchType: string;
  imcScore: SimpleScoreModel;
  financingScore: SimpleScoreModel;
  launchpadScore: SimpleScoreModel;
  investorScore: SimpleScoreModel;
  totalAiScore: SimpleScoreModel; // Değişti: number -> SimpleScoreModel
  socialScore: SimpleScoreModel; // Değişti: number -> SimpleScoreModel
  rank?: number; // Sıralama için opsiyonel değer
  demo?: boolean; // Demo proje işaretleyici
}

// API Response formatı
export interface UpcomingCoinResponse {
  success: boolean;
  output: UpcomingCoin[];
  meta?: {
    isDemo: boolean;
    count: number;
  };
}