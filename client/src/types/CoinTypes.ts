import { CoinStatus } from "./CoinStatus";

export interface CoinScore {
  score: number;
  label: string;
  status: CoinStatus;
}

export interface Coin {
  id: string;
  rank: number;
  name: string;
  symbol: string;
  price?: number;
  priceChanges?: {
    "24h": number;
    "7d": number;
    "30d": number;
    "90d": number;
    "1y": number;
  };
  tokenomics: CoinScore;
  security: CoinScore;
  social: CoinScore;
  market: CoinScore;
  insights: CoinScore;
  totalScore: CoinScore;
  sevenDayChange: number;
  // Optional fields for TopGainedCoins page
  category?: string;
  coin_age?: string;
  exchange?: string;
  marketCap?: string;
  volume24h?: string;
  score?: number;
  // Optional fields for upcoming coins
  saleType?: 'IDO' | 'IEO' | 'ICO' | 'SHO' | 'Seed' | 'IGO' | 'ISO';
  saleDate?: string;
  // Optional fields for UI display
  image?: string;
  // Demo flag for subscription limitations
  demo?: boolean;
}