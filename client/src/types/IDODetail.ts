/**
 * Interface for IDO Detail data
 * Supports both basic and advanced mode data structures
 */

export interface IDODetail {
  // Common fields for both modes
  id: string;
  name: string;
  symbol: string;
  description: string;
  logo: string;
  status: string;
  category: string;
  saleType: string;
  launchpad?: Array<{
    name: string;
    image: string;
  }>;
  launchpadInfo?: {
    name: string;
    image: string;
  };
  idoPrice: number;
  currentPrice?: number | null;
  priceChange?: number | null;
  website: string;
  whitepaper: string;
  github: string;
  twitter: string;
  telegram: string;
  discord?: string | null;
  medium?: string | null;
  facebook?: string | null;
  youtube?: string | null;
  linkedin?: string | null;
  reddit?: string | null;
  explorer?: string | null;
  announcement?: string | null;
  otherLinks?: string[];
  score?: number;
  
  // Optional fields that may exist in one or both modes
  tags?: string[];
  contracts?: Array<{
    network: string;
    address: string;
    tokenType?: string;
    decimals?: number;
    platformKey?: string;
  }>;
  
  // Advanced mode specific fields
  socials?: Array<{
    platform: string;
    url: string;
    handle?: string;
  }>;
  tokenAllocation?: Array<{
    name: string;
    percentage: number;
    tokens: string | number;
  }>;
  vestingInfoLinks?: Array<{
    title: string;
    url: string;
    label: string;
  }>;
  
  // Basic mode specific fields
  socialLinks?: {
    telegram?: string;
    website?: string;
    whitepaper?: string;
    twitter?: string;
    discord?: string;
    [key: string]: string | undefined;
  };
  
  // Fields that exist in advanced mode and might be undefined in basic mode
  allocation?: {
    teamAndAdvisors?: number | null;
    publicSale?: number | null;
    privateSale?: number | null;
    ecosystem?: number | null;
    liquidity?: number | null;
    treasury?: number | null;
    marketing?: number | null;
    maxSupply?: number;
    allocations?: Array<{
      name: string;
      value: number | null;
      percentage: number | null;
    }>;
  };
  
  distribution?: {
    released: number;
    locked: number;
    nextRelease: string;
    nextUnlock: number;
    untracked: number;
  };
  
  unlockEvents?: Array<{
    date: string;
    percentage: number;
    description: string;
    tokens: number;
    daysLeft: number;
    unlockAmount: number;
    unlockPercentage: number;
    maxSupply: number;
    allocations: Array<{
      name: string;
      percentage: number;
      tokens: string | number;
    }> | string[];
  }>;
  
  nextUnlockEvent?: {
    date: string;
    percentage: number;
    description: string;
    tokens: number;
    daysRemaining: number;
    unlockAmount: number;
    unlockPercentage: number;
  };
  
  funding?: {
    total?: number;
    totalRaised?: number;
    projectName?: string;
    description?: string | null;
    publicSale?: number;
    fundingRounds?: number | null;
    rounds?: Array<{
      name: string;
      amount: number;
      date: string;
    }>;
    valuations?: {
      seed?: number | null;
      private?: number | null;
      public?: number | null;
    };
    saleStartDate?: number;
    saleEndDate?: number;
    athRoi?: number;
    currentRoi?: number;
  };
  
  priceProjections?: Array<{
    scenario: string;
    price: string;
    marketCap: string;
    multiplier: string;
    usdPrice: number;
    btcPrice: number;
  }>;
  
  team?: Array<{
    name: string;
    role: string;
    bio?: string;
    linkedin?: string;
    twitter?: string;
    image?: string;
    position?: string;
    avatar?: string;
  }>;
  
  investors?: Array<{
    name: string | null;
    type: string | null;
    amount: string | null;
    logo: string | null;
  }>;

  metrics?: {
    totalScore?: number;
    scoreStatus?: string;
    scoreDetails?: {
      technology?: number;
      team?: number;
      tokenomics?: number;
      roadmap?: number;
    };
  };
}

// API response interface
export interface IDODetailResponse {
  success: boolean;
  output: IDODetail;
  error?: string;
}