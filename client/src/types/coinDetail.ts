export interface LinkItem {
  type: string;
  url: string;
}

export interface CoinDetailModel {
  id: string;
  name: string;
  symbol: string;
  gid: string;
  image: string;
  categories: string[];
  scores: Score[];
  marketData: MarketData;
  total_score: number;
  description: string;
  summary?: string;  // Short summary of the coin
  links: string | ProjectLinks;
  homepage: string;
  chat_url_1: string;
  chat_url_2: string;
  chat_url_3: string;
  geckoslug: string;
  marketcap_rank: string;
  sparkline7d: string;
  // Watchlist count - kaç kullanıcının izlediği
  watchlist_count?: number;
  // ATH/ATL Bilgileri - CoinDetailModel'de de mevcut olacak
  ath?: number | null;
  atl?: number | null;
  ath_change_percentage?: number | null;
  atl_change_percentage?: number | null;
  // Supply metrics - Added to base model so always available
  max_supply?: string | null;
  total_supply?: string | null;
  circulating_supply?: string | null;
  // Next unlock bilgileri
  unlock_amount?: string | null;
  nextunlock?: string | null;
  // Optional fields that may be present in API response
  socials?: any;
  risk?: any;
  metrics?: any;
  // New API structure for links
  socialLinks?: LinkItem[];
  otherLinks?: LinkItem[];
  // Launchpad information as string array
  launchpad?: string[];
}

export interface CoinAdvancedDetailModel extends CoinDetailModel {
  // Eski tip tanımları - geriye dönük uyumluluk için tutuyoruz
  ath_change?: string;
  atl_change?: string;
  ico_roi: string;
  ico_price: string;
  // Yeni eklenen unlock bilgileri
  unlock_amount?: string | null;
  nextunlock?: string | null;
  // Advanced mode'da gelen sparkline verisi
  sparkline?: any[] | string;
}

// ProjectLinks interface for properly typed links object
export interface ProjectLinks {
  homepage: string[];
  blockchain_site: string[];
  official_forum_url: string[];
  chat_url: string[];
  announcement_url: string[];
  twitter_screen_name?: string;
  facebook_username?: string;
  telegram_channel_identifier?: string;
  subreddit_url?: string;
  repos_url: {
    github: string[];
    bitbucket: string[];
  };
  // Legacy fields for backwards compatibility
  website?: string;
  explorer?: string;
  github?: string;
  whitepaper?: string;
  twitter?: string;
  reddit?: string;
  telegram?: string;
  [key: string]: any; // For flexible typing of other potential links
}

export interface Score {
  name: string;
  total: number;
  subScores: SubScore[];
}

export interface SubScore {
  id: string;
  name: string;
  value: number;
}

export interface MarketData {
  Price: number;
  H24Volume: number;
  MarketCap: number;
  FullyDilutedValuation: number;
  Changes: Change[];
}

export interface Change {
  name: string;
  value: number;
}

// Calculation data types for metric text responses
export interface CalculationDataM2 {
  max_supply: string;
  total_supply: string;
  circulating_supply: string;
}

export interface CalculationDataM3 {
  // Add specific fields for m3 when available
  [key: string]: any;
}

export interface CalculationDataM4 {
  // Add specific fields for m4 when available
  [key: string]: any;
}

export interface CalculationDataM5 {
  // Add specific fields for m5 when available
  [key: string]: any;
}

export interface CalculationDataM11 {
  overall_vesting_completion: string;
  team_members: string | null;
  advisors: string | null;
  seed_investors: string | null;
  private_investors: string | null;
  strategic_investors: string | null;
  kols: string | null;
}

export interface TeamMember {
  name: string;
  title: string;
}

export interface CalculationDataM50 {
  team_anonymity: {
    status: string;
    team_members: TeamMember[];
    total_members: number;
  };
}

export interface UseCase {
  type: string;
  description: string;
  analysis: string;
  check_status: string;
  date: string;
}

export interface CalculationDataM36 {
  token_use_case: {
    use_cases: UseCase[];
    total_use_cases: number;
    use_case_types: Record<string, number>;
    summary: string;
  };
}

export interface DeflatinaryMechanism {
  type: string;
  description: string;
  analysis: string;
  check_status: string;
  date: string;
}

export interface CalculationDataM38 {
  def_or_inf: {
    mechanisms: DeflatinaryMechanism[];
    total_mechanisms: number;
    mechanism_types: Record<string, number>;
    burn_mechanisms: number;
    buyback_mechanisms: number;
    overall_trend: string;
    summary: string;
  };
}

export interface RedistributionMechanism {
  type: string;
  description: string;
  analysis: string;
  check_status: string;
  date: string;
}

export interface CalculationDataM40 {
  token_redist_score: {
    redistribution_mechanisms: RedistributionMechanism[];
    total_mechanisms: number;
    mechanism_types: Record<string, number>;
    staking_mechanisms: number;
    fee_sharing_mechanisms: number;
    reward_mechanisms: number;
    burn_mechanisms: number;
    redistribution_score: string;
    summary: string;
  };
}

export interface GovernanceMechanism {
  type: string;
  description: string;
  analysis: string;
  check_status: string;
  date: string;
  is_positive: boolean;
}

export interface CalculationDataM52 {
  dao_governance: {
    governance_mechanisms: GovernanceMechanism[];
    total_mechanisms: number;
    governance_types: Record<string, number>;
    positive_mechanisms: number;
    decentralized_count: number;
    token_voting_count: number;
    delegation_count: number;
    multisig_count: number;
    limited_count: number;
    governance_score: string;
    is_decentralized: boolean;
    has_token_voting: boolean;
    has_delegation: boolean;
    has_multisig: boolean;
    summary: string;
  };
}

export interface BuybackMechanism {
  type: string;
  description: string;
  analysis: string;
  check_status: string;
  date: string;
}

export interface CalculationDataM41 {
  token_buyback: {
    buyback_mechanisms: BuybackMechanism[];
    total_mechanisms: number;
    mechanism_types: Record<string, number>;
    revenue_buybacks: number;
    burn_buybacks: number;
    buyback_score: string;
    has_burn_component: boolean;
    revenue_based: boolean;
    summary: string;
  };
}

export interface RevenueSharingMechanism {
  type: string;
  description: string;
  analysis: string;
  check_status: string;
  date: string;
  is_positive: boolean;
}

export interface CalculationDataM42 {
  revenue_sharing: {
    revenue_sharing_mechanisms: RevenueSharingMechanism[];
    total_mechanisms: number;
    sharing_types: Record<string, number>;
    positive_mechanisms: number;
    profit_sharing_count: number;
    fee_distribution_count: number;
    passive_income_count: number;
    staking_required_count: number;
    revenue_sharing_score: string;
    has_profit_sharing: boolean;
    has_fee_distribution: boolean;
    has_passive_income: boolean;
    requires_staking: boolean;
    summary: string;
  };
}

// Single value calculation data interfaces
export interface CalculationDataSingleValue {
  [key: string]: string;
}

export interface CalculationData {
  m1?: CalculationDataSingleValue;   // mcap_fdv_ratio
  m2?: CalculationDataSingleValue;   // max_supply (changed from complex to single)
  m3?: CalculationDataSingleValue;   // code_security
  m4?: CalculationDataSingleValue;   // community_trust
  m5?: CalculationDataSingleValue;   // total_volume
  m6?: CalculationDataSingleValue;   // best_cex_rank
  m7?: CalculationDataSingleValue;   // best_dex_rank
  m8?: CalculationDataSingleValue;   // cex_count
  m9?: CalculationDataSingleValue;   // dex_count
  m11?: CalculationDataSingleValue;  // vesting_schedule (changed from complex to single)
  m12?: CalculationDataSingleValue;  // emission_score_1y
  m14?: CalculationDataSingleValue;  // risk_reward_rating
  m17?: CalculationDataSingleValue;  // fundamental_health
  m18?: CalculationDataSingleValue;  // governance_strength
  m19?: CalculationDataSingleValue;  // market_stability
  m20?: CalculationDataSingleValue;  // operational_resilience
  m23?: CalculationDataSingleValue;  // risk_reward_rating
  m24?: CalculationDataSingleValue;  // alt_rank
  m27?: CalculationDataSingleValue;  // sentiment
  m28?: CalculationDataSingleValue;  // gecko_portfolio_count
  m29?: CalculationDataSingleValue;  // social_volume_24h
  m30?: CalculationDataSingleValue;  // social_dominance
  m36?: CalculationDataM36;
  m38?: CalculationDataM38;
  m40?: CalculationDataM40;
  m41?: CalculationDataM41;
  m42?: CalculationDataM42;
  m50?: CalculationDataM50;
  m52?: CalculationDataM52;
}

export interface MetricTextResponse {
  conclusion_text: string;
  what_are_we_scoring: string;
  why_is_this_important: string;
  calculation_data?: CalculationData;
}