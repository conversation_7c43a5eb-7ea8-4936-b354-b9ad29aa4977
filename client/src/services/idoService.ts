import api from "@/lib/api";
import { IDODetail, IDODetailResponse } from "@/types/IDODetail";

/**
 * Interface for error report request
 */
export interface ErrorReportRequest {
  ido_id: string;
  ido_name: string;
  detail: string;
}

/**
 * Interface for feature request
 */
export interface FeatureRequestDto {
  title?: string;
  description: string;
  message?: string;
  type?: string;
  userEmail?: string;
}

/**
 * Fetches basic information for a specific IDO by ID
 * Used when advanced mode is OFF
 * @param id - The ID of the IDO to fetch details for
 * @returns Promise<IDODetail> - The basic IDO information
 */
export const getBasicIDODetailById = async (id: string): Promise<IDODetail> => {
  try {
    console.log(`📡 Fetching basic IDO details for ID: ${id}`);

    const response = await api.post("/client.php", {
      f: "get_ido_detail_by_id", // Basic IDO details endpoint
      id: id, // IDO identifier
    });

    // API yanıtını kontrol et
    if (response.data && response.data.success && response.data.output) {
      console.log("✅ Successfully fetched basic IDO details:", response.data.output);
      return response.data.output;
    } else {
      const errorMessage =
        response.data?.errormsg || "Failed to fetch basic IDO details";
      console.error("❌ API error:", errorMessage);
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error("❌ Error fetching basic IDO details:", error);
    throw error;
  }
};

/**
 * Fetches advanced detailed information for a specific IDO by ID
 * Used when advanced mode is ON
 * @param id - The ID of the IDO to fetch details for
 * @returns Promise<IDODetail> - The detailed IDO information
 */
export const getAdvancedIDODetailById = async (id: string): Promise<IDODetail> => {
  try {
    console.log(`📡 Fetching advanced IDO details for ID: ${id}`);

    // Diğer servislerle aynı formatı kullanarak client.php'ye istek gönder
    const response = await api.post("/client.php", {
      f: "get_adv_ido_detail_by_id", // Advanced details endpoint
      id: id, // IDO identifier
    });

    // API yanıtını kontrol et
    if (response.data && response.data.success && response.data.output) {
      console.log("✅ Successfully fetched advanced IDO details:", response.data.output);
      return response.data.output;
    } else {
      const errorMessage =
        response.data?.errormsg || "Failed to fetch advanced IDO details";
      console.error("❌ API error:", errorMessage);
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error("❌ Error fetching advanced IDO details:", error);
    throw error;
  }
};

/**
 * Fetches IDO detail based on mode (advanced or basic)
 * @param id - The ID of the IDO to fetch details for
 * @param advancedMode - Whether to fetch advanced details
 * @returns Promise<IDODetail> - The IDO information
 */
export const getIDODetailById = async (id: string, advancedMode: boolean = false): Promise<IDODetail> => {
  if (advancedMode) {
    return getAdvancedIDODetailById(id);
  } else {
    return getBasicIDODetailById(id);
  }
};

/**
 * Sends a feature request
 * @param data - Feature request details
 * @returns Promise with API response
 */
export const requestFeature = async (data: FeatureRequestDto): Promise<any> => {
  try {
    console.log("📝 Submitting IDO feature request:", data);

    const response = await api.post("/client.php", {
      f: "request_feature",
      ...data,
    });

    if (response.data && response.data.success) {
      console.log("✅ Feature request submitted successfully:", response.data);
      return response.data;
    } else {
      const errorMessage =
        response.data?.errormsg || "Failed to submit feature request";
      console.error("❌ API error:", errorMessage);
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error("❌ Error submitting feature request:", error);
    throw error;
  }
};

/**
 * Reports an error related to an IDO
 * @param data - Error report details
 * @returns Promise with API response
 */
export const reportError = async (data: ErrorReportRequest): Promise<any> => {
  try {
    console.log("🐞 Submitting IDO error report:", data);

    const response = await api.post("/client.php", {
      f: "report_error_v2",
      ...data,
    });

    if (response.data && response.data.success) {
      console.log("✅ Error report submitted successfully:", response.data);
      return response.data;
    } else {
      const errorMessage =
        response.data?.errormsg || "Failed to submit error report";
      console.error("❌ API error:", errorMessage);
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error("❌ Error submitting error report:", error);
    throw error;
  }
};

/**
 * IDO service object to manage all IDO-related API calls
 */
const IDOService = {
  getIDODetailById,
  requestFeature,
  reportError,
};

export default IDOService;
