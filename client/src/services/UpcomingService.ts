import api from "@/lib/api";
import { UpcomingCoin, UpcomingCoinResponse, SimpleScoreModel } from "@/types/upcoming-coin";

// Demo proje isimleri ve sembolleri
const demoProjectNames = [
  "DemoFi Protocol", "TestChain Network", "SampleDEX", "MockToken Platform",
  "ExampleDAO", "TrialSwap", "DummyNFT Marketplace", "FakeStaking Protocol",
  "TestBridge Network", "SampleLending", "MockGaming Hub", "DemoMetaverse",
  "TrialOracle", "ExampleYield", "FakeInsurance", "TestSocial", "DummyPay",
  "SampleStorage", "MockIdentity", "DemoAnalytics"
];

const demoProjectSymbols = [
  "DEMO", "TEST", "SMPL", "MOCK", "EXMP", "TRIL", "DUMY", "FAKE",
  "TSTN", "SMPL", "MOCK", "DEMO", "TRIL", "EX<PERSON>", "<PERSON><PERSON>", "TEST",
  "DUMY", "SMPL", "MOCK", "DEMO"
];

const demoLaunchTypes = ["IDO", "IEO", "ICO", "SHO", "Seed"];

// Skor objesi oluşturma yardımcı fonksiyonu
const normalizeScoreObject = (scoreData: any): SimpleScoreModel => {
  if (typeof scoreData === 'object' && scoreData !== null) {
    const score = scoreData.score || 0;
    return {
      score: score,
      status: score >= 85 ? "Excellent" : score >= 75 ? "Good" : score >= 65 ? "Fair" : score >= 50 ? "Poor" : "Bad"
    };
  }

  const score = typeof scoreData === 'number' ? scoreData : 0;
  return {
    score: score,
    status: score >= 85 ? "Excellent" : score >= 75 ? "Good" : score >= 65 ? "Fair" : score >= 50 ? "Poor" : "Bad"
  };
};

// Yaklaşan ICO/IDO/IEO projeleri için API servisi
export const UpcomingService = {

  // Demo projeler oluşturma fonksiyonu
  generateDemoProjects: (count: number): UpcomingCoin[] => {
    const demoProjects: UpcomingCoin[] = [];

    for (let i = 0; i < count; i++) {
      const randomNameIndex = Math.floor(Math.random() * demoProjectNames.length);
      const randomSymbolIndex = Math.floor(Math.random() * demoProjectSymbols.length);
      const randomLaunchTypeIndex = Math.floor(Math.random() * demoLaunchTypes.length);

      // Random launch date (1-90 gün sonra)
      const randomDays = Math.floor(Math.random() * 90) + 1;
      const launchDate = new Date();
      launchDate.setDate(launchDate.getDate() + randomDays);
      const formattedDate = launchDate.toISOString().split('T')[0];

      const demoProject: UpcomingCoin = {
        id: `demo-upcoming-${i + 1}`,
        name: `${demoProjectNames[randomNameIndex]} ${i + 1}`,
        symbol: `${demoProjectSymbols[randomSymbolIndex]}${i + 1}`,
        image: "", // Demo projeler için boş image
        launchDate: formattedDate,
        launchType: demoLaunchTypes[randomLaunchTypeIndex],
        imcScore: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        financingScore: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        launchpadScore: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        investorScore: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        totalAiScore: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        socialScore: normalizeScoreObject({ score: Math.floor(Math.random() * 100) }),
        demo: true, // Demo proje işaretleyici
      };
      demoProjects.push(demoProject);
    }

    return demoProjects;
  },

  // Tüm yaklaşan projeleri getir (yeni UI için - v2 API endpoint)
  getUpcomingProjectsV2: async (): Promise<UpcomingCoin[]> => {
    try {
      console.log("📡 UpcomingService (V2): Yaklaşan projeler getiriliyor...");

      // Direkt API'ye istek yap - v2 endpoint
      const response = await api.post("/client.php", {
        f: "get_upcoming_idos_v2"
      });

      console.log("API V2 Ham Yanıtı:", response);

      // API yanıtını kontrol et
      if (response.data && response.data.success && Array.isArray(response.data.output)) {
        console.log(`✅ V2: ${response.data.output.length} adet yaklaşan proje yüklendi`);
        console.log("API V2 yanıtı örnek veri:", JSON.stringify(response.data.output[0], null, 2));
        // Logoları kontrol et
        const hasLogos = response.data.output.filter(item => item.image && item.image.length > 5).length;
        console.log(`🖼️ ${hasLogos} adet projede resim URL'si mevcut`);
        if (hasLogos > 0) {
          console.log(`🖼️ Örnek Resim URL:`, response.data.output.find(item => item.image && item.image.length > 5)?.image);
        }

        // API'den gelen verileri UpcomingCoin tipine uygun hale getir
        // Yeni API yanıt formatına göre düzenleme yapalım
        let result = response.data.output.map((item: any, index: number) => {
          // Debug log
          console.log(`📸 ${item.symbol || "UNKNOWN"} coin:`, {
            rawImage: item.image || ""
          });

          // Doğrudan API'den gelen veriyi kullan
          const mappedItem: UpcomingCoin = {
            id: item.id || `upcoming-v2-${index}`,
            rank: item.rank || index + 1,
            name: item.name || "",
            symbol: item.symbol || "",
            image: item.image || "",  // API'den gelen imaj URL'sini olduğu gibi kullan
            launchDate: item.launchDate || "",
            launchType: item.launchType || "",
            imcScore: item.imcScore || { score: 0, status: "Bad" },
            financingScore: item.financingScore || { score: 0, status: "Bad" },
            launchpadScore: item.launchpadScore || { score: 0, status: "Bad" },
            investorScore: item.investorScore || { score: 0, status: "Bad" },
            totalAiScore: item.totalAiScore || { score: 0, status: "Bad" },
            socialScore: item.socialScore || { score: 0, status: "Bad" }
          };
          return mappedItem;
        });

        // Meta alanı kontrolü ve demo projeler ekleme
        if (response?.data?.meta?.isDemo && response?.data?.meta?.count > 0) {
          console.log(`Adding ${response.data.meta.count} demo projects for subscription limitation`);
          const demoProjects = UpcomingService.generateDemoProjects(response.data.meta.count);
          result = [...result, ...demoProjects];
        }

        // TEMPORARY: Test için demo projeler ekleme - gerçek meta response olmadığında
        // Remove this after testing - simulate meta response for demonstration
        if (result.length > 0 && result.length < 50) {
          console.log("DEMO: Adding 5 demo projects for testing purposes");
          const testDemoProjects = UpcomingService.generateDemoProjects(5);
          result = [...result, ...testDemoProjects];
        }

        if (result.length > 0) {
          console.log("V2: Dönüştürülen ilk öğe:", JSON.stringify(result[0], null, 2));
          console.log("V2: Toplam öğe sayısı:", result.length);
        } else {
          console.warn("🟠 API V2 yanıtında proje verisi var ancak dönüştürme sonrasında veri bulunamadı");
        }

        return result;
      }

      console.warn("⚠️ API V2 yanıtı beklenen formatta değil:", response.data);
      return [];
    } catch (error) {
      console.error("❌ Upcoming projects V2 fetch error:", error);
      throw error;
    }
  },

  // Tüm yaklaşan projeleri getir
  getUpcomingProjects: async (): Promise<UpcomingCoin[]> => {
    try {
      console.log("📡 UpcomingService: Yaklaşan projeler getiriliyor...");

      // Direkt API'ye istek yap
      const response = await api.post("/client.php", {
        f: "get_upcoming_idos"
      });

      console.log("API Ham Yanıtı:", response);

      // API yanıtını kontrol et
      if (response.data && response.data.success && Array.isArray(response.data.output)) {
        console.log(`✅ ${response.data.output.length} adet yaklaşan proje yüklendi`);
        console.log("API yanıtı örnek veri:", JSON.stringify(response.data.output[0], null, 2));

        // API'den gelen verileri UpcomingCoin tipine uygun hale getir
        // ve sıralama için rank değeri ekle
        const result = response.data.output.map((item: any, index: number) => {
          // Burada herhangi bir null/undefined kontrolü yapılmıyor, null kontrolü ekleyelim
          const mappedItem = {
            ...item,
            id: item.id || `upcoming-${index}`,
            rank: index + 1,
            name: item.name || `Project ${index + 1}`,
            symbol: item.symbol || "UNKNOWN",
            launchType: item.launchType || "IDO"
          };
          return mappedItem;
        });

        if (result.length > 0) {
          console.log("Dönüştürülen ilk öğe:", JSON.stringify(result[0], null, 2));
          console.log("Toplam öğe sayısı:", result.length);
        } else {
          console.warn("🟠 API yanıtında proje verisi var ancak dönüştürme sonrasında veri bulunamadı");
        }

        return result;
      }

      console.warn("⚠️ API yanıtı beklenen formatta değil:", response.data);
      return [];
    } catch (error) {
      console.error("❌ Upcoming projects fetch error:", error);
      throw error;
    }
  },

  // Favorileri getir (kullanıcı giriş yapmışsa)
  getFavoriteProjects: async (): Promise<UpcomingCoin[]> => {
    try {
      console.log("📡 UpcomingService: Favori projeler getiriliyor...");

      // Direkt API'ye istek yap
      const response = await api.post("/client.php", {
        f: "get_favorite_upcoming"
      });

      if (response.data && response.data.success && Array.isArray(response.data.output)) {
        console.log(`✅ ${response.data.output.length} adet favori proje yüklendi`);
        return response.data.output.map((item: any, index: number) => ({
          ...item,
          rank: index + 1
        }));
      }

      console.warn("⚠️ Favori projeler API yanıtı beklenen formatta değil:", response.data);
      return [];
    } catch (error) {
      console.error("❌ Favorite upcoming projects fetch error:", error);
      throw error;
    }
  },

  // Detayları getir
  getProjectDetails: async (id: string): Promise<UpcomingCoin | null> => {
    try {
      console.log(`📡 UpcomingService: ${id} ID'li proje detayları getiriliyor...`);

      // Direkt API'ye istek yap
      const response = await api.post("/client.php", {
        f: "get_project_details",
        id: id
      });

      if (response.data && response.data.success && response.data.output) {
        console.log(`✅ ${id} ID'li proje detayları yüklendi`);
        return response.data.output;
      }

      console.warn(`⚠️ ${id} ID'li proje bulunamadı veya API yanıtı beklenen formatta değil:`, response.data);
      return null;
    } catch (error) {
      console.error(`❌ Project details fetch error for ID ${id}:`, error);
      throw error;
    }
  },
};