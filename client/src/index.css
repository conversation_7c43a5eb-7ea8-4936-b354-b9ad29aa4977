@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+Arabic:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;600;700&family=Noto+Sans+JP:wght@400;500;600;700&family=Noto+Sans+KR:wght@400;500;600;700&display=swap');

/* Import high contrast mode styles */
@import url('./styles/high-contrast.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {

  /* Enforce dark mode as the only option */
  :root {
    color-scheme: dark;
  }
}

body,
html,
#root {
  overflow-x: hidden;
}



/* Animation for card shimmer effect */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* Styling for metric-html-content from API */
.metric-html-content {
  /* Base text styling */
  color: #e2e8f0;
  /* Light text for dark background */
  font-size: 0.875rem;
  line-height: 1.6;
}

/* Styling for coin about section HTML content */
.coin-about-content {
  /* Base text styling */
  color: #e2e8f0;
  font-size: 0.9rem;
  line-height: 1.7;
}

/* Headings in coin about content */
.coin-about-content h1 {
  @apply text-xl font-bold mb-4 mt-6;
  color: #f8fafc;
}

.coin-about-content h2 {
  @apply text-lg font-bold mb-3 mt-5;
  color: #f8fafc;
}

.coin-about-content h3 {
  @apply text-base font-semibold mb-3 mt-4;
  color: #f1f5f9;
}

.coin-about-content h4 {
  @apply text-sm font-semibold mb-2 mt-3;
  color: #f1f5f9;
}

.coin-about-content h5,
.coin-about-content h6 {
  @apply text-sm font-medium mb-2 mt-3;
  color: #e2e8f0;
}

/* Lists in coin about content */
.coin-about-content ul {
  @apply list-disc list-inside mb-4 mt-2;
  color: #cbd5e1;
}

.coin-about-content ol {
  @apply list-decimal list-inside mb-4 mt-2;
  color: #cbd5e1;
}

.coin-about-content li {
  @apply mb-2 leading-relaxed;
  color: #cbd5e1;
}

.coin-about-content li::marker {
  color: #64b5f6;
}

/* Nested lists */
.coin-about-content ul ul,
.coin-about-content ol ol {
  @apply ml-4 mt-1 mb-2;
}

/* Paragraphs in coin about content */
.coin-about-content p {
  @apply mb-3;
  color: #e2e8f0;
}

/* Links in coin about content */
.coin-about-content a {
  @apply text-blue-400 hover:text-blue-300 underline transition-colors duration-200;
}

/* Strong and emphasis text */
.coin-about-content strong,
.coin-about-content b {
  @apply font-semibold;
  color: #f1f5f9;
}

.coin-about-content em,
.coin-about-content i {
  @apply italic;
  color: #cbd5e1;
}

/* Styling for IDO metric description HTML content */
.ido-metric-content {
  /* Base text styling */
  color: #cbd5e1;
  font-size: 0.8125rem;
  line-height: 1.6;
}

/* Headings in IDO metric content */
.ido-metric-content h1 {
  @apply text-lg font-bold mb-3 mt-4;
  color: #f8fafc;
}

.ido-metric-content h2 {
  @apply text-base font-bold mb-3 mt-4;
  color: #f8fafc;
}

.ido-metric-content h3 {
  @apply text-sm font-semibold mb-2 mt-3;
  color: #f1f5f9;
}

.ido-metric-content h4,
.ido-metric-content h5,
.ido-metric-content h6 {
  @apply text-sm font-medium mb-2 mt-3;
  color: #e2e8f0;
}

/* Lists in IDO metric content */
.ido-metric-content ul {
  @apply list-disc list-inside mb-3 mt-2;
  color: #94a3b8;
}

.ido-metric-content ol {
  @apply list-decimal list-inside mb-3 mt-2;
  color: #94a3b8;
}

.ido-metric-content li {
  @apply mb-1.5 leading-relaxed;
  color: #94a3b8;
}

.ido-metric-content li::marker {
  color: #3b82f6;
}

/* Nested lists */
.ido-metric-content ul ul,
.ido-metric-content ol ol {
  @apply ml-4 mt-1 mb-2;
}

/* Paragraphs in IDO metric content */
.ido-metric-content p {
  @apply mb-2;
  color: #cbd5e1;
}

/* Links in IDO metric content */
.ido-metric-content a {
  @apply text-blue-400 hover:text-blue-300 underline transition-colors duration-200;
}

/* Strong and emphasis text in IDO content */
.ido-metric-content strong,
.ido-metric-content b {
  @apply font-semibold;
  color: #e2e8f0;
}

.ido-metric-content em,
.ido-metric-content i {
  @apply italic;
  color: #94a3b8;
}

/* Divs and span elements in IDO metric content */
.ido-metric-content div,
.ido-metric-content span {
  color: inherit;
}

/* Headings in metric content - Tailwind stiller kullanılmıştır */
.metric-html-content h1 {
  @apply py-3 text-xl font-bold lg:py-6;
  color: #f8fafc;
}

.metric-html-content h2 {
  @apply py-3 text-lg font-bold lg:py-6;
  color: #f8fafc;
}

.metric-html-content h3,
.metric-html-content h4,
.metric-html-content h5,
.metric-html-content h6 {
  color: #f8fafc;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.metric-html-content h3 {
  font-size: 1.125rem;
}

/* Lists in metric content */
.metric-html-content ul,
.metric-html-content ol {
  @apply list-inside list-disc;
  margin: 0.75rem 0;
}

.metric-html-content li {
  margin-bottom: 0.375rem;
}

/* Table styling - Güncellenmiş Tailwind tabanlı tablo stilleri */
.metric-html-content table {
  @apply mt-2;
  width: 100%;
  border-collapse: collapse;
}

.metric-html-content tr {
  @apply flex;
}

.metric-html-content th {
  @apply m-1 flex w-full items-center rounded-md bg-[#b1cddb] px-1 py-1 shadow-sm shadow-black/20 lg:px-[12px] lg:py-[8px] lg:last:!w-full dark:bg-[#0EA5E915] dark:shadow-md dark:shadow-[#0f172a];
  text-align: left;
  color: #f8fafc;
  font-weight: 600;
}

.metric-html-content th:first-child {
  @apply lg:w-[50%];
}

.metric-html-content th:nth-child(2) {
  @apply lg:w-[35%];
}

.metric-html-content td {
  @apply m-1 flex w-full items-center rounded-md bg-gray-400/5 px-1 py-1 shadow-sm shadow-black/20 lg:px-[12px] lg:py-[8px] lg:last:!w-full dark:shadow-md dark:shadow-[#0f172a];
  text-align: left;
}

.metric-html-content td:first-child {
  @apply lg:w-[50%];
}

.metric-html-content td:nth-child(2) {
  @apply lg:w-[35%];
}

/* Links in metric content */
.metric-html-content a {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s;
}

.metric-html-content a:hover {
  color: #60a5fa;
  text-decoration: underline;
}

/* Paragraphs in metric content */
.metric-html-content p {
  margin-bottom: 0.75rem;
}

/* Blockquotes in metric content */
.metric-html-content blockquote {
  border-left: 3px solid #3b82f6;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #cbd5e1;
}

/* Code blocks and pre elements */
.metric-html-content code,
.metric-html-content pre {
  font-family: monospace;
  background-color: rgba(15, 23, 42, 0.7);
  border-radius: 0.25rem;
  padding: 0.2rem 0.4rem;
  font-size: 0.875rem;
}

.metric-html-content pre {
  white-space: pre-wrap;
  /* Otomatik satır kaydırma */
  word-wrap: break-word;
  /* Uzun kelimeleri bölme */
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

/* Animation for subtle background pulse effect */
@keyframes pulse-subtle {
  0% {
    opacity: 0.7;
  }

  50% {
    opacity: 0.9;
  }

  100% {
    opacity: 0.7;
  }
}

/* Animation for score value subtle pulse */
@keyframes subtle-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 3px rgba(var(--primary), 0.2);
  }

  50% {
    transform: scale(1.03);
    box-shadow: 0 0 6px rgba(var(--primary), 0.3);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 3px rgba(var(--primary), 0.2);
  }
}



@layer base {
  :root {
    /* Dark mode palette used as default */
    --background: 220 45% 11%;
    --foreground: 0 0% 100%;
    --card: 215 49% 16%;
    --card-foreground: 0 0% 100%;
    --popover: 220 45% 11%;
    --popover-foreground: 0 0% 100%;
    --primary: 201 90% 50%;
    --primary-foreground: 210 40% 12%;
    --secondary: 266 82% 60%;
    --secondary-foreground: 0 0% 100%;
    --muted: 215 25% 65%;
    --muted-foreground: 215 25% 65%;
    --accent: 38 95% 64%;
    --accent-foreground: 215 45% 12%;
    --destructive: 0 86% 65%;
    --destructive-foreground: 0 0% 100%;
    --border: 212 46% 21%;
    --input: 212 46% 21%;
    --ring: 201 90% 50%;
    --success: 142 76% 45%;
    --success-foreground: 210 40% 12%;
    --radius: 0.5rem;
    color-scheme: dark;
  }

  /* We keep this for backward compatibility but it uses the same values as :root */
  .dark {
    --background: 220 45% 11%;
    --foreground: 0 0% 100%;
    --card: 215 49% 16%;
    --card-foreground: 0 0% 100%;
    --popover: 220 45% 11%;
    --popover-foreground: 0 0% 100%;
    --primary: 201 90% 50%;
    --primary-foreground: 210 40% 12%;
    --secondary: 266 82% 60%;
    --secondary-foreground: 0 0% 100%;
    --muted: 215 25% 65%;
    --muted-foreground: 215 25% 65%;
    --accent: 38 95% 64%;
    --accent-foreground: 215 45% 12%;
    --destructive: 0 86% 65%;
    --destructive-foreground: 0 0% 100%;
    --border: 212 46% 21%;
    --input: 212 46% 21%;
    --ring: 201 90% 50%;
    --success: 142 76% 45%;
    --success-foreground: 210 40% 12%;
    --radius: 0.5rem;
    color-scheme: dark;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Global page width container settings - consistent across all sections */
  .main-container {
    width: 100%;
    /* Forces global alignment */
    max-width: 1600px;
    /* Matches outermost structure */
    margin: 0 auto;
    /* Centers all sections correctly */
    padding: 0 2rem;
    /* Consistent padding on all sides */
  }

  /* Hide navigation when in fullscreen iframe mode */
  body.fullscreen-iframe #main-navigation,
  body.fullscreen-iframe .sidebar-navigation {
    display: none !important;
  }

  /* Fullscreen iframe mode - prevents scrolling of parent page */
  body.fullscreen-iframe {
    overflow: hidden;
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-4px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(4px);
  }
}

@keyframes pulse-slow {
  0% {
    opacity: 0.85;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.85;
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-5px);
  }

  60% {
    transform: translateY(-2px);
  }
}

@keyframes pulse-subtle {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.05);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes bell-ring {

  0%,
  100% {
    transform: rotate(0);
  }

  10%,
  30%,
  50% {
    transform: rotate(8deg);
  }

  20%,
  40%,
  60% {
    transform: rotate(-8deg);
  }

  70% {
    transform: rotate(4deg);
  }

  80% {
    transform: rotate(-4deg);
  }

  90% {
    transform: rotate(0);
  }
}

@keyframes score-pulse-subtle {
  0% {
    opacity: 0;
    transform: scale(0.2);
  }

  50% {
    opacity: 0.12;
    transform: scale(1.35);
  }

  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 15px 2px rgba(102, 178, 255, 0.5), inset 0 0 0 1px rgba(102, 178, 255, 0.4);
    outline-color: rgba(102, 178, 255, 0.9);
  }

  50% {
    box-shadow: 0 0 30px 8px rgba(102, 178, 255, 0.7), inset 0 0 0 1px rgba(102, 178, 255, 0.6);
    outline-color: rgba(102, 178, 255, 1);
  }

  100% {
    box-shadow: 0 0 15px 2px rgba(102, 178, 255, 0.5), inset 0 0 0 1px rgba(102, 178, 255, 0.4);
    outline-color: rgba(102, 178, 255, 0.9);
  }
}

@keyframes outline-pulse {
  0% {
    outline-color: rgba(102, 178, 255, 0.6);
  }

  50% {
    outline-color: rgba(102, 178, 255, 1);
  }

  100% {
    outline-color: rgba(102, 178, 255, 0.6);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Firefox-specific scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--primary), 0.3) transparent;
}

/* Force scrollbars to always be visible even in Firefox */
.scrollbar-custom,
[class*="overflow-"] {
  scrollbar-width: thin !important;
  scrollbar-color: rgba(var(--primary), 0.3) transparent !important;
}

@layer utilities {

  /* Background position and size utilities for gradients */
  .bg-size-200 {
    background-size: 200% 200%;
  }

  .bg-pos-0 {
    background-position: 0% 0%;
  }

  .bg-pos-100 {
    background-position: 100% 100%;
  }

  .animate-shake {
    animation: shake 0.6s cubic-bezier(.36, .07, .19, .97) both;
  }

  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 2.5s infinite;
  }

  .animate-score-pulse-subtle {
    animation: score-pulse-subtle 2s ease-in-out infinite;
  }

  .animate-fadeIn {
    animation: fadeIn 0.4s ease-out forwards;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 2s ease-in-out infinite;
  }

  .animate-subtle-pulse {
    animation: pulse-subtle 3s ease-in-out infinite;
    transform-origin: center;
  }

  .animate-bounce {
    animation: bounce 1s ease infinite;
  }

  .animate-bell-ring {
    animation: bell-ring 1.8s ease-in-out infinite;
    transform-origin: top center;
  }

  /* Radial gradient utility for enterprise-grade UI */
  .bg-gradient-radial {
    background-image: radial-gradient(var(--tw-gradient-stops));
  }

  /* Security gradients */
  .bg-security-gradient {
    background: linear-gradient(145deg,
        rgba(var(--primary), 0.05) 0%,
        rgba(var(--secondary), 0.05) 100%);
  }

  /* Background gradients for authentication pages - dark mode only */
  .bg-auth-gradient {
    background: linear-gradient(135deg,
        hsl(215, 43%, 10%) 0%,
        hsl(220, 40%, 14%) 50%,
        hsl(240, 40%, 18%) 100%);
  }

  /* Interactive effects */
  .hover-scale {
    transition: transform 0.2s ease-in-out;
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }

  /* Button pulse effect */

  /* Spin-once animation for feature icons */
  @keyframes spin-once {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .group-hover\:animate-spin-once {
    transition: transform 0.3s ease;
  }

  .group:hover .group-hover\:animate-spin-once {
    animation: spin-once 0.6s cubic-bezier(0.2, 0.8, 0.2, 1);
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(var(--primary), 0.4);
    }

    70% {
      box-shadow: 0 0 0 10px rgba(var(--primary), 0);
    }

    100% {
      box-shadow: 0 0 0 0 rgba(var(--primary), 0);
    }
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-5px);
    }

    100% {
      transform: translateY(0px);
    }
  }

  .text-gradient-primary {
    background: linear-gradient(90deg, #0B86FB, #8A3AFB);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .perspective-800 {
    perspective: 800px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .animate-pulse-on-click:active {
    animation: pulse 0.5s;
  }

  /* Card fluent shadow */
  .fluent-shadow {
    box-shadow:
      0 4px 14px rgba(0, 0, 0, 0.25),
      0 12px 32px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    border: 1px solid rgba(30, 41, 59, 0.8);
    background-color: rgba(15, 23, 42, 0.6);
  }

  .fluent-shadow:hover {
    box-shadow:
      0 6px 18px rgba(0, 0, 0, 0.3),
      0 20px 40px rgba(0, 0, 0, 0.5);
    background-color: rgba(15, 23, 42, 0.7);
    transform: translateY(-1px);
  }

  /* Micro-interactions */
  .button-press {
    transition: transform 0.1s ease;
  }

  .button-press:active {
    transform: scale(0.97);
  }

  /* Enterprise-grade form element focus states */

  /* CoinList Tour custom styles */
  .tour-spotlight-enhanced {
    filter: drop-shadow(0 0 15px rgba(102, 178, 255, 0.7)) !important;
    animation: pulse-border 2s infinite !important;
  }

  .tour-interactive-element {
    cursor: pointer !important;
    z-index: 10001 !important;
    position: relative !important;
  }

  @keyframes pulse-border {
    0% {
      box-shadow: 0 0 0 4px rgba(51, 153, 255, 0.8), 0 0 30px 10px rgba(51, 153, 255, 0.4) !important;
    }

    70% {
      box-shadow: 0 0 0 4px rgba(51, 153, 255, 0.8), 0 0 30px 15px rgba(51, 153, 255, 0.7) !important;
    }

    100% {
      box-shadow: 0 0 0 4px rgba(51, 153, 255, 0.8), 0 0 30px 10px rgba(51, 153, 255, 0.4) !important;
    }
  }

  /* Badge notification animations */
  @keyframes slide-in {
    from {
      transform: translateX(100%);
      opacity: 0;
    }

    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-out {
    from {
      transform: translateX(0);
      opacity: 1;
    }

    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out forwards;
  }

  .animate-slide-out {
    animation: slide-out 0.3s ease-out forwards;
  }

  .focus-enterprise {
    @apply transition-all duration-200;
  }

  .focus-enterprise:focus-within {
    @apply ring-2 ring-primary/20 ring-offset-0;
  }

  /* Custom scrollbar for enhanced UI */
  .scrollbar-enterprise {
    scrollbar-width: thin;
    scrollbar-color: rgba(var(--primary), 0.3) transparent;
  }

  .scrollbar-enterprise::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-enterprise::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-enterprise::-webkit-scrollbar-thumb {
    background-color: rgba(var(--primary), 0.3);
    border-radius: 20px;
  }



  /* Coin table responsive styling for screens larger than 1600px */
  @media (min-width: 1600px) {

    #top-gained-coins-table,
    #recently-listed-coins-table,
    #coinlist-table {
      width: 1600px !important;
      max-width: 1600px !important;
      margin: 0 auto !important;
    }

    .name-column {
      width: 200px !important;
      flex-grow: 1 !important;
    }

    .total-score-col {
      width: 100px !important;
    }

    .seven-day-change-col {
      width: 100px !important;
    }
  }

  /* Global column widths outside media query */
  .seven-day-change-col {
    width: 100px !important;
    min-width: 100px !important;
  }

  .total-score-col {
    width: 140px !important;
    min-width: 140px !important;
  }

  .name-column {
    width: 200px !important;
    min-width: 200px !important;
    flex-grow: 1 !important;
  }

  /* Upcoming page table cell styling */
  .upcoming-table-container .TableCell {
    color: #E7EBF0;
  }

  /* Global scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    display: block !important;
    /* Force scrollbars to display */
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  /* Scrollbars - light & dark mode compatible */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(var(--primary), 0.3);
    border-radius: 20px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--primary), 0.5);
  }

  /* Force scrollbars to be visible on all overflow containers */
  [class*="overflow-"]::-webkit-scrollbar {
    display: block !important;
  }

  /* Custom scrollbar class - more subtle for special containers */
  .scrollbar-custom::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    /* Important: This is for horizontal scrollbars */
  }

  .scrollbar-custom::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb {
    background-color: rgba(var(--primary), 0.3);
    border-radius: 20px;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--primary), 0.5);
  }
}