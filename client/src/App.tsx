import React, { Suspense, useEffect } from 'react';
import { Switch, Route, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "@/components/theme-provider";
import { Navigation } from "@/components/Navigation";
import { queryClient } from "./lib/queryClient";
import { Sidebar as OldSidebar } from "@/components/Sidebar";
import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { AuthProvider } from "@/hooks/use-auth";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { SidebarProvider } from "@/contexts/SidebarContext";
import { FilterProvider } from "@/contexts/FilterContext";
import { monitorPerformance } from "@/features/shared/utils/performance";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { UserGuidePage } from "@/pages/UserGuide";
import Footer from "@/components/Footer";
import { TooltipProvider } from "@/components/ui/tooltip";
import { LanguageDebugger } from "@/components/LanguageDebugger";
import { WatchlistProvider } from "@/components/watchlists/WatchlistProvider";
import { Sidebar } from "@/components/ui/sidebar";
import { useSidebar } from "@/contexts/SidebarContext";
import { Toaster } from "@/components/ui/toaster";
import TestFeedbackToast from "@/components/TestFeedbackToast";
import { SubscriptionProvider } from "@/contexts/SubscriptionContext";
import { useScrollToTop } from "@/hooks/useScrollToTop";
import { useGlobalSubscriptionModal } from "@/hooks/useGlobalSubscriptionModal";
import { SubscriptionUpgradeModal } from "@/components/modals/SubscriptionUpgradeModal";

// test
// Import from existing file as is
// All the lazy loaded component imports remain the same

// Lazy load all route components using React.lazy directly
const NewLandingPage = React.lazy(() => import('@/pages/NewLandingPage'));
const AdPolicy = React.lazy(() => import('@/pages/AdPolicy'));
const CoinList = React.lazy(() => import('@/pages/CoinList'));
// CoinListBadgeExperiment removed as requested
const CoinDetail = React.lazy(() => import('@/pages/CoinDetail'));
const ComparePage = React.lazy(() => import('@/pages/ComparePage'));
// WatchList and WatchlistDashboard removed as requested
const NewlyListedCoins = React.lazy(() => import('@/pages/NewlyListedCoins'));
// Import the consolidated WatchList component
const WatchList = React.lazy(() => import('./pages/WatchList'));
// Import TopMovers (renamed from TopGainedCoins)
const TopMovers = React.lazy(() => import('@/pages/TopMovers'));
// Import FeedbackPage for user feedback during testing phase
const FeedbackPage = React.lazy(() => import('@/pages/FeedbackPage'));
// Yeni UpcomingIDO sayfası (eski UpcomingIDONew)
const UpcomingIDO = React.lazy(() => import('@/pages/UpcomingIDO'));
const IDODetail = React.lazy(() => import('@/pages/IDODetail'));
// TopGainers sayfası artık kullanılmıyor
// Feature pages removed as requested
const Academy = React.lazy(() => import('@/pages/Academy'));
const UpcomingIDOsInfo = React.lazy(() => import('@/pages/UpcomingIDOsInfo'));
const CompareCoinsInfo = React.lazy(() => import('@/pages/CompareCoinsInfo'));
const Generator = React.lazy(() => import('@/pages/Generator'));
const PortfolioAnalysis = React.lazy(() => import('@/pages/PortfolioAnalysis'));
const PortfolioGeneratorInfo = React.lazy(() => import('@/pages/PortfolioGeneratorInfo'));
const PortfolioAnalysisInfo = React.lazy(() => import('@/pages/PortfolioAnalysisInfo'));
const Launchpads = React.lazy(() => import('@/pages/Launchpads'));
const TopLaunchpadsInfo = React.lazy(() => import('@/pages/TopLaunchpadsInfo'));
// MemeScoreInfo removed as requested test
const AirdropScore = React.lazy(() => import('@/pages/AirdropScore'));
const AirdropScoreInfo = React.lazy(() => import('@/pages/AirdropScoreInfo'));
const GemScout = React.lazy(() => import('@/pages/GemScout'));
const GemScoutInfo = React.lazy(() => import('@/pages/GemScoutInfo'));
const Documentation = React.lazy(() => import('@/pages/Documentation'));
const DocusaurusDocumentation = React.lazy(() => import('@/pages/DocusaurusDocumentation'));
const NotFound = React.lazy(() => import('@/pages/not-found'));
const AdminDashboard = React.lazy(() => import('@/pages/AdminDashboard'));
const AdminUsers = React.lazy(() => import('@/pages/AdminUsers')); // Admin users management page
const AdminForum = React.lazy(() => import('@/pages/AdminForum')); // Admin forum management page
const AdminBlog = React.lazy(() => import('@/pages/AdminBlog')); // Admin blog management page
const Login = React.lazy(() => import('@/pages/NewLoginPage')); // Custom new login page with authentication
const AdminLogin = React.lazy(() => import('@/pages/AdminLogin')); // Admin-specific login page
const ResetPassword = React.lazy(() => import('@/pages/ResetPassword')); // Password reset page
const Forum = React.lazy(() => import('@/pages/Forum'));
const Blog = React.lazy(() => import('@/pages/Blog'));
const BlogDetail = React.lazy(() => import('@/pages/BlogDetail'));
const PublicPortfolios = React.lazy(() => import('@/pages/PublicPortfolios'));
const PublicPortfolioDetail = React.lazy(() => import('@/pages/PublicPortfolioDetail'));
const CookiePolicy = React.lazy(() => import('@/pages/CookiePolicy'));
const Careers = React.lazy(() => import('@/pages/Careers'));
const Disclaimer = React.lazy(() => import('@/pages/Disclaimer'));
const PrivacyPolicy = React.lazy(() => import('@/pages/PrivacyPolicy'));
const Terms = React.lazy(() => import('@/pages/Terms'));
const Features = React.lazy(() => import('@/pages/Features'));
const FAQ = React.lazy(() => import('@/pages/FAQ'));
const PricingPage = React.lazy(() => import('@/pages/PricingPage')); // Add PricingPage import
const Profile = React.lazy(() => import('@/pages/Profile'));
const RewardsShop = React.lazy(() => import('@/pages/RewardsShop'));
const Leaderboard = React.lazy(() => import('@/pages/Leaderboard'));
const AIAssistant = React.lazy(() => import('@/pages/AIAssistant'));
const AdminN8N = React.lazy(() => import('@/pages/AdminN8N')); // Add AdminN8N page
const StripeSubscriptionPage = React.lazy(() => import('@/pages/StripeSubscriptionPage'));
const StripeCheckoutPage = React.lazy(() => import('@/pages/StripeCheckoutPage'));
// NewPricingPage removed - functionality has been integrated into PricingPage
const PaymentSuccessPage = React.lazy(() => import('@/pages/PaymentSuccessPage'));
const PaymentCancelPage = React.lazy(() => import('@/pages/PaymentCancelPage'));
const MembershipManagementPage = React.lazy(() => import('@/pages/MembershipManagementPage'));
// Animation demo system has been removed

// PHP API entegrasyonunu test etmek için APITest bileşeni
const APITest = React.lazy(() => import('@/components/APITest'));
// Watchlist API test page
const ApiTestPage = React.lazy(() => import('@/pages/ApiTest'));

// TokenDebugger component removed as it doesn't exist

// Alert testi bileşeni kaldırıldı

// Sidebar demo page for testing expandable sidebar
const SidebarDemo = React.lazy(() => import('@/pages/SidebarDemo'));

// WebSocket crypto alert test page
const TestAlertsPage = React.lazy(() => import('@/pages/TestAlertsPage'));

// Email verification pages
const EmailVerificationPage = React.lazy(() => import('@/pages/EmailVerificationPage'));
const VerifyEmail = React.lazy(() => import('@/pages/VerifyEmail'));
const RequestEmailVerification = React.lazy(() => import('@/pages/RequestEmailVerification'));

// Enhanced loading fallback component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="space-y-4 text-center">
      <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
      <div className="text-primary">Loading...</div>
    </div>
  </div>
);

// Import our RouteAuthWrapper component
import { RouteAuthWrapper } from '@/components/auth/RouteAuthWrapper';

function Router() {
  const [location] = useLocation();
  const { isExpanded } = useSidebar();

  // Automatically scroll to top when route changes
  useScrollToTop();

  // Special styling for pages where the sidebar is hidden
  const noSidebarPages = ['/', '/pricing', '/stripe-subscription', '/stripe-checkout', '/payment-success', '/payment-cancel', '/profile/membership', '/docusaurus-docs', '/features', '/faq', '/public-portfolios', '/privacy', '/terms', '/cookie-policy', '/disclaimer', '/login', '/verify-email'];
  const isNoSidebarPage = noSidebarPages.includes(location) || location.startsWith('/public-portfolios/');
  const isAdminPage = location.startsWith('/admin');

  // All pages except specifically excluded ones should use the sidebar
  const useNewSidebar = !isNoSidebarPage && !isAdminPage;

  return (
    <main className={cn(
      "min-h-screen w-full overflow-x-hidden flex justify-center",
      isNoSidebarPage ? "" : ""
    )}>
      {/* Display the new sidebar for all pages that need a sidebar */}
      {useNewSidebar && <Sidebar />}

      {/* Display admin sidebar */}
      {isAdminPage && <AdminSidebar />}

      <div className={cn(
        "px-4 sm:px-8 py-6 relative",
        "max-w-[1600px] w-full overflow-x-hidden ml-[56px]",
        isNoSidebarPage
          ? ""
          : useNewSidebar
            ? isExpanded ? "transition-all duration-300" : "transition-all duration-300" // Using fixed width for content margin
            : isAdminPage
              ? "ml-[67px] sm:ml-[75px] lg:ml-[79px]"
              : "ml-[67px] sm:ml-[75px] lg:ml-[79px]" // Original margins
      )}>
        <ErrorBoundary>
          <Suspense fallback={<PageLoader />}>
            <Switch>
              {/* Public information pages - No auth required */}
              <Route path="/" component={NewLandingPage} />
              <Route path="/ad-policy" component={AdPolicy} />
              <Route path="/cookie-policy" component={CookiePolicy} />
              <Route path="/careers" component={Careers} />
              <Route path="/login" component={Login} />
              <Route path="/login/admin" component={AdminLogin} />
              <Route path="/reset-password" component={ResetPassword} />
              <Route path="/verify-email" component={VerifyEmail} />
              <Route path="/request-email-verification" component={RequestEmailVerification} />
              {/* User Guide route removed - using Documentation instead */}
              <Route path="/disclaimer" component={Disclaimer} />
              <Route path="/privacy" component={PrivacyPolicy} />
              <Route path="/terms" component={Terms} />
              <Route path="/features" component={Features} />
              <Route path="/faq" component={FAQ} />
              <Route path="/pricing" component={PricingPage} />
              {/* Route for /new-pricing redirects to /pricing */}
              <Route path="/new-pricing">
                {() => {
                  const [, setLocation] = useLocation();
                  setLocation("/pricing");
                  return null;
                }}
              </Route>
              <Route path="/stripe-subscription" component={StripeSubscriptionPage} />
              <Route path="/stripe-checkout" component={StripeCheckoutPage} />
              <Route path="/payment-success" component={PaymentSuccessPage} />
              <Route path="/payment-cancel" component={PaymentCancelPage} />
              <Route path="/profile/membership" component={MembershipManagementPage} />
              <Route path="/blog" component={Blog} />
              <Route path="/blog/:slug" component={BlogDetail} />
              <Route path="/forum" component={Forum} />
              <Route path="/documentation" component={Documentation} />
              <Route path="/docusaurus-docs" component={DocusaurusDocumentation} />
              {/* External feedback form redirection */}
              <Route path="/feedback">
                {() => {
                  window.location.href = 'https://forms.gle/nRmGJUoMfdpaVZjVA';
                  return null;
                }}
              </Route>

              {/* Information pages about features (no auth required) */}
              <Route path="/upcoming-idos-info" component={UpcomingIDOsInfo} />
              <Route path="/compare-coins-info" component={CompareCoinsInfo} />
              <Route path="/portfolio-generator-info" component={PortfolioGeneratorInfo} />
              <Route path="/portfolio-analysis-info" component={PortfolioAnalysisInfo} />
              <Route path="/top-launchpads-info" component={TopLaunchpadsInfo} />
              <Route path="/gemscout-info" component={GemScoutInfo} />
              {/* Feature info pages removed as requested */}
              <Route path="/public-portfolios" component={PublicPortfolios} />
              <Route path="/public-portfolios/:id" component={PublicPortfolioDetail} />

              {/* Browse-only pages (see content, but interactive features need auth) */}
              <Route path="/coinlist">
                {() => <CoinList />}
              </Route>
              {/* CoinListBadgeExperiment route removed as requested */}
              <Route path="/coin/:id">
                {(params) => <CoinDetail />}
              </Route>
              <Route path="/coindetail/:id">
                {(params) => <CoinDetail />}
              </Route>

              <Route path="/topgainers">
                {() => {
                  window.location.href = '/top-movers';
                  return null;
                }}
              </Route>
              <Route path="/upcoming" component={UpcomingIDO} />
              {/* Eski sayfayı kaldırdık, yeni tasarımı kullanıyoruz */}
              <Route path="/ido/:id" component={IDODetail} />
              <Route path="/launchpads" component={Launchpads} />
              <Route path="/airdropscore" component={AirdropScore} />
              <Route path="/gemscout" component={GemScout} />
              <Route path="/aiassistant" component={AIAssistant} />
              <Route path="/academy" component={Academy} />
              {/* Redirected to external feedback form instead of local form */}
              {/* API testi için özel sayfa */}
              <Route path="/api-test" component={APITest} />
              {/* New Watchlist API Test page */}
              <Route path="/watchlist-api-test" component={ApiTestPage} />
              {/* WebSocket crypto alert test page */}
              <Route path="/test-alerts" component={TestAlertsPage} />
              {/* TokenDebugger route removed as component doesn't exist */}
              {/* Twitter Login Callback */}
              <Route path="/callback" component={React.lazy(() => import('@/pages/TwitterCallback'))} />
              {/* Feature pages removed as requested */}

              {/* Personalized/interactive features (auth required) */}
              <Route path="/compare">
                {() => (
                  <RouteAuthWrapper
                    requireAuth
                    showAuthPrompt
                    promptTitle="Authentication Required"
                    promptDescription="Please log in to use the comparison feature"
                  >
                    <ComparePage />
                  </RouteAuthWrapper>
                )}
              </Route>

              {/* Removed watchlist routes */}
              {/* Redirect /new-coins to /newly-listed-coins */}
              <Route path="/new-coins">
                {() => {
                  const [, setLocation] = useLocation();
                  // Using Wouter's setLocation for client-side routing
                  setLocation("/newly-listed-coins");
                  return null;
                }}
              </Route>
              {/* Also handle /newcoins (without hyphen) for backward compatibility */}
              <Route path="/newcoins">
                {() => {
                  const [, setLocation] = useLocation();
                  // Using Wouter's setLocation for client-side routing
                  setLocation("/newly-listed-coins");
                  return null;
                }}
              </Route>
              {/* Redirect from old path to new path */}
              <Route path="/top-gained-coins">
                {() => {
                  const [, setLocation] = useLocation();
                  setLocation("/top-movers");
                  return null;
                }}
              </Route>

              {/* New top-movers route */}
              <Route path="/top-movers">
                {() => (
                  <RouteAuthWrapper requireAuth>
                    <TopMovers />
                  </RouteAuthWrapper>
                )}
              </Route>
              <Route path="/newly-listed-coins">
                {() => (
                  <RouteAuthWrapper requireAuth>
                    <NewlyListedCoins />
                  </RouteAuthWrapper>
                )}
              </Route>

              {/* Redirect from old route to new route */}
              <Route path="/recently-listed-coins">
                {() => {
                  const [, setLocation] = useLocation();
                  setLocation("/newly-listed-coins");
                  return null;
                }}
              </Route>
              <Route path="/watchlist">
                {() => (
                  <RouteAuthWrapper
                    requireAuth
                    showAuthPrompt
                    promptTitle="İzleme Listelerine Erişim"
                    promptDescription="İzleme listelerini görüntülemek ve düzenlemek için giriş yapmalısınız"
                  >
                    <WatchList />
                  </RouteAuthWrapper>
                )}
              </Route>
              <Route path="/generator">
                {() => (
                  <RouteAuthWrapper requireAuth>
                    <Generator />
                  </RouteAuthWrapper>
                )}
              </Route>
              <Route path="/portfolio-analysis">
                {() => (
                  <RouteAuthWrapper requireAuth>
                    <PortfolioAnalysis />
                  </RouteAuthWrapper>
                )}
              </Route>
              {/* Feature page routes removed as requested */}

              <Route path="/profile">
                {() => (
                  <RouteAuthWrapper
                    requireAuth
                    showAuthPrompt
                    promptTitle="Authentication Required"
                    promptDescription="Please log in to view your profile"
                  >
                    <Profile />
                  </RouteAuthWrapper>
                )}
              </Route>

              <Route path="/rewards">
                {() => (
                  <RouteAuthWrapper
                    requireAuth
                    showAuthPrompt
                    promptTitle="Authentication Required"
                    promptDescription="Please log in to access the rewards shop"
                  >
                    <RewardsShop />
                  </RouteAuthWrapper>
                )}
              </Route>

              <Route path="/leaderboard">
                {() => (
                  <RouteAuthWrapper
                    requireAuth
                    showAuthPrompt
                    promptTitle="Authentication Required"
                    promptDescription="Please log in to view the leaderboard"
                  >
                    <Leaderboard />
                  </RouteAuthWrapper>
                )}
              </Route>

              {/* Admin routes (admin auth required) */}
              <Route path="/admin">
                {() => (
                  <RouteAuthWrapper requireAuth requireAdmin>
                    <AdminDashboard />
                  </RouteAuthWrapper>
                )}
              </Route>

              <Route path="/admin/workflow-automation">
                {() => (
                  <RouteAuthWrapper requireAuth requireAdmin>
                    <AdminN8N />
                  </RouteAuthWrapper>
                )}
              </Route>

              <Route path="/admin/forum">
                {() => (
                  <RouteAuthWrapper requireAuth requireAdmin>
                    <AdminForum />
                  </RouteAuthWrapper>
                )}
              </Route>

              <Route path="/admin/blog">
                {() => (
                  <RouteAuthWrapper requireAuth requireAdmin>
                    <AdminBlog />
                  </RouteAuthWrapper>
                )}
              </Route>

              <Route path="/admin/users">
                {() => (
                  <RouteAuthWrapper requireAuth requireAdmin>
                    <AdminUsers />
                  </RouteAuthWrapper>
                )}
              </Route>

              {/* Demo page for expandable sidebar */}
              <Route path="/sidebar-demo" component={SidebarDemo} />

              {/* Fallback for 404 */}
              <Route>
                <NotFound />
              </Route>
            </Switch>
          </Suspense>
        </ErrorBoundary>
      </div>
    </main>
  );
}

function App() {
  useEffect(() => {
    monitorPerformance();
  }, []);

  // Global subscription modal hook
  const { isOpen, message, closeModal } = useGlobalSubscriptionModal();

  return (
    <ThemeProvider storageKey="ui-theme">
      <React.StrictMode>
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <LanguageProvider>
              <SidebarProvider>
                <FilterProvider>
                  <TooltipProvider>
                    <SubscriptionProvider>
                      <WatchlistProvider>
                      {/* Using the AppLayout for pages that need the sidebar */}
                      <div className="relative bg-background overflow-x-hidden min-h-screen flex flex-col">
                        <Navigation />

                        {/* Conditional rendering based on URL - Show new expandable sidebar on specific pages */}
                        <Router />
                        <Footer />
                        {/* Add Toaster component here */}
                        <Toaster />
                        {/* Test Feedback component for development phase */}
                        <TestFeedbackToast />
                        {/* Global Subscription Upgrade Modal */}
                        <SubscriptionUpgradeModal
                          open={isOpen}
                          onOpenChange={closeModal}
                          message={message}
                        />
                        {/* Subscription Debugger component removed - will add it more properly later */}
                      </div>
                      </WatchlistProvider>
                    </SubscriptionProvider>
                  </TooltipProvider>
                </FilterProvider>
              </SidebarProvider>
            </LanguageProvider>
          </AuthProvider>
        </QueryClientProvider>
      </React.StrictMode>
    </ThemeProvider>
  );
}

export default App;