import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ComparisonCoin } from '@/data/compareDemoData';

interface ComparisonStore {
  selectedCoins: ComparisonCoin[];
  currentUserId: string | null;
  addCoin: (coin: ComparisonCoin) => void;
  removeCoin: (coinId: string) => void;
  clearCoins: () => void;
  clearOnUserChange: (newUserId: string | null) => void;
  isSelected: (coinId: string) => boolean;
  getSelectedIds: () => string[];
}

export const useComparisonStore = create<ComparisonStore>()(
  persist(
    (set, get) => ({
      selectedCoins: [],
      currentUserId: null,
      
      addCoin: (coin: ComparisonCoin) => {
        set((state) => {
          const isAlreadySelected = state.selectedCoins.some(c => c.id === coin.id);
          if (isAlreadySelected) {
            return state;
          }
          return {
            selectedCoins: [...state.selectedCoins, coin]
          };
        });
      },
      
      removeCoin: (coinId: string) => {
        set((state) => ({
          selectedCoins: state.selectedCoins.filter(coin => coin.id !== coinId)
        }));
      },
      
      clearCoins: () => {
        set({ selectedCoins: [] });
      },
      
      clearOnUserChange: (newUserId: string | null) => {
        const currentState = get();
        // Only update state if user actually changed to prevent infinite loops
        if (currentState.currentUserId !== newUserId) {
          console.log('User changed, clearing comparison coins:', { 
            previous: currentState.currentUserId, 
            new: newUserId 
          });
          set({ 
            selectedCoins: [], 
            currentUserId: newUserId 
          });
        }
        // No state update needed if user ID is the same
      },
      
      isSelected: (coinId: string) => {
        return get().selectedCoins.some(coin => coin.id === coinId);
      },
      
      getSelectedIds: () => {
        return get().selectedCoins.map(coin => coin.id);
      }
    }),
    {
      name: 'comparison-coins-storage',
      // Persist both selected coins and current user ID
      partialize: (state) => ({ 
        selectedCoins: state.selectedCoins,
        currentUserId: state.currentUserId 
      }),
    }
  )
);