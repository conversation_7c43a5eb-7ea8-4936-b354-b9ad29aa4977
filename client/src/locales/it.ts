/**
 * Italian localization strings
 */
const it = {
  // Common strings
  "common": {
    "loading": "Caricamento...",
    "error": "Errore",
    "retry": "<PERSON><PERSON><PERSON><PERSON>",
    "save": "<PERSON><PERSON>",
    "cancel": "<PERSON><PERSON><PERSON>",
    "close": "<PERSON><PERSON>",
    "success": "Successo",
    "viewMore": "Vedi di più",
    "back": "Indietro",
    "next": "Avanti",
    "search": "Cerca",
    "searchCoinsAndTokens": "Cerca Monete e Progetti Futuri",
    "searching": "Ricerca in corso...",
    "noResults": "Nessun risultato trovato",
    "coins": "Monete",
    "upcomingIdos": "Prossimi IDO",
  },

  // Nav related strings
  "nav": {
    "home": "Home",
    "portfolio": "Portafoglio",
    "explore": "Esplora",
    "news": "Notizie",
    "learn": "Impara",
    "profile": "Profilo",
    "settings": "Impostazioni",
    "logout": "Disconnettersi",
    "login": "Accedi",
    "register": "Registrati",
    "trending": "Tendenze",
    "favorites": "Preferiti",
    "watchlist": "Lista Preferiti",
  },

  // System related strings
  "system": {
    "choose": "Scegli la lingua",
    "current": "Lingua corrente",
    "searching": "Ricerca in corso...",
  },

  // Error related strings
  "error": {
    "criticalError": "Errore Critico",
    "somethingWentWrong": "Qualcosa è andato storto",
    "criticalErrorMessage": "Si è verificato un errore critico. Prova ad aggiornare la pagina o torna alla homepage.",
    "returnToHome": "Torna alla Home",
    "multipleErrorsDetected": "Rilevati Errori Multipli",
    "unexpectedError": "Si è verificato un errore inaspettato",
    "refreshPage": "Aggiorna Pagina",
    "goToHome": "Vai alla Home",
    "clearErrorLogs": "Cancella Log Errori",
    "anErrorOccurred": "Si è verificato un errore",
  },

  // Data related strings
  "data": {
    "loading": "Caricamento dati",
    "empty": "Nessun dato disponibile",
    "error": "Errore nel caricamento dei dati",
  },

  // Auth related strings
  "auth": {
    "email": "Email",
    "email.placeholder": "Inserisci la tua email",
    "email.description": "Non condivideremo mai la tua email con nessun altro",
    "password": "Password",
    "password.placeholder": "Inserisci la tua password",
    "password.create": "Crea una password",
    "password.confirm": "Conferma Password",
    "password.confirm.placeholder": "Conferma la tua password",
    "password.show": "Mostra password",
    "password.hide": "Nascondi password",
    "password.strength": "Sicurezza password",
    "password.strength.weak": "Debole",
    "password.strength.good": "Buona",
    "password.strength.strong": "Forte",
    "password.reset.message": "La funzione di reimpostazione della password arriverà presto",
    "forgotPassword": "Password dimenticata?",
    "resetPassword": "Reimposta Password",
    "signin": "Accedi",
    "signin.loading": "Accesso in corso...",
    "signin.securely": "Accedi in modo sicuro",
    "signup": "Registrati",
    "signout": "Esci",
    "accountCreated": "Account creato con successo",
    "passwordResetSent": "Email per reimpostazione password inviata",
    "invalidCredentials": "Email o password non valida",
    "continueWith": "Continua con",
    "username": "Nome utente",
    "username.placeholder": "Scegli un nome utente",
    "agree": "Accetto i",
    "service": "Termini di Servizio",
    "and": "e",
    "privacy": "Informativa sulla Privacy",
    "password.criteria.length": "[MISSING] At least 8 characters",
    "password.criteria.uppercase": "[MISSING] At least one uppercase letter",
    "password.criteria.lowercase": "[MISSING] At least one lowercase letter",
    "password.criteria.number": "[MISSING] At least one number",
    "password.criteria.special": "[MISSING] At least one special character",
    "captcha.protected": "[MISSING] This form is protected by reCAPTCHA",
    "terms.service": "Termini di Servizio",
    "terms.and": "e",
    "terms.privacy": "Informativa sulla Privacy",
  },

  // Login related strings
  "login": {
    "prompt": "Inserisci le tue credenziali per accedere",
  },

  // Success related strings
  "success": {
    "title": "Successo",
    "description": "Accesso effettuato con successo",
  },

  // Register related strings
  "register": {
    "title": "Crea un account",
    "create": "Crea account",
    "creating": "Creazione account in corso...",
    "haveAccount": "Hai già un account?",
    "success": "Registrazione riuscita",
    "success.detail": "Il tuo account è stato creato con successo",
    "success.login": "Il tuo account è stato creato, effettua il login.",
    "failed": "Registrazione fallita",
    "failed.generic": "Si è verificato un errore durante la registrazione. Riprova.",
    "generic": "Si è verificato un errore. Riprova.",
    "success.email_verify": "[MISSING] Registration successful. Please check your email to verify your account.",
  },

  // Form related strings
  "form": {
    "invalidFields": "Per favore compila correttamente tutti i campi obbligatori",
  },

  // Validation related strings
  "validation": {
    "email": "Inserisci un indirizzo email valido",
    "length": "La password deve contenere almeno 6 caratteri",
  },

  // Coindetail related strings
  "coindetail": {
    "overview": "Panoramica",
    "fundamentals": "Fondamentali",
    "technicals": "Tecnici",
    "news": "Notizie",
    "social": "Social",
    "developers": "Sviluppatori",
    "analysis": "Analisi",
    "price": "Prezzo",
    "volume": "Volume",
    "marketCap": "Capitalizzazione di mercato",
    "circulatingSupply": "Offerta circolante",
    "totalSupply": "Offerta totale",
    "maxSupply": "Offerta massima",
    "allTimeHigh": "Massimo storico",
    "allTimeLow": "Minimo storico",
    "pricePrediction": "Previsione di prezzo",
    "addToWatchlist": "Aggiungi alla Watchlist",
    "removeFromWatchlist": "Rimuovi dalla Watchlist",
  },

  // Highlights related strings
  "highlights": {
    "topGainers": "Top Movimenti",
    "newListings": "Nuovi Listini",
    "upcomingIDOs": "Prossimi IDO",
    "gemCoins": "Monete Gemma",
    "topAirdrops": "Top Airdrop",
    "score": "Punteggio",
  },

  // Portfolio related strings
  "portfolio": {
    "totalValue": "Valore Totale",
    "recentActivity": "Attività Recente",
    "performance": "Performance",
    "holdings": "Possedimenti",
    "addAsset": "Aggiungi Asset",
    "editAsset": "Modifica Asset",
    "noAssets": "Nessun asset nel portafoglio",
    "24hChange": "Variazione 24h",
    "allocation": "Allocazione",
    "profit": "Profitto/Perdita",
  },

  // Settings related strings
  "settings": {
    "appearance": "Aspetto",
    "language": "Lingua",
    "notifications": "Notifiche",
    "security": "Sicurezza",
    "preferences": "Preferenze",
    "theme": "Tema",
    "lightMode": "Modalità Chiara",
    "darkMode": "Modalità Scura",
    "systemDefault": "Predefinito Sistema",
  },

  // Sidebar related strings
  "sidebar": {
    "lock": "Blocca",
    "unlock": "Sblocca",
    "collapse": "Riduci",
    "cryptoRating": "Valutazione Crypto",
    "idoRating": "Valutazione IDO",
    "compareCoins": "Confronta Monete",
    "recentListings": "Listini Recenti",
    "topMovers": "Top Movimenti",
    "watchlist": "La mia Watchlist",
    "aiPortfolio": "Guida Portafoglio AI",
    "portfolioAudit": "Analisi Portafoglio",
    "launchpads": "Launchpad",
    "airdrops": "Centro Airdrop",
    "aiAssistant": "Assistente AI",
    "gemScout": "Gem Scout",
    "soon": "PRESTO",
  },

  // Format related strings
  "format": {
    "thousand": "K",
    "million": "M",
    "billion": "B",
    "trillion": "T",
  },

  // Coin related strings
  "coin": {
    "age": "Età:",
  },

  // Score related strings
  "score": {
    "title": "Punteggio CoinScout",
    "description": "Il nostro punteggio proprietario valuta la qualità complessiva e il potenziale del progetto.",
    "details": "Basato su tokenomics, sicurezza, attività sui social media e altro.",
  },

  // Footer related strings
  "footer": {
    "description": "Piattaforma avanzata di analisi delle criptovalute e gestione del portafoglio alimentata dall'intelligenza artificiale.",
    "bankGradeSecurity": "Sicurezza di Livello Bancario",
    "allRightsReserved": "Tutti i diritti riservati",
    "product": "Prodotto",
    "learn": "Impara",
    "community": "Comunità",
    "legal": "Legale",
  },

  // Links related strings
  "links": {
    "privacyPolicy": "Informativa sulla Privacy",
    "termsOfService": "Termini di Servizio",
    "cookiePolicy": "Politica sui Cookie",
    "disclaimer": "Disclaimer",
    "advertisingPolicy": "Politica Pubblicitaria",
    "careers": "Carriere",
    "soon": "presto",
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "Cerca Monete e Progetti Futuri",
    "goToHomepage": "Vai alla homepage",
    "coinScoutAlt": "CoinScout",
    "pricing": "Prezzi",
    "goToApp": "Vai all'App",
    "login": "Accedi",
    "signUp": "Registrati",
    "profile": "Profilo",
    "membershipManagement": "Gestione Abbonamento",
    "feedback": "Feedback",
    "adminDashboard": "Dashboard Admin",
    "logout": "Esci",
    "premium": "Premium",
    "pro": "Pro",
    "free": "Gratuito",
  },

  // Watchlist related strings
  "watchlist": {
    "title": "La tua Watchlist è vuota",
    "description": "Non hai ancora nessuna watchlist. Crea una watchlist per tracciare le tue criptovalute.",
    "createWatchlist": "Crea Watchlist",
    "addCoins": "Aggiungi Monete",
  },

  // UpcomingTour related strings
  "upcomingTour": {
    "title": "Benvenuto negli IDO Futuri",
    "description": "Vuoi un tour veloce delle funzionalità degli IDO Futuri?",
    "info": "Impara come filtrare le vendite di token future, comprendere i dettagli del progetto e valutare i progetti prima del lancio.",
    "dontShowAgain": "Non mostrare più",
    "skipButton": "Salta per ora",
    "startButton": "Inizia Tour",
  },

  // Steps related strings
  "steps": {
    "title": "Panoramica IDO Futuri",
    "description": "Benvenuto nella pagina IDO Futuri, dove puoi scoprire e valutare nuovi lanci di token prima che vadano live.",
    "details": "Sfoglia, filtra e analizza le vendite di token future su diverse blockchain e launchpad.",
  },

  // Filters related strings
  "filters": {
    "title": "Opzioni di Filtro",
    "description": "Usa questi filtri per trovare tipi specifici di vendite di token che corrispondono ai tuoi criteri di investimento.",
    "details": "Filtra per tipo di vendita, launchpad, categoria, blockchain o investitori.",
    "action": "Prova a selezionare un filtro per vedere come aggiorna la lista.",
  },

  // Search related strings
  "search": {
    "title": "Cerca e Trova",
    "description": "Cerca rapidamente qualsiasi vendita di token futura per nome o simbolo.",
    "details": "I risultati si aggiornano in tempo reale mentre digiti.",
  },

  // ProjectInfo related strings
  "projectInfo": {
    "title": "Informazioni Progetto",
    "description": "Ogni riga contiene informazioni dettagliate su una vendita di token futura.",
    "details": "Clicca su qualsiasi riga per vedere l'analisi dettagliata di quel progetto.",
    "action": "Prova a passare il mouse su un progetto per vedere più informazioni.",
  },

  // InitialCap related strings
  "initialCap": {
    "title": "Capitalizzazione Iniziale",
    "description": "La capitalizzazione di mercato iniziale prevista del token al momento della quotazione.",
    "details": "Calcolata come prezzo del token × offerta circolante al TGE.",
  },

  // LaunchDate related strings
  "launchDate": {
    "title": "Data di Lancio",
    "description": "La data programmata quando il token sarà disponibile per il trading.",
    "details": "Rimani aggiornato sui lanci futuri per preparare la tua strategia di investimento.",
  },

  // Pagination related strings
  "pagination": {
    "title": "Navigazione Pagina",
    "description": "Naviga attraverso la lista delle vendite di token future.",
    "details": "Regola il numero di progetti visualizzati per pagina.",
  },

  // Completion related strings
  "completion": {
    "title": "Sei Pronto!",
    "description": "Hai completato il tour della pagina IDO Futuri.",
    "details": "Inizia a esplorare e analizzare le vendite di token future per trovare la tua prossima opportunità di investimento.",
    "help": "Puoi riavviare questo tour in qualsiasi momento cliccando sul pulsante Tour Guidato.",
  },

  // Methodology related strings
  "methodology": {
    "whatAreWeScoring": "Cosa Stiamo Valutando",
    "whyIsThisImportant": "Perché È Importante",
    "scoringLevels": "Livelli di Punteggio",
  },

  // Profile related strings
  "profile": {
    "yourProfile": "Il Tuo Profilo",
    "howOthersSeeYou": "Ecco come gli altri ti vedranno sulla piattaforma.",
    "verified": "Verificato",
    "unverified": "Non verificato",
    "memberSince": "Membro dal",
    "unknown": "Sconosciuto",
    "status": "Stato",
    "active": "Attivo",
    "plan": "Piano",
    "unknownPlan": "Piano Sconosciuto",
    "planStatus": "Stato del Piano",
    "started": "Iniziato",
    "expires": "Scade",
    "lastLogin": "Ultimo accesso",
    "never": "Mai",
    "profileCompletion": "[MISSING] Profile completion",
    "improveTip": "[MISSING] Add a bio and profile picture to improve your profile.",
    "signOut": "[MISSING] Sign Out",
    "areYouSure": "[MISSING] Are you sure?",
    "signOutConfirmation": "[MISSING] You'll be signed out from your account. You can sign back in at any time.",
    "cancel": "[MISSING] Cancel",
    "personalInformation": "[MISSING] Personal Information",
    "updateDescription": "[MISSING] Update your personal information and how your profile appears.",
    "username": "[MISSING] Username",
    "email": "[MISSING] Email",
    "emailPlaceholder": "[MISSING] <EMAIL>",
    "emailSent": "[MISSING] Email sent",
    "verificationEmailSent": "[MISSING] Verification email sent successfully. Please check your inbox.",
    "error": "[MISSING] Error",
    "emailSendError": "[MISSING] An error occurred while sending email. Please try again.",
    "resendVerification": "[MISSING] Resend verification email",
    "membershipTier": "[MISSING] Membership Tier",
    "pro": "[MISSING] Pro",
    "premium": "[MISSING] Premium",
    "free": "[MISSING] Free",
    "manageSubscription": "[MISSING] Manage your subscription in the",
    "membership": "[MISSING] Membership",
    "tab": "[MISSING] tab",
    "downloadData": "[MISSING] Download Your Data",
    "deleteAccount": "[MISSING] Delete Account",
    "absolutelySure": "[MISSING] Are you absolutely sure?",
    "deleteWarning": "[MISSING] This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    "title": "[MISSING] Profile",
    "description": "[MISSING] Manage your account settings and preferences.",
    "saveChanges": "[MISSING] Save Changes",
    "profile": "[MISSING] Profile",
    "notifications": "[MISSING] Notifications",
  },

  // Logs related strings
  "logs": {
    "usingDirectAPIData": "[MISSING] 🔄 Using direct API data, data length:",
    "imageLoadError": "[MISSING] 🖼️ Error loading image:",
    "usingIdoWatchlists": "[MISSING] Using IDO watchlists for upcoming projects:",
    "rawAPIData": "[MISSING] 🟡 Raw data from API:",
    "apiDataLength": "[MISSING] 🟡 API data length:",
    "filteredData": "[MISSING] 🔍 Filtered data:",
    "filteredDataLength": "[MISSING] 🔍 Filtered data length:",
    "imageUrlChecks": "[MISSING] 🖼️ Image URL checks:",
    "allProjectsFromAPI": "[MISSING] 📌 All projects from API:",
    "usingDirectAPI": "[MISSING] 🟢 Using direct API data",
    "rawAPIDataFirst5": "[MISSING] 🔍 Raw API data (first 5 items):",
    "emptyAPIResult": "[MISSING] ⚠️ API returned empty result, using empty array",
    "errorUsingEmptyArray": "[MISSING] ❌ Using empty array due to error",
    "totalAIScoreClicked": "[MISSING] Total AI Score clicked:",
    "filtersChanged": "[MISSING] Filters changed:",
    "upcomingIDOFirstUseEffect": "[MISSING] 🔄 UpcomingIDO - First useEffect triggered",
    "fetchInitialDataStarted": "[MISSING] 🔄 fetchInitialData started",
    "fetchInitialDataCompleted": "[MISSING] 🔄 fetchInitialData completed",
    "filterRunning": "[MISSING] 🔍 Filter running, coins length:",
    "paginatedData": "[MISSING] 📄 Paginated data:",
    "first2Records": "[MISSING] 📄 First 2 records:",
    "paginatedDataImageFields": "[MISSING] 📄 Image fields in paginated data:",
    "firstRecordDetails": "[MISSING] 🧩 First record details:",
    "firstRecordSocialScore": "[MISSING] 📢 First record social score value:",
    "clickedProjectInfo": "[MISSING] Clicked project info:",
    "redirectingWithDirectID": "[MISSING] Redirecting with direct ID:",
  },

  // Homepage related strings
  "homepage": {
    "badge": "[MISSING] AI-Powered Crypto Analysis",
    "title": "[MISSING] Make smarter crypto investments",
    "titleHighlight": "[MISSING] with AI insights",
    "description": "[MISSING] CoinScout analyzes over 3,000 cryptocurrencies across 40+ metrics to help you identify the best investment opportunities before others see them.",
    "ctaButton": "[MISSING] Start Free Analysis",
    "cryptocurrencies": "[MISSING] Cryptocurrencies",
    "analysisMetrics": "[MISSING] Analysis Metrics",
    "moreAccuracy": "[MISSING] More Accuracy",
  },

  // Features related strings
  "features": {
    "title": "[MISSING] AI-Enhanced Crypto Rating",
    "subtitle": "[MISSING] Our comprehensive suite of AI-powered tools helps you navigate the crypto market with confidence",
    "description": "[MISSING] Proprietary algorithms assess 50+ metrics across tokenomics, security, social engagement, and market factors",
  },

  // IdoRating related strings
  "idoRating": {
    "title": "[MISSING] IDO Rating",
    "description": "[MISSING] Discover and evaluate promising token offerings before their launch with detailed project analysis and scoring",
  },

  // CompareCoins related strings
  "compareCoins": {
    "title": "[MISSING] Compare Coins",
    "description": "[MISSING] Side-by-side analysis of multiple cryptocurrencies to identify the best investment opportunities based on your criteria",
  },

  // PortfolioGenerator related strings
  "portfolioGenerator": {
    "title": "[MISSING] Custom Portfolio Generator",
    "description": "[MISSING] AI-powered recommendations to build a diversified portfolio based on your risk tolerance and investment goals",
  },

  // PortfolioAnalysis related strings
  "portfolioAnalysis": {
    "title": "[MISSING] AI Portfolio Analysis",
    "description": "[MISSING] Gain AI-powered insights into your existing portfolio with automated risk analysis, diversification score, and optimization tips",
  },

  // Launchpads related strings
  "launchpads": {
    "title": "[MISSING] Top Launchpads",
    "description": "[MISSING] Discover the best platforms for new token offerings with performance metrics, success rates, and investor returns",
  },

  // AiAssistant related strings
  "aiAssistant": {
    "title": "[MISSING] AI Assistant",
    "description": "[MISSING] Get instant answers to your crypto questions with our advanced AI assistant that provides personalized guidance and insights",
  },

  // AirdropScore related strings
  "airdropScore": {
    "title": "[MISSING] Airdrop Score",
    "description": "[MISSING] Find legitimate airdrops with the highest potential value and lowest participation barriers through our verification system",
  },

  // GemScout related strings
  "gemScout": {
    "title": "[MISSING] Gem Scout",
    "description": "[MISSING] Discover early-stage projects with exceptional growth potential before they hit mainstream markets and major exchanges",
  },

  // Badges related strings
  "badges": {
    "betaTestingLive": "[MISSING] Beta Testing Live",
    "betaTestingSoon": "[MISSING] Beta Testing Soon",
    "comingSoon": "[MISSING] Coming Soon",
  },

  // Intelligence related strings
  "intelligence": {
    "title": "[MISSING] AI-Driven Intelligence for Smarter Crypto Decisions",
  },

  // Faq related strings
  "faq": {
    "title": "[MISSING] Frequently Asked Questions",
    "question": "[MISSING] What makes CoinScout's analysis different from other platforms?",
    "answer": "[MISSING] CoinScout leverages AI-driven analytics to assess over 50 key factors across tokenomics, security, social sentiment, market performance, and project fundamentals. Unlike traditional platforms that primarily provide raw data, we transform complex metrics into clear, actionable insights—making it easier for investors at all levels to understand and make informed decisions.",
  },

  // Upcoming related strings
  "upcoming": {
    "title": "Vendite di Token Future",
    "subtitle": "Scopri e valuta nuovi token prima del lancio",
    "saleType": "Tipo di Vendita",
    "allTypes": "[MISSING] All Types",
    "launchpad": "[MISSING] Launchpad",
    "allLaunchpads": "[MISSING] All Launchpads",
    "category": "[MISSING] Category",
    "allCategories": "[MISSING] All Categories",
    "blockchain": "[MISSING] Blockchain",
    "allBlockchains": "[MISSING] All Blockchains",
    "investor": "[MISSING] Investor",
    "allInvestors": "[MISSING] All Investors",
    "projectScore": "[MISSING] Project Score",
    "listingDate": "[MISSING] Listing Date",
    "reset": "[MISSING] Reset Filters",
    "apply": "[MISSING] Apply Filters",
    "searchCategories": "[MISSING] Search categories...",
    "searchChains": "[MISSING] Search chains...",
    "selectDateRange": "[MISSING] Select date range",
    "last24Hours": "[MISSING] Last 24 Hours",
    "last7Days": "[MISSING] Last 7 Days",
    "last14Days": "[MISSING] Last 14 Days",
    "last30Days": "[MISSING] Last 30 Days",
    "last90Days": "[MISSING] Last 90 Days",
  },

  // Table related strings
  "table": {
    "name": "[MISSING] Name",
    "launchDate": "[MISSING] Launch Date",
    "initialCap": "[MISSING] Initial Cap",
    "totalRaised": "[MISSING] Total Raised",
    "score": "[MISSING] Score",
    "actions": "[MISSING] Actions",
  },

  // SaleType related strings
  "saleType": {
    "IDO": "[MISSING] Initial DEX Offering - Token sale conducted on a decentralized exchange (DEX)",
    "IEO": "[MISSING] Initial Exchange Offering - Token sale hosted on a centralized cryptocurrency exchange",
    "ICO": "[MISSING] Initial Coin Offering - First fundraising stage where new projects offer tokens to early investors",
    "SHO": "[MISSING] Strong Holder Offering - Token sale giving priority to long-term token holders",
    "Seed": "[MISSING] Seed Round - Early private funding round before public sale",
    "IGO": "[MISSING] Initial Game Offering - Fundraising focused on blockchain gaming projects",
    "ISO": "[MISSING] Initial Stake Offering - Token distribution through staking mechanism",
  },

  // ListingDate related strings
  "listingDate": {
    "24hours": "[MISSING] Last 24 Hours",
    "7days": "[MISSING] Last 7 Days",
    "14days": "[MISSING] Last 14 Days",
    "30days": "[MISSING] Last 30 Days",
    "90days": "[MISSING] Last 90 Days",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "Caricamento...",
  "common:error": "Errore",
  "common:retry": "Riprova",
  "common:save": "Salva",
  "common:cancel": "Annulla",
  "common:close": "Chiudi",
  "common:success": "Successo",
  "common:viewMore": "Vedi di più",
  "common:back": "Indietro",
  "common:next": "Avanti",
  "common:search": "Cerca",
  "common:searchCoinsAndTokens": "Cerca Monete e Progetti Futuri",
  "common:searching": "Ricerca in corso...",
  "common:noResults": "Nessun risultato trovato",
  "common:coins": "Monete",
  "common:upcomingIdos": "Prossimi IDO",

  // Flat format for nav (for backward compatibility)
  "nav:home": "Home",
  "nav:portfolio": "Portafoglio",
  "nav:explore": "Esplora",
  "nav:news": "Notizie",
  "nav:learn": "Impara",
  "nav:profile": "Profilo",
  "nav:settings": "Impostazioni",
  "nav:logout": "Disconnettersi",
  "nav:login": "Accedi",
  "nav:register": "Registrati",
  "nav:trending": "Tendenze",
  "nav:favorites": "Preferiti",
  "nav:watchlist": "Lista Preferiti",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "Ricerca in corso...",
  "system:error.translation": "Errore di traduzione",
  "system:data.loading": "Caricamento dati",
  "system:data.empty": "Nessun dato disponibile",
  "system:data.error": "Errore nel caricamento dei dati",

  // Flat format for auth (for backward compatibility)
  "auth:email": "Email",
  "auth:email.placeholder": "Inserisci la tua email",
  "auth:email.description": "Non condivideremo mai la tua email con nessun altro",
  "auth:password": "Password",
  "auth:password.placeholder": "Inserisci la tua password",
  "auth:password.create": "Crea una password",
  "auth:password.confirm": "Conferma Password",
  "auth:password.confirm.placeholder": "Conferma la tua password",
  "auth:password.show": "Mostra password",
  "auth:password.hide": "Nascondi password",
  "auth:password.strength": "Sicurezza password",
  "auth:password.strength.weak": "Debole",
  "auth:password.strength.good": "Buona",
  "auth:password.strength.strong": "Forte",
  "auth:password.reset.message": "La funzione di reimpostazione della password arriverà presto",
  "auth:forgotPassword": "Password dimenticata?",
  "auth:resetPassword": "Reimposta Password",
  "auth:signin": "Accedi",
  "auth:signin.loading": "Accesso in corso...",
  "auth:signin.securely": "Accedi in modo sicuro",
  "auth:signup": "Registrati",
  "auth:signout": "Esci",
  "auth:accountCreated": "Account creato con successo",
  "auth:passwordResetSent": "Email per reimpostazione password inviata",
  "auth:invalidCredentials": "Email o password non valida",
  "auth:continueWith": "Continua con",
  "auth:username": "Nome utente",
  "auth:username.placeholder": "Scegli un nome utente",
  "auth:terms.agree": "Accetto i",
  "auth:terms.service": "Termini di Servizio",
  "auth:terms.and": "e",
  "auth:terms.privacy": "Informativa sulla Privacy",
  "auth:termsAccept": "Continuando, accetti i nostri Termini di Servizio e Informativa sulla Privacy",
  "auth:remember": "Ricordami per 30 giorni",
  "auth:welcome.back": "Bentornato",
  "auth:login.credential.prompt": "Inserisci le tue credenziali per accedere",
  "auth:login.success.title": "Successo",
  "auth:login.success.description": "Accesso effettuato con successo",
  "auth:login.error.title": "Errore di accesso",
  "auth:login.error.unknown": "Qualcosa è andato storto. Riprova.",
  "auth:register.title": "Crea un account",
  "auth:register.create": "Crea account",
  "auth:register.creating": "Creazione account in corso...",
  "auth:register.haveAccount": "Hai già un account?",
  "auth:register.success": "Registrazione riuscita",
  "auth:register.success.detail": "Il tuo account è stato creato con successo",
  "auth:register.success.login": "Il tuo account è stato creato, effettua il login.",
  "auth:register.success.email_verify": "Registrazione riuscita. Controlla la tua email per verificare il tuo account.",
  "auth:register.failed": "Registrazione fallita",
  "auth:register.failed.generic": "Si è verificato un errore durante la registrazione. Riprova.",
  "auth:register.error.generic": "Si è verificato un errore. Riprova.",
  "auth:register.description": "Crea un account per iniziare",
  "auth:form.invalidFields": "Per favore compila correttamente tutti i campi obbligatori",
  "auth:validation.email": "Inserisci un indirizzo email valido",
  "auth:validation.password.length": "La password deve contenere almeno 6 caratteri",
  "auth:authentication.required": "Autenticazione Richiesta",
  "auth:authentication.required.description": "Per favore, accedi per utilizzare questa funzione",
  "auth:authentication.signin": "Accedi",
  "auth:authentication.continueWithEmail": "Continua con email",
  "auth:authentication.goBack": "Indietro",
  "auth:authentication.signInPrompt": "Accedi per accedere alle funzionalità personalizzate, salvare le tue preferenze e sbloccare tutte le capacità di CoinScout.",
  "auth:authentication.comparisonPrompt": "Accedi per accedere alle funzionalità personalizzate, salvare le tue preferenze e sbloccare tutte le capacità di CoinScout per il confronto.",
  "auth:backToHome": "Torna alla Home",
  "auth:validation.email.required": "[MISSING] Email is required",
  "auth:validation.email.invalid": "[MISSING] Please enter a valid email address",
  "auth:validation.email.complete": "[MISSING] Please enter a complete email address",
  "auth:validation.password.required": "[MISSING] Password is required",
  "auth:validation.password.uppercase": "[MISSING] Password must contain at least one uppercase letter",
  "auth:validation.password.lowercase": "[MISSING] Password must contain at least one lowercase letter",
  "auth:validation.password.number": "[MISSING] Password must contain at least one number",
  "auth:validation.password.special": "[MISSING] Password must contain at least one special character",
  "auth:login.failed": "[MISSING] Login Failed",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "Tutte le Monete",
  "coinlist:coinDetailDescription": "Clicca su una moneta per un'analisi dettagliata",
  "coinlist:searchCoins": "Cerca monete...",
  "coinlist:searchBox.ariaLabel": "Cerca monete",
  "coinlist:aiPoweredTitle": "Valutazioni Fondamentali delle Criptovalute Basate su AI e Dati",
  "coinlist:comprehensiveAnalysis": "Analisi e valutazione complete delle criptovalute su",
  "coinlist:multipleMetrics": "diversi parametri",
  "coinlist:highlights": "Punti salienti",
  "coinlist:viewAll": "Vedi tutto",
  "coinlist:currentPrice": "Prezzo attuale",
  "coinlist:marketCap": "Capitalizzazione di mercato",
  "coinlist:rank": "Classifica",
  "coinlist:filters.button": "Filtri",
  "coinlist:filters.title": "Opzioni di filtro",
  "coinlist:filters.description": "Personalizza la tua visualizzazione con opzioni di filtro avanzate",
  "coinlist:columns.name": "Nome",
  "coinlist:columns.tokenomics": "Tokenomics",
  "coinlist:columns.security": "Sicurezza",
  "coinlist:columns.social": "Social",
  "coinlist:columns.market": "Mercato",
  "coinlist:columns.insights": "Analisi",
  "coinlist:columns.totalScore": "Punteggio AI Totale",
  "coinlist:columns.sevenDayChange": "Variazione 7G",
  "coinlist:tooltips.name.title": "[MISSING] Coin Name & Symbol",
  "coinlist:tooltips.name.description": "[MISSING] The official name and symbol of the cryptocurrency as listed on exchanges.",
  "coinlist:tooltips.tokenomics.title": "[MISSING] Tokenomics Analysis",
  "coinlist:tooltips.tokenomics.description": "[MISSING] Measures token supply mechanisms, inflation risks, and vesting structures.",
  "coinlist:tooltips.security.title": "[MISSING] Security Analysis",
  "coinlist:tooltips.security.description": "[MISSING] Security audit results and risk assessment metrics.",
  "coinlist:tooltips.social.title": "[MISSING] Social Analysis",
  "coinlist:tooltips.social.description": "[MISSING] Social media presence, community engagement and sentiment analysis.",
  "coinlist:tooltips.market.title": "[MISSING] Market Performance Analysis",
  "coinlist:tooltips.market.description": "[MISSING] Measures trading volume, liquidity and overall market health.",
  "coinlist:tooltips.insights.title": "[MISSING] AI Insights Analysis",
  "coinlist:tooltips.insights.description": "[MISSING] AI-powered project insights, sentiment analysis and forecasting metrics.",
  "coinlist:tooltips.totalScore.title": "[MISSING] Total Score",
  "coinlist:tooltips.totalScore.description": "[MISSING] The overall rating calculated from all scoring metrics.",
  "coinlist:tooltips.sevenDayChange.title": "[MISSING] 7 Day Change",
  "coinlist:tooltips.sevenDayChange.description": "[MISSING] Percentage price change over the last 7 days.",
  "coinlist:search.inProgress": "[MISSING] Search in progress...",
  "coinlist:search.resultsUpdate": "[MISSING] Results will update automatically",
  "coinlist:search.clearSearch": "[MISSING] Clear search",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "Panoramica",
  "coindetail:fundamentals": "Fondamentali",
  "coindetail:technicals": "Tecnici",
  "coindetail:news": "Notizie",
  "coindetail:social": "Social",
  "coindetail:developers": "Sviluppatori",
  "coindetail:analysis": "Analisi",
  "coindetail:price": "Prezzo",
  "coindetail:volume": "Volume",
  "coindetail:marketCap": "Capitalizzazione di mercato",
  "coindetail:circulatingSupply": "Offerta circolante",
  "coindetail:totalSupply": "Offerta totale",
  "coindetail:maxSupply": "Offerta massima",
  "coindetail:allTimeHigh": "Massimo storico",
  "coindetail:allTimeLow": "Minimo storico",
  "coindetail:pricePrediction": "Previsione di prezzo",
  "coindetail:addToWatchlist": "Aggiungi alla Watchlist",
  "coindetail:removeFromWatchlist": "Rimuovi dalla Watchlist",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "Top Movimenti",
  "highlights:newListings": "Nuovi Listini",
  "highlights:upcomingIDOs": "Prossimi IDO",
  "highlights:gemCoins": "Monete Gemma",
  "highlights:topAirdrops": "Top Airdrop",
  "highlights:score": "Punteggio",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "Valore Totale",
  "portfolio:recentActivity": "Attività Recente",
  "portfolio:performance": "Performance",
  "portfolio:holdings": "Possedimenti",
  "portfolio:addAsset": "Aggiungi Asset",
  "portfolio:editAsset": "Modifica Asset",
  "portfolio:noAssets": "Nessun asset nel portafoglio",
  "portfolio:24hChange": "Variazione 24h",
  "portfolio:allocation": "Allocazione",
  "portfolio:profit": "Profitto/Perdita",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "Aspetto",
  "settings:language": "Lingua",
  "settings:notifications": "Notifiche",
  "settings:security": "Sicurezza",
  "settings:preferences": "Preferenze",
  "settings:theme": "Tema",
  "settings:lightMode": "Modalità Chiara",
  "settings:darkMode": "Modalità Scura",
  "settings:systemDefault": "Predefinito Sistema",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "Blocca",
  "sidebar:unlock": "Sblocca",
  "sidebar:collapse": "Riduci",
  "sidebar:cryptoRating": "Valutazione Crypto",
  "sidebar:idoRating": "Valutazione IDO",
  "sidebar:compareCoins": "Confronta Monete",
  "sidebar:recentListings": "Listini Recenti",
  "sidebar:topMovers": "Top Movimenti",
  "sidebar:watchlist": "La mia Watchlist",
  "sidebar:aiPortfolio": "Guida Portafoglio AI",
  "sidebar:portfolioAudit": "Analisi Portafoglio",
  "sidebar:launchpads": "Launchpad",
  "sidebar:airdrops": "Centro Airdrop",
  "sidebar:aiAssistant": "Assistente AI",
  "sidebar:gemScout": "Gem Scout",
  "sidebar:soon": "PRESTO",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "Qualcosa è andato storto",
  "error:tryAgain": "Riprova",
  "error:networkIssue": "Problema di connessione di rete",
  "error:dataFetch": "Impossibile recuperare i dati",
  "error:timeOut": "Richiesta scaduta",
  "error:invalidInput": "Input non valido",
  "error:pageNotFound": "Pagina non trovata",

  // Flat format for format (for backward compatibility)
  "format:thousand": "K",
  "format:million": "M",
  "format:billion": "B",
  "format:trillion": "T",

  // Flat format for coin (for backward compatibility)
  "coin:age": "Età:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "Eccellente",
  "score:positive": "Positivo",
  "score:average": "Medio",
  "score:weak": "Debole",
  "score:critical": "Critico",

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "Vendite di Token Future",
  "upcoming:subtitle": "Scopri e valuta nuovi token prima del lancio",
  "upcoming:filters.title": "Filtri",
  "upcoming:filters.saleType": "Tipo di Vendita",
  "upcoming:filters.allTypes": "Tutti i Tipi",
  "upcoming:filters.launchpad": "Launchpad",
  "upcoming:filters.allLaunchpads": "Tutti i Launchpad",
  "upcoming:filters.category": "Categoria",
  "upcoming:filters.allCategories": "Tutte le Categorie",
  "upcoming:filters.blockchain": "Blockchain",
  "upcoming:filters.allBlockchains": "Tutte le Blockchain",
  "upcoming:filters.investor": "Investitore",
  "upcoming:filters.allInvestors": "Tutti gli Investitori",
  "upcoming:filters.projectScore": "Punteggio Progetto",
  "upcoming:filters.listingDate": "Data di Quotazione",
  "upcoming:filters.reset": "Reimposta Filtri",
  "upcoming:filters.apply": "Applica Filtri",
  "upcoming:table.name": "Nome",
  "upcoming:table.launchDate": "Data di Lancio",
  "upcoming:table.initialCap": "Cap Iniziale",
  "upcoming:table.totalRaised": "Totale Raccolto",
  "upcoming:table.score": "Punteggio",
  "upcoming:table.actions": "Azioni",
  "upcoming:search": "Cerca vendite di token future...",
  "upcoming:noResults": "Nessuna vendita di token futura trovata",
  "upcoming:loading": "Caricamento vendite di token future...",
  "upcoming:error": "Errore nel caricamento delle vendite di token future",
  "upcoming:retryButton": "Riprova",
  "upcoming:tba": "TBA",
  "upcoming:rank": "Classifica #{number}",
  "upcoming:saleType": "Tipo di Vendita",
  "upcoming:totalAiScore": "Punteggio AI Totale",
  "upcoming:points": "Punti",
  "upcoming:tokenomics": "Tokenomics",
  "upcoming:security": "Sicurezza",
  "upcoming:social": "Social",
  "upcoming:market": "Mercato",
  "upcoming:insights": "Analisi",

};

export default it;
