/**
 * Russian localization strings
 */
const ru = {
  // Common strings
  "common": {
    "loading": "Загрузка...",
    "error": "Ошибка",
    "retry": "Повторить",
    "save": "Сохранить",
    "cancel": "Отменить",
    "close": "Закрыть",
    "success": "Успешно",
    "viewMore": "Смотреть больше",
    "back": "Назад",
    "next": "Далее",
    "search": "Поиск",
    "searchCoinsAndTokens": "Поиск монет и предстоящих проектов",
    "searching": "Поиск...",
    "noResults": "Результатов не найдено",
    "coins": "Монеты",
    "upcomingIdos": "Предстоящие IDO",
  },

  // Nav related strings
  "nav": {
    "home": "Главная",
    "portfolio": "Портфолио",
    "explore": "Обзор",
    "news": "Новости",
    "learn": "Обучение",
    "profile": "Профиль",
    "settings": "Настройки",
    "logout": "Выйти",
    "login": "Войти",
    "register": "Регистрация",
    "trending": "В тренде",
    "favorites": "Избранное",
    "watchlist": "Список наблюдения",
  },

  // System related strings
  "system": {
    "choose": "Выбрать язык",
    "current": "Текущий язык",
    "searching": "Поиск...",
  },

  // Error related strings
  "error": {
    "criticalError": "Критическая Ошибка",
    "somethingWentWrong": "Что-то пошло не так",
    "criticalErrorMessage": "Произошла критическая ошибка. Пожалуйста, обновите страницу или вернитесь на главную.",
    "returnToHome": "Вернуться на Главную",
    "multipleErrorsDetected": "Обнаружено Несколько Ошибок",
    "unexpectedError": "Произошла неожиданная ошибка",
    "refreshPage": "Обновить Страницу",
    "goToHome": "Перейти на Главную",
    "clearErrorLogs": "Очистить Журналы Ошибок",
    "anErrorOccurred": "Произошла ошибка",
  },

  // Data related strings
  "data": {
    "loading": "Загрузка данных",
    "empty": "Нет доступных данных",
    "error": "Ошибка загрузки данных",
  },

  // Auth related strings
  "auth": {
    "email": "Электронная почта",
    "emailPlaceholder": "Введите вашу электронную почту",
    "emailDescription": "Ваша электронная почта никогда не будет передана третьим лицам",
    "password": "Пароль",
    "passwordPlaceholder": "Введите ваш пароль",
    "passwordCreate": "Создать пароль",
    "passwordConfirm": "Подтвердите пароль",
    "passwordConfirmPlaceholder": "Повторно введите пароль",
    "passwordShow": "Показать пароль",
    "passwordHide": "Скрыть пароль",
    "passwordStrength": "Надежность пароля",
    "passwordStrengthWeak": "Слабый",
    "passwordStrengthGood": "Хороший",
    "passwordStrengthStrong": "Надежный",
    "passwordResetMessage": "Функция сброса пароля будет доступна в ближайшее время",
    "forgotPassword": "Забыли пароль?",
    "resetPassword": "Сбросить пароль",
    "signin": "Войти",
    "signinLoading": "Вход...",
    "signinSecurely": "Безопасный вход",
    "signup": "Регистрация",
    "signout": "Выйти",
    "accountCreated": "Аккаунт успешно создан",
    "passwordResetSent": "Отправлено письмо для сброса пароля",
    "invalidCredentials": "Неверная электронная почта или пароль",
    "continueWith": "Продолжить с",
    "username": "Имя пользователя",
    "usernamePlaceholder": "Выберите имя пользователя",
    "agree": "Я согласен с",
    "service": "Условиями использования",
    "and": "и",
    "privacy": "Политикой конфиденциальности",
    "email.placeholder": "Введите вашу электронную почту",
    "email.description": "Ваша электронная почта никогда не будет передана третьим лицам",
    "password.placeholder": "Введите ваш пароль",
    "password.create": "Создать пароль",
    "password.confirm": "Подтвердите пароль",
    "password.confirm.placeholder": "Повторно введите пароль",
    "password.show": "Показать пароль",
    "password.hide": "Скрыть пароль",
    "password.strength": "Надежность пароля",
    "password.strength.weak": "Слабый",
    "password.strength.good": "Хороший",
    "password.strength.strong": "Надежный",
    "password.reset.message": "Функция сброса пароля будет доступна в ближайшее время",
    "signin.loading": "Вход...",
    "signin.securely": "Безопасный вход",
    "username.placeholder": "Выберите имя пользователя",
    "password.criteria.length": "[MISSING] At least 8 characters",
    "password.criteria.uppercase": "[MISSING] At least one uppercase letter",
    "password.criteria.lowercase": "[MISSING] At least one lowercase letter",
    "password.criteria.number": "[MISSING] At least one number",
    "password.criteria.special": "[MISSING] At least one special character",
    "captcha.protected": "[MISSING] This form is protected by reCAPTCHA",
    "terms.service": "Условиями использования",
    "terms.and": "и",
    "terms.privacy": "Политикой конфиденциальности",
  },

  // Login related strings
  "login": {
    "prompt": "Введите ваши данные для входа",
  },

  // Success related strings
  "success": {
    "title": "Успешно",
    "description": "Вход выполнен успешно",
  },

  // Register related strings
  "register": {
    "title": "Создать аккаунт",
    "create": "Создать аккаунт",
    "creating": "Создание аккаунта...",
    "haveAccount": "Уже есть аккаунт?",
    "success": "Регистрация успешна",
    "successDetail": "Аккаунт успешно создан",
    "successLogin": "Аккаунт создан. Пожалуйста, войдите.",
    "failed": "Ошибка регистрации",
    "failedGeneric": "Произошла ошибка при регистрации. Пожалуйста, попробуйте снова.",
    "generic": "Произошла ошибка. Пожалуйста, попробуйте снова.",
    "success.detail": "[MISSING] Your account has been created successfully",
    "success.login": "[MISSING] Your account has been created, please login.",
    "failed.generic": "[MISSING] An error occurred during registration. Please try again.",
    "success.email_verify": "[MISSING] Registration successful. Please check your email to verify your account.",
  },

  // Form related strings
  "form": {
    "invalidFields": "Пожалуйста, заполните все обязательные поля корректно",
  },

  // Validation related strings
  "validation": {
    "email": "Пожалуйста, введите корректный адрес электронной почты",
    "length": "Пароль должен содержать не менее 6 символов",
  },

  // Coinlist related strings
  "coinlist": {
    "allCoins": "Все монеты",
    "coinDetailDescription": "Нажмите на любую монету для подробного анализа",
    "searchCoins": "Поиск монет...",
    "ariaLabel": "Поиск монет",
  },

  // Filters related strings
  "filters": {
    "button": "Фильтры",
    "title": "Параметры фильтрации",
    "description": "Настройте просмотр с помощью расширенных параметров фильтрации",
    "sortBy": "Сортировать по",
    "reset": "Сбросить фильтры",
    "details": "[MISSING] Filter by sale type, launchpad, category, blockchain, or investors.",
    "action": "[MISSING] Try selecting a filter to see how it updates the list.",
  },

  // Columns related strings
  "columns": {
    "name": "Имя",
    "tokenomics": "Токеномика",
    "security": "Безопасность",
    "social": "Социальное",
    "market": "Рынок",
    "insights": "Аналитика",
    "totalScore": "Общая оценка ИИ",
    "sevenDayChange": "Изменение за 7Д",
    "price": "Цена",
  },

  // Highlights related strings
  "highlights": {
    "topGainers": "Монеты с Максимальным Ростом",
    "newListings": "Новые Листинги",
    "upcomingIDOs": "Предстоящие IDO",
    "gemCoins": "Перспективные Монеты",
    "topAirdrops": "Лучшие Airdrop",
    "score": "Оценка",
  },

  // Coindetail related strings
  "coindetail": {
    "overview": "Обзор",
    "fundamentals": "Фундаментальные показатели",
    "technicals": "Технический анализ",
    "news": "Новости",
    "social": "Социальное",
    "developers": "Разработчики",
    "analysis": "Анализ",
    "price": "Цена",
    "volume": "Объём",
    "marketCap": "Капитализация",
    "circulatingSupply": "В обращении",
    "totalSupply": "Общее предложение",
    "maxSupply": "Максимальное предложение",
    "allTimeHigh": "Исторический максимум",
    "allTimeLow": "Исторический минимум",
    "pricePrediction": "Прогноз цены",
    "addToWatchlist": "Добавить в список наблюдения",
    "removeFromWatchlist": "Удалить из списка наблюдения",
  },

  // Portfolio related strings
  "portfolio": {
    "totalValue": "Общая стоимость",
    "recentActivity": "Недавняя активность",
    "performance": "Производительность",
    "holdings": "Активы",
    "addAsset": "Добавить актив",
    "editAsset": "Изменить актив",
    "noAssets": "Нет активов в портфолио",
    "24hChange": "Изменение за 24ч",
    "allocation": "Распределение",
    "profit": "Прибыль/Убыток",
  },

  // Settings related strings
  "settings": {
    "appearance": "Внешний вид",
    "language": "Язык",
    "notifications": "Уведомления",
    "security": "Безопасность",
    "preferences": "Предпочтения",
    "theme": "Тема",
    "lightMode": "Светлый режим",
    "darkMode": "Тёмный режим",
    "systemDefault": "Системный режим",
  },

  // Sidebar related strings
  "sidebar": {
    "lock": "Закрепить",
    "unlock": "Открепить",
    "collapse": "Свернуть",
    "cryptoRating": "Рейтинг криптовалют",
    "idoRating": "Рейтинг IDO",
    "compareCoins": "Сравнение монет",
    "recentListings": "Новые листинги",
    "topMovers": "Лидеры роста",
    "watchlist": "Мой список наблюдения",
    "aiPortfolio": "ИИ-гид по портфолио",
    "portfolioAudit": "Аудит портфолио",
    "launchpads": "Площадки запуска",
    "airdrops": "Центр airdrop",
    "aiAssistant": "ИИ-ассистент",
    "gemScout": "Поиск перспективных монет",
    "soon": "СКОРО",
  },

  // Format related strings
  "format": {
    "thousand": "тыс",
    "million": "млн",
    "billion": "млрд",
    "trillion": "трлн",
  },

  // Coin related strings
  "coin": {
    "age": "Возраст:",
  },

  // Score related strings
  "score": {
    "excellent": "Отлично",
    "positive": "Хорошо",
    "average": "Средне",
    "weak": "Слабо",
    "critical": "Критично",
    "title": "[MISSING] CoinScout Score",
    "description": "[MISSING] Our proprietary score evaluates the overall quality and potential of the project.",
    "details": "[MISSING] Based on tokenomics, security, social media activity, and more.",
  },

  // Footer related strings
  "footer": {
    "description": "Продвинутая платформа анализа криптовалют и управления портфелем на основе искусственного интеллекта.",
    "bankGradeSecurity": "Безопасность Банковского Уровня",
    "allRightsReserved": "Все права защищены",
    "product": "Продукт",
    "learn": "Обучение",
    "community": "Сообщество",
    "legal": "Правовая информация",
  },

  // Links related strings
  "links": {
    "privacyPolicy": "Политика Конфиденциальности",
    "termsOfService": "Условия Использования",
    "cookiePolicy": "Политика Cookies",
    "disclaimer": "Отказ от Ответственности",
    "advertisingPolicy": "Рекламная Политика",
    "careers": "Карьера",
    "soon": "скоро",
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "Поиск Монет и Предстоящих Проектов",
    "goToHomepage": "Перейти на главную",
    "coinScoutAlt": "CoinScout",
    "pricing": "Цены",
    "goToApp": "Перейти в Приложение",
    "login": "Войти",
    "signUp": "Регистрация",
    "profile": "Профиль",
    "membershipManagement": "Управление Членством",
    "feedback": "Обратная Связь",
    "adminDashboard": "Панель Администратора",
    "logout": "Выйти",
    "premium": "Премиум",
    "pro": "Про",
    "free": "Бесплатно",
  },

  // Watchlist related strings
  "watchlist": {
    "title": "Ваш список наблюдения пуст",
    "description": "У вас пока нет списков наблюдения. Создайте список наблюдения для отслеживания ваших криптовалют.",
    "createWatchlist": "Создать Список Наблюдения",
    "addCoins": "Добавить Монеты",
  },

  // Methodology related strings
  "methodology": {
    "whatAreWeScoring": "Что Мы Оцениваем",
    "whyIsThisImportant": "Почему Это Важно",
    "scoringLevels": "Уровни Оценки",
  },

  // Profile related strings
  "profile": {
    "yourProfile": "Ваш Профиль",
    "howOthersSeeYou": "Так другие пользователи будут видеть вас на платформе.",
    "verified": "Верифицирован",
    "unverified": "Не верифицирован",
    "memberSince": "Участник с",
    "unknown": "Неизвестно",
    "status": "Статус",
    "active": "Активный",
    "plan": "План",
    "unknownPlan": "Неизвестный План",
    "planStatus": "Статус Плана",
    "started": "Начат",
    "expires": "Истекает",
    "lastLogin": "Последний вход",
    "never": "Никогда",
    "profileCompletion": "[MISSING] Profile completion",
    "improveTip": "[MISSING] Add a bio and profile picture to improve your profile.",
    "signOut": "[MISSING] Sign Out",
    "areYouSure": "[MISSING] Are you sure?",
    "signOutConfirmation": "[MISSING] You'll be signed out from your account. You can sign back in at any time.",
    "cancel": "[MISSING] Cancel",
    "personalInformation": "[MISSING] Personal Information",
    "updateDescription": "[MISSING] Update your personal information and how your profile appears.",
    "username": "[MISSING] Username",
    "email": "[MISSING] Email",
    "emailPlaceholder": "[MISSING] <EMAIL>",
    "emailSent": "[MISSING] Email sent",
    "verificationEmailSent": "[MISSING] Verification email sent successfully. Please check your inbox.",
    "error": "[MISSING] Error",
    "emailSendError": "[MISSING] An error occurred while sending email. Please try again.",
    "resendVerification": "[MISSING] Resend verification email",
    "membershipTier": "[MISSING] Membership Tier",
    "pro": "[MISSING] Pro",
    "premium": "[MISSING] Premium",
    "free": "[MISSING] Free",
    "manageSubscription": "[MISSING] Manage your subscription in the",
    "membership": "[MISSING] Membership",
    "tab": "[MISSING] tab",
    "downloadData": "[MISSING] Download Your Data",
    "deleteAccount": "[MISSING] Delete Account",
    "absolutelySure": "[MISSING] Are you absolutely sure?",
    "deleteWarning": "[MISSING] This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    "title": "[MISSING] Profile",
    "description": "[MISSING] Manage your account settings and preferences.",
    "saveChanges": "[MISSING] Save Changes",
    "profile": "[MISSING] Profile",
    "notifications": "[MISSING] Notifications",
  },

  // Logs related strings
  "logs": {
    "usingDirectAPIData": "[MISSING] 🔄 Using direct API data, data length:",
    "imageLoadError": "[MISSING] 🖼️ Error loading image:",
    "usingIdoWatchlists": "[MISSING] Using IDO watchlists for upcoming projects:",
    "rawAPIData": "[MISSING] 🟡 Raw data from API:",
    "apiDataLength": "[MISSING] 🟡 API data length:",
    "filteredData": "[MISSING] 🔍 Filtered data:",
    "filteredDataLength": "[MISSING] 🔍 Filtered data length:",
    "imageUrlChecks": "[MISSING] 🖼️ Image URL checks:",
    "allProjectsFromAPI": "[MISSING] 📌 All projects from API:",
    "usingDirectAPI": "[MISSING] 🟢 Using direct API data",
    "rawAPIDataFirst5": "[MISSING] 🔍 Raw API data (first 5 items):",
    "emptyAPIResult": "[MISSING] ⚠️ API returned empty result, using empty array",
    "errorUsingEmptyArray": "[MISSING] ❌ Using empty array due to error",
    "totalAIScoreClicked": "[MISSING] Total AI Score clicked:",
    "filtersChanged": "[MISSING] Filters changed:",
    "upcomingIDOFirstUseEffect": "[MISSING] 🔄 UpcomingIDO - First useEffect triggered",
    "fetchInitialDataStarted": "[MISSING] 🔄 fetchInitialData started",
    "fetchInitialDataCompleted": "[MISSING] 🔄 fetchInitialData completed",
    "filterRunning": "[MISSING] 🔍 Filter running, coins length:",
    "paginatedData": "[MISSING] 📄 Paginated data:",
    "first2Records": "[MISSING] 📄 First 2 records:",
    "paginatedDataImageFields": "[MISSING] 📄 Image fields in paginated data:",
    "firstRecordDetails": "[MISSING] 🧩 First record details:",
    "firstRecordSocialScore": "[MISSING] 📢 First record social score value:",
    "clickedProjectInfo": "[MISSING] Clicked project info:",
    "redirectingWithDirectID": "[MISSING] Redirecting with direct ID:",
  },

  // Homepage related strings
  "homepage": {
    "badge": "[MISSING] AI-Powered Crypto Analysis",
    "title": "[MISSING] Make smarter crypto investments",
    "titleHighlight": "[MISSING] with AI insights",
    "description": "[MISSING] CoinScout analyzes over 3,000 cryptocurrencies across 40+ metrics to help you identify the best investment opportunities before others see them.",
    "ctaButton": "[MISSING] Start Free Analysis",
    "cryptocurrencies": "[MISSING] Cryptocurrencies",
    "analysisMetrics": "[MISSING] Analysis Metrics",
    "moreAccuracy": "[MISSING] More Accuracy",
  },

  // Features related strings
  "features": {
    "title": "[MISSING] AI-Enhanced Crypto Rating",
    "subtitle": "[MISSING] Our comprehensive suite of AI-powered tools helps you navigate the crypto market with confidence",
    "description": "[MISSING] Proprietary algorithms assess 50+ metrics across tokenomics, security, social engagement, and market factors",
  },

  // IdoRating related strings
  "idoRating": {
    "title": "[MISSING] IDO Rating",
    "description": "[MISSING] Discover and evaluate promising token offerings before their launch with detailed project analysis and scoring",
  },

  // CompareCoins related strings
  "compareCoins": {
    "title": "[MISSING] Compare Coins",
    "description": "[MISSING] Side-by-side analysis of multiple cryptocurrencies to identify the best investment opportunities based on your criteria",
  },

  // PortfolioGenerator related strings
  "portfolioGenerator": {
    "title": "[MISSING] Custom Portfolio Generator",
    "description": "[MISSING] AI-powered recommendations to build a diversified portfolio based on your risk tolerance and investment goals",
  },

  // PortfolioAnalysis related strings
  "portfolioAnalysis": {
    "title": "[MISSING] AI Portfolio Analysis",
    "description": "[MISSING] Gain AI-powered insights into your existing portfolio with automated risk analysis, diversification score, and optimization tips",
  },

  // Launchpads related strings
  "launchpads": {
    "title": "[MISSING] Top Launchpads",
    "description": "[MISSING] Discover the best platforms for new token offerings with performance metrics, success rates, and investor returns",
  },

  // AiAssistant related strings
  "aiAssistant": {
    "title": "[MISSING] AI Assistant",
    "description": "[MISSING] Get instant answers to your crypto questions with our advanced AI assistant that provides personalized guidance and insights",
  },

  // AirdropScore related strings
  "airdropScore": {
    "title": "[MISSING] Airdrop Score",
    "description": "[MISSING] Find legitimate airdrops with the highest potential value and lowest participation barriers through our verification system",
  },

  // GemScout related strings
  "gemScout": {
    "title": "[MISSING] Gem Scout",
    "description": "[MISSING] Discover early-stage projects with exceptional growth potential before they hit mainstream markets and major exchanges",
  },

  // Badges related strings
  "badges": {
    "betaTestingLive": "[MISSING] Beta Testing Live",
    "betaTestingSoon": "[MISSING] Beta Testing Soon",
    "comingSoon": "[MISSING] Coming Soon",
  },

  // Intelligence related strings
  "intelligence": {
    "title": "[MISSING] AI-Driven Intelligence for Smarter Crypto Decisions",
  },

  // Faq related strings
  "faq": {
    "title": "[MISSING] Frequently Asked Questions",
    "question": "[MISSING] What makes CoinScout's analysis different from other platforms?",
    "answer": "[MISSING] CoinScout leverages AI-driven analytics to assess over 50 key factors across tokenomics, security, social sentiment, market performance, and project fundamentals. Unlike traditional platforms that primarily provide raw data, we transform complex metrics into clear, actionable insights—making it easier for investors at all levels to understand and make informed decisions.",
  },

  // Upcoming related strings
  "upcoming": {
    "title": "[MISSING] Filters",
    "subtitle": "[MISSING] Discover and evaluate new tokens before they launch",
    "saleType": "[MISSING] Sale Type",
    "allTypes": "[MISSING] All Types",
    "launchpad": "[MISSING] Launchpad",
    "allLaunchpads": "[MISSING] All Launchpads",
    "category": "[MISSING] Category",
    "allCategories": "[MISSING] All Categories",
    "blockchain": "[MISSING] Blockchain",
    "allBlockchains": "[MISSING] All Blockchains",
    "investor": "[MISSING] Investor",
    "allInvestors": "[MISSING] All Investors",
    "projectScore": "[MISSING] Project Score",
    "listingDate": "[MISSING] Listing Date",
    "reset": "[MISSING] Reset Filters",
    "apply": "[MISSING] Apply Filters",
    "searchCategories": "[MISSING] Search categories...",
    "searchChains": "[MISSING] Search chains...",
    "selectDateRange": "[MISSING] Select date range",
    "last24Hours": "[MISSING] Last 24 Hours",
    "last7Days": "[MISSING] Last 7 Days",
    "last14Days": "[MISSING] Last 14 Days",
    "last30Days": "[MISSING] Last 30 Days",
    "last90Days": "[MISSING] Last 90 Days",
  },

  // Table related strings
  "table": {
    "name": "[MISSING] Name",
    "launchDate": "[MISSING] Launch Date",
    "initialCap": "[MISSING] Initial Cap",
    "totalRaised": "[MISSING] Total Raised",
    "score": "[MISSING] Score",
    "actions": "[MISSING] Actions",
  },

  // SaleType related strings
  "saleType": {
    "IDO": "[MISSING] Initial DEX Offering - Token sale conducted on a decentralized exchange (DEX)",
    "IEO": "[MISSING] Initial Exchange Offering - Token sale hosted on a centralized cryptocurrency exchange",
    "ICO": "[MISSING] Initial Coin Offering - First fundraising stage where new projects offer tokens to early investors",
    "SHO": "[MISSING] Strong Holder Offering - Token sale giving priority to long-term token holders",
    "Seed": "[MISSING] Seed Round - Early private funding round before public sale",
    "IGO": "[MISSING] Initial Game Offering - Fundraising focused on blockchain gaming projects",
    "ISO": "[MISSING] Initial Stake Offering - Token distribution through staking mechanism",
  },

  // ListingDate related strings
  "listingDate": {
    "24hours": "[MISSING] Last 24 Hours",
    "7days": "[MISSING] Last 7 Days",
    "14days": "[MISSING] Last 14 Days",
    "30days": "[MISSING] Last 30 Days",
    "90days": "[MISSING] Last 90 Days",
  },

  // UpcomingTour related strings
  "upcomingTour": {
    "title": "[MISSING] Welcome to Upcoming IDOs",
    "description": "[MISSING] Would you like a quick tour of the Upcoming IDOs features?",
    "info": "[MISSING] Learn how to filter upcoming token sales, understand the project details, and evaluate projects before they launch.",
    "dontShowAgain": "[MISSING] Don't show this again",
    "skipButton": "[MISSING] Skip for now",
    "startButton": "[MISSING] Start Tour",
  },

  // Steps related strings
  "steps": {
    "title": "[MISSING] Upcoming IDOs Overview",
    "description": "[MISSING] Welcome to the Upcoming IDOs page, where you can discover and evaluate new token launches before they go live.",
    "details": "[MISSING] Browse, filter, and analyze upcoming token sales across different blockchains and launchpads.",
  },

  // Search related strings
  "search": {
    "title": "[MISSING] Search & Find",
    "description": "[MISSING] Quickly search for any upcoming token sale by name or symbol.",
    "details": "[MISSING] Results update in real-time as you type.",
  },

  // ProjectInfo related strings
  "projectInfo": {
    "title": "[MISSING] Project Information",
    "description": "[MISSING] Each row contains detailed information about an upcoming token sale.",
    "details": "[MISSING] Click on any row to see detailed analysis for that project.",
    "action": "[MISSING] Try hovering over a project to see more information.",
  },

  // InitialCap related strings
  "initialCap": {
    "title": "[MISSING] Initial Market Cap",
    "description": "[MISSING] The expected initial market capitalization of the token upon listing.",
    "details": "[MISSING] Calculated as token price × circulating supply at TGE.",
  },

  // LaunchDate related strings
  "launchDate": {
    "title": "[MISSING] Launch Date",
    "description": "[MISSING] The scheduled date when the token will be available for trading.",
    "details": "[MISSING] Stay updated with upcoming launches to prepare your investment strategy.",
  },

  // Pagination related strings
  "pagination": {
    "title": "[MISSING] Page Navigation",
    "description": "[MISSING] Navigate through the list of upcoming token sales.",
    "details": "[MISSING] Adjust the number of projects displayed per page.",
  },

  // Completion related strings
  "completion": {
    "title": "[MISSING] You're All Set!",
    "description": "[MISSING] You've completed the tour of the Upcoming IDOs page.",
    "details": "[MISSING] Start exploring and analyzing upcoming token sales to find your next investment opportunity.",
    "help": "[MISSING] You can restart this tour anytime by clicking on the Guided Tour button.",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "Загрузка...",
  "common:error": "Ошибка",
  "common:retry": "Повторить",
  "common:save": "Сохранить",
  "common:cancel": "Отменить",
  "common:close": "Закрыть",
  "common:success": "Успешно",
  "common:viewMore": "Смотреть больше",
  "common:back": "Назад",
  "common:next": "Далее",
  "common:search": "Поиск",
  "common:searchCoinsAndTokens": "Поиск монет и предстоящих проектов",
  "common:searching": "Поиск...",
  "common:noResults": "Результатов не найдено",
  "common:coins": "Монеты",
  "common:upcomingIdos": "Предстоящие IDO",

  // Flat format for nav (for backward compatibility)
  "nav:home": "Главная",
  "nav:portfolio": "Портфолио",
  "nav:explore": "Обзор",
  "nav:news": "Новости",
  "nav:learn": "Обучение",
  "nav:profile": "Профиль",
  "nav:settings": "Настройки",
  "nav:logout": "Выйти",
  "nav:login": "Войти",
  "nav:register": "Регистрация",
  "nav:trending": "В тренде",
  "nav:favorites": "Избранное",
  "nav:watchlist": "Список наблюдения",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "Поиск...",
  "system:error.translation": "Ошибка перевода",
  "system:data.loading": "Загрузка данных",
  "system:data.empty": "Нет доступных данных",
  "system:data.error": "Ошибка загрузки данных",

  // Flat format for auth (for backward compatibility)
  "auth:email": "Электронная почта",
  "auth:email.placeholder": "Введите вашу электронную почту",
  "auth:email.description": "Ваша электронная почта никогда не будет передана третьим лицам",
  "auth:password": "Пароль",
  "auth:password.placeholder": "Введите ваш пароль",
  "auth:password.create": "Создать пароль",
  "auth:password.confirm": "Подтвердите пароль",
  "auth:password.confirm.placeholder": "Повторно введите пароль",
  "auth:password.show": "Показать пароль",
  "auth:password.hide": "Скрыть пароль",
  "auth:password.strength": "Надежность пароля",
  "auth:password.strength.weak": "Слабый",
  "auth:password.strength.good": "Хороший",
  "auth:password.strength.strong": "Надежный",
  "auth:password.reset.message": "Функция сброса пароля будет доступна в ближайшее время",
  "auth:forgotPassword": "Забыли пароль?",
  "auth:resetPassword": "Сбросить пароль",
  "auth:signin": "Войти",
  "auth:signin.loading": "Вход...",
  "auth:signin.securely": "Безопасный вход",
  "auth:signup": "Регистрация",
  "auth:signout": "Выйти",
  "auth:accountCreated": "Аккаунт успешно создан",
  "auth:passwordResetSent": "Отправлено письмо для сброса пароля",
  "auth:invalidCredentials": "Неверная электронная почта или пароль",
  "auth:continueWith": "Продолжить с",
  "auth:username": "Имя пользователя",
  "auth:username.placeholder": "Выберите имя пользователя",
  "auth:terms.agree": "Я согласен с",
  "auth:terms.service": "Условиями использования",
  "auth:terms.and": "и",
  "auth:terms.privacy": "Политикой конфиденциальности",
  "auth:termsAccept": "Продолжая, вы соглашаетесь с нашими Условиями использования и Политикой конфиденциальности",
  "auth:remember": "Запомнить меня на 30 дней",
  "auth:welcome.back": "С возвращением",
  "auth:login.credential.prompt": "Введите ваши данные для входа",
  "auth:login.success.title": "Успешно",
  "auth:login.success.description": "Вход выполнен успешно",
  "auth:login.error.title": "Ошибка входа",
  "auth:login.error.unknown": "Что-то пошло не так. Пожалуйста, попробуйте снова.",
  "auth:register.title": "Создать аккаунт",
  "auth:register.create": "Создать аккаунт",
  "auth:register.creating": "Создание аккаунта...",
  "auth:register.haveAccount": "Уже есть аккаунт?",
  "auth:register.success": "Регистрация успешна",
  "auth:register.success.detail": "Аккаунт успешно создан",
  "auth:register.success.login": "Аккаунт создан. Пожалуйста, войдите.",
  "auth:register.success.email_verify": "Регистрация успешна. Пожалуйста, проверьте свою электронную почту для активации аккаунта.",
  "auth:register.failed": "Ошибка регистрации",
  "auth:register.failed.generic": "Произошла ошибка при регистрации. Пожалуйста, попробуйте снова.",
  "auth:register.error.generic": "Произошла ошибка. Пожалуйста, попробуйте снова.",
  "auth:register.description": "Создайте аккаунт, чтобы начать",
  "auth:form.invalidFields": "Пожалуйста, заполните все обязательные поля корректно",
  "auth:validation.email": "Пожалуйста, введите действительный адрес электронной почты",
  "auth:validation.password.length": "Пароль должен содержать не менее 6 символов",
  "auth:validation.email.required": "Электронная почта обязательна",
  "auth:validation.email.invalid": "Пожалуйста, введите действительный адрес электронной почты",
  "auth:validation.email.complete": "Пожалуйста, введите полный адрес электронной почты",
  "auth:validation.password.required": "Пароль обязателен",
  "auth:validation.password.uppercase": "Пароль должен содержать хотя бы одну заглавную букву",
  "auth:validation.password.lowercase": "Пароль должен содержать хотя бы одну строчную букву",
  "auth:validation.password.number": "Пароль должен содержать хотя бы одну цифру",
  "auth:validation.password.special": "Пароль должен содержать хотя бы один специальный символ",
  "auth:login.failed": "Ошибка входа",
  "auth:authentication.required": "Требуется аутентификация",
  "auth:authentication.required.description": "Пожалуйста, войдите в систему для доступа к этой функции",
  "auth:authentication.signin": "Войти",
  "auth:authentication.continueWithEmail": "Продолжить с email",
  "auth:authentication.goBack": "Назад",
  "auth:authentication.signInPrompt": "Войдите в систему для доступа к персонализированным функциям, сохранения настроек и разблокировки всех возможностей CoinScout.",
  "auth:authentication.comparisonPrompt": "Войдите в систему для доступа к персонализированным функциям, сохранения настроек и разблокировки всех возможностей CoinScout для сравнения.",
  "auth:backToHome": "На главную",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "Все монеты",
  "coinlist:coinDetailDescription": "Нажмите на любую монету для подробного анализа",
  "coinlist:searchCoins": "Поиск монет...",
  "coinlist:searchBox.ariaLabel": "Поиск монет",
  "coinlist:aiPoweredTitle": "Фундаментальные рейтинги криптовалют на основе ИИ и данных",
  "coinlist:comprehensiveAnalysis": "Комплексный анализ и оценка криптовалют по",
  "coinlist:multipleMetrics": "множеству показателей",
  "coinlist:highlights": "Основные моменты",
  "coinlist:viewAll": "Смотреть все",
  "coinlist:currentPrice": "Текущая цена",
  "coinlist:marketCap": "Капитализация",
  "coinlist:rank": "Ранг",
  "coinlist:filters.button": "Фильтры",
  "coinlist:filters.title": "Параметры фильтрации",
  "coinlist:filters.description": "Настройте просмотр с помощью расширенных параметров фильтрации",
  "coinlist:filters.sortBy": "Сортировать по",
  "coinlist:filters.reset": "Сбросить фильтры",
  "coinlist:columns.name": "Имя",
  "coinlist:columns.tokenomics": "Токеномика",
  "coinlist:columns.security": "Безопасность",
  "coinlist:columns.social": "Социальное",
  "coinlist:columns.market": "Рынок",
  "coinlist:columns.insights": "Аналитика",
  "coinlist:columns.totalScore": "Общая оценка ИИ",
  "coinlist:columns.sevenDayChange": "Изменение за 7Д",
  "coinlist:columns.price": "Цена",
  "coinlist:tooltips.name.title": "Название и символ монеты",
  "coinlist:tooltips.name.description": "Официальное название и символ криптовалюты, как указано на биржах.",
  "coinlist:tooltips.tokenomics.title": "Анализ токеномики",
  "coinlist:tooltips.tokenomics.description": "Измеряет механизмы предложения токенов, риски инфляции и структуры вестинга.",
  "coinlist:tooltips.security.title": "Анализ безопасности",
  "coinlist:tooltips.security.description": "Результаты аудита безопасности и метрики оценки рисков.",
  "coinlist:tooltips.social.title": "Социальный анализ",
  "coinlist:tooltips.social.description": "Присутствие в социальных сетях, вовлеченность сообщества и анализ настроений.",
  "coinlist:tooltips.market.title": "Анализ рыночной производительности",
  "coinlist:tooltips.market.description": "Измеряет объем торгов, ликвидность и общее состояние рынка.",
  "coinlist:tooltips.insights.title": "Анализ ИИ-инсайтов",
  "coinlist:tooltips.insights.description": "ИИ-инсайты проекта, анализ настроений и метрики прогнозирования.",
  "coinlist:tooltips.totalScore.title": "Общий балл",
  "coinlist:tooltips.totalScore.description": "Общий рейтинг, рассчитанный на основе всех метрик оценки.",
  "coinlist:tooltips.sevenDayChange.title": "Изменение за 7 дней",
  "coinlist:tooltips.sevenDayChange.description": "Процентное изменение цены за последние 7 дней.",
  "coinlist:search.inProgress": "Поиск в процессе...",
  "coinlist:search.resultsUpdate": "Результаты будут обновляться автоматически",
  "coinlist:search.clearSearch": "Очистить поиск",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "Монеты с Максимальным Ростом",
  "highlights:newListings": "Новые Листинги",
  "highlights:upcomingIDOs": "Предстоящие IDO",
  "highlights:gemCoins": "Перспективные Монеты",
  "highlights:topAirdrops": "Лучшие Airdrop",
  "highlights:score": "Оценка",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "Обзор",
  "coindetail:fundamentals": "Фундаментальные показатели",
  "coindetail:technicals": "Технический анализ",
  "coindetail:news": "Новости",
  "coindetail:social": "Социальное",
  "coindetail:developers": "Разработчики",
  "coindetail:analysis": "Анализ",
  "coindetail:price": "Цена",
  "coindetail:volume": "Объём",
  "coindetail:marketCap": "Капитализация",
  "coindetail:circulatingSupply": "В обращении",
  "coindetail:totalSupply": "Общее предложение",
  "coindetail:maxSupply": "Максимальное предложение",
  "coindetail:allTimeHigh": "Исторический максимум",
  "coindetail:allTimeLow": "Исторический минимум",
  "coindetail:pricePrediction": "Прогноз цены",
  "coindetail:addToWatchlist": "Добавить в список наблюдения",
  "coindetail:removeFromWatchlist": "Удалить из списка наблюдения",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "Общая стоимость",
  "portfolio:recentActivity": "Недавняя активность",
  "portfolio:performance": "Производительность",
  "portfolio:holdings": "Активы",
  "portfolio:addAsset": "Добавить актив",
  "portfolio:editAsset": "Изменить актив",
  "portfolio:noAssets": "Нет активов в портфолио",
  "portfolio:24hChange": "Изменение за 24ч",
  "portfolio:allocation": "Распределение",
  "portfolio:profit": "Прибыль/Убыток",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "Внешний вид",
  "settings:language": "Язык",
  "settings:notifications": "Уведомления",
  "settings:security": "Безопасность",
  "settings:preferences": "Предпочтения",
  "settings:theme": "Тема",
  "settings:lightMode": "Светлый режим",
  "settings:darkMode": "Тёмный режим",
  "settings:systemDefault": "Системный режим",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "Закрепить",
  "sidebar:unlock": "Открепить",
  "sidebar:collapse": "Свернуть",
  "sidebar:cryptoRating": "Рейтинг криптовалют",
  "sidebar:idoRating": "Рейтинг IDO",
  "sidebar:compareCoins": "Сравнение монет",
  "sidebar:recentListings": "Новые листинги",
  "sidebar:topMovers": "Лидеры роста",
  "sidebar:watchlist": "Мой список наблюдения",
  "sidebar:aiPortfolio": "ИИ-гид по портфолио",
  "sidebar:portfolioAudit": "Аудит портфолио",
  "sidebar:launchpads": "Площадки запуска",
  "sidebar:airdrops": "Центр airdrop",
  "sidebar:aiAssistant": "ИИ-ассистент",
  "sidebar:gemScout": "Поиск перспективных монет",
  "sidebar:soon": "СКОРО",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "Что-то пошло не так",
  "error:tryAgain": "Пожалуйста, попробуйте снова",
  "error:networkIssue": "Проблема с сетевым подключением",
  "error:dataFetch": "Не удалось получить данные",
  "error:timeOut": "Превышено время ожидания",
  "error:invalidInput": "Неверный ввод",
  "error:pageNotFound": "Страница не найдена",

  // Flat format for format (for backward compatibility)
  "format:thousand": "тыс",
  "format:million": "млн",
  "format:billion": "млрд",
  "format:trillion": "трлн",

  // Flat format for coin (for backward compatibility)
  "coin:age": "Возраст:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "Отлично",
  "score:positive": "Хорошо",
  "score:average": "Средне",
  "score:weak": "Слабо",
  "score:critical": "Критично",

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "[MISSING] Filters",
  "upcoming:subtitle": "[MISSING] Discover and evaluate new tokens before they launch",
  "upcoming:filters.title": "[MISSING] Filters",
  "upcoming:filters.saleType": "[MISSING] Sale Type",
  "upcoming:filters.allTypes": "[MISSING] All Types",
  "upcoming:filters.launchpad": "[MISSING] Launchpad",
  "upcoming:filters.allLaunchpads": "[MISSING] All Launchpads",
  "upcoming:filters.category": "[MISSING] Category",
  "upcoming:filters.allCategories": "[MISSING] All Categories",
  "upcoming:filters.blockchain": "[MISSING] Blockchain",
  "upcoming:filters.allBlockchains": "[MISSING] All Blockchains",
  "upcoming:filters.investor": "[MISSING] Investor",
  "upcoming:filters.allInvestors": "[MISSING] All Investors",
  "upcoming:filters.projectScore": "[MISSING] Project Score",
  "upcoming:filters.listingDate": "[MISSING] Listing Date",
  "upcoming:filters.reset": "[MISSING] Reset Filters",
  "upcoming:filters.apply": "[MISSING] Apply Filters",
  "upcoming:table.name": "[MISSING] Name",
  "upcoming:table.launchDate": "[MISSING] Launch Date",
  "upcoming:table.initialCap": "[MISSING] Initial Cap",
  "upcoming:table.totalRaised": "[MISSING] Total Raised",
  "upcoming:table.score": "[MISSING] Score",
  "upcoming:table.actions": "[MISSING] Actions",
  "upcoming:search": "[MISSING] Search upcoming token sales...",
  "upcoming:noResults": "[MISSING] No upcoming token sales found",
  "upcoming:loading": "[MISSING] Loading upcoming token sales...",
  "upcoming:error": "[MISSING] Error loading upcoming token sales",
  "upcoming:retryButton": "[MISSING] Retry",
  "upcoming:tba": "[MISSING] TBA",
  "upcoming:rank": "[MISSING] Rank #{number}",
  "upcoming:saleType": "[MISSING] Sale Type",
  "upcoming:totalAiScore": "[MISSING] Total AI Score",
  "upcoming:points": "[MISSING] Points",
  "upcoming:tokenomics": "[MISSING] Tokenomics",
  "upcoming:security": "[MISSING] Security",
  "upcoming:social": "[MISSING] Social",
  "upcoming:market": "[MISSING] Market",
  "upcoming:insights": "[MISSING] Insights",

};

export default ru;