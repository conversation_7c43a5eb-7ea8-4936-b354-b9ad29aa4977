/**
 * Arabic localization strings
 */
const ar = {
  // Common strings
  "common": {
    "loading": "جاري التحميل...",
    "error": "خطأ",
    "retry": "إعادة المحاولة",
    "save": "حفظ",
    "cancel": "إلغاء",
    "close": "إغلاق",
    "success": "نجاح",
    "viewMore": "عرض المزيد",
    "back": "رجوع",
    "next": "التالي",
    "search": "بحث",
    "searchCoinsAndTokens": "ابحث عن العملات والرموز",
    "searching": "جاري البحث...",
    "noResults": "لم يتم العثور على نتائج",
    "coins": "العملات",
    "upcomingIdos": "العروض الأولية القادمة",
  },

  // Nav related strings
  "nav": {
    "home": "الرئيسية",
    "portfolio": "المحفظة",
    "explore": "استكشاف",
    "news": "الأخبار",
    "learn": "تعلم",
    "profile": "الملف الشخصي",
    "settings": "الإعدادات",
    "logout": "تسجيل الخروج",
    "login": "تسجيل الدخول",
    "register": "التسجيل",
    "trending": "الرائج",
    "favorites": "المفضلة",
    "watchlist": "قائمة المراقبة",
  },

  // System related strings
  "system": {
    "choose": "اختر اللغة",
    "current": "اللغة الحالية",
    "searching": "جاري البحث...",
  },

  // Error related strings
  "error": {
    "criticalError": "خطأ حرج",
    "somethingWentWrong": "حدث خطأ ما",
    "criticalErrorMessage": "حدث خطأ حرج. يرجى تحديث الصفحة أو العودة إلى الصفحة الرئيسية.",
    "returnToHome": "العودة إلى الرئيسية",
    "multipleErrorsDetected": "تم اكتشاف أخطاء متعددة",
    "unexpectedError": "حدث خطأ غير متوقع",
    "refreshPage": "تحديث الصفحة",
    "goToHome": "الذهاب إلى الرئيسية",
    "clearErrorLogs": "مسح سجلات الأخطاء",
    "anErrorOccurred": "حدث خطأ",
  },

  // Data related strings
  "data": {
    "loading": "جاري تحميل البيانات",
    "empty": "لا توجد بيانات متاحة",
    "error": "خطأ في تحميل البيانات",
  },

  // Auth related strings
  "auth": {
    "email": "البريد الإلكتروني",
    "email.placeholder": "أدخل بريدك الإلكتروني",
    "email.description": "لن نشارك بريدك الإلكتروني مع أي شخص آخر",
    "password": "كلمة المرور",
    "password.placeholder": "أدخل كلمة المرور",
    "password.create": "إنشاء كلمة مرور",
    "password.confirm": "تأكيد كلمة المرور",
    "password.confirm.placeholder": "تأكيد كلمة المرور",
    "password.show": "إظهار كلمة المرور",
    "password.hide": "إخفاء كلمة المرور",
    "password.strength": "قوة كلمة المرور",
    "password.strength.weak": "ضعيفة",
    "password.strength.good": "جيدة",
    "password.strength.strong": "قوية",
    "password.reset.message": "وظيفة إعادة تعيين كلمة المرور قادمة قريبًا",
    "forgotPassword": "نسيت كلمة المرور؟",
    "resetPassword": "إعادة تعيين كلمة المرور",
    "signin": "تسجيل الدخول",
    "signin.loading": "جاري تسجيل الدخول...",
    "signin.securely": "تسجيل الدخول بأمان",
    "signup": "التسجيل",
    "signout": "تسجيل الخروج",
    "accountCreated": "تم إنشاء الحساب بنجاح",
    "passwordResetSent": "تم إرسال بريد إعادة تعيين كلمة المرور",
    "invalidCredentials": "بريد إلكتروني أو كلمة مرور غير صالحة",
    "continueWith": "المتابعة باستخدام",
    "username": "اسم المستخدم",
    "username.placeholder": "اختر اسم مستخدم",
    "agree": "أوافق على",
    "service": "شروط الخدمة",
    "and": "و",
    "privacy": "سياسة الخصوصية",
    "password.criteria.length": "[MISSING] At least 8 characters",
    "password.criteria.uppercase": "[MISSING] At least one uppercase letter",
    "password.criteria.lowercase": "[MISSING] At least one lowercase letter",
    "password.criteria.number": "[MISSING] At least one number",
    "password.criteria.special": "[MISSING] At least one special character",
    "captcha.protected": "[MISSING] This form is protected by reCAPTCHA",
    "terms.service": "شروط الخدمة",
    "terms.and": "و",
    "terms.privacy": "سياسة الخصوصية",
  },

  // Login related strings
  "login": {
    "prompt": "أدخل بيانات الاعتماد الخاصة بك لتسجيل الدخول إلى حسابك",
  },

  // Success related strings
  "success": {
    "title": "نجاح",
    "description": "لقد قمت بتسجيل الدخول بنجاح",
  },

  // Register related strings
  "register": {
    "title": "إنشاء حساب",
    "create": "إنشاء حساب",
    "creating": "جاري إنشاء الحساب...",
    "haveAccount": "هل لديك حساب بالفعل؟",
    "success": "تم التسجيل بنجاح",
    "success.detail": "تم إنشاء حسابك بنجاح",
    "success.login": "تم إنشاء حسابك، يرجى تسجيل الدخول.",
    "failed": "فشل التسجيل",
    "failed.generic": "حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.",
    "generic": "حدث خطأ. يرجى المحاولة مرة أخرى.",
    "success.email_verify": "[MISSING] Registration successful. Please check your email to verify your account.",
  },

  // Form related strings
  "form": {
    "invalidFields": "يرجى ملء جميع الحقول المطلوبة بشكل صحيح",
  },

  // Validation related strings
  "validation": {
    "email": "يرجى إدخال عنوان بريد إلكتروني صالح",
    "length": "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
  },

  // Footer related strings
  "footer": {
    "description": "منصة متقدمة لتحليل العملات المشفرة وإدارة المحافظ مدعومة بالذكاء الاصطناعي.",
    "bankGradeSecurity": "أمان بمستوى مصرفي",
    "allRightsReserved": "جميع الحقوق محفوظة",
    "product": "المنتج",
    "learn": "تعلم",
    "community": "المجتمع",
    "legal": "قانوني",
  },

  // Links related strings
  "links": {
    "privacyPolicy": "سياسة الخصوصية",
    "termsOfService": "شروط الخدمة",
    "cookiePolicy": "سياسة ملفات تعريف الارتباط",
    "disclaimer": "إخلاء المسؤولية",
    "advertisingPolicy": "سياسة الإعلان",
    "careers": "الوظائف",
    "soon": "قريباً",
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "البحث عن العملات والمشاريع القادمة",
    "goToHomepage": "الذهاب إلى الصفحة الرئيسية",
    "coinScoutAlt": "CoinScout",
    "pricing": "التسعير",
    "goToApp": "الذهاب إلى التطبيق",
    "login": "تسجيل الدخول",
    "signUp": "التسجيل",
    "profile": "الملف الشخصي",
    "membershipManagement": "إدارة العضوية",
    "feedback": "التعليقات",
    "adminDashboard": "لوحة الإدارة",
    "logout": "تسجيل الخروج",
    "premium": "مميز",
    "pro": "محترف",
    "free": "مجاني",
  },

  // Watchlist related strings
  "watchlist": {
    "title": "قائمة المراقبة فارغة",
    "description": "ليس لديك أي قائمة مراقبة بعد. أنشئ قائمة مراقبة لتتبع عملاتك المشفرة.",
    "createWatchlist": "إنشاء قائمة مراقبة",
    "addCoins": "إضافة عملات",
  },

  // SaleType related strings
  "saleType": {
    "IDO": "العرض الأولي للبورصة اللامركزية - بيع الرموز المميزة في البورصة اللامركزية (DEX)",
    "IEO": "العرض الأولي للبورصة - بيع الرموز المميزة المستضاف في بورصة العملات المشفرة المركزية",
    "ICO": "العرض الأولي للعملة - مرحلة جمع الأموال الأولى حيث تقدم المشاريع الجديدة الرموز للمستثمرين الأوائل",
    "SHO": "عرض حامل قوي - بيع الرموز المميزة يعطي الأولوية لحاملي الرموز طويلة المدى",
    "Seed": "جولة البذور - جولة التمويل الخاص المبكر قبل البيع العام",
    "IGO": "العرض الأولي للألعاب - جمع الأموال يركز على مشاريع ألعاب البلوك تشين",
    "ISO": "العرض الأولي للرهان - توزيع الرموز من خلال آلية الرهان",
  },

  // ListingDate related strings
  "listingDate": {
    "24hours": "آخر 24 ساعة",
    "7days": "آخر 7 أيام",
    "14days": "آخر 14 يوم",
    "30days": "آخر 30 يوم",
    "90days": "آخر 90 يوم",
  },

  // UpcomingTour related strings
  "upcomingTour": {
    "title": "مرحباً بك في العروض الأولية القادمة",
    "description": "هل تريد جولة سريعة في ميزات العروض الأولية القادمة؟",
    "info": "تعلم كيفية تصفية مبيعات الرموز القادمة، وفهم تفاصيل المشروع، وتقييم المشاريع قبل إطلاقها.",
    "dontShowAgain": "لا تظهر هذا مرة أخرى",
    "skipButton": "تخطي الآن",
    "startButton": "بدء الجولة",
  },

  // Steps related strings
  "steps": {
    "title": "نظرة عامة على العروض الأولية القادمة",
    "description": "مرحباً بك في صفحة العروض الأولية القادمة، حيث يمكنك اكتشاف وتقييم إطلاقات الرموز الجديدة قبل أن تصبح مباشرة.",
    "details": "تصفح وصفي وحلل مبيعات الرموز القادمة عبر بلوك تشين ومنصات إطلاق مختلفة.",
  },

  // Filters related strings
  "filters": {
    "title": "خيارات التصفية",
    "description": "استخدم هذه المرشحات للعثور على أنواع محددة من مبيعات الرموز التي تتطابق مع معايير الاستثمار الخاصة بك.",
    "details": "صفي حسب نوع البيع أو منصة الإطلاق أو الفئة أو البلوك تشين أو المستثمرين.",
    "action": "جرب تحديد مرشح لترى كيف يحدث القائمة.",
  },

  // Search related strings
  "search": {
    "title": "البحث والعثور",
    "description": "ابحث بسرعة عن أي بيع رموز قادم بالاسم أو الرمز.",
    "details": "تتحدث النتائج في الوقت الفعلي أثناء الكتابة.",
  },

  // ProjectInfo related strings
  "projectInfo": {
    "title": "معلومات المشروع",
    "description": "كل صف يحتوي على معلومات مفصلة حول بيع رموز قادم.",
    "details": "انقر على أي صف لرؤية التحليل المفصل لذلك المشروع.",
    "action": "جرب تمرير الماوس فوق مشروع لرؤية المزيد من المعلومات.",
  },

  // InitialCap related strings
  "initialCap": {
    "title": "رأس المال الأولي",
    "description": "رأس المال السوقي الأولي المتوقع للرمز عند الإدراج.",
    "details": "محسوب كسعر الرمز × العرض المتداول في TGE.",
  },

  // Score related strings
  "score": {
    "title": "نقاط CoinScout",
    "description": "نقاطنا الخاصة تقيم الجودة الإجمالية وإمكانات المشروع.",
    "details": "بناءً على اقتصاديات الرمز والأمان ونشاط وسائل التواصل الاجتماعي والمزيد.",
  },

  // LaunchDate related strings
  "launchDate": {
    "title": "تاريخ الإطلاق",
    "description": "التاريخ المجدول عندما سيكون الرمز متاحاً للتداول.",
    "details": "ابق محدثاً مع الإطلاقات القادمة لإعداد استراتيجية الاستثمار الخاصة بك.",
  },

  // Pagination related strings
  "pagination": {
    "title": "تنقل الصفحة",
    "description": "تنقل عبر قائمة مبيعات الرموز القادمة.",
    "details": "اضبط عدد المشاريع المعروضة لكل صفحة.",
  },

  // Completion related strings
  "completion": {
    "title": "أنت جاهز!",
    "description": "لقد أكملت جولة صفحة العروض الأولية القادمة.",
    "details": "ابدأ في استكشاف وتحليل مبيعات الرموز القادمة للعثور على فرصة الاستثمار التالية.",
    "help": "يمكنك إعادة تشغيل هذه الجولة في أي وقت بالنقر على زر الجولة المرشدة.",
  },

  // Upcoming related strings
  "upcoming": {
    "title": "المرشحات",
    "subtitle": "اكتشف وقيم الرموز الجديدة قبل إطلاقها",
    "saleType": "نوع البيع",
    "allTypes": "جميع الأنواع",
    "launchpad": "منصة الإطلاق",
    "allLaunchpads": "جميع منصات الإطلاق",
    "category": "الفئة",
    "allCategories": "جميع الفئات",
    "blockchain": "البلوك تشين",
    "allBlockchains": "جميع البلوك تشين",
    "investor": "المستثمر",
    "allInvestors": "جميع المستثمرين",
    "projectScore": "نقاط المشروع",
    "listingDate": "تاريخ الإدراج",
    "reset": "إعادة تعيين المرشحات",
    "apply": "تطبيق المرشحات",
    "searchCategories": "البحث في الفئات...",
    "searchChains": "البحث في البلوك تشين...",
    "selectDateRange": "اختر نطاق التاريخ",
    "last24Hours": "آخر 24 ساعة",
    "last7Days": "آخر 7 أيام",
    "last14Days": "آخر 14 يوم",
    "last30Days": "آخر 30 يوم",
    "last90Days": "آخر 90 يوم",
  },

  // Table related strings
  "table": {
    "name": "الاسم",
    "launchDate": "تاريخ الإطلاق",
    "initialCap": "رأس المال الأولي",
    "totalRaised": "إجمالي المبلغ المجمع",
    "score": "النقاط",
    "actions": "الإجراءات",
  },

  // Methodology related strings
  "methodology": {
    "whatAreWeScoring": "ما الذي نقوم بتقييمه",
    "whyIsThisImportant": "لماذا هذا مهم",
    "scoringLevels": "مستويات التقييم",
  },

  // Profile related strings
  "profile": {
    "yourProfile": "ملفك الشخصي",
    "howOthersSeeYou": "هذه هي الطريقة التي سيراك بها الآخرون على المنصة.",
    "verified": "محقق",
    "unverified": "غير محقق",
    "memberSince": "عضو منذ",
    "unknown": "غير معروف",
    "status": "الحالة",
    "active": "نشط",
    "plan": "الخطة",
    "unknownPlan": "خطة غير معروفة",
    "planStatus": "حالة الخطة",
    "started": "بدأت",
    "expires": "تنتهي",
    "lastLogin": "آخر تسجيل دخول",
    "never": "أبداً",
    "profileCompletion": "[MISSING] Profile completion",
    "improveTip": "[MISSING] Add a bio and profile picture to improve your profile.",
    "signOut": "[MISSING] Sign Out",
    "areYouSure": "[MISSING] Are you sure?",
    "signOutConfirmation": "[MISSING] You'll be signed out from your account. You can sign back in at any time.",
    "cancel": "[MISSING] Cancel",
    "personalInformation": "[MISSING] Personal Information",
    "updateDescription": "[MISSING] Update your personal information and how your profile appears.",
    "username": "[MISSING] Username",
    "email": "[MISSING] Email",
    "emailPlaceholder": "[MISSING] <EMAIL>",
    "emailSent": "[MISSING] Email sent",
    "verificationEmailSent": "[MISSING] Verification email sent successfully. Please check your inbox.",
    "error": "[MISSING] Error",
    "emailSendError": "[MISSING] An error occurred while sending email. Please try again.",
    "resendVerification": "[MISSING] Resend verification email",
    "membershipTier": "[MISSING] Membership Tier",
    "pro": "[MISSING] Pro",
    "premium": "[MISSING] Premium",
    "free": "[MISSING] Free",
    "manageSubscription": "[MISSING] Manage your subscription in the",
    "membership": "[MISSING] Membership",
    "tab": "[MISSING] tab",
    "downloadData": "[MISSING] Download Your Data",
    "deleteAccount": "[MISSING] Delete Account",
    "absolutelySure": "[MISSING] Are you absolutely sure?",
    "deleteWarning": "[MISSING] This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    "title": "[MISSING] Profile",
    "description": "[MISSING] Manage your account settings and preferences.",
    "saveChanges": "[MISSING] Save Changes",
    "profile": "[MISSING] Profile",
    "notifications": "[MISSING] Notifications",
  },

  // Logs related strings
  "logs": {
    "usingDirectAPIData": "[MISSING] 🔄 Using direct API data, data length:",
    "imageLoadError": "[MISSING] 🖼️ Error loading image:",
    "usingIdoWatchlists": "[MISSING] Using IDO watchlists for upcoming projects:",
    "rawAPIData": "[MISSING] 🟡 Raw data from API:",
    "apiDataLength": "[MISSING] 🟡 API data length:",
    "filteredData": "[MISSING] 🔍 Filtered data:",
    "filteredDataLength": "[MISSING] 🔍 Filtered data length:",
    "imageUrlChecks": "[MISSING] 🖼️ Image URL checks:",
    "allProjectsFromAPI": "[MISSING] 📌 All projects from API:",
    "usingDirectAPI": "[MISSING] 🟢 Using direct API data",
    "rawAPIDataFirst5": "[MISSING] 🔍 Raw API data (first 5 items):",
    "emptyAPIResult": "[MISSING] ⚠️ API returned empty result, using empty array",
    "errorUsingEmptyArray": "[MISSING] ❌ Using empty array due to error",
    "totalAIScoreClicked": "[MISSING] Total AI Score clicked:",
    "filtersChanged": "[MISSING] Filters changed:",
    "upcomingIDOFirstUseEffect": "[MISSING] 🔄 UpcomingIDO - First useEffect triggered",
    "fetchInitialDataStarted": "[MISSING] 🔄 fetchInitialData started",
    "fetchInitialDataCompleted": "[MISSING] 🔄 fetchInitialData completed",
    "filterRunning": "[MISSING] 🔍 Filter running, coins length:",
    "paginatedData": "[MISSING] 📄 Paginated data:",
    "first2Records": "[MISSING] 📄 First 2 records:",
    "paginatedDataImageFields": "[MISSING] 📄 Image fields in paginated data:",
    "firstRecordDetails": "[MISSING] 🧩 First record details:",
    "firstRecordSocialScore": "[MISSING] 📢 First record social score value:",
    "clickedProjectInfo": "[MISSING] Clicked project info:",
    "redirectingWithDirectID": "[MISSING] Redirecting with direct ID:",
  },

  // Homepage related strings
  "homepage": {
    "badge": "[MISSING] AI-Powered Crypto Analysis",
    "title": "[MISSING] Make smarter crypto investments",
    "titleHighlight": "[MISSING] with AI insights",
    "description": "[MISSING] CoinScout analyzes over 3,000 cryptocurrencies across 40+ metrics to help you identify the best investment opportunities before others see them.",
    "ctaButton": "[MISSING] Start Free Analysis",
    "cryptocurrencies": "[MISSING] Cryptocurrencies",
    "analysisMetrics": "[MISSING] Analysis Metrics",
    "moreAccuracy": "[MISSING] More Accuracy",
  },

  // Features related strings
  "features": {
    "title": "[MISSING] AI-Enhanced Crypto Rating",
    "subtitle": "[MISSING] Our comprehensive suite of AI-powered tools helps you navigate the crypto market with confidence",
    "description": "[MISSING] Proprietary algorithms assess 50+ metrics across tokenomics, security, social engagement, and market factors",
  },

  // IdoRating related strings
  "idoRating": {
    "title": "[MISSING] IDO Rating",
    "description": "[MISSING] Discover and evaluate promising token offerings before their launch with detailed project analysis and scoring",
  },

  // CompareCoins related strings
  "compareCoins": {
    "title": "[MISSING] Compare Coins",
    "description": "[MISSING] Side-by-side analysis of multiple cryptocurrencies to identify the best investment opportunities based on your criteria",
  },

  // PortfolioGenerator related strings
  "portfolioGenerator": {
    "title": "[MISSING] Custom Portfolio Generator",
    "description": "[MISSING] AI-powered recommendations to build a diversified portfolio based on your risk tolerance and investment goals",
  },

  // PortfolioAnalysis related strings
  "portfolioAnalysis": {
    "title": "[MISSING] AI Portfolio Analysis",
    "description": "[MISSING] Gain AI-powered insights into your existing portfolio with automated risk analysis, diversification score, and optimization tips",
  },

  // Launchpads related strings
  "launchpads": {
    "title": "[MISSING] Top Launchpads",
    "description": "[MISSING] Discover the best platforms for new token offerings with performance metrics, success rates, and investor returns",
  },

  // AiAssistant related strings
  "aiAssistant": {
    "title": "[MISSING] AI Assistant",
    "description": "[MISSING] Get instant answers to your crypto questions with our advanced AI assistant that provides personalized guidance and insights",
  },

  // AirdropScore related strings
  "airdropScore": {
    "title": "[MISSING] Airdrop Score",
    "description": "[MISSING] Find legitimate airdrops with the highest potential value and lowest participation barriers through our verification system",
  },

  // GemScout related strings
  "gemScout": {
    "title": "[MISSING] Gem Scout",
    "description": "[MISSING] Discover early-stage projects with exceptional growth potential before they hit mainstream markets and major exchanges",
  },

  // Badges related strings
  "badges": {
    "betaTestingLive": "[MISSING] Beta Testing Live",
    "betaTestingSoon": "[MISSING] Beta Testing Soon",
    "comingSoon": "[MISSING] Coming Soon",
  },

  // Intelligence related strings
  "intelligence": {
    "title": "[MISSING] AI-Driven Intelligence for Smarter Crypto Decisions",
  },

  // Faq related strings
  "faq": {
    "title": "[MISSING] Frequently Asked Questions",
    "question": "[MISSING] What makes CoinScout's analysis different from other platforms?",
    "answer": "[MISSING] CoinScout leverages AI-driven analytics to assess over 50 key factors across tokenomics, security, social sentiment, market performance, and project fundamentals. Unlike traditional platforms that primarily provide raw data, we transform complex metrics into clear, actionable insights—making it easier for investors at all levels to understand and make informed decisions.",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "جاري التحميل...",
  "common:error": "خطأ",
  "common:retry": "إعادة المحاولة",
  "common:save": "حفظ",
  "common:cancel": "إلغاء",
  "common:close": "إغلاق",
  "common:success": "نجاح",
  "common:viewMore": "عرض المزيد",
  "common:back": "رجوع",
  "common:next": "التالي",
  "common:search": "بحث",
  "common:searchCoinsAndTokens": "ابحث عن العملات والرموز",
  "common:searching": "جاري البحث...",
  "common:noResults": "لم يتم العثور على نتائج",
  "common:coins": "العملات",
  "common:upcomingIdos": "العروض الأولية القادمة",

  // Flat format for nav (for backward compatibility)
  "nav:home": "الرئيسية",
  "nav:portfolio": "المحفظة",
  "nav:explore": "استكشاف",
  "nav:news": "الأخبار",
  "nav:learn": "تعلم",
  "nav:profile": "الملف الشخصي",
  "nav:settings": "الإعدادات",
  "nav:logout": "تسجيل الخروج",
  "nav:login": "تسجيل الدخول",
  "nav:register": "التسجيل",
  "nav:trending": "الرائج",
  "nav:favorites": "المفضلة",
  "nav:watchlist": "قائمة المراقبة",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "جاري البحث...",
  "system:error.translation": "خطأ في الترجمة",
  "system:data.loading": "جاري تحميل البيانات",
  "system:data.empty": "لا توجد بيانات متاحة",
  "system:data.error": "خطأ في تحميل البيانات",

  // Flat format for auth (for backward compatibility)
  "auth:email": "البريد الإلكتروني",
  "auth:email.placeholder": "أدخل بريدك الإلكتروني",
  "auth:email.description": "لن نشارك بريدك الإلكتروني مع أي شخص آخر",
  "auth:password": "كلمة المرور",
  "auth:password.placeholder": "أدخل كلمة المرور",
  "auth:password.create": "إنشاء كلمة مرور",
  "auth:confirmPassword": "تأكيد كلمة المرور",
  "auth:password.confirm.placeholder": "تأكيد كلمة المرور",
  "auth:password.show": "إظهار كلمة المرور",
  "auth:password.hide": "إخفاء كلمة المرور",
  "auth:password.strength": "قوة كلمة المرور",
  "auth:password.strength.weak": "ضعيفة",
  "auth:password.strength.good": "جيدة",
  "auth:password.strength.strong": "قوية",
  "auth:password.reset.message": "وظيفة إعادة تعيين كلمة المرور قادمة قريبًا",
  "auth:forgotPassword": "نسيت كلمة المرور؟",
  "auth:resetPassword": "إعادة تعيين كلمة المرور",
  "auth:signin": "تسجيل الدخول",
  "auth:signin.loading": "جاري تسجيل الدخول...",
  "auth:signin.securely": "تسجيل الدخول بأمان",
  "auth:signup": "التسجيل",
  "auth:signout": "تسجيل الخروج",
  "auth:accountCreated": "تم إنشاء الحساب بنجاح",
  "auth:passwordResetSent": "تم إرسال بريد إعادة تعيين كلمة المرور",
  "auth:invalidCredentials": "بريد إلكتروني أو كلمة مرور غير صالحة",
  "auth:continueWith": "المتابعة باستخدام",
  "auth:username": "اسم المستخدم",
  "auth:username.placeholder": "اختر اسم مستخدم",
  "auth:terms.agree": "أوافق على",
  "auth:terms.service": "شروط الخدمة",
  "auth:terms.and": "و",
  "auth:terms.privacy": "سياسة الخصوصية",
  "auth:termsAccept": "بالمتابعة، أنت توافق على شروط الخدمة وسياسة الخصوصية",
  "auth:remember": "تذكرني لمدة 30 يومًا",
  "auth:welcome.back": "مرحبًا بعودتك",
  "auth:login.credential.prompt": "أدخل بيانات الاعتماد الخاصة بك لتسجيل الدخول إلى حسابك",
  "auth:login.success.title": "نجاح",
  "auth:login.success.description": "لقد قمت بتسجيل الدخول بنجاح",
  "auth:login.error.title": "خطأ في تسجيل الدخول",
  "auth:login.error.unknown": "حدث خطأ ما. يرجى المحاولة مرة أخرى.",
  "auth:register.title": "إنشاء حساب",
  "auth:register.create": "إنشاء حساب",
  "auth:register.creating": "جاري إنشاء الحساب...",
  "auth:register.haveAccount": "هل لديك حساب بالفعل؟",
  "auth:register.success": "تم التسجيل بنجاح",
  "auth:register.success.detail": "تم إنشاء حسابك بنجاح",
  "auth:register.success.login": "تم إنشاء حسابك، يرجى تسجيل الدخول.",
  "auth:register.success.email_verify": "تم التسجيل بنجاح. يرجى التحقق من بريدك الإلكتروني لتفعيل حسابك.",
  "auth:register.failed": "فشل التسجيل",
  "auth:register.failed.generic": "حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.",
  "auth:register.error.generic": "حدث خطأ. يرجى المحاولة مرة أخرى.",
  "auth:register.description": "إنشاء حساب للبدء",
  "auth:form.invalidFields": "يرجى ملء جميع الحقول المطلوبة بشكل صحيح",
  "auth:validation.email": "يرجى إدخال عنوان بريد إلكتروني صالح",
  "auth:validation.password.length": "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
  "auth:password.confirm": "تأكيد كلمة المرور",
  "auth:authentication.required": "المصادقة مطلوبة",
  "auth:authentication.required.description": "يرجى تسجيل الدخول للوصول إلى هذه الميزة",
  "auth:authentication.signin": "تسجيل الدخول",
  "auth:authentication.continueWithEmail": "المتابعة بالبريد الإلكتروني",
  "auth:authentication.goBack": "العودة",
  "auth:authentication.signInPrompt": "قم بتسجيل الدخول للوصول إلى الميزات المخصصة وحفظ تفضيلاتك وإلغاء قفل جميع إمكانيات CoinScout.",
  "auth:authentication.comparisonPrompt": "قم بتسجيل الدخول للوصول إلى الميزات المخصصة وحفظ تفضيلاتك وإلغاء قفل جميع إمكانيات CoinScout للمقارنة.",
  "auth:backToHome": "العودة إلى الصفحة الرئيسية",
  "auth:validation.email.required": "[MISSING] Email is required",
  "auth:validation.email.invalid": "[MISSING] Please enter a valid email address",
  "auth:validation.email.complete": "[MISSING] Please enter a complete email address",
  "auth:validation.password.required": "[MISSING] Password is required",
  "auth:validation.password.uppercase": "[MISSING] Password must contain at least one uppercase letter",
  "auth:validation.password.lowercase": "[MISSING] Password must contain at least one lowercase letter",
  "auth:validation.password.number": "[MISSING] Password must contain at least one number",
  "auth:validation.password.special": "[MISSING] Password must contain at least one special character",
  "auth:login.failed": "[MISSING] Login Failed",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "جميع العملات",
  "coinlist:coinDetailDescription": "انقر على أي عملة للتحليل المفصل",
  "coinlist:searchCoins": "بحث عن العملات...",
  "coinlist:searchBox.ariaLabel": "بحث عن العملات",
  "coinlist:aiPoweredTitle": "تقييمات أساسية للعملات المشفرة مدعومة بالذكاء الاصطناعي والبيانات",
  "coinlist:comprehensiveAnalysis": "تحليل شامل وتقييم للعملات المشفرة عبر",
  "coinlist:multipleMetrics": "مقاييس متعددة",
  "coinlist:highlights": "أبرز النقاط",
  "coinlist:viewAll": "عرض الكل",
  "coinlist:currentPrice": "السعر الحالي",
  "coinlist:marketCap": "القيمة السوقية",
  "coinlist:rank": "التصنيف",
  "coinlist:filters.button": "الفلاتر",
  "coinlist:filters.title": "خيارات التصفية",
  "coinlist:filters.description": "خصص عرضك بخيارات تصفية متقدمة",
  "coinlist:columns.name": "الاسم",
  "coinlist:columns.tokenomics": "اقتصاديات التوكن",
  "coinlist:columns.security": "الأمان",
  "coinlist:columns.social": "اجتماعي",
  "coinlist:columns.market": "السوق",
  "coinlist:columns.insights": "نظرات ثاقبة",
  "coinlist:columns.totalScore": "إجمالي تقييم الذكاء الاصطناعي",
  "coinlist:columns.sevenDayChange": "تغير 7 أيام",
  "coinlist:tooltips.name.title": "[MISSING] Coin Name & Symbol",
  "coinlist:tooltips.name.description": "[MISSING] The official name and symbol of the cryptocurrency as listed on exchanges.",
  "coinlist:tooltips.tokenomics.title": "[MISSING] Tokenomics Analysis",
  "coinlist:tooltips.tokenomics.description": "[MISSING] Measures token supply mechanisms, inflation risks, and vesting structures.",
  "coinlist:tooltips.security.title": "[MISSING] Security Analysis",
  "coinlist:tooltips.security.description": "[MISSING] Security audit results and risk assessment metrics.",
  "coinlist:tooltips.social.title": "[MISSING] Social Analysis",
  "coinlist:tooltips.social.description": "[MISSING] Social media presence, community engagement and sentiment analysis.",
  "coinlist:tooltips.market.title": "[MISSING] Market Performance Analysis",
  "coinlist:tooltips.market.description": "[MISSING] Measures trading volume, liquidity and overall market health.",
  "coinlist:tooltips.insights.title": "[MISSING] AI Insights Analysis",
  "coinlist:tooltips.insights.description": "[MISSING] AI-powered project insights, sentiment analysis and forecasting metrics.",
  "coinlist:tooltips.totalScore.title": "[MISSING] Total Score",
  "coinlist:tooltips.totalScore.description": "[MISSING] The overall rating calculated from all scoring metrics.",
  "coinlist:tooltips.sevenDayChange.title": "[MISSING] 7 Day Change",
  "coinlist:tooltips.sevenDayChange.description": "[MISSING] Percentage price change over the last 7 days.",
  "coinlist:search.inProgress": "[MISSING] Search in progress...",
  "coinlist:search.resultsUpdate": "[MISSING] Results will update automatically",
  "coinlist:search.clearSearch": "[MISSING] Clear search",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "العملات الأكثر ربحاً",
  "highlights:newListings": "الإدراجات الجديدة",
  "highlights:upcomingIDOs": "العروض الأولية القادمة",
  "highlights:gemCoins": "عملات مخفية",
  "highlights:topAirdrops": "أفضل التوزيعات المجانية",
  "highlights:score": "النقاط",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "نظرة عامة",
  "coindetail:fundamentals": "الأساسيات",
  "coindetail:technicals": "التقنيات",
  "coindetail:news": "الأخبار",
  "coindetail:social": "اجتماعي",
  "coindetail:developers": "المطورون",
  "coindetail:analysis": "التحليل",
  "coindetail:price": "السعر",
  "coindetail:volume": "الحجم",
  "coindetail:marketCap": "القيمة السوقية",
  "coindetail:circulatingSupply": "العرض المتداول",
  "coindetail:totalSupply": "إجمالي العرض",
  "coindetail:maxSupply": "الحد الأقصى للعرض",
  "coindetail:allTimeHigh": "أعلى قيمة على الإطلاق",
  "coindetail:allTimeLow": "أدنى قيمة على الإطلاق",
  "coindetail:pricePrediction": "توقعات السعر",
  "coindetail:addToWatchlist": "إضافة إلى قائمة المراقبة",
  "coindetail:removeFromWatchlist": "إزالة من قائمة المراقبة",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "القيمة الإجمالية",
  "portfolio:recentActivity": "النشاط الأخير",
  "portfolio:performance": "الأداء",
  "portfolio:holdings": "الممتلكات",
  "portfolio:addAsset": "إضافة أصل",
  "portfolio:editAsset": "تعديل أصل",
  "portfolio:noAssets": "لا توجد أصول في المحفظة",
  "portfolio:24hChange": "تغير 24 ساعة",
  "portfolio:allocation": "التخصيص",
  "portfolio:profit": "الربح/الخسارة",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "المظهر",
  "settings:language": "اللغة",
  "settings:notifications": "الإشعارات",
  "settings:security": "الأمان",
  "settings:preferences": "التفضيلات",
  "settings:theme": "السمة",
  "settings:lightMode": "الوضع الفاتح",
  "settings:darkMode": "الوضع الداكن",
  "settings:systemDefault": "افتراضي النظام",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "إقفال",
  "sidebar:unlock": "فتح القفل",
  "sidebar:collapse": "طي",
  "sidebar:cryptoRating": "تقييم العملات المشفرة",
  "sidebar:idoRating": "تقييم العروض الأولية",
  "sidebar:compareCoins": "مقارنة العملات",
  "sidebar:recentListings": "الإدراجات الحديثة",
  "sidebar:topMovers": "الأكثر حركة",
  "sidebar:watchlist": "قائمة المراقبة",
  "sidebar:aiPortfolio": "دليل محفظة الذكاء الاصطناعي",
  "sidebar:portfolioAudit": "تدقيق المحفظة",
  "sidebar:launchpads": "منصات الإطلاق",
  "sidebar:airdrops": "مركز التوزيعات المجانية",
  "sidebar:aiAssistant": "مساعد الذكاء الاصطناعي",
  "sidebar:gemScout": "مستكشف العملات الواعدة",
  "sidebar:soon": "قريباً",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "حدث خطأ ما",
  "error:tryAgain": "يرجى المحاولة مرة أخرى",
  "error:networkIssue": "مشكلة في اتصال الشبكة",
  "error:dataFetch": "تعذر جلب البيانات",
  "error:timeOut": "انتهت مهلة الطلب",
  "error:invalidInput": "إدخال غير صالح",
  "error:pageNotFound": "الصفحة غير موجودة",

  // Flat format for format (for backward compatibility)
  "format:thousand": "ألف",
  "format:million": "مليون",
  "format:billion": "مليار",
  "format:trillion": "تريليون",

  // Flat format for coin (for backward compatibility)
  "coin:age": "العمر:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "ممتاز",
  "score:positive": "إيجابي",
  "score:average": "متوسط",
  "score:weak": "ضعيف",
  "score:critical": "حرج",

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "المرشحات",
  "upcoming:subtitle": "اكتشف وقيم الرموز الجديدة قبل إطلاقها",
  "upcoming:filters.title": "[MISSING] Filters",
  "upcoming:filters.saleType": "[MISSING] Sale Type",
  "upcoming:filters.allTypes": "[MISSING] All Types",
  "upcoming:filters.launchpad": "[MISSING] Launchpad",
  "upcoming:filters.allLaunchpads": "[MISSING] All Launchpads",
  "upcoming:filters.category": "[MISSING] Category",
  "upcoming:filters.allCategories": "[MISSING] All Categories",
  "upcoming:filters.blockchain": "[MISSING] Blockchain",
  "upcoming:filters.allBlockchains": "[MISSING] All Blockchains",
  "upcoming:filters.investor": "[MISSING] Investor",
  "upcoming:filters.allInvestors": "[MISSING] All Investors",
  "upcoming:filters.projectScore": "[MISSING] Project Score",
  "upcoming:filters.listingDate": "[MISSING] Listing Date",
  "upcoming:filters.reset": "[MISSING] Reset Filters",
  "upcoming:filters.apply": "[MISSING] Apply Filters",
  "upcoming:table.name": "[MISSING] Name",
  "upcoming:table.launchDate": "[MISSING] Launch Date",
  "upcoming:table.initialCap": "[MISSING] Initial Cap",
  "upcoming:table.totalRaised": "[MISSING] Total Raised",
  "upcoming:table.score": "[MISSING] Score",
  "upcoming:table.actions": "[MISSING] Actions",
  "upcoming:search": "[MISSING] Search upcoming token sales...",
  "upcoming:noResults": "[MISSING] No upcoming token sales found",
  "upcoming:loading": "[MISSING] Loading upcoming token sales...",
  "upcoming:error": "[MISSING] Error loading upcoming token sales",
  "upcoming:retryButton": "[MISSING] Retry",
  "upcoming:tba": "[MISSING] TBA",
  "upcoming:rank": "[MISSING] Rank #{number}",
  "upcoming:saleType": "نوع البيع",
  "upcoming:totalAiScore": "[MISSING] Total AI Score",
  "upcoming:points": "[MISSING] Points",
  "upcoming:tokenomics": "[MISSING] Tokenomics",
  "upcoming:security": "[MISSING] Security",
  "upcoming:social": "[MISSING] Social",
  "upcoming:market": "[MISSING] Market",
  "upcoming:insights": "[MISSING] Insights",

};

export default ar;