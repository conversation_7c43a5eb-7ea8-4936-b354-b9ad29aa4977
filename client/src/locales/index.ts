/**
 * Import all locale files and export them as a single object
 */

import en from './en';
import tr from './tr';
import ja from './ja';
import es from './es';
import ar from './ar';
import pt from './pt';
import esAR from './es-AR';
import ru from './ru';
import vi from './vi';
import ko from './ko';
import it from './it';
import zh from './zh';

// Export all translations
export const locales = {
  en,
  tr,
  ja,
  es,
  ar,
  pt,
  'es-AR': esAR,
  ru,
  vi,
  ko,
  it,
  zh
};

// Export available languages with metadata
export const availableLanguages = [
  {
    id: 1,
    code: 'en',
    name: 'English',
    nativeName: 'English',
    isRTL: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 2,
    code: 'tr',
    name: 'Turkish',
    nativeName: 'Türkçe',
    isRTL: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 3,
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    isRTL: false,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 4,
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    isRTL: false,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 5,
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    isRTL: true,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 6,
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    isRTL: false,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 7,
    code: 'es-AR',
    name: 'Spanish (Argentina)',
    nativeName: 'Español (Argentina)',
    isRTL: false,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 8,
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    isRTL: false, 
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 9,
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    isRTL: false,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 10,
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    isRTL: false,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 11,
    code: 'it',
    name: 'Italian',
    nativeName: 'Italiano',
    isRTL: false,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 12,
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    isRTL: false,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Types
export type SupportedLocale = keyof typeof locales;
export type Translation = Record<string, string | Record<string, string>>;

// Default language
export const defaultLocale: SupportedLocale = 'en';

/**
 * Flatten nested translation objects into dot notation
 * Example: 
 * { auth: { email: 'Email' } } becomes { 'auth.email': 'Email' }
 */
function flattenTranslations(obj: any, prefix = ''): Record<string, string> {
  return Object.keys(obj).reduce((acc: Record<string, string>, key: string) => {
    const prefixedKey = prefix ? `${prefix}.${key}` : key;
    
    // If the value is a nested object and not a string, recursively flatten it
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      Object.assign(acc, flattenTranslations(obj[key], prefixedKey));
    } else {
      // Handle the case where the value might be a string
      acc[prefixedKey] = obj[key];
    }
    
    return acc;
  }, {});
}

/**
 * Process translations to handle both flat and nested formats
 * This ensures backward compatibility with old format while supporting new one
 */
function processTranslations(translations: any): Record<string, string> {
  // Create a flattened version for consistent access
  const flattenedTranslations = flattenTranslations(translations);
  
  // Combine flattened with original for maximum compatibility
  return {
    ...flattenedTranslations,
    ...translations
  };
}

// Get translations for a specific language
export function getTranslations(locale: string): Record<string, string> {
  const rawTranslations = (locales as Record<string, any>)[locale as SupportedLocale] || locales[defaultLocale];
  return processTranslations(rawTranslations);
}