/**
 * Argentinian Spanish localization strings
 */
const esAR = {
  // Common strings
  "common": {
    "loading": "Cargando...",
    "error": "Error",
    "retry": "Reintentar",
    "save": "Guardar",
    "cancel": "Cancelar",
    "close": "Cerrar",
    "success": "<PERSON>xito",
    "viewMore": "Ver más",
    "back": "Atrás",
    "next": "Siguiente",
    "search": "Buscar",
    "searchCoinsAndTokens": "Buscar monedas y proyectos próximos",
    "searching": "Buscando...",
    "noResults": "No se encontraron resultados",
    "coins": "Monedas",
    "upcomingIdos": "IDOs Próximas",
  },

  // Nav related strings
  "nav": {
    "home": "Inicio",
    "portfolio": "Portafolio",
    "explore": "Explorar",
    "news": "Noticias",
    "learn": "Aprender",
    "profile": "Perfil",
    "settings": "Configuración",
    "logout": "Cerrar sesión",
    "login": "Iniciar sesión",
    "register": "Registrarse",
    "trending": "Tendencias",
    "favorites": "Favoritos",
    "watchlist": "Lista de seguimiento",
  },

  // System related strings
  "system": {
    "choose": "Elegir idioma",
    "current": "Idioma actual",
    "searching": "Buscando...",
  },

  // Error related strings
  "error": {
    "criticalError": "Error Crítico",
    "somethingWentWrong": "Algo salió mal",
    "criticalErrorMessage": "Ha ocurrido un error crítico. Por favor, actualizá la página o regresá a la página principal.",
    "returnToHome": "Regresar al Inicio",
    "multipleErrorsDetected": "Múltiples Errores Detectados",
    "unexpectedError": "Ocurrió un error inesperado",
    "refreshPage": "Actualizar Página",
    "goToHome": "Ir al Inicio",
    "clearErrorLogs": "Limpiar Registros de Errores",
    "anErrorOccurred": "Ocurrió un error",
  },

  // Data related strings
  "data": {
    "loading": "Cargando datos",
    "empty": "No hay datos disponibles",
    "error": "Error al cargar datos",
  },

  // Auth related strings
  "auth": {
    "email": "Correo electrónico",
    "email.placeholder": "Ingresá tu correo electrónico",
    "email.description": "Nunca compartiremos tu correo electrónico con nadie más",
    "password": "Contraseña",
    "password.placeholder": "Ingresá tu contraseña",
    "password.create": "Creá una contraseña",
    "password.confirm": "Confirmar Contraseña",
    "password.confirm.placeholder": "Confirmá tu contraseña",
    "password.show": "Mostrar contraseña",
    "password.hide": "Ocultar contraseña",
    "password.strength": "Seguridad de la contraseña",
    "password.strength.weak": "Débil",
    "password.strength.good": "Buena",
    "password.strength.strong": "Fuerte",
    "password.reset.message": "La funcionalidad de restablecer contraseña estará disponible pronto",
    "forgotPassword": "¿Olvidaste tu contraseña?",
    "resetPassword": "Restablecer Contraseña",
    "signin": "Iniciar sesión",
    "signin.loading": "Iniciando sesión...",
    "signin.securely": "Iniciar sesión de forma segura",
    "signup": "Registrarse",
    "signout": "Cerrar sesión",
    "accountCreated": "Cuenta creada exitosamente",
    "passwordResetSent": "Correo de restablecimiento de contraseña enviado",
    "invalidCredentials": "Correo electrónico o contraseña inválidos",
    "continueWith": "Continuar con",
    "username": "Nombre de usuario",
    "username.placeholder": "Elegí un nombre de usuario",
    "agree": "Acepto los",
    "service": "Términos de Servicio",
    "and": "y",
    "privacy": "Política de Privacidad",
    "password.criteria.length": "[MISSING] At least 8 characters",
    "password.criteria.uppercase": "[MISSING] At least one uppercase letter",
    "password.criteria.lowercase": "[MISSING] At least one lowercase letter",
    "password.criteria.number": "[MISSING] At least one number",
    "password.criteria.special": "[MISSING] At least one special character",
    "captcha.protected": "[MISSING] This form is protected by reCAPTCHA",
    "terms.service": "Términos de Servicio",
    "terms.and": "y",
    "terms.privacy": "Política de Privacidad",
  },

  // Login related strings
  "login": {
    "prompt": "Ingresá tus credenciales para iniciar sesión en tu cuenta",
  },

  // Success related strings
  "success": {
    "title": "Éxito",
    "description": "Has iniciado sesión exitosamente",
  },

  // Register related strings
  "register": {
    "title": "Crear una cuenta",
    "create": "Crear cuenta",
    "creating": "Creando cuenta...",
    "haveAccount": "¿Ya tenés una cuenta?",
    "success": "Registro exitoso",
    "success.detail": "Tu cuenta ha sido creada exitosamente",
    "success.login": "Tu cuenta ha sido creada, por favor iniciá sesión.",
    "failed": "Registro fallido",
    "failed.generic": "Ocurrió un error durante el registro. Por favor, intentá de nuevo.",
    "generic": "Ocurrió un error. Por favor, intentá de nuevo.",
    "success.email_verify": "[MISSING] Registration successful. Please check your email to verify your account.",
  },

  // Form related strings
  "form": {
    "invalidFields": "Por favor completá todos los campos requeridos correctamente",
  },

  // Validation related strings
  "validation": {
    "email": "Por favor ingresá una dirección de correo electrónico válida",
    "length": "La contraseña debe tener al menos 6 caracteres",
  },

  // Coinlist related strings
  "coinlist": {
    "allCoins": "Todas las monedas",
    "coinDetailDescription": "Hacé clic en cualquier moneda para un análisis detallado",
    "searchCoins": "Buscar monedas...",
    "ariaLabel": "Buscar monedas",
  },

  // Filters related strings
  "filters": {
    "button": "Filtros",
    "title": "Opciones de filtrado",
    "description": "Personalizá tu vista con opciones de filtrado avanzadas",
    "details": "[MISSING] Filter by sale type, launchpad, category, blockchain, or investors.",
    "action": "[MISSING] Try selecting a filter to see how it updates the list.",
  },

  // Columns related strings
  "columns": {
    "name": "Nombre",
    "tokenomics": "Tokenomía",
    "security": "Seguridad",
    "social": "Social",
    "market": "Mercado",
    "insights": "Perspectivas",
    "totalScore": "Puntuación total de IA",
    "sevenDayChange": "Cambio 7D",
  },

  // Highlights related strings
  "highlights": {
    "topGainers": "Monedas con Mayor Ganancia",
    "newListings": "Nuevos Listados",
    "upcomingIDOs": "Próximas IDOs",
    "gemCoins": "Monedas Gema",
    "topAirdrops": "Mejores Airdrops",
    "score": "Puntuación",
  },

  // Coindetail related strings
  "coindetail": {
    "overview": "Resumen",
    "fundamentals": "Fundamentales",
    "technicals": "Técnicos",
    "news": "Noticias",
    "social": "Social",
    "developers": "Desarrolladores",
    "analysis": "Análisis",
    "price": "Precio",
    "volume": "Volumen",
    "marketCap": "Capitalización de mercado",
    "circulatingSupply": "Suministro circulante",
    "totalSupply": "Suministro total",
    "maxSupply": "Suministro máximo",
    "allTimeHigh": "Máximo histórico",
    "allTimeLow": "Mínimo histórico",
    "pricePrediction": "Predicción de precio",
    "addToWatchlist": "Añadir a lista de seguimiento",
    "removeFromWatchlist": "Eliminar de lista de seguimiento",
  },

  // Portfolio related strings
  "portfolio": {
    "totalValue": "Valor total",
    "recentActivity": "Actividad reciente",
    "performance": "Rendimiento",
    "holdings": "Posiciones",
    "addAsset": "Añadir activo",
    "editAsset": "Editar activo",
    "noAssets": "No hay activos en el portafolio",
    "24hChange": "Cambio 24h",
    "allocation": "Asignación",
    "profit": "Ganancia/Pérdida",
  },

  // Settings related strings
  "settings": {
    "appearance": "Apariencia",
    "language": "Idioma",
    "notifications": "Notificaciones",
    "security": "Seguridad",
    "preferences": "Preferencias",
    "theme": "Tema",
    "lightMode": "Modo claro",
    "darkMode": "Modo oscuro",
    "systemDefault": "Predeterminado del sistema",
  },

  // Sidebar related strings
  "sidebar": {
    "lock": "Bloquear",
    "unlock": "Desbloquear",
    "collapse": "Contraer",
    "cryptoRating": "Calificación de Criptos",
    "idoRating": "Calificación de IDO",
    "compareCoins": "Comparar Monedas",
    "recentListings": "Listados Recientes",
    "topMovers": "Mayores Movimientos",
    "watchlist": "Mi Lista de Seguimiento",
    "aiPortfolio": "Guía de Portafolio IA",
    "portfolioAudit": "Auditoría de Portafolio",
    "launchpads": "Plataformas de Lanzamiento",
    "airdrops": "Centro de Airdrops",
    "aiAssistant": "Asistente IA",
    "gemScout": "Buscador de Gemas",
    "soon": "PRÓXIMAMENTE",
  },

  // Footer related strings
  "footer": {
    "description": "Plataforma avanzada de análisis de criptomonedas y gestión de portafolios impulsada por inteligencia artificial.",
    "bankGradeSecurity": "Seguridad de Grado Bancario",
    "allRightsReserved": "Todos los derechos reservados",
    "product": "Producto",
    "learn": "Aprender",
    "community": "Comunidad",
    "legal": "Legal",
  },

  // Links related strings
  "links": {
    "privacyPolicy": "Política de Privacidad",
    "termsOfService": "Términos de Servicio",
    "cookiePolicy": "Política de Cookies",
    "disclaimer": "Descargo de Responsabilidad",
    "advertisingPolicy": "Política de Publicidad",
    "careers": "Carreras",
    "soon": "próximamente",
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "Buscar Monedas y Proyectos Próximos",
    "goToHomepage": "Ir a la página principal",
    "coinScoutAlt": "CoinScout",
    "pricing": "Precios",
    "goToApp": "Ir a la App",
    "login": "Iniciar Sesión",
    "signUp": "Registrarse",
    "profile": "Perfil",
    "membershipManagement": "Gestión de Membresía",
    "feedback": "Comentarios",
    "adminDashboard": "Panel de Administración",
    "logout": "Cerrar Sesión",
    "premium": "Premium",
    "pro": "Pro",
    "free": "Gratis",
  },

  // Watchlist related strings
  "watchlist": {
    "title": "Tu Lista de Seguimiento está vacía",
    "description": "Aún no tenés ninguna lista de seguimiento. Creá una lista de seguimiento para rastrear tus criptomonedas.",
    "createWatchlist": "Crear Lista de Seguimiento",
    "addCoins": "Agregar Monedas",
  },

  // Upcoming related strings
  "upcoming": {
    "title": "Filtros",
    "subtitle": "Descubrí y evaluá nuevos tokens antes de que se lancen",
    "saleType": "Tipo de Venta",
    "allTypes": "Todos los Tipos",
    "launchpad": "Plataforma de Lanzamiento",
    "allLaunchpads": "Todas las Plataformas",
    "category": "Categoría",
    "allCategories": "Todas las Categorías",
    "blockchain": "Blockchain",
    "allBlockchains": "Todas las Blockchains",
    "investor": "Inversor",
    "allInvestors": "Todos los Inversores",
    "projectScore": "Puntuación del Proyecto",
    "listingDate": "Fecha de Listado",
    "reset": "Resetear Filtros",
    "apply": "Aplicar Filtros",
    "searchCategories": "Buscar categorías...",
    "searchChains": "Buscar blockchains...",
    "selectDateRange": "Seleccionar rango de fechas",
    "last24Hours": "Últimas 24 horas",
    "last7Days": "Últimos 7 días",
    "last14Days": "Últimos 14 días",
    "last30Days": "Últimos 30 días",
    "last90Days": "Últimos 90 días",
  },

  // Table related strings
  "table": {
    "name": "Nombre",
    "launchDate": "Fecha de Lanzamiento",
    "initialCap": "Cap Inicial",
    "totalRaised": "Total Recaudado",
    "score": "Puntuación",
    "actions": "Acciones",
  },

  // Methodology related strings
  "methodology": {
    "whatAreWeScoring": "Qué Estamos Evaluando",
    "whyIsThisImportant": "Por Qué Es Importante",
    "scoringLevels": "Niveles de Puntuación",
  },

  // Profile related strings
  "profile": {
    "yourProfile": "Tu Perfil",
    "howOthersSeeYou": "Así es como otros te verán en la plataforma.",
    "verified": "Verificado",
    "unverified": "No verificado",
    "memberSince": "Miembro desde",
    "unknown": "Desconocido",
    "status": "Estado",
    "active": "Activo",
    "plan": "Plan",
    "unknownPlan": "Plan Desconocido",
    "planStatus": "Estado del Plan",
    "started": "Iniciado",
    "expires": "Expira",
    "lastLogin": "Último acceso",
    "never": "Nunca",
    "profileCompletion": "[MISSING] Profile completion",
    "improveTip": "[MISSING] Add a bio and profile picture to improve your profile.",
    "signOut": "[MISSING] Sign Out",
    "areYouSure": "[MISSING] Are you sure?",
    "signOutConfirmation": "[MISSING] You'll be signed out from your account. You can sign back in at any time.",
    "cancel": "[MISSING] Cancel",
    "personalInformation": "[MISSING] Personal Information",
    "updateDescription": "[MISSING] Update your personal information and how your profile appears.",
    "username": "[MISSING] Username",
    "email": "[MISSING] Email",
    "emailPlaceholder": "[MISSING] <EMAIL>",
    "emailSent": "[MISSING] Email sent",
    "verificationEmailSent": "[MISSING] Verification email sent successfully. Please check your inbox.",
    "error": "[MISSING] Error",
    "emailSendError": "[MISSING] An error occurred while sending email. Please try again.",
    "resendVerification": "[MISSING] Resend verification email",
    "membershipTier": "[MISSING] Membership Tier",
    "pro": "[MISSING] Pro",
    "premium": "[MISSING] Premium",
    "free": "[MISSING] Free",
    "manageSubscription": "[MISSING] Manage your subscription in the",
    "membership": "[MISSING] Membership",
    "tab": "[MISSING] tab",
    "downloadData": "[MISSING] Download Your Data",
    "deleteAccount": "[MISSING] Delete Account",
    "absolutelySure": "[MISSING] Are you absolutely sure?",
    "deleteWarning": "[MISSING] This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    "title": "[MISSING] Profile",
    "description": "[MISSING] Manage your account settings and preferences.",
    "saveChanges": "[MISSING] Save Changes",
    "profile": "[MISSING] Profile",
    "notifications": "[MISSING] Notifications",
  },

  // Logs related strings
  "logs": {
    "usingDirectAPIData": "[MISSING] 🔄 Using direct API data, data length:",
    "imageLoadError": "[MISSING] 🖼️ Error loading image:",
    "usingIdoWatchlists": "[MISSING] Using IDO watchlists for upcoming projects:",
    "rawAPIData": "[MISSING] 🟡 Raw data from API:",
    "apiDataLength": "[MISSING] 🟡 API data length:",
    "filteredData": "[MISSING] 🔍 Filtered data:",
    "filteredDataLength": "[MISSING] 🔍 Filtered data length:",
    "imageUrlChecks": "[MISSING] 🖼️ Image URL checks:",
    "allProjectsFromAPI": "[MISSING] 📌 All projects from API:",
    "usingDirectAPI": "[MISSING] 🟢 Using direct API data",
    "rawAPIDataFirst5": "[MISSING] 🔍 Raw API data (first 5 items):",
    "emptyAPIResult": "[MISSING] ⚠️ API returned empty result, using empty array",
    "errorUsingEmptyArray": "[MISSING] ❌ Using empty array due to error",
    "totalAIScoreClicked": "[MISSING] Total AI Score clicked:",
    "filtersChanged": "[MISSING] Filters changed:",
    "upcomingIDOFirstUseEffect": "[MISSING] 🔄 UpcomingIDO - First useEffect triggered",
    "fetchInitialDataStarted": "[MISSING] 🔄 fetchInitialData started",
    "fetchInitialDataCompleted": "[MISSING] 🔄 fetchInitialData completed",
    "filterRunning": "[MISSING] 🔍 Filter running, coins length:",
    "paginatedData": "[MISSING] 📄 Paginated data:",
    "first2Records": "[MISSING] 📄 First 2 records:",
    "paginatedDataImageFields": "[MISSING] 📄 Image fields in paginated data:",
    "firstRecordDetails": "[MISSING] 🧩 First record details:",
    "firstRecordSocialScore": "[MISSING] 📢 First record social score value:",
    "clickedProjectInfo": "[MISSING] Clicked project info:",
    "redirectingWithDirectID": "[MISSING] Redirecting with direct ID:",
  },

  // Homepage related strings
  "homepage": {
    "badge": "[MISSING] AI-Powered Crypto Analysis",
    "title": "[MISSING] Make smarter crypto investments",
    "titleHighlight": "[MISSING] with AI insights",
    "description": "[MISSING] CoinScout analyzes over 3,000 cryptocurrencies across 40+ metrics to help you identify the best investment opportunities before others see them.",
    "ctaButton": "[MISSING] Start Free Analysis",
    "cryptocurrencies": "[MISSING] Cryptocurrencies",
    "analysisMetrics": "[MISSING] Analysis Metrics",
    "moreAccuracy": "[MISSING] More Accuracy",
  },

  // Features related strings
  "features": {
    "title": "[MISSING] AI-Enhanced Crypto Rating",
    "subtitle": "[MISSING] Our comprehensive suite of AI-powered tools helps you navigate the crypto market with confidence",
    "description": "[MISSING] Proprietary algorithms assess 50+ metrics across tokenomics, security, social engagement, and market factors",
  },

  // IdoRating related strings
  "idoRating": {
    "title": "[MISSING] IDO Rating",
    "description": "[MISSING] Discover and evaluate promising token offerings before their launch with detailed project analysis and scoring",
  },

  // CompareCoins related strings
  "compareCoins": {
    "title": "[MISSING] Compare Coins",
    "description": "[MISSING] Side-by-side analysis of multiple cryptocurrencies to identify the best investment opportunities based on your criteria",
  },

  // PortfolioGenerator related strings
  "portfolioGenerator": {
    "title": "[MISSING] Custom Portfolio Generator",
    "description": "[MISSING] AI-powered recommendations to build a diversified portfolio based on your risk tolerance and investment goals",
  },

  // PortfolioAnalysis related strings
  "portfolioAnalysis": {
    "title": "[MISSING] AI Portfolio Analysis",
    "description": "[MISSING] Gain AI-powered insights into your existing portfolio with automated risk analysis, diversification score, and optimization tips",
  },

  // Launchpads related strings
  "launchpads": {
    "title": "[MISSING] Top Launchpads",
    "description": "[MISSING] Discover the best platforms for new token offerings with performance metrics, success rates, and investor returns",
  },

  // AiAssistant related strings
  "aiAssistant": {
    "title": "[MISSING] AI Assistant",
    "description": "[MISSING] Get instant answers to your crypto questions with our advanced AI assistant that provides personalized guidance and insights",
  },

  // AirdropScore related strings
  "airdropScore": {
    "title": "[MISSING] Airdrop Score",
    "description": "[MISSING] Find legitimate airdrops with the highest potential value and lowest participation barriers through our verification system",
  },

  // GemScout related strings
  "gemScout": {
    "title": "[MISSING] Gem Scout",
    "description": "[MISSING] Discover early-stage projects with exceptional growth potential before they hit mainstream markets and major exchanges",
  },

  // Badges related strings
  "badges": {
    "betaTestingLive": "[MISSING] Beta Testing Live",
    "betaTestingSoon": "[MISSING] Beta Testing Soon",
    "comingSoon": "[MISSING] Coming Soon",
  },

  // Intelligence related strings
  "intelligence": {
    "title": "[MISSING] AI-Driven Intelligence for Smarter Crypto Decisions",
  },

  // Faq related strings
  "faq": {
    "title": "[MISSING] Frequently Asked Questions",
    "question": "[MISSING] What makes CoinScout's analysis different from other platforms?",
    "answer": "[MISSING] CoinScout leverages AI-driven analytics to assess over 50 key factors across tokenomics, security, social sentiment, market performance, and project fundamentals. Unlike traditional platforms that primarily provide raw data, we transform complex metrics into clear, actionable insights—making it easier for investors at all levels to understand and make informed decisions.",
  },

  // SaleType related strings
  "saleType": {
    "IDO": "[MISSING] Initial DEX Offering - Token sale conducted on a decentralized exchange (DEX)",
    "IEO": "[MISSING] Initial Exchange Offering - Token sale hosted on a centralized cryptocurrency exchange",
    "ICO": "[MISSING] Initial Coin Offering - First fundraising stage where new projects offer tokens to early investors",
    "SHO": "[MISSING] Strong Holder Offering - Token sale giving priority to long-term token holders",
    "Seed": "[MISSING] Seed Round - Early private funding round before public sale",
    "IGO": "[MISSING] Initial Game Offering - Fundraising focused on blockchain gaming projects",
    "ISO": "[MISSING] Initial Stake Offering - Token distribution through staking mechanism",
  },

  // ListingDate related strings
  "listingDate": {
    "24hours": "[MISSING] Last 24 Hours",
    "7days": "[MISSING] Last 7 Days",
    "14days": "[MISSING] Last 14 Days",
    "30days": "[MISSING] Last 30 Days",
    "90days": "[MISSING] Last 90 Days",
  },

  // UpcomingTour related strings
  "upcomingTour": {
    "title": "[MISSING] Welcome to Upcoming IDOs",
    "description": "[MISSING] Would you like a quick tour of the Upcoming IDOs features?",
    "info": "[MISSING] Learn how to filter upcoming token sales, understand the project details, and evaluate projects before they launch.",
    "dontShowAgain": "[MISSING] Don't show this again",
    "skipButton": "[MISSING] Skip for now",
    "startButton": "[MISSING] Start Tour",
  },

  // Steps related strings
  "steps": {
    "title": "[MISSING] Upcoming IDOs Overview",
    "description": "[MISSING] Welcome to the Upcoming IDOs page, where you can discover and evaluate new token launches before they go live.",
    "details": "[MISSING] Browse, filter, and analyze upcoming token sales across different blockchains and launchpads.",
  },

  // Search related strings
  "search": {
    "title": "[MISSING] Search & Find",
    "description": "[MISSING] Quickly search for any upcoming token sale by name or symbol.",
    "details": "[MISSING] Results update in real-time as you type.",
  },

  // ProjectInfo related strings
  "projectInfo": {
    "title": "[MISSING] Project Information",
    "description": "[MISSING] Each row contains detailed information about an upcoming token sale.",
    "details": "[MISSING] Click on any row to see detailed analysis for that project.",
    "action": "[MISSING] Try hovering over a project to see more information.",
  },

  // InitialCap related strings
  "initialCap": {
    "title": "[MISSING] Initial Market Cap",
    "description": "[MISSING] The expected initial market capitalization of the token upon listing.",
    "details": "[MISSING] Calculated as token price × circulating supply at TGE.",
  },

  // Score related strings
  "score": {
    "title": "[MISSING] CoinScout Score",
    "description": "[MISSING] Our proprietary score evaluates the overall quality and potential of the project.",
    "details": "[MISSING] Based on tokenomics, security, social media activity, and more.",
  },

  // LaunchDate related strings
  "launchDate": {
    "title": "[MISSING] Launch Date",
    "description": "[MISSING] The scheduled date when the token will be available for trading.",
    "details": "[MISSING] Stay updated with upcoming launches to prepare your investment strategy.",
  },

  // Pagination related strings
  "pagination": {
    "title": "[MISSING] Page Navigation",
    "description": "[MISSING] Navigate through the list of upcoming token sales.",
    "details": "[MISSING] Adjust the number of projects displayed per page.",
  },

  // Completion related strings
  "completion": {
    "title": "[MISSING] You're All Set!",
    "description": "[MISSING] You've completed the tour of the Upcoming IDOs page.",
    "details": "[MISSING] Start exploring and analyzing upcoming token sales to find your next investment opportunity.",
    "help": "[MISSING] You can restart this tour anytime by clicking on the Guided Tour button.",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "Cargando...",
  "common:error": "Error",
  "common:retry": "Reintentar",
  "common:save": "Guardar",
  "common:cancel": "Cancelar",
  "common:close": "Cerrar",
  "common:success": "Éxito",
  "common:viewMore": "Ver más",
  "common:back": "Atrás",
  "common:next": "Siguiente",
  "common:search": "Buscar",
  "common:searchCoinsAndTokens": "Buscar monedas y proyectos próximos",
  "common:searching": "Buscando...",
  "common:noResults": "No se encontraron resultados",
  "common:coins": "Monedas",
  "common:upcomingIdos": "IDOs Próximas",

  // Flat format for nav (for backward compatibility)
  "nav:home": "Inicio",
  "nav:portfolio": "Portafolio",
  "nav:explore": "Explorar",
  "nav:news": "Noticias",
  "nav:learn": "Aprender",
  "nav:profile": "Perfil",
  "nav:settings": "Configuración",
  "nav:logout": "Cerrar sesión",
  "nav:login": "Iniciar sesión",
  "nav:register": "Registrarse",
  "nav:trending": "Tendencias",
  "nav:favorites": "Favoritos",
  "nav:watchlist": "Lista de seguimiento",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "Buscando...",
  "system:error.translation": "Error de traducción",
  "system:data.loading": "Cargando datos",
  "system:data.empty": "No hay datos disponibles",
  "system:data.error": "Error al cargar datos",

  // Flat format for auth (for backward compatibility)
  "auth:email": "Correo electrónico",
  "auth:email.placeholder": "Ingresá tu correo electrónico",
  "auth:email.description": "Nunca compartiremos tu correo electrónico con nadie más",
  "auth:password": "Contraseña",
  "auth:password.placeholder": "Ingresá tu contraseña",
  "auth:password.create": "Creá una contraseña",
  "auth:password.confirm": "Confirmar Contraseña",
  "auth:password.confirm.placeholder": "Confirmá tu contraseña",
  "auth:password.show": "Mostrar contraseña",
  "auth:password.hide": "Ocultar contraseña",
  "auth:password.strength": "Seguridad de la contraseña",
  "auth:password.strength.weak": "Débil",
  "auth:password.strength.good": "Buena",
  "auth:password.strength.strong": "Fuerte",
  "auth:password.reset.message": "La funcionalidad de restablecer contraseña estará disponible pronto",
  "auth:forgotPassword": "¿Olvidaste tu contraseña?",
  "auth:resetPassword": "Restablecer contraseña",
  "auth:signin": "Iniciar sesión",
  "auth:signin.loading": "Iniciando sesión...",
  "auth:signin.securely": "Iniciar sesión de forma segura",
  "auth:signup": "Registrarse",
  "auth:signout": "Cerrar sesión",
  "auth:accountCreated": "Cuenta creada exitosamente",
  "auth:passwordResetSent": "Correo de restablecimiento de contraseña enviado",
  "auth:invalidCredentials": "Correo electrónico o contraseña inválidos",
  "auth:continueWith": "Continuar con",
  "auth:username": "Nombre de usuario",
  "auth:username.placeholder": "Elegí un nombre de usuario",
  "auth:terms.agree": "Acepto los",
  "auth:terms.service": "Términos de Servicio",
  "auth:terms.and": "y",
  "auth:terms.privacy": "Política de Privacidad",
  "auth:termsAccept": "Al continuar, aceptás nuestros Términos de servicio y Política de privacidad",
  "auth:remember": "Recordarme durante 30 días",
  "auth:welcome.back": "Bienvenido/a de nuevo",
  "auth:login.credential.prompt": "Ingresá tus credenciales para iniciar sesión en tu cuenta",
  "auth:login.success.title": "Éxito",
  "auth:login.success.description": "Has iniciado sesión exitosamente",
  "auth:login.error.title": "Error de inicio de sesión",
  "auth:login.error.unknown": "Algo salió mal. Por favor, intentá de nuevo.",
  "auth:register.title": "Crear una cuenta",
  "auth:register.create": "Crear cuenta",
  "auth:register.creating": "Creando cuenta...",
  "auth:register.haveAccount": "¿Ya tenés una cuenta?",
  "auth:register.success": "Registro exitoso",
  "auth:register.success.detail": "Tu cuenta ha sido creada exitosamente",
  "auth:register.success.login": "Tu cuenta ha sido creada, por favor iniciá sesión.",
  "auth:register.success.email_verify": "Registro exitoso. Por favor, verificá tu correo electrónico para activar tu cuenta.",
  "auth:register.failed": "Registro fallido",
  "auth:register.failed.generic": "Ocurrió un error durante el registro. Por favor, intentá de nuevo.",
  "auth:register.error.generic": "Ocurrió un error. Por favor, intentá de nuevo.",
  "auth:register.description": "Creá una cuenta para comenzar",
  "auth:form.invalidFields": "Por favor completá todos los campos requeridos correctamente",
  "auth:validation.email": "Por favor ingresá una dirección de correo electrónico válida",
  "auth:validation.password.length": "La contraseña debe tener al menos 6 caracteres",
  "auth:authentication.required": "Autenticación Requerida",
  "auth:authentication.required.description": "Por favor, iniciá sesión para acceder a esta función",
  "auth:authentication.signin": "Iniciar sesión",
  "auth:authentication.continueWithEmail": "Continuar con email",
  "auth:authentication.goBack": "Volver",
  "auth:authentication.signInPrompt": "Iniciá sesión para acceder a funciones personalizadas, guardar tus preferencias y desbloquear todas las capacidades de CoinScout.",
  "auth:authentication.comparisonPrompt": "Iniciá sesión para acceder a funciones personalizadas, guardar tus preferencias y desbloquear todas las capacidades de CoinScout para comparación.",
  "auth:backToHome": "Volver al Inicio",
  "auth:validation.email.required": "[MISSING] Email is required",
  "auth:validation.email.invalid": "[MISSING] Please enter a valid email address",
  "auth:validation.email.complete": "[MISSING] Please enter a complete email address",
  "auth:validation.password.required": "[MISSING] Password is required",
  "auth:validation.password.uppercase": "[MISSING] Password must contain at least one uppercase letter",
  "auth:validation.password.lowercase": "[MISSING] Password must contain at least one lowercase letter",
  "auth:validation.password.number": "[MISSING] Password must contain at least one number",
  "auth:validation.password.special": "[MISSING] Password must contain at least one special character",
  "auth:login.failed": "[MISSING] Login Failed",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "Todas las monedas",
  "coinlist:coinDetailDescription": "Hacé clic en cualquier moneda para un análisis detallado",
  "coinlist:searchCoins": "Buscar monedas...",
  "coinlist:searchBox.ariaLabel": "Buscar monedas",
  "coinlist:aiPoweredTitle": "Calificaciones fundamentales de criptomonedas impulsadas por IA y datos",
  "coinlist:comprehensiveAnalysis": "Análisis integral y puntuación de criptomonedas a través de",
  "coinlist:multipleMetrics": "múltiples métricas",
  "coinlist:highlights": "Destacados",
  "coinlist:viewAll": "Ver todo",
  "coinlist:currentPrice": "Precio actual",
  "coinlist:marketCap": "Capitalización de mercado",
  "coinlist:rank": "Ranking",
  "coinlist:filters.button": "Filtros",
  "coinlist:filters.title": "Opciones de filtrado",
  "coinlist:filters.description": "Personalizá tu vista con opciones de filtrado avanzadas",
  "coinlist:columns.name": "Nombre",
  "coinlist:columns.tokenomics": "Tokenomía",
  "coinlist:columns.security": "Seguridad",
  "coinlist:columns.social": "Social",
  "coinlist:columns.market": "Mercado",
  "coinlist:columns.insights": "Perspectivas",
  "coinlist:columns.totalScore": "Puntuación total de IA",
  "coinlist:columns.sevenDayChange": "Cambio 7D",
  "coinlist:tooltips.name.title": "[MISSING] Coin Name & Symbol",
  "coinlist:tooltips.name.description": "[MISSING] The official name and symbol of the cryptocurrency as listed on exchanges.",
  "coinlist:tooltips.tokenomics.title": "[MISSING] Tokenomics Analysis",
  "coinlist:tooltips.tokenomics.description": "[MISSING] Measures token supply mechanisms, inflation risks, and vesting structures.",
  "coinlist:tooltips.security.title": "[MISSING] Security Analysis",
  "coinlist:tooltips.security.description": "[MISSING] Security audit results and risk assessment metrics.",
  "coinlist:tooltips.social.title": "[MISSING] Social Analysis",
  "coinlist:tooltips.social.description": "[MISSING] Social media presence, community engagement and sentiment analysis.",
  "coinlist:tooltips.market.title": "[MISSING] Market Performance Analysis",
  "coinlist:tooltips.market.description": "[MISSING] Measures trading volume, liquidity and overall market health.",
  "coinlist:tooltips.insights.title": "[MISSING] AI Insights Analysis",
  "coinlist:tooltips.insights.description": "[MISSING] AI-powered project insights, sentiment analysis and forecasting metrics.",
  "coinlist:tooltips.totalScore.title": "[MISSING] Total Score",
  "coinlist:tooltips.totalScore.description": "[MISSING] The overall rating calculated from all scoring metrics.",
  "coinlist:tooltips.sevenDayChange.title": "[MISSING] 7 Day Change",
  "coinlist:tooltips.sevenDayChange.description": "[MISSING] Percentage price change over the last 7 days.",
  "coinlist:search.inProgress": "[MISSING] Search in progress...",
  "coinlist:search.resultsUpdate": "[MISSING] Results will update automatically",
  "coinlist:search.clearSearch": "[MISSING] Clear search",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "Monedas con Mayor Ganancia",
  "highlights:newListings": "Nuevos Listados",
  "highlights:upcomingIDOs": "Próximas IDOs",
  "highlights:gemCoins": "Monedas Gema",
  "highlights:topAirdrops": "Mejores Airdrops",
  "highlights:score": "Puntuación",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "Resumen",
  "coindetail:fundamentals": "Fundamentales",
  "coindetail:technicals": "Técnicos",
  "coindetail:news": "Noticias",
  "coindetail:social": "Social",
  "coindetail:developers": "Desarrolladores",
  "coindetail:analysis": "Análisis",
  "coindetail:price": "Precio",
  "coindetail:volume": "Volumen",
  "coindetail:marketCap": "Capitalización de mercado",
  "coindetail:circulatingSupply": "Suministro circulante",
  "coindetail:totalSupply": "Suministro total",
  "coindetail:maxSupply": "Suministro máximo",
  "coindetail:allTimeHigh": "Máximo histórico",
  "coindetail:allTimeLow": "Mínimo histórico",
  "coindetail:pricePrediction": "Predicción de precio",
  "coindetail:addToWatchlist": "Añadir a lista de seguimiento",
  "coindetail:removeFromWatchlist": "Eliminar de lista de seguimiento",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "Valor total",
  "portfolio:recentActivity": "Actividad reciente",
  "portfolio:performance": "Rendimiento",
  "portfolio:holdings": "Posiciones",
  "portfolio:addAsset": "Añadir activo",
  "portfolio:editAsset": "Editar activo",
  "portfolio:noAssets": "No hay activos en el portafolio",
  "portfolio:24hChange": "Cambio 24h",
  "portfolio:allocation": "Asignación",
  "portfolio:profit": "Ganancia/Pérdida",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "Apariencia",
  "settings:language": "Idioma",
  "settings:notifications": "Notificaciones",
  "settings:security": "Seguridad",
  "settings:preferences": "Preferencias",
  "settings:theme": "Tema",
  "settings:lightMode": "Modo claro",
  "settings:darkMode": "Modo oscuro",
  "settings:systemDefault": "Predeterminado del sistema",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "Bloquear",
  "sidebar:unlock": "Desbloquear",
  "sidebar:collapse": "Contraer",
  "sidebar:cryptoRating": "Calificación de Criptos",
  "sidebar:idoRating": "Calificación de IDO",
  "sidebar:compareCoins": "Comparar Monedas",
  "sidebar:recentListings": "Listados Recientes",
  "sidebar:topMovers": "Mayores Movimientos",
  "sidebar:watchlist": "Mi Lista de Seguimiento",
  "sidebar:aiPortfolio": "Guía de Portafolio IA",
  "sidebar:portfolioAudit": "Auditoría de Portafolio",
  "sidebar:launchpads": "Plataformas de Lanzamiento",
  "sidebar:airdrops": "Centro de Airdrops",
  "sidebar:aiAssistant": "Asistente IA",
  "sidebar:gemScout": "Buscador de Gemas",
  "sidebar:soon": "PRÓXIMAMENTE",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "Algo salió mal",
  "error:tryAgain": "Por favor, intentá de nuevo",
  "error:networkIssue": "Problema de conexión de red",
  "error:dataFetch": "No se pudieron obtener los datos",
  "error:timeOut": "Tiempo de espera agotado",
  "error:invalidInput": "Entrada inválida",
  "error:pageNotFound": "Página no encontrada",

  // Flat format for format (for backward compatibility)
  "format:thousand": "K",
  "format:million": "M",
  "format:billion": "B",
  "format:trillion": "T",

  // Flat format for coin (for backward compatibility)
  "coin:age": "Edad:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "Excelente",
  "score:positive": "Positivo",
  "score:average": "Promedio",
  "score:weak": "Débil",
  "score:critical": "Crítico",

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "Filtros",
  "upcoming:subtitle": "Descubrí y evaluá nuevos tokens antes de que se lancen",
  "upcoming:filters.title": "[MISSING] Filters",
  "upcoming:filters.saleType": "[MISSING] Sale Type",
  "upcoming:filters.allTypes": "[MISSING] All Types",
  "upcoming:filters.launchpad": "[MISSING] Launchpad",
  "upcoming:filters.allLaunchpads": "[MISSING] All Launchpads",
  "upcoming:filters.category": "[MISSING] Category",
  "upcoming:filters.allCategories": "[MISSING] All Categories",
  "upcoming:filters.blockchain": "[MISSING] Blockchain",
  "upcoming:filters.allBlockchains": "[MISSING] All Blockchains",
  "upcoming:filters.investor": "[MISSING] Investor",
  "upcoming:filters.allInvestors": "[MISSING] All Investors",
  "upcoming:filters.projectScore": "[MISSING] Project Score",
  "upcoming:filters.listingDate": "[MISSING] Listing Date",
  "upcoming:filters.reset": "[MISSING] Reset Filters",
  "upcoming:filters.apply": "[MISSING] Apply Filters",
  "upcoming:table.name": "[MISSING] Name",
  "upcoming:table.launchDate": "[MISSING] Launch Date",
  "upcoming:table.initialCap": "[MISSING] Initial Cap",
  "upcoming:table.totalRaised": "[MISSING] Total Raised",
  "upcoming:table.score": "[MISSING] Score",
  "upcoming:table.actions": "[MISSING] Actions",
  "upcoming:search": "[MISSING] Search upcoming token sales...",
  "upcoming:noResults": "[MISSING] No upcoming token sales found",
  "upcoming:loading": "[MISSING] Loading upcoming token sales...",
  "upcoming:error": "[MISSING] Error loading upcoming token sales",
  "upcoming:retryButton": "[MISSING] Retry",
  "upcoming:tba": "[MISSING] TBA",
  "upcoming:rank": "[MISSING] Rank #{number}",
  "upcoming:saleType": "Tipo de Venta",
  "upcoming:totalAiScore": "[MISSING] Total AI Score",
  "upcoming:points": "[MISSING] Points",
  "upcoming:tokenomics": "[MISSING] Tokenomics",
  "upcoming:security": "[MISSING] Security",
  "upcoming:social": "[MISSING] Social",
  "upcoming:market": "[MISSING] Market",
  "upcoming:insights": "[MISSING] Insights",

};

export default esAR;
