/**
 * Vietnamese localization strings
 */
const vi = {
  // Common strings
  "common": {
    "loading": "Đang tải...",
    "error": "Lỗi",
    "retry": "Th<PERSON> lại",
    "save": "<PERSON><PERSON><PERSON>",
    "cancel": "<PERSON>ủy",
    "close": "<PERSON>óng",
    "success": "Thành công",
    "viewMore": "Xem thêm",
    "back": "Quay lại",
    "next": "Tiếp theo",
    "search": "Tìm kiếm",
    "searchCoinsAndTokens": "Tìm kiếm tiền điện tử và dự án sắp tới",
    "searching": "<PERSON>ang tìm kiếm...",
    "noResults": "<PERSON>hông tìm thấy kết quả",
    "coins": "Tiền điện tử",
    "upcomingIdos": "IDO sắp tới",
  },

  // Nav related strings
  "nav": {
    "home": "Trang chủ",
    "portfolio": "<PERSON><PERSON> mục đầu tư",
    "explore": "<PERSON>h<PERSON><PERSON> phá",
    "news": "<PERSON> tức",
    "learn": "<PERSON>ọc hỏi",
    "profile": "<PERSON><PERSON> sơ",
    "settings": "Cài đặt",
    "logout": "Đăng xuất",
    "login": "Đăng nhập",
    "register": "Đăng ký",
    "trending": "Xu hướng",
    "favorites": "Yêu thích",
    "watchlist": "Danh sách theo dõi",
  },

  // System related strings
  "system": {
    "choose": "Chọn ngôn ngữ",
    "current": "Ngôn ngữ hiện tại",
    "searching": "Đang tìm kiếm...",
  },

  // Error related strings
  "error": {
    "criticalError": "Lỗi Nghiêm Trọng",
    "somethingWentWrong": "Đã xảy ra lỗi",
    "criticalErrorMessage": "Đã xảy ra lỗi nghiêm trọng. Vui lòng làm mới trang hoặc quay về trang chủ.",
    "returnToHome": "Quay Về Trang Chủ",
    "multipleErrorsDetected": "Phát Hiện Nhiều Lỗi",
    "unexpectedError": "Đã xảy ra lỗi không mong muốn",
    "refreshPage": "Làm Mới Trang",
    "goToHome": "Về Trang Chủ",
    "clearErrorLogs": "Xóa Nhật Ký Lỗi",
    "anErrorOccurred": "Đã xảy ra lỗi",
  },

  // Data related strings
  "data": {
    "loading": "Đang tải dữ liệu",
    "empty": "Không có dữ liệu",
    "error": "Lỗi tải dữ liệu",
  },

  // Auth related strings
  "auth": {
    "email": "Email",
    "emailPlaceholder": "Nhập email của bạn",
    "emailDescription": "Email của bạn sẽ không bao giờ được chia sẻ với bất kỳ ai khác",
    "password": "Mật khẩu",
    "passwordPlaceholder": "Nhập mật khẩu của bạn",
    "passwordCreate": "Tạo mật khẩu",
    "passwordConfirm": "Xác nhận mật khẩu",
    "passwordConfirmPlaceholder": "Nhập lại mật khẩu của bạn",
    "passwordShow": "Hiện mật khẩu",
    "passwordHide": "Ẩn mật khẩu",
    "passwordStrength": "Độ mạnh mật khẩu",
    "passwordStrengthWeak": "Yếu",
    "passwordStrengthGood": "Tốt",
    "passwordStrengthStrong": "Mạnh",
    "passwordResetMessage": "Tính năng đặt lại mật khẩu sẽ sớm có",
    "forgotPassword": "Quên mật khẩu?",
    "resetPassword": "Đặt lại mật khẩu",
    "signin": "Đăng nhập",
    "signinLoading": "Đang đăng nhập...",
    "signinSecurely": "Đăng nhập an toàn",
    "signup": "Đăng ký",
    "signout": "Đăng xuất",
    "accountCreated": "Tài khoản đã được tạo thành công",
    "passwordResetSent": "Email đặt lại mật khẩu đã được gửi",
    "invalidCredentials": "Email hoặc mật khẩu không hợp lệ",
    "continueWith": "Tiếp tục với",
    "username": "Tên người dùng",
    "usernamePlaceholder": "Chọn tên người dùng",
    "agree": "Tôi đồng ý với",
    "service": "Điều khoản dịch vụ",
    "and": "và",
    "privacy": "Chính sách quyền riêng tư",
    "email.placeholder": "Nhập email của bạn",
    "email.description": "Email của bạn sẽ không bao giờ được chia sẻ với bất kỳ ai khác",
    "password.placeholder": "Nhập mật khẩu của bạn",
    "password.create": "Tạo mật khẩu",
    "password.confirm": "Xác nhận mật khẩu",
    "password.confirm.placeholder": "Nhập lại mật khẩu của bạn",
    "password.show": "Hiện mật khẩu",
    "password.hide": "Ẩn mật khẩu",
    "password.strength": "Độ mạnh mật khẩu",
    "password.strength.weak": "Yếu",
    "password.strength.good": "Tốt",
    "password.strength.strong": "Mạnh",
    "password.reset.message": "Tính năng đặt lại mật khẩu sẽ sớm có",
    "signin.loading": "Đang đăng nhập...",
    "signin.securely": "Đăng nhập an toàn",
    "username.placeholder": "Chọn tên người dùng",
    "password.criteria.length": "[MISSING] At least 8 characters",
    "password.criteria.uppercase": "[MISSING] At least one uppercase letter",
    "password.criteria.lowercase": "[MISSING] At least one lowercase letter",
    "password.criteria.number": "[MISSING] At least one number",
    "password.criteria.special": "[MISSING] At least one special character",
    "captcha.protected": "[MISSING] This form is protected by reCAPTCHA",
    "terms.service": "Điều khoản dịch vụ",
    "terms.and": "và",
    "terms.privacy": "Chính sách quyền riêng tư",
  },

  // Login related strings
  "login": {
    "prompt": "Nhập thông tin đăng nhập của bạn",
  },

  // Success related strings
  "success": {
    "title": "Thành công",
    "description": "Đăng nhập thành công",
  },

  // Register related strings
  "register": {
    "title": "Tạo tài khoản",
    "create": "Tạo tài khoản",
    "creating": "Đang tạo tài khoản...",
    "haveAccount": "Đã có tài khoản?",
    "success": "Đăng ký thành công",
    "successDetail": "Tài khoản đã được tạo thành công",
    "successLogin": "Tài khoản đã tạo. Vui lòng đăng nhập.",
    "failed": "Đăng ký thất bại",
    "failedGeneric": "Đã xảy ra lỗi trong quá trình đăng ký. Vui lòng thử lại.",
    "generic": "Đã xảy ra lỗi. Vui lòng thử lại.",
    "success.detail": "[MISSING] Your account has been created successfully",
    "success.login": "[MISSING] Your account has been created, please login.",
    "failed.generic": "[MISSING] An error occurred during registration. Please try again.",
    "success.email_verify": "[MISSING] Registration successful. Please check your email to verify your account.",
  },

  // Form related strings
  "form": {
    "invalidFields": "Vui lòng điền đúng tất cả các trường bắt buộc",
  },

  // Validation related strings
  "validation": {
    "email": "Vui lòng nhập một địa chỉ email hợp lệ",
    "length": "Mật khẩu phải có ít nhất 6 ký tự",
  },

  // Coinlist related strings
  "coinlist": {
    "allCoins": "Tất cả các đồng tiền",
    "coinDetailDescription": "Nhấp vào bất kỳ đồng tiền nào để xem phân tích chi tiết",
    "searchCoins": "Tìm kiếm đồng tiền...",
    "ariaLabel": "Tìm kiếm đồng tiền",
  },

  // Filters related strings
  "filters": {
    "button": "Bộ lọc",
    "title": "Tùy chọn lọc",
    "description": "Tùy chỉnh chế độ xem của bạn với các tùy chọn lọc nâng cao",
    "sortBy": "Sắp xếp theo",
    "reset": "Đặt lại bộ lọc",
    "details": "[MISSING] Filter by sale type, launchpad, category, blockchain, or investors.",
    "action": "[MISSING] Try selecting a filter to see how it updates the list.",
  },

  // Columns related strings
  "columns": {
    "name": "Tên",
    "tokenomics": "Tokenomics",
    "security": "Bảo mật",
    "social": "Xã hội",
    "market": "Thị trường",
    "insights": "Phân tích",
    "totalScore": "Tổng điểm AI",
    "sevenDayChange": "Thay đổi 7 ngày",
    "price": "Giá",
  },

  // Highlights related strings
  "highlights": {
    "topGainers": "Tiền Điện Tử Tăng Nhiều Nhất",
    "newListings": "Niêm Yết Mới",
    "upcomingIDOs": "IDO Sắp Tới",
    "gemCoins": "Tiền Điện Tử Tiềm Năng",
    "topAirdrops": "Airdrop Hàng Đầu",
    "score": "Điểm",
  },

  // Coindetail related strings
  "coindetail": {
    "overview": "Tổng quan",
    "fundamentals": "Cơ bản",
    "technicals": "Kỹ thuật",
    "news": "Tin tức",
    "social": "Xã hội",
    "developers": "Nhà phát triển",
    "analysis": "Phân tích",
    "price": "Giá",
    "volume": "Khối lượng",
    "marketCap": "Vốn hóa thị trường",
    "circulatingSupply": "Cung lưu hành",
    "totalSupply": "Tổng cung",
    "maxSupply": "Cung tối đa",
    "allTimeHigh": "Cao nhất mọi thời đại",
    "allTimeLow": "Thấp nhất mọi thời đại",
    "pricePrediction": "Dự đoán giá",
    "addToWatchlist": "Thêm vào danh sách theo dõi",
    "removeFromWatchlist": "Xóa khỏi danh sách theo dõi",
  },

  // Portfolio related strings
  "portfolio": {
    "totalValue": "Tổng giá trị",
    "recentActivity": "Hoạt động gần đây",
    "performance": "Hiệu suất",
    "holdings": "Nắm giữ",
    "addAsset": "Thêm tài sản",
    "editAsset": "Chỉnh sửa tài sản",
    "noAssets": "Không có tài sản trong danh mục đầu tư",
    "24hChange": "Thay đổi 24h",
    "allocation": "Phân bổ",
    "profit": "Lợi nhuận/Thua lỗ",
  },

  // Settings related strings
  "settings": {
    "appearance": "Giao diện",
    "language": "Ngôn ngữ",
    "notifications": "Thông báo",
    "security": "Bảo mật",
    "preferences": "Tùy chọn",
    "theme": "Chủ đề",
    "lightMode": "Chế độ sáng",
    "darkMode": "Chế độ tối",
    "systemDefault": "Mặc định hệ thống",
  },

  // Sidebar related strings
  "sidebar": {
    "lock": "Khóa",
    "unlock": "Mở khóa",
    "collapse": "Thu gọn",
    "cryptoRating": "Đánh Giá Tiền Điện Tử",
    "idoRating": "Đánh Giá IDO",
    "compareCoins": "So Sánh Tiền Điện Tử",
    "recentListings": "Niêm Yết Gần Đây",
    "topMovers": "Biến Động Mạnh Nhất",
    "watchlist": "Danh Sách Theo Dõi Của Tôi",
    "aiPortfolio": "Hướng Dẫn Danh Mục AI",
    "portfolioAudit": "Kiểm Tra Danh Mục",
    "launchpads": "Nền Tảng Phát Hành",
    "airdrops": "Trung Tâm Airdrop",
    "aiAssistant": "Trợ Lý AI",
    "gemScout": "Tìm Kiếm Tiền Tiềm Năng",
    "soon": "SẮP RA MẮT",
  },

  // Format related strings
  "format": {
    "thousand": "N",
    "million": "Tr",
    "billion": "T",
    "trillion": "NT",
  },

  // Coin related strings
  "coin": {
    "age": "Tuổi:",
  },

  // Score related strings
  "score": {
    "excellent": "Xuất sắc",
    "positive": "Tích cực",
    "average": "Trung bình",
    "weak": "Yếu",
    "critical": "Nguy hiểm",
    "title": "[MISSING] CoinScout Score",
    "description": "[MISSING] Our proprietary score evaluates the overall quality and potential of the project.",
    "details": "[MISSING] Based on tokenomics, security, social media activity, and more.",
  },

  // Footer related strings
  "footer": {
    "description": "Nền tảng phân tích tiền điện tử và quản lý danh mục đầu tư tiên tiến được hỗ trợ bởi trí tuệ nhân tạo.",
    "bankGradeSecurity": "Bảo Mật Cấp Ngân Hàng",
    "allRightsReserved": "Bảo lưu mọi quyền",
    "product": "Sản phẩm",
    "learn": "Học hỏi",
    "community": "Cộng đồng",
    "legal": "Pháp lý",
  },

  // Links related strings
  "links": {
    "privacyPolicy": "Chính Sách Bảo Mật",
    "termsOfService": "Điều Khoản Dịch Vụ",
    "cookiePolicy": "Chính Sách Cookie",
    "disclaimer": "Tuyên Bố Miễn Trừ",
    "advertisingPolicy": "Chính Sách Quảng Cáo",
    "careers": "Tuyển Dụng",
    "soon": "sắp ra mắt",
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "Tìm Kiếm Tiền Điện Tử và Dự Án Sắp Tới",
    "goToHomepage": "Về trang chủ",
    "coinScoutAlt": "CoinScout",
    "pricing": "Bảng Giá",
    "goToApp": "Vào Ứng Dụng",
    "login": "Đăng Nhập",
    "signUp": "Đăng Ký",
    "profile": "Hồ Sơ",
    "membershipManagement": "Quản Lý Thành Viên",
    "feedback": "Phản Hồi",
    "adminDashboard": "Bảng Điều Khiển Quản Trị",
    "logout": "Đăng Xuất",
    "premium": "Cao Cấp",
    "pro": "Chuyên Nghiệp",
    "free": "Miễn Phí",
  },

  // Watchlist related strings
  "watchlist": {
    "title": "Danh sách theo dõi của bạn trống",
    "description": "Bạn chưa có danh sách theo dõi nào. Tạo danh sách theo dõi để theo dõi các tiền điện tử của bạn.",
    "createWatchlist": "Tạo Danh Sách Theo Dõi",
    "addCoins": "Thêm Tiền Điện Tử",
  },

  // Upcoming related strings
  "upcoming": {
    "title": "Bộ Lọc",
    "subtitle": "Khám phá và đánh giá token mới trước khi ra mắt",
    "saleType": "Loại Bán",
    "allTypes": "Tất Cả Loại",
    "launchpad": "Nền Tảng Phát Hành",
    "allLaunchpads": "Tất Cả Nền Tảng",
    "category": "Danh Mục",
    "allCategories": "Tất Cả Danh Mục",
    "blockchain": "Blockchain",
    "allBlockchains": "Tất Cả Blockchain",
    "investor": "Nhà Đầu Tư",
    "allInvestors": "Tất Cả Nhà Đầu Tư",
    "projectScore": "Điểm Dự Án",
    "listingDate": "Ngày Niêm Yết",
    "reset": "Đặt Lại Bộ Lọc",
    "apply": "Áp Dụng Bộ Lọc",
    "searchCategories": "Tìm kiếm danh mục...",
    "searchChains": "Tìm kiếm blockchain...",
    "selectDateRange": "Chọn khoảng thời gian",
    "last24Hours": "24 giờ qua",
    "last7Days": "7 ngày qua",
    "last14Days": "14 ngày qua",
    "last30Days": "30 ngày qua",
    "last90Days": "90 ngày qua",
  },

  // Table related strings
  "table": {
    "name": "Tên",
    "launchDate": "Ngày Ra Mắt",
    "initialCap": "Vốn Hóa Ban Đầu",
    "totalRaised": "Tổng Gây Quỹ",
    "score": "Điểm",
    "actions": "Hành Động",
  },

  // Methodology related strings
  "methodology": {
    "whatAreWeScoring": "Chúng Tôi Đánh Giá Gì",
    "whyIsThisImportant": "Tại Sao Điều Này Quan Trọng",
    "scoringLevels": "Mức Độ Đánh Giá",
  },

  // Profile related strings
  "profile": {
    "yourProfile": "Hồ Sơ Của Bạn",
    "howOthersSeeYou": "Đây là cách những người khác sẽ thấy bạn trên nền tảng.",
    "verified": "Đã xác minh",
    "unverified": "Chưa xác minh",
    "memberSince": "Thành viên từ",
    "unknown": "Không rõ",
    "status": "Trạng thái",
    "active": "Hoạt động",
    "plan": "Gói",
    "unknownPlan": "Gói Không Rõ",
    "planStatus": "Trạng thái Gói",
    "started": "Bắt đầu",
    "expires": "Hết hạn",
    "lastLogin": "Đăng nhập cuối",
    "never": "Chưa bao giờ",
    "profileCompletion": "[MISSING] Profile completion",
    "improveTip": "[MISSING] Add a bio and profile picture to improve your profile.",
    "signOut": "[MISSING] Sign Out",
    "areYouSure": "[MISSING] Are you sure?",
    "signOutConfirmation": "[MISSING] You'll be signed out from your account. You can sign back in at any time.",
    "cancel": "[MISSING] Cancel",
    "personalInformation": "[MISSING] Personal Information",
    "updateDescription": "[MISSING] Update your personal information and how your profile appears.",
    "username": "[MISSING] Username",
    "email": "[MISSING] Email",
    "emailPlaceholder": "[MISSING] <EMAIL>",
    "emailSent": "[MISSING] Email sent",
    "verificationEmailSent": "[MISSING] Verification email sent successfully. Please check your inbox.",
    "error": "[MISSING] Error",
    "emailSendError": "[MISSING] An error occurred while sending email. Please try again.",
    "resendVerification": "[MISSING] Resend verification email",
    "membershipTier": "[MISSING] Membership Tier",
    "pro": "[MISSING] Pro",
    "premium": "[MISSING] Premium",
    "free": "[MISSING] Free",
    "manageSubscription": "[MISSING] Manage your subscription in the",
    "membership": "[MISSING] Membership",
    "tab": "[MISSING] tab",
    "downloadData": "[MISSING] Download Your Data",
    "deleteAccount": "[MISSING] Delete Account",
    "absolutelySure": "[MISSING] Are you absolutely sure?",
    "deleteWarning": "[MISSING] This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    "title": "[MISSING] Profile",
    "description": "[MISSING] Manage your account settings and preferences.",
    "saveChanges": "[MISSING] Save Changes",
    "profile": "[MISSING] Profile",
    "notifications": "[MISSING] Notifications",
  },

  // Logs related strings
  "logs": {
    "usingDirectAPIData": "[MISSING] 🔄 Using direct API data, data length:",
    "imageLoadError": "[MISSING] 🖼️ Error loading image:",
    "usingIdoWatchlists": "[MISSING] Using IDO watchlists for upcoming projects:",
    "rawAPIData": "[MISSING] 🟡 Raw data from API:",
    "apiDataLength": "[MISSING] 🟡 API data length:",
    "filteredData": "[MISSING] 🔍 Filtered data:",
    "filteredDataLength": "[MISSING] 🔍 Filtered data length:",
    "imageUrlChecks": "[MISSING] 🖼️ Image URL checks:",
    "allProjectsFromAPI": "[MISSING] 📌 All projects from API:",
    "usingDirectAPI": "[MISSING] 🟢 Using direct API data",
    "rawAPIDataFirst5": "[MISSING] 🔍 Raw API data (first 5 items):",
    "emptyAPIResult": "[MISSING] ⚠️ API returned empty result, using empty array",
    "errorUsingEmptyArray": "[MISSING] ❌ Using empty array due to error",
    "totalAIScoreClicked": "[MISSING] Total AI Score clicked:",
    "filtersChanged": "[MISSING] Filters changed:",
    "upcomingIDOFirstUseEffect": "[MISSING] 🔄 UpcomingIDO - First useEffect triggered",
    "fetchInitialDataStarted": "[MISSING] 🔄 fetchInitialData started",
    "fetchInitialDataCompleted": "[MISSING] 🔄 fetchInitialData completed",
    "filterRunning": "[MISSING] 🔍 Filter running, coins length:",
    "paginatedData": "[MISSING] 📄 Paginated data:",
    "first2Records": "[MISSING] 📄 First 2 records:",
    "paginatedDataImageFields": "[MISSING] 📄 Image fields in paginated data:",
    "firstRecordDetails": "[MISSING] 🧩 First record details:",
    "firstRecordSocialScore": "[MISSING] 📢 First record social score value:",
    "clickedProjectInfo": "[MISSING] Clicked project info:",
    "redirectingWithDirectID": "[MISSING] Redirecting with direct ID:",
  },

  // Homepage related strings
  "homepage": {
    "badge": "[MISSING] AI-Powered Crypto Analysis",
    "title": "[MISSING] Make smarter crypto investments",
    "titleHighlight": "[MISSING] with AI insights",
    "description": "[MISSING] CoinScout analyzes over 3,000 cryptocurrencies across 40+ metrics to help you identify the best investment opportunities before others see them.",
    "ctaButton": "[MISSING] Start Free Analysis",
    "cryptocurrencies": "[MISSING] Cryptocurrencies",
    "analysisMetrics": "[MISSING] Analysis Metrics",
    "moreAccuracy": "[MISSING] More Accuracy",
  },

  // Features related strings
  "features": {
    "title": "[MISSING] AI-Enhanced Crypto Rating",
    "subtitle": "[MISSING] Our comprehensive suite of AI-powered tools helps you navigate the crypto market with confidence",
    "description": "[MISSING] Proprietary algorithms assess 50+ metrics across tokenomics, security, social engagement, and market factors",
  },

  // IdoRating related strings
  "idoRating": {
    "title": "[MISSING] IDO Rating",
    "description": "[MISSING] Discover and evaluate promising token offerings before their launch with detailed project analysis and scoring",
  },

  // CompareCoins related strings
  "compareCoins": {
    "title": "[MISSING] Compare Coins",
    "description": "[MISSING] Side-by-side analysis of multiple cryptocurrencies to identify the best investment opportunities based on your criteria",
  },

  // PortfolioGenerator related strings
  "portfolioGenerator": {
    "title": "[MISSING] Custom Portfolio Generator",
    "description": "[MISSING] AI-powered recommendations to build a diversified portfolio based on your risk tolerance and investment goals",
  },

  // PortfolioAnalysis related strings
  "portfolioAnalysis": {
    "title": "[MISSING] AI Portfolio Analysis",
    "description": "[MISSING] Gain AI-powered insights into your existing portfolio with automated risk analysis, diversification score, and optimization tips",
  },

  // Launchpads related strings
  "launchpads": {
    "title": "[MISSING] Top Launchpads",
    "description": "[MISSING] Discover the best platforms for new token offerings with performance metrics, success rates, and investor returns",
  },

  // AiAssistant related strings
  "aiAssistant": {
    "title": "[MISSING] AI Assistant",
    "description": "[MISSING] Get instant answers to your crypto questions with our advanced AI assistant that provides personalized guidance and insights",
  },

  // AirdropScore related strings
  "airdropScore": {
    "title": "[MISSING] Airdrop Score",
    "description": "[MISSING] Find legitimate airdrops with the highest potential value and lowest participation barriers through our verification system",
  },

  // GemScout related strings
  "gemScout": {
    "title": "[MISSING] Gem Scout",
    "description": "[MISSING] Discover early-stage projects with exceptional growth potential before they hit mainstream markets and major exchanges",
  },

  // Badges related strings
  "badges": {
    "betaTestingLive": "[MISSING] Beta Testing Live",
    "betaTestingSoon": "[MISSING] Beta Testing Soon",
    "comingSoon": "[MISSING] Coming Soon",
  },

  // Intelligence related strings
  "intelligence": {
    "title": "[MISSING] AI-Driven Intelligence for Smarter Crypto Decisions",
  },

  // Faq related strings
  "faq": {
    "title": "[MISSING] Frequently Asked Questions",
    "question": "[MISSING] What makes CoinScout's analysis different from other platforms?",
    "answer": "[MISSING] CoinScout leverages AI-driven analytics to assess over 50 key factors across tokenomics, security, social sentiment, market performance, and project fundamentals. Unlike traditional platforms that primarily provide raw data, we transform complex metrics into clear, actionable insights—making it easier for investors at all levels to understand and make informed decisions.",
  },

  // SaleType related strings
  "saleType": {
    "IDO": "[MISSING] Initial DEX Offering - Token sale conducted on a decentralized exchange (DEX)",
    "IEO": "[MISSING] Initial Exchange Offering - Token sale hosted on a centralized cryptocurrency exchange",
    "ICO": "[MISSING] Initial Coin Offering - First fundraising stage where new projects offer tokens to early investors",
    "SHO": "[MISSING] Strong Holder Offering - Token sale giving priority to long-term token holders",
    "Seed": "[MISSING] Seed Round - Early private funding round before public sale",
    "IGO": "[MISSING] Initial Game Offering - Fundraising focused on blockchain gaming projects",
    "ISO": "[MISSING] Initial Stake Offering - Token distribution through staking mechanism",
  },

  // ListingDate related strings
  "listingDate": {
    "24hours": "[MISSING] Last 24 Hours",
    "7days": "[MISSING] Last 7 Days",
    "14days": "[MISSING] Last 14 Days",
    "30days": "[MISSING] Last 30 Days",
    "90days": "[MISSING] Last 90 Days",
  },

  // UpcomingTour related strings
  "upcomingTour": {
    "title": "[MISSING] Welcome to Upcoming IDOs",
    "description": "[MISSING] Would you like a quick tour of the Upcoming IDOs features?",
    "info": "[MISSING] Learn how to filter upcoming token sales, understand the project details, and evaluate projects before they launch.",
    "dontShowAgain": "[MISSING] Don't show this again",
    "skipButton": "[MISSING] Skip for now",
    "startButton": "[MISSING] Start Tour",
  },

  // Steps related strings
  "steps": {
    "title": "[MISSING] Upcoming IDOs Overview",
    "description": "[MISSING] Welcome to the Upcoming IDOs page, where you can discover and evaluate new token launches before they go live.",
    "details": "[MISSING] Browse, filter, and analyze upcoming token sales across different blockchains and launchpads.",
  },

  // Search related strings
  "search": {
    "title": "[MISSING] Search & Find",
    "description": "[MISSING] Quickly search for any upcoming token sale by name or symbol.",
    "details": "[MISSING] Results update in real-time as you type.",
  },

  // ProjectInfo related strings
  "projectInfo": {
    "title": "[MISSING] Project Information",
    "description": "[MISSING] Each row contains detailed information about an upcoming token sale.",
    "details": "[MISSING] Click on any row to see detailed analysis for that project.",
    "action": "[MISSING] Try hovering over a project to see more information.",
  },

  // InitialCap related strings
  "initialCap": {
    "title": "[MISSING] Initial Market Cap",
    "description": "[MISSING] The expected initial market capitalization of the token upon listing.",
    "details": "[MISSING] Calculated as token price × circulating supply at TGE.",
  },

  // LaunchDate related strings
  "launchDate": {
    "title": "[MISSING] Launch Date",
    "description": "[MISSING] The scheduled date when the token will be available for trading.",
    "details": "[MISSING] Stay updated with upcoming launches to prepare your investment strategy.",
  },

  // Pagination related strings
  "pagination": {
    "title": "[MISSING] Page Navigation",
    "description": "[MISSING] Navigate through the list of upcoming token sales.",
    "details": "[MISSING] Adjust the number of projects displayed per page.",
  },

  // Completion related strings
  "completion": {
    "title": "[MISSING] You're All Set!",
    "description": "[MISSING] You've completed the tour of the Upcoming IDOs page.",
    "details": "[MISSING] Start exploring and analyzing upcoming token sales to find your next investment opportunity.",
    "help": "[MISSING] You can restart this tour anytime by clicking on the Guided Tour button.",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "Đang tải...",
  "common:error": "Lỗi",
  "common:retry": "Thử lại",
  "common:save": "Lưu",
  "common:cancel": "Hủy",
  "common:close": "Đóng",
  "common:success": "Thành công",
  "common:viewMore": "Xem thêm",
  "common:back": "Quay lại",
  "common:next": "Tiếp theo",
  "common:search": "Tìm kiếm",
  "common:searchCoinsAndTokens": "Tìm kiếm tiền điện tử và dự án sắp tới",
  "common:searching": "Đang tìm kiếm...",
  "common:noResults": "Không tìm thấy kết quả",
  "common:coins": "Tiền điện tử",
  "common:upcomingIdos": "IDO sắp tới",

  // Flat format for nav (for backward compatibility)
  "nav:home": "Trang chủ",
  "nav:portfolio": "Danh mục đầu tư",
  "nav:explore": "Khám phá",
  "nav:news": "Tin tức",
  "nav:learn": "Học hỏi",
  "nav:profile": "Hồ sơ",
  "nav:settings": "Cài đặt",
  "nav:logout": "Đăng xuất",
  "nav:login": "Đăng nhập",
  "nav:register": "Đăng ký",
  "nav:trending": "Xu hướng",
  "nav:favorites": "Yêu thích",
  "nav:watchlist": "Danh sách theo dõi",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "Đang tìm kiếm...",
  "system:error.translation": "Lỗi dịch",
  "system:data.loading": "Đang tải dữ liệu",
  "system:data.empty": "Không có dữ liệu",
  "system:data.error": "Lỗi tải dữ liệu",

  // Flat format for auth (for backward compatibility)
  "auth:email": "Email",
  "auth:email.placeholder": "Nhập email của bạn",
  "auth:email.description": "Email của bạn sẽ không bao giờ được chia sẻ với bất kỳ ai khác",
  "auth:password": "Mật khẩu",
  "auth:password.placeholder": "Nhập mật khẩu của bạn",
  "auth:password.create": "Tạo mật khẩu",
  "auth:password.confirm": "Xác nhận mật khẩu",
  "auth:password.confirm.placeholder": "Nhập lại mật khẩu của bạn",
  "auth:password.show": "Hiện mật khẩu",
  "auth:password.hide": "Ẩn mật khẩu",
  "auth:password.strength": "Độ mạnh mật khẩu",
  "auth:password.strength.weak": "Yếu",
  "auth:password.strength.good": "Tốt",
  "auth:password.strength.strong": "Mạnh",
  "auth:password.reset.message": "Tính năng đặt lại mật khẩu sẽ sớm có",
  "auth:forgotPassword": "Quên mật khẩu?",
  "auth:resetPassword": "Đặt lại mật khẩu",
  "auth:signin": "Đăng nhập",
  "auth:signin.loading": "Đang đăng nhập...",
  "auth:signin.securely": "Đăng nhập an toàn",
  "auth:signup": "Đăng ký",
  "auth:signout": "Đăng xuất",
  "auth:accountCreated": "Tài khoản đã được tạo thành công",
  "auth:passwordResetSent": "Email đặt lại mật khẩu đã được gửi",
  "auth:invalidCredentials": "Email hoặc mật khẩu không hợp lệ",
  "auth:continueWith": "Tiếp tục với",
  "auth:username": "Tên người dùng",
  "auth:username.placeholder": "Chọn tên người dùng",
  "auth:terms.agree": "Tôi đồng ý với",
  "auth:terms.service": "Điều khoản dịch vụ",
  "auth:terms.and": "và",
  "auth:terms.privacy": "Chính sách quyền riêng tư",
  "auth:termsAccept": "Bằng cách tiếp tục, bạn đồng ý với Điều khoản dịch vụ và Chính sách quyền riêng tư của chúng tôi",
  "auth:remember": "Giữ tôi đăng nhập trong 30 ngày",
  "auth:welcome.back": "Chào mừng trở lại",
  "auth:login.credential.prompt": "Nhập thông tin đăng nhập của bạn",
  "auth:login.success.title": "Thành công",
  "auth:login.success.description": "Đăng nhập thành công",
  "auth:login.error.title": "Lỗi đăng nhập",
  "auth:login.error.unknown": "Đã xảy ra lỗi. Vui lòng thử lại.",
  "auth:register.title": "Tạo tài khoản",
  "auth:register.create": "Tạo tài khoản",
  "auth:register.creating": "Đang tạo tài khoản...",
  "auth:register.haveAccount": "Đã có tài khoản?",
  "auth:register.success": "Đăng ký thành công",
  "auth:register.success.detail": "Tài khoản đã được tạo thành công",
  "auth:register.success.login": "Tài khoản đã tạo. Vui lòng đăng nhập.",
  "auth:register.success.email_verify": "Đăng ký thành công. Vui lòng kiểm tra email của bạn để kích hoạt tài khoản.",
  "auth:register.failed": "Đăng ký thất bại",
  "auth:register.failed.generic": "Đã xảy ra lỗi trong quá trình đăng ký. Vui lòng thử lại.",
  "auth:register.error.generic": "Đã xảy ra lỗi. Vui lòng thử lại.",
  "auth:register.description": "Tạo tài khoản để bắt đầu",
  "auth:form.invalidFields": "Vui lòng điền đúng tất cả các trường bắt buộc",
  "auth:validation.email": "Vui lòng nhập một địa chỉ email hợp lệ",
  "auth:validation.password.length": "Mật khẩu phải có ít nhất 6 ký tự",
  "auth:authentication.required": "Yêu Cầu Xác Thực",
  "auth:authentication.required.description": "Vui lòng đăng nhập để truy cập tính năng này",
  "auth:authentication.signin": "Đăng nhập",
  "auth:authentication.continueWithEmail": "Tiếp tục với email",
  "auth:authentication.goBack": "Quay lại",
  "auth:authentication.signInPrompt": "Đăng nhập để truy cập các tính năng được cá nhân hóa, lưu tùy chọn của bạn và mở khóa tất cả khả năng của CoinScout.",
  "auth:authentication.comparisonPrompt": "Đăng nhập để truy cập các tính năng được cá nhân hóa, lưu tùy chọn của bạn và mở khóa tất cả khả năng của CoinScout để so sánh.",
  "auth:backToHome": "Về Trang Chủ",
  "auth:validation.email.required": "[MISSING] Email is required",
  "auth:validation.email.invalid": "[MISSING] Please enter a valid email address",
  "auth:validation.email.complete": "[MISSING] Please enter a complete email address",
  "auth:validation.password.required": "[MISSING] Password is required",
  "auth:validation.password.uppercase": "[MISSING] Password must contain at least one uppercase letter",
  "auth:validation.password.lowercase": "[MISSING] Password must contain at least one lowercase letter",
  "auth:validation.password.number": "[MISSING] Password must contain at least one number",
  "auth:validation.password.special": "[MISSING] Password must contain at least one special character",
  "auth:login.failed": "[MISSING] Login Failed",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "Tất cả các đồng tiền",
  "coinlist:coinDetailDescription": "Nhấp vào bất kỳ đồng tiền nào để xem phân tích chi tiết",
  "coinlist:searchCoins": "Tìm kiếm đồng tiền...",
  "coinlist:searchBox.ariaLabel": "Tìm kiếm đồng tiền",
  "coinlist:aiPoweredTitle": "Đánh giá nền tảng tiền điện tử được hỗ trợ bởi AI và dữ liệu",
  "coinlist:comprehensiveAnalysis": "Phân tích toàn diện và chấm điểm tiền điện tử qua",
  "coinlist:multipleMetrics": "nhiều chỉ số",
  "coinlist:highlights": "Nổi bật",
  "coinlist:viewAll": "Xem tất cả",
  "coinlist:currentPrice": "Giá hiện tại",
  "coinlist:marketCap": "Vốn hóa thị trường",
  "coinlist:rank": "Xếp hạng",
  "coinlist:filters.button": "Bộ lọc",
  "coinlist:filters.title": "Tùy chọn lọc",
  "coinlist:filters.description": "Tùy chỉnh chế độ xem của bạn với các tùy chọn lọc nâng cao",
  "coinlist:filters.sortBy": "Sắp xếp theo",
  "coinlist:filters.reset": "Đặt lại bộ lọc",
  "coinlist:columns.name": "Tên",
  "coinlist:columns.tokenomics": "Tokenomics",
  "coinlist:columns.security": "Bảo mật",
  "coinlist:columns.social": "Xã hội",
  "coinlist:columns.market": "Thị trường",
  "coinlist:columns.insights": "Phân tích",
  "coinlist:columns.totalScore": "Tổng điểm AI",
  "coinlist:columns.sevenDayChange": "Thay đổi 7 ngày",
  "coinlist:columns.price": "Giá",
  "coinlist:tooltips.name.title": "[MISSING] Coin Name & Symbol",
  "coinlist:tooltips.name.description": "[MISSING] The official name and symbol of the cryptocurrency as listed on exchanges.",
  "coinlist:tooltips.tokenomics.title": "[MISSING] Tokenomics Analysis",
  "coinlist:tooltips.tokenomics.description": "[MISSING] Measures token supply mechanisms, inflation risks, and vesting structures.",
  "coinlist:tooltips.security.title": "[MISSING] Security Analysis",
  "coinlist:tooltips.security.description": "[MISSING] Security audit results and risk assessment metrics.",
  "coinlist:tooltips.social.title": "[MISSING] Social Analysis",
  "coinlist:tooltips.social.description": "[MISSING] Social media presence, community engagement and sentiment analysis.",
  "coinlist:tooltips.market.title": "[MISSING] Market Performance Analysis",
  "coinlist:tooltips.market.description": "[MISSING] Measures trading volume, liquidity and overall market health.",
  "coinlist:tooltips.insights.title": "[MISSING] AI Insights Analysis",
  "coinlist:tooltips.insights.description": "[MISSING] AI-powered project insights, sentiment analysis and forecasting metrics.",
  "coinlist:tooltips.totalScore.title": "[MISSING] Total Score",
  "coinlist:tooltips.totalScore.description": "[MISSING] The overall rating calculated from all scoring metrics.",
  "coinlist:tooltips.sevenDayChange.title": "[MISSING] 7 Day Change",
  "coinlist:tooltips.sevenDayChange.description": "[MISSING] Percentage price change over the last 7 days.",
  "coinlist:search.inProgress": "[MISSING] Search in progress...",
  "coinlist:search.resultsUpdate": "[MISSING] Results will update automatically",
  "coinlist:search.clearSearch": "[MISSING] Clear search",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "Tiền Điện Tử Tăng Nhiều Nhất",
  "highlights:newListings": "Niêm Yết Mới",
  "highlights:upcomingIDOs": "IDO Sắp Tới",
  "highlights:gemCoins": "Tiền Điện Tử Tiềm Năng",
  "highlights:topAirdrops": "Airdrop Hàng Đầu",
  "highlights:score": "Điểm",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "Tổng quan",
  "coindetail:fundamentals": "Cơ bản",
  "coindetail:technicals": "Kỹ thuật",
  "coindetail:news": "Tin tức",
  "coindetail:social": "Xã hội",
  "coindetail:developers": "Nhà phát triển",
  "coindetail:analysis": "Phân tích",
  "coindetail:price": "Giá",
  "coindetail:volume": "Khối lượng",
  "coindetail:marketCap": "Vốn hóa thị trường",
  "coindetail:circulatingSupply": "Cung lưu hành",
  "coindetail:totalSupply": "Tổng cung",
  "coindetail:maxSupply": "Cung tối đa",
  "coindetail:allTimeHigh": "Cao nhất mọi thời đại",
  "coindetail:allTimeLow": "Thấp nhất mọi thời đại",
  "coindetail:pricePrediction": "Dự đoán giá",
  "coindetail:addToWatchlist": "Thêm vào danh sách theo dõi",
  "coindetail:removeFromWatchlist": "Xóa khỏi danh sách theo dõi",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "Tổng giá trị",
  "portfolio:recentActivity": "Hoạt động gần đây",
  "portfolio:performance": "Hiệu suất",
  "portfolio:holdings": "Nắm giữ",
  "portfolio:addAsset": "Thêm tài sản",
  "portfolio:editAsset": "Chỉnh sửa tài sản",
  "portfolio:noAssets": "Không có tài sản trong danh mục đầu tư",
  "portfolio:24hChange": "Thay đổi 24h",
  "portfolio:allocation": "Phân bổ",
  "portfolio:profit": "Lợi nhuận/Thua lỗ",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "Giao diện",
  "settings:language": "Ngôn ngữ",
  "settings:notifications": "Thông báo",
  "settings:security": "Bảo mật",
  "settings:preferences": "Tùy chọn",
  "settings:theme": "Chủ đề",
  "settings:lightMode": "Chế độ sáng",
  "settings:darkMode": "Chế độ tối",
  "settings:systemDefault": "Mặc định hệ thống",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "Khóa",
  "sidebar:unlock": "Mở khóa",
  "sidebar:collapse": "Thu gọn",
  "sidebar:cryptoRating": "Đánh Giá Tiền Điện Tử",
  "sidebar:idoRating": "Đánh Giá IDO",
  "sidebar:compareCoins": "So Sánh Tiền Điện Tử",
  "sidebar:recentListings": "Niêm Yết Gần Đây",
  "sidebar:topMovers": "Biến Động Mạnh Nhất",
  "sidebar:watchlist": "Danh Sách Theo Dõi Của Tôi",
  "sidebar:aiPortfolio": "Hướng Dẫn Danh Mục AI",
  "sidebar:portfolioAudit": "Kiểm Tra Danh Mục",
  "sidebar:launchpads": "Nền Tảng Phát Hành",
  "sidebar:airdrops": "Trung Tâm Airdrop",
  "sidebar:aiAssistant": "Trợ Lý AI",
  "sidebar:gemScout": "Tìm Kiếm Tiền Tiềm Năng",
  "sidebar:soon": "SẮP RA MẮT",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "Đã xảy ra lỗi",
  "error:tryAgain": "Vui lòng thử lại",
  "error:networkIssue": "Vấn đề kết nối mạng",
  "error:dataFetch": "Không thể lấy dữ liệu",
  "error:timeOut": "Hết thời gian yêu cầu",
  "error:invalidInput": "Đầu vào không hợp lệ",
  "error:pageNotFound": "Không tìm thấy trang",

  // Flat format for format (for backward compatibility)
  "format:thousand": "N",
  "format:million": "Tr",
  "format:billion": "T",
  "format:trillion": "NT",

  // Flat format for coin (for backward compatibility)
  "coin:age": "Tuổi:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "Xuất sắc",
  "score:positive": "Tích cực",
  "score:average": "Trung bình",
  "score:weak": "Yếu",
  "score:critical": "Nguy hiểm",

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "Bộ Lọc",
  "upcoming:subtitle": "Khám phá và đánh giá token mới trước khi ra mắt",
  "upcoming:filters.title": "[MISSING] Filters",
  "upcoming:filters.saleType": "[MISSING] Sale Type",
  "upcoming:filters.allTypes": "[MISSING] All Types",
  "upcoming:filters.launchpad": "[MISSING] Launchpad",
  "upcoming:filters.allLaunchpads": "[MISSING] All Launchpads",
  "upcoming:filters.category": "[MISSING] Category",
  "upcoming:filters.allCategories": "[MISSING] All Categories",
  "upcoming:filters.blockchain": "[MISSING] Blockchain",
  "upcoming:filters.allBlockchains": "[MISSING] All Blockchains",
  "upcoming:filters.investor": "[MISSING] Investor",
  "upcoming:filters.allInvestors": "[MISSING] All Investors",
  "upcoming:filters.projectScore": "[MISSING] Project Score",
  "upcoming:filters.listingDate": "[MISSING] Listing Date",
  "upcoming:filters.reset": "[MISSING] Reset Filters",
  "upcoming:filters.apply": "[MISSING] Apply Filters",
  "upcoming:table.name": "[MISSING] Name",
  "upcoming:table.launchDate": "[MISSING] Launch Date",
  "upcoming:table.initialCap": "[MISSING] Initial Cap",
  "upcoming:table.totalRaised": "[MISSING] Total Raised",
  "upcoming:table.score": "[MISSING] Score",
  "upcoming:table.actions": "[MISSING] Actions",
  "upcoming:search": "[MISSING] Search upcoming token sales...",
  "upcoming:noResults": "[MISSING] No upcoming token sales found",
  "upcoming:loading": "[MISSING] Loading upcoming token sales...",
  "upcoming:error": "[MISSING] Error loading upcoming token sales",
  "upcoming:retryButton": "[MISSING] Retry",
  "upcoming:tba": "[MISSING] TBA",
  "upcoming:rank": "[MISSING] Rank #{number}",
  "upcoming:saleType": "Loại Bán",
  "upcoming:totalAiScore": "[MISSING] Total AI Score",
  "upcoming:points": "[MISSING] Points",
  "upcoming:tokenomics": "[MISSING] Tokenomics",
  "upcoming:security": "[MISSING] Security",
  "upcoming:social": "[MISSING] Social",
  "upcoming:market": "[MISSING] Market",
  "upcoming:insights": "[MISSING] Insights",

};

export default vi;