/**
 * Chinese localization strings
 */
const zh = {
  // Common strings
  "common": {
    "loading": "加载中...",
    "error": "错误",
    "retry": "重试",
    "save": "保存",
    "cancel": "取消",
    "close": "关闭",
    "success": "成功",
    "viewMore": "查看更多",
    "back": "返回",
    "next": "下一步",
    "search": "搜索",
    "searchCoinsAndTokens": "搜索加密货币和即将推出的项目",
    "searching": "搜索中...",
    "noResults": "未找到结果",
    "coins": "加密货币",
    "upcomingIdos": "即将到来的IDO",
  },

  // Nav related strings
  "nav": {
    "home": "首页",
    "portfolio": "投资组合",
    "explore": "探索",
    "news": "新闻",
    "learn": "学习",
    "profile": "个人资料",
    "settings": "设置",
    "logout": "退出",
    "login": "登录",
    "register": "注册",
    "trending": "热门",
    "favorites": "收藏",
    "watchlist": "关注列表",
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "搜索币种和即将推出的项目",
    "goToHomepage": "前往首页",
    "coinScoutAlt": "CoinScout",
    "pricing": "定价",
    "goToApp": "前往应用",
    "login": "登录",
    "signUp": "注册",
    "profile": "个人资料",
    "membershipManagement": "会员管理",
    "feedback": "反馈",
    "adminDashboard": "管理面板",
    "logout": "退出",
    "premium": "高级版",
    "pro": "专业版",
    "free": "免费版",
  },

  // Footer related strings
  "footer": {
    "description": "由人工智能驱动的先进加密货币分析和投资组合管理平台。",
    "bankGradeSecurity": "银行级安全",
    "allRightsReserved": "版权所有",
    "product": "产品",
    "learn": "学习",
    "community": "社区",
    "legal": "法律",
  },

  // Links related strings
  "links": {
    "privacyPolicy": "隐私政策",
    "termsOfService": "服务条款",
    "cookiePolicy": "Cookie政策",
    "disclaimer": "免责声明",
    "advertisingPolicy": "广告政策",
    "careers": "招聘",
    "soon": "即将推出",
  },

  // System related strings
  "system": {
    "title": "语言设置",
    "label": "选择语言",
    "search": "搜索语言...",
    "available": "可用语言",
  },

  // Homepage related strings
  "homepage": {
    "badge": "AI驱动的加密货币分析",
    "title": "做出更明智的加密货币投资",
    "titleHighlight": "借助AI洞察",
    "description": "CoinScout通过40多个指标分析超过3,000种加密货币，帮助您在他人发现之前识别最佳投资机会。",
    "ctaButton": "开始免费分析",
    "cryptocurrencies": "加密货币",
    "analysisMetrics": "分析指标",
    "moreAccuracy": "更高准确性",
  },

  // Features related strings
  "features": {
    "title": "AI增强加密货币评级",
    "subtitle": "我们全面的AI驱动工具套件帮助您自信地驾驭加密货币市场",
    "description": "专有算法评估代币经济学、安全性、社交参与度和市场因素等50多个指标",
  },

  // IdoRating related strings
  "idoRating": {
    "title": "IDO评级",
    "description": "在有前景的代币发行前发现并评估，提供详细的项目分析和评分",
  },

  // CompareCoins related strings
  "compareCoins": {
    "title": "对比币种",
    "description": "多种加密货币的并排分析，根据您的标准识别最佳投资机会",
  },

  // PortfolioGenerator related strings
  "portfolioGenerator": {
    "title": "定制投资组合生成器",
    "description": "基于您的风险承受能力和投资目标，AI驱动的建议构建多样化投资组合",
  },

  // PortfolioAnalysis related strings
  "portfolioAnalysis": {
    "title": "AI投资组合分析",
    "description": "通过自动风险分析、多样化评分和优化建议，获得对现有投资组合的AI驱动洞察",
  },

  // Launchpads related strings
  "launchpads": {
    "title": "顶级启动平台",
    "description": "发现新代币发行的最佳平台，包含性能指标、成功率和投资者回报",
  },

  // AiAssistant related strings
  "aiAssistant": {
    "title": "AI助手",
    "description": "通过我们先进的AI助手获得加密货币问题的即时答案，提供个性化指导和洞察",
  },

  // AirdropScore related strings
  "airdropScore": {
    "title": "空投评分",
    "description": "通过我们的验证系统找到具有最高潜在价值和最低参与门槛的合法空投",
  },

  // GemScout related strings
  "gemScout": {
    "title": "宝石侦察",
    "description": "在早期项目进入主流市场和主要交易所之前，发现具有卓越增长潜力的项目",
  },

  // Badges related strings
  "badges": {
    "betaTestingLive": "Beta测试进行中",
    "betaTestingSoon": "Beta测试即将开始",
    "comingSoon": "即将推出",
  },

  // Intelligence related strings
  "intelligence": {
    "title": "AI驱动的智能化，助力更明智的加密货币决策",
  },

  // Faq related strings
  "faq": {
    "title": "常见问题",
    "question": "CoinScout的分析与其他平台有何不同？",
    "answer": "CoinScout利用AI驱动的分析来评估代币经济学、安全性、社交情绪、市场表现和项目基础等50多个关键因素。与主要提供原始数据的传统平台不同，我们将复杂指标转化为清晰、可行的洞察——让各级投资者更容易理解并做出明智决策。",
  },

  // Error related strings
  "error": {
    "criticalError": "严重错误",
    "somethingWentWrong": "出现错误",
    "criticalErrorMessage": "发生严重错误。请刷新页面或返回首页。",
    "returnToHome": "返回首页",
    "multipleErrorsDetected": "检测到多个错误",
    "unexpectedError": "发生意外错误",
    "refreshPage": "刷新页面",
    "goToHome": "前往首页",
    "clearErrorLogs": "清除错误日志",
    "anErrorOccurred": "发生错误",
  },

  // Data related strings
  "data": {
    "loading": "数据加载中",
    "empty": "没有可用数据",
    "error": "数据加载错误",
  },

  // Auth related strings
  "auth": {
    "email": "电子邮箱",
    "emailPlaceholder": "输入您的电子邮箱",
    "emailDescription": "您的电子邮箱不会与任何人分享",
    "password": "密码",
    "passwordPlaceholder": "输入您的密码",
    "passwordCreate": "创建密码",
    "passwordConfirm": "确认密码",
    "passwordConfirmPlaceholder": "再次输入您的密码",
    "passwordShow": "显示密码",
    "passwordHide": "隐藏密码",
    "passwordStrength": "密码强度",
    "passwordStrengthWeak": "弱",
    "passwordStrengthGood": "中",
    "passwordStrengthStrong": "强",
    "passwordResetMessage": "密码重置功能即将推出",
    "forgotPassword": "忘记密码？",
    "resetPassword": "重置密码",
    "signin": "登录",
    "signinLoading": "登录中...",
    "signinSecurely": "安全登录",
    "signup": "注册",
    "signout": "退出",
    "accountCreated": "账户创建成功",
    "passwordResetSent": "密码重置邮件已发送",
    "invalidCredentials": "无效的电子邮箱或密码",
    "continueWith": "继续使用",
    "username": "用户名",
    "usernamePlaceholder": "选择用户名",
    "agree": "我同意",
    "service": "服务条款",
    "and": "和",
    "privacy": "隐私政策",
    "email.placeholder": "输入您的电子邮箱",
    "email.description": "您的电子邮箱不会与任何人分享",
    "password.placeholder": "输入您的密码",
    "password.create": "创建密码",
    "password.confirm": "确认密码",
    "password.confirm.placeholder": "再次输入您的密码",
    "password.show": "显示密码",
    "password.hide": "隐藏密码",
    "password.strength": "密码强度",
    "password.strength.weak": "弱",
    "password.strength.good": "中",
    "password.strength.strong": "强",
    "password.reset.message": "密码重置功能即将推出",
    "signin.loading": "登录中...",
    "signin.securely": "安全登录",
    "username.placeholder": "选择用户名",
    "password.criteria.length": "[MISSING] At least 8 characters",
    "password.criteria.uppercase": "[MISSING] At least one uppercase letter",
    "password.criteria.lowercase": "[MISSING] At least one lowercase letter",
    "password.criteria.number": "[MISSING] At least one number",
    "password.criteria.special": "[MISSING] At least one special character",
    "captcha.protected": "[MISSING] This form is protected by reCAPTCHA",
    "terms.service": "服务条款",
    "terms.and": "和",
    "terms.privacy": "隐私政策",
  },

  // Login related strings
  "login": {
    "prompt": "输入您的登录凭证",
  },

  // Success related strings
  "success": {
    "title": "成功",
    "description": "登录成功",
  },

  // Register related strings
  "register": {
    "title": "创建账户",
    "create": "创建账户",
    "creating": "正在创建账户...",
    "haveAccount": "已有账户？",
    "success": "注册成功",
    "successDetail": "账户创建成功",
    "successLogin": "账户已创建。请登录。",
    "failed": "注册失败",
    "failedGeneric": "注册过程中出现错误。请重试。",
    "generic": "出现错误。请重试。",
    "success.detail": "[MISSING] Your account has been created successfully",
    "success.login": "[MISSING] Your account has been created, please login.",
    "failed.generic": "[MISSING] An error occurred during registration. Please try again.",
    "success.email_verify": "[MISSING] Registration successful. Please check your email to verify your account.",
  },

  // Form related strings
  "form": {
    "invalidFields": "请正确填写所有必填字段",
  },

  // Validation related strings
  "validation": {
    "email": "请输入有效的电子邮箱地址",
    "length": "密码至少需要6个字符",
  },

  // Coinlist related strings
  "coinlist": {
    "allCoins": "所有币种",
    "coinDetailDescription": "点击任何币种获取详细分析",
    "searchCoins": "搜索币种...",
    "ariaLabel": "搜索币种",
  },

  // Filters related strings
  "filters": {
    "title": "筛选选项",
    "description": "使用这些筛选器找到符合您投资标准的特定类型代币销售。",
    "details": "按销售类型、启动平台、类别、区块链或投资者进行筛选。",
    "action": "尝试选择一个筛选器，看看它如何更新列表。",
  },

  // Columns related strings
  "columns": {
    "name": "名称",
    "tokenomics": "代币经济学",
    "security": "安全性",
    "social": "社交",
    "market": "市场",
    "insights": "洞察",
    "totalScore": "AI总评分",
    "sevenDayChange": "7天变化",
    "price": "价格",
  },

  // Highlights related strings
  "highlights": {
    "topGainers": "涨幅最高的币种",
    "newListings": "新上线币种",
    "upcomingIDOs": "即将到来的IDO",
    "gemCoins": "潜力币",
    "topAirdrops": "热门空投",
    "score": "评分",
  },

  // Coindetail related strings
  "coindetail": {
    "overview": "概览",
    "fundamentals": "基本面",
    "technicals": "技术面",
    "news": "新闻",
    "social": "社交",
    "developers": "开发者",
    "analysis": "分析",
    "price": "价格",
    "volume": "交易量",
    "marketCap": "市值",
    "circulatingSupply": "流通供应量",
    "totalSupply": "总供应量",
    "maxSupply": "最大供应量",
    "allTimeHigh": "历史最高价",
    "allTimeLow": "历史最低价",
    "pricePrediction": "价格预测",
    "addToWatchlist": "添加到关注列表",
    "removeFromWatchlist": "从关注列表中移除",
  },

  // Portfolio related strings
  "portfolio": {
    "totalValue": "总价值",
    "recentActivity": "最近活动",
    "performance": "表现",
    "holdings": "持仓",
    "addAsset": "添加资产",
    "editAsset": "编辑资产",
    "noAssets": "投资组合中没有资产",
    "24hChange": "24小时变化",
    "allocation": "分配",
    "profit": "盈亏",
  },

  // Settings related strings
  "settings": {
    "appearance": "外观",
    "language": "语言",
    "notifications": "通知",
    "security": "安全",
    "preferences": "偏好设置",
    "theme": "主题",
    "lightMode": "浅色模式",
    "darkMode": "深色模式",
    "systemDefault": "系统默认",
  },

  // Sidebar related strings
  "sidebar": {
    "lock": "锁定",
    "unlock": "解锁",
    "collapse": "折叠",
    "cryptoRating": "加密货币评级",
    "idoRating": "IDO评级",
    "compareCoins": "币种比较",
    "recentListings": "近期上市",
    "topMovers": "涨幅榜",
    "watchlist": "我的关注",
    "aiPortfolio": "AI投资组合指南",
    "portfolioAudit": "投资组合审计",
    "launchpads": "发射台",
    "airdrops": "空投中心",
    "aiAssistant": "AI助手",
    "gemScout": "潜力币探索",
    "soon": "即将推出",
  },

  // Format related strings
  "format": {
    "thousand": "千",
    "million": "百万",
    "billion": "十亿",
    "trillion": "万亿",
  },

  // Coin related strings
  "coin": {
    "age": "年龄:",
  },

  // Score related strings
  "score": {
    "title": "CoinScout评分",
    "description": "我们的专有评分评估项目的整体质量和潜力。",
    "details": "基于代币经济学、安全性、社交媒体活动等。",
  },

  // Watchlist related strings
  "watchlist": {
    "title": "您的关注列表为空",
    "description": "您还没有任何关注列表。创建关注列表来跟踪您的加密货币。",
    "createWatchlist": "创建关注列表",
    "addCoins": "添加币种",
  },

  // SaleType related strings
  "saleType": {
    "IDO": "初始DEX发行 - 在去中心化交易所(DEX)进行的代币销售",
    "IEO": "初始交易所发行 - 在中心化加密货币交易所举办的代币销售",
    "ICO": "初始代币发行 - 新项目向早期投资者提供代币的首次融资阶段",
    "SHO": "强持有者发行 - 优先考虑长期代币持有者的代币销售",
    "Seed": "种子轮 - 公开销售前的早期私人融资轮",
    "IGO": "初始游戏发行 - 专注于区块链游戏项目的融资",
    "ISO": "初始质押发行 - 通过质押机制进行代币分发",
  },

  // ListingDate related strings
  "listingDate": {
    "24hours": "过去24小时",
    "7days": "过去7天",
    "14days": "过去14天",
    "30days": "过去30天",
    "90days": "过去90天",
  },

  // UpcomingTour related strings
  "upcomingTour": {
    "title": "欢迎来到即将推出的IDO",
    "description": "您想要快速了解即将推出的IDO功能吗？",
    "info": "学习如何筛选即将推出的代币销售，了解项目详情，并在项目启动前进行评估。",
    "dontShowAgain": "不再显示",
    "skipButton": "暂时跳过",
    "startButton": "开始导览",
  },

  // Steps related strings
  "steps": {
    "title": "即将推出的IDO概览",
    "description": "欢迎来到即将推出的IDO页面，您可以在新代币上线前发现和评估它们。",
    "details": "浏览、筛选和分析不同区块链和启动平台上的即将推出的代币销售。",
  },

  // Search related strings
  "search": {
    "title": "搜索和查找",
    "description": "通过名称或符号快速搜索任何即将推出的代币销售。",
    "details": "结果会在您输入时实时更新。",
  },

  // ProjectInfo related strings
  "projectInfo": {
    "title": "项目信息",
    "description": "每一行都包含有关即将推出的代币销售的详细信息。",
    "details": "点击任何行查看该项目的详细分析。",
    "action": "尝试将鼠标悬停在项目上以查看更多信息。",
  },

  // InitialCap related strings
  "initialCap": {
    "title": "初始市值",
    "description": "代币上市时的预期初始市值。",
    "details": "计算为代币价格 × TGE时的流通供应量。",
  },

  // LaunchDate related strings
  "launchDate": {
    "title": "启动日期",
    "description": "代币可供交易的预定日期。",
    "details": "及时了解即将推出的项目，为您的投资策略做准备。",
  },

  // Pagination related strings
  "pagination": {
    "title": "页面导航",
    "description": "浏览即将推出的代币销售列表。",
    "details": "调整每页显示的项目数量。",
  },

  // Completion related strings
  "completion": {
    "title": "您已准备就绪！",
    "description": "您已完成即将推出的IDO页面导览。",
    "details": "开始探索和分析即将推出的代币销售，寻找您的下一个投资机会。",
    "help": "您可以随时点击引导导览按钮重新开始此导览。",
  },

  // Methodology related strings
  "methodology": {
    "whatAreWeScoring": "我们评估什么",
    "whyIsThisImportant": "为什么这很重要",
    "scoringLevels": "评分等级",
  },

  // Profile related strings
  "profile": {
    "yourProfile": "您的个人资料",
    "howOthersSeeYou": "这是其他人在平台上看到您的方式。",
    "verified": "已验证",
    "unverified": "未验证",
    "memberSince": "注册时间",
    "unknown": "未知",
    "status": "状态",
    "active": "活跃",
    "plan": "计划",
    "unknownPlan": "未知计划",
    "planStatus": "计划状态",
    "started": "开始时间",
    "expires": "到期时间",
    "lastLogin": "最后登录",
    "never": "从未",
    "profileCompletion": "[MISSING] Profile completion",
    "improveTip": "[MISSING] Add a bio and profile picture to improve your profile.",
    "signOut": "[MISSING] Sign Out",
    "areYouSure": "[MISSING] Are you sure?",
    "signOutConfirmation": "[MISSING] You'll be signed out from your account. You can sign back in at any time.",
    "cancel": "[MISSING] Cancel",
    "personalInformation": "[MISSING] Personal Information",
    "updateDescription": "[MISSING] Update your personal information and how your profile appears.",
    "username": "[MISSING] Username",
    "email": "[MISSING] Email",
    "emailPlaceholder": "[MISSING] <EMAIL>",
    "emailSent": "[MISSING] Email sent",
    "verificationEmailSent": "[MISSING] Verification email sent successfully. Please check your inbox.",
    "error": "[MISSING] Error",
    "emailSendError": "[MISSING] An error occurred while sending email. Please try again.",
    "resendVerification": "[MISSING] Resend verification email",
    "membershipTier": "[MISSING] Membership Tier",
    "pro": "[MISSING] Pro",
    "premium": "[MISSING] Premium",
    "free": "[MISSING] Free",
    "manageSubscription": "[MISSING] Manage your subscription in the",
    "membership": "[MISSING] Membership",
    "tab": "[MISSING] tab",
    "downloadData": "[MISSING] Download Your Data",
    "deleteAccount": "[MISSING] Delete Account",
    "absolutelySure": "[MISSING] Are you absolutely sure?",
    "deleteWarning": "[MISSING] This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    "title": "[MISSING] Profile",
    "description": "[MISSING] Manage your account settings and preferences.",
    "saveChanges": "[MISSING] Save Changes",
    "profile": "[MISSING] Profile",
    "notifications": "[MISSING] Notifications",
  },

  // Logs related strings
  "logs": {
    "usingDirectAPIData": "[MISSING] 🔄 Using direct API data, data length:",
    "imageLoadError": "[MISSING] 🖼️ Error loading image:",
    "usingIdoWatchlists": "[MISSING] Using IDO watchlists for upcoming projects:",
    "rawAPIData": "[MISSING] 🟡 Raw data from API:",
    "apiDataLength": "[MISSING] 🟡 API data length:",
    "filteredData": "[MISSING] 🔍 Filtered data:",
    "filteredDataLength": "[MISSING] 🔍 Filtered data length:",
    "imageUrlChecks": "[MISSING] 🖼️ Image URL checks:",
    "allProjectsFromAPI": "[MISSING] 📌 All projects from API:",
    "usingDirectAPI": "[MISSING] 🟢 Using direct API data",
    "rawAPIDataFirst5": "[MISSING] 🔍 Raw API data (first 5 items):",
    "emptyAPIResult": "[MISSING] ⚠️ API returned empty result, using empty array",
    "errorUsingEmptyArray": "[MISSING] ❌ Using empty array due to error",
    "totalAIScoreClicked": "[MISSING] Total AI Score clicked:",
    "filtersChanged": "[MISSING] Filters changed:",
    "upcomingIDOFirstUseEffect": "[MISSING] 🔄 UpcomingIDO - First useEffect triggered",
    "fetchInitialDataStarted": "[MISSING] 🔄 fetchInitialData started",
    "fetchInitialDataCompleted": "[MISSING] 🔄 fetchInitialData completed",
    "filterRunning": "[MISSING] 🔍 Filter running, coins length:",
    "paginatedData": "[MISSING] 📄 Paginated data:",
    "first2Records": "[MISSING] 📄 First 2 records:",
    "paginatedDataImageFields": "[MISSING] 📄 Image fields in paginated data:",
    "firstRecordDetails": "[MISSING] 🧩 First record details:",
    "firstRecordSocialScore": "[MISSING] 📢 First record social score value:",
    "clickedProjectInfo": "[MISSING] Clicked project info:",
    "redirectingWithDirectID": "[MISSING] Redirecting with direct ID:",
  },

  // Upcoming related strings
  "upcoming": {
    "title": "即将推出的代币销售",
    "subtitle": "在代币启动前发现和评估新代币",
    "saleType": "销售类型",
    "allTypes": "[MISSING] All Types",
    "launchpad": "[MISSING] Launchpad",
    "allLaunchpads": "[MISSING] All Launchpads",
    "category": "[MISSING] Category",
    "allCategories": "[MISSING] All Categories",
    "blockchain": "[MISSING] Blockchain",
    "allBlockchains": "[MISSING] All Blockchains",
    "investor": "[MISSING] Investor",
    "allInvestors": "[MISSING] All Investors",
    "projectScore": "[MISSING] Project Score",
    "listingDate": "[MISSING] Listing Date",
    "reset": "[MISSING] Reset Filters",
    "apply": "[MISSING] Apply Filters",
    "searchCategories": "[MISSING] Search categories...",
    "searchChains": "[MISSING] Search chains...",
    "selectDateRange": "[MISSING] Select date range",
    "last24Hours": "[MISSING] Last 24 Hours",
    "last7Days": "[MISSING] Last 7 Days",
    "last14Days": "[MISSING] Last 14 Days",
    "last30Days": "[MISSING] Last 30 Days",
    "last90Days": "[MISSING] Last 90 Days",
  },

  // Table related strings
  "table": {
    "name": "[MISSING] Name",
    "launchDate": "[MISSING] Launch Date",
    "initialCap": "[MISSING] Initial Cap",
    "totalRaised": "[MISSING] Total Raised",
    "score": "[MISSING] Score",
    "actions": "[MISSING] Actions",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "加载中...",
  "common:error": "错误",
  "common:retry": "重试",
  "common:save": "保存",
  "common:cancel": "取消",
  "common:close": "关闭",
  "common:success": "成功",
  "common:viewMore": "查看更多",
  "common:back": "返回",
  "common:next": "下一步",
  "common:search": "搜索",
  "common:searchCoinsAndTokens": "搜索加密货币和即将推出的项目",
  "common:searching": "搜索中...",
  "common:noResults": "未找到结果",
  "common:coins": "加密货币",
  "common:upcomingIdos": "即将到来的IDO",

  // Flat format for nav (for backward compatibility)
  "nav:home": "首页",
  "nav:portfolio": "投资组合",
  "nav:explore": "探索",
  "nav:news": "新闻",
  "nav:learn": "学习",
  "nav:profile": "个人资料",
  "nav:settings": "设置",
  "nav:logout": "退出",
  "nav:login": "登录",
  "nav:register": "注册",
  "nav:trending": "热门",
  "nav:favorites": "收藏",
  "nav:watchlist": "关注列表",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "搜索中...",
  "system:error.translation": "翻译错误",
  "system:data.loading": "数据加载中",
  "system:data.empty": "没有可用数据",
  "system:data.error": "数据加载错误",

  // Flat format for auth (for backward compatibility)
  "auth:email": "电子邮箱",
  "auth:email.placeholder": "输入您的电子邮箱",
  "auth:email.description": "您的电子邮箱不会与任何人分享",
  "auth:password": "密码",
  "auth:password.placeholder": "输入您的密码",
  "auth:password.create": "创建密码",
  "auth:password.confirm": "确认密码",
  "auth:password.confirm.placeholder": "再次输入您的密码",
  "auth:password.show": "显示密码",
  "auth:password.hide": "隐藏密码",
  "auth:password.strength": "密码强度",
  "auth:password.strength.weak": "弱",
  "auth:password.strength.good": "中",
  "auth:password.strength.strong": "强",
  "auth:password.reset.message": "密码重置功能即将推出",
  "auth:forgotPassword": "忘记密码？",
  "auth:resetPassword": "重置密码",
  "auth:signin": "登录",
  "auth:signin.loading": "登录中...",
  "auth:signin.securely": "安全登录",
  "auth:signup": "注册",
  "auth:signout": "退出",
  "auth:accountCreated": "账户创建成功",
  "auth:passwordResetSent": "密码重置邮件已发送",
  "auth:invalidCredentials": "无效的电子邮箱或密码",
  "auth:continueWith": "继续使用",
  "auth:username": "用户名",
  "auth:username.placeholder": "选择用户名",
  "auth:terms.agree": "我同意",
  "auth:terms.service": "服务条款",
  "auth:terms.and": "和",
  "auth:terms.privacy": "隐私政策",
  "auth:termsAccept": "继续即表示您同意我们的服务条款和隐私政策",
  "auth:remember": "30天内保持登录状态",
  "auth:welcome.back": "欢迎回来",
  "auth:login.credential.prompt": "输入您的登录凭证",
  "auth:login.success.title": "成功",
  "auth:login.success.description": "登录成功",
  "auth:login.error.title": "登录错误",
  "auth:login.error.unknown": "出现错误。请重试。",
  "auth:register.title": "创建账户",
  "auth:register.create": "创建账户",
  "auth:register.creating": "正在创建账户...",
  "auth:register.haveAccount": "已有账户？",
  "auth:register.success": "注册成功",
  "auth:register.success.detail": "账户创建成功",
  "auth:register.success.login": "账户已创建。请登录。",
  "auth:register.success.email_verify": "注册成功。请检查您的电子邮件以激活账户。",
  "auth:register.failed": "注册失败",
  "auth:register.failed.generic": "注册过程中出现错误。请重试。",
  "auth:register.error.generic": "出现错误。请重试。",
  "auth:register.description": "创建账户开始使用",
  "auth:form.invalidFields": "请正确填写所有必填字段",
  "auth:validation.email": "请输入有效的电子邮箱地址",
  "auth:validation.email.required": "电子邮箱是必需的",
  "auth:validation.email.invalid": "请输入有效的电子邮箱地址",
  "auth:validation.email.complete": "请输入完整的电子邮箱地址",
  "auth:validation.password.required": "密码是必需的",
  "auth:validation.password.length": "密码至少需要6个字符",
  "auth:validation.password.uppercase": "密码必须包含至少一个大写字母",
  "auth:validation.password.lowercase": "密码必须包含至少一个小写字母",
  "auth:validation.password.number": "密码必须包含至少一个数字",
  "auth:validation.password.special": "密码必须包含至少一个特殊字符",
  "auth:login.failed": "登录失败",
  "auth:authentication.required": "需要身份验证",
  "auth:authentication.required.description": "请登录以访问此功能",
  "auth:authentication.signin": "登录",
  "auth:authentication.continueWithEmail": "使用邮箱继续",
  "auth:authentication.goBack": "返回",
  "auth:authentication.signInPrompt": "登录以访问个性化功能，保存您的偏好设置，并解锁CoinScout的所有功能。",
  "auth:backToHome": "返回首页",
  "auth:authentication.comparisonPrompt": "登录以访问个性化功能，保存您的偏好设置，并解锁CoinScout的所有比较功能。",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "所有币种",
  "coinlist:coinDetailDescription": "点击任何币种获取详细分析",
  "coinlist:searchCoins": "搜索币种...",
  "coinlist:searchBox.ariaLabel": "搜索币种",
  "coinlist:aiPoweredTitle": "AI驱动和数据驱动的加密货币基本面评级",
  "coinlist:comprehensiveAnalysis": "跨多个指标的加密货币综合分析和评分",
  "coinlist:multipleMetrics": "多重指标",
  "coinlist:highlights": "亮点",
  "coinlist:viewAll": "查看全部",
  "coinlist:currentPrice": "当前价格",
  "coinlist:marketCap": "市值",
  "coinlist:rank": "排名",
  "coinlist:filters.button": "筛选",
  "coinlist:filters.title": "筛选选项",
  "coinlist:filters.description": "使用高级筛选选项自定义视图",
  "coinlist:filters.sortBy": "排序方式",
  "coinlist:filters.reset": "重置筛选",
  "coinlist:columns.name": "名称",
  "coinlist:columns.tokenomics": "代币经济学",
  "coinlist:columns.security": "安全性",
  "coinlist:columns.social": "社交",
  "coinlist:columns.market": "市场",
  "coinlist:columns.insights": "洞察",
  "coinlist:columns.totalScore": "AI总评分",
  "coinlist:columns.sevenDayChange": "7天变化",
  "coinlist:columns.price": "价格",
  "coinlist:tooltips.name.title": "币种名称和符号",
  "coinlist:tooltips.name.description": "在交易所上市的加密货币的官方名称和符号。",
  "coinlist:tooltips.tokenomics.title": "代币经济学分析",
  "coinlist:tooltips.tokenomics.description": "衡量代币供应机制、通胀风险和锁仓结构。",
  "coinlist:tooltips.security.title": "安全分析",
  "coinlist:tooltips.security.description": "安全审计结果和风险评估指标。",
  "coinlist:tooltips.social.title": "社交分析",
  "coinlist:tooltips.social.description": "社交媒体存在、社区参与度和情感分析。",
  "coinlist:tooltips.market.title": "市场表现分析",
  "coinlist:tooltips.market.description": "衡量交易量、流动性和整体市场健康状况。",
  "coinlist:tooltips.insights.title": "AI洞察分析",
  "coinlist:tooltips.insights.description": "AI驱动的项目洞察、情感分析和预测指标。",
  "coinlist:tooltips.totalScore.title": "总评分",
  "coinlist:tooltips.totalScore.description": "从所有评分指标计算出的整体评级。",
  "coinlist:tooltips.sevenDayChange.title": "7天变化",
  "coinlist:tooltips.sevenDayChange.description": "过去7天的百分比价格变化。",
  "coinlist:search.inProgress": "搜索进行中...",
  "coinlist:search.resultsUpdate": "结果将自动更新",
  "coinlist:search.clearSearch": "清除搜索",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "涨幅最高的币种",
  "highlights:newListings": "新上线币种",
  "highlights:upcomingIDOs": "即将到来的IDO",
  "highlights:gemCoins": "潜力币",
  "highlights:topAirdrops": "热门空投",
  "highlights:score": "评分",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "概览",
  "coindetail:fundamentals": "基本面",
  "coindetail:technicals": "技术面",
  "coindetail:news": "新闻",
  "coindetail:social": "社交",
  "coindetail:developers": "开发者",
  "coindetail:analysis": "分析",
  "coindetail:price": "价格",
  "coindetail:volume": "交易量",
  "coindetail:marketCap": "市值",
  "coindetail:circulatingSupply": "流通供应量",
  "coindetail:totalSupply": "总供应量",
  "coindetail:maxSupply": "最大供应量",
  "coindetail:allTimeHigh": "历史最高价",
  "coindetail:allTimeLow": "历史最低价",
  "coindetail:pricePrediction": "价格预测",
  "coindetail:addToWatchlist": "添加到关注列表",
  "coindetail:removeFromWatchlist": "从关注列表中移除",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "总价值",
  "portfolio:recentActivity": "最近活动",
  "portfolio:performance": "表现",
  "portfolio:holdings": "持仓",
  "portfolio:addAsset": "添加资产",
  "portfolio:editAsset": "编辑资产",
  "portfolio:noAssets": "投资组合中没有资产",
  "portfolio:24hChange": "24小时变化",
  "portfolio:allocation": "分配",
  "portfolio:profit": "盈亏",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "外观",
  "settings:language": "语言",
  "settings:notifications": "通知",
  "settings:security": "安全",
  "settings:preferences": "偏好设置",
  "settings:theme": "主题",
  "settings:lightMode": "浅色模式",
  "settings:darkMode": "深色模式",
  "settings:systemDefault": "系统默认",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "锁定",
  "sidebar:unlock": "解锁",
  "sidebar:collapse": "折叠",
  "sidebar:cryptoRating": "加密货币评级",
  "sidebar:idoRating": "IDO评级",
  "sidebar:compareCoins": "币种比较",
  "sidebar:recentListings": "近期上市",
  "sidebar:topMovers": "涨幅榜",
  "sidebar:watchlist": "我的关注",
  "sidebar:aiPortfolio": "AI投资组合指南",
  "sidebar:portfolioAudit": "投资组合审计",
  "sidebar:launchpads": "发射台",
  "sidebar:airdrops": "空投中心",
  "sidebar:aiAssistant": "AI助手",
  "sidebar:gemScout": "潜力币探索",
  "sidebar:soon": "即将推出",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "出现错误",
  "error:tryAgain": "请重试",
  "error:networkIssue": "网络连接问题",
  "error:dataFetch": "无法获取数据",
  "error:timeOut": "请求超时",
  "error:invalidInput": "无效输入",
  "error:pageNotFound": "页面未找到",

  // Flat format for format (for backward compatibility)
  "format:thousand": "千",
  "format:million": "百万",
  "format:billion": "十亿",
  "format:trillion": "万亿",

  // Flat format for coin (for backward compatibility)
  "coin:age": "年龄:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "优秀",
  "score:positive": "良好",
  "score:average": "一般",
  "score:weak": "较弱",
  "score:critical": "危险",

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "即将推出的代币销售",
  "upcoming:subtitle": "在代币启动前发现和评估新代币",
  "upcoming:filters.title": "筛选器",
  "upcoming:filters.saleType": "销售类型",
  "upcoming:filters.allTypes": "所有类型",
  "upcoming:filters.launchpad": "启动平台",
  "upcoming:filters.allLaunchpads": "所有启动平台",
  "upcoming:filters.category": "类别",
  "upcoming:filters.allCategories": "所有类别",
  "upcoming:filters.blockchain": "区块链",
  "upcoming:filters.allBlockchains": "所有区块链",
  "upcoming:filters.investor": "投资者",
  "upcoming:filters.allInvestors": "所有投资者",
  "upcoming:filters.projectScore": "项目评分",
  "upcoming:filters.listingDate": "上市日期",
  "upcoming:filters.reset": "重置筛选器",
  "upcoming:filters.apply": "应用筛选器",
  "upcoming:table.name": "名称",
  "upcoming:table.launchDate": "启动日期",
  "upcoming:table.initialCap": "初始市值",
  "upcoming:table.totalRaised": "总筹集",
  "upcoming:table.score": "评分",
  "upcoming:table.actions": "操作",
  "upcoming:search": "搜索即将推出的代币销售...",
  "upcoming:noResults": "未找到即将推出的代币销售",
  "upcoming:loading": "正在加载即将推出的代币销售...",
  "upcoming:error": "加载即将推出的代币销售时出错",
  "upcoming:retryButton": "重试",
  "upcoming:tba": "待定",
  "upcoming:rank": "排名 #{number}",
  "upcoming:saleType": "销售类型",
  "upcoming:totalAiScore": "总AI评分",
  "upcoming:points": "分数",
  "upcoming:tokenomics": "代币经济学",
  "upcoming:security": "安全性",
  "upcoming:social": "社交",
  "upcoming:market": "市场",
  "upcoming:insights": "洞察",

};

export default zh;