/**
 * English localization strings
 */

export default {
  // Coin list related translations
  coinlist: {
    title: "All Coins",
    search: "Search coins...",
    filters: "Filters",
    viewAll: "View All",
    headers: {
      name: "Name",
      tokenomics: "Tokenomics",
      security: "Security",
      social: "Social",
      market: "Market",
      insights: "Insights",
      totalAIScore: "Total AI Score",
      "7dChange": "7d Change",
    },
  },

  // Compare page translations
  compare: {
    title: "Compare Cryptocurrencies",
    overview: "Overview",
    metrics: "Metrics",
    categorySelection: "Category Selection",
    gettingStarted: "Getting Started",
    selectInstruction: "Select up to 4 cryptocurrencies to compare their performance metrics and identify the strongest investment options.",
    selectCoin: "Select coin",
    bestPerformer: "Best Performer",
    overallScore: "Overall Score",
    noCoinsSelected: "No coins selected for comparison",
    selectCoinsForBest: "Please select coins to see the best performer",
    selectCoinsForMetrics: "Please select coins to view metrics",
    metric: "Metric",
    winner: "Winner",
    price: "Price",
    marketCap: "Market Cap",
    overallRating: "Overall Rating",
    tokenomics: "Tokenomics",
    security: "Security",
    socials: "Socials",
    market: "Market",
    insights: "Insights",
  },

  // Pagination strings
  pagination: {
    showing: "Showing",
    of: "of",
    rows: "rows",
  },

  // Authentication namespace with colon-separated keys
  "auth:fields.password": "Password",
  "auth:forgotPassword": "Forgot Password?",
  "auth:remember": "Remember me for 30 days",
  "auth:signin.securely": "Sign in securely",
  "auth:signin.loading": "Signing in...",
  "auth:continueWith": "Or continue with",
  "auth:termsAccept": "By clicking continue, you agree to our",
  "auth:terms.service": "Terms of Service",
  "auth:terms.privacy": "Privacy Policy",
  "auth:terms.and": "and",
  "auth:resetPassword": "Reset Your Password",
  "auth:backToLogin": "Back to Login",
  "auth:email": "Email",
  "auth:password": "Password",
  "auth:login": "Login",
  "auth:register": "Register",
  "auth:login.title": "CoinScout Login",
  "auth:register.title": "Create Account",

  // Password validation
  "auth:password.criteria.length": "At least 8 characters",
  "auth:password.criteria.uppercase": "At least one uppercase letter",
  "auth:password.criteria.lowercase": "At least one lowercase letter",
  "auth:password.criteria.number": "At least one number",
  "auth:password.criteria.special": "At least one special character",

  // Common auth validation
  "common:auth:validation.username.min":
    "Username must be at least 3 characters",
  "common:auth:validation.email.invalid": "Please enter a valid email address",
  "common:auth:validation.password.min":
    "Password must be at least 8 characters",
  "common:auth:validation.password.uppercase":
    "Must contain at least one uppercase letter",
  "common:auth:validation.password.lowercase":
    "Must contain at least one lowercase letter",
  "common:auth:validation.password.number": "Must contain at least one number",
  "common:auth:validation.password.special":
    "Must contain at least one special character",
  "common:auth:validation.terms": "You must accept the terms of service",
  "common:auth:validation.password.match": "Passwords do not match",

  // Register form fields
  "common:auth:username": "Username",
  "common:auth:username.placeholder": "Choose a username",
  "common:auth:email.placeholder": "Enter your email",
  "common:auth:email.description":
    "We'll never share your email with anyone else",
  "common:auth:password.create": "Create a password",
  "common:auth:password.show": "Show password",
  "common:auth:password.confirm": "Confirm Password",
  "common:auth:password.confirm.placeholder": "Confirm your password",

  // Register form additional text
  "auth:terms.agree": "I agree to the Terms of Service and Privacy Policy",
  "auth:captcha.protected": "This form is protected by reCAPTCHA",
  "auth:register.create": "Create account",
  "auth:register.haveAccount": "Already have an account?",
  "auth:login.noAccount": "Don't have an account?",

  // Common namespace with colon-separated keys
  "common:back.home": "Back to Home",

  // Nav strings
  "nav:home": "Home",
  "nav:coins": "Coins",
  "nav:idos": "IDOs",
  "nav:portfolio": "Portfolio",
  "nav:profile": "Profile",
  "nav:login": "Login",
  "nav:register": "Register",
  "nav:trending": "Trending",
  "nav:favorites": "Favorites",
  "nav:watchlist": "Watchlist",

  // Navigation namespace
  navigation: {
    pricing: "Pricing",
    goToApp: "Go to App",
    Pricing: "Pricing",
    Documentation: "Documentation",
    goToHomepage: "Go to Homepage",
    coinScoutAlt: "CoinScout - AI-Powered Crypto Analysis",
    login: "Login",
    signUp: "Sign Up",
    membershipManagement: "Membership Management",
  },

  // Navigation colon-separated keys for compatibility
  "navigation:pricing": "Pricing",
  "navigation:goToApp": "Go to App",
  "navigation:Pricing": "Pricing",
  "navigation:Documentation": "Documentation",
  "navigation:goToHomepage": "Go to Homepage",
  "navigation:coinScoutAlt": "CoinScout - AI-Powered Crypto Analysis",
  "navigation:login": "Login",
  "navigation:signUp": "Sign Up",
  "navigation:membershipManagement": "Membership Management",

  // System namespace
  system: {
    language: {
      selector: {
        title: "Select Language",
        label: "Language",
        available: "Available Languages",
      },
    },
    auth: {
      required: "Login required to view coin summary",
      loginButton: "Login",
    },
    subscription: {
      filterRestriction: {
        title: "Subscription Required",
        message:
          "You need at least a Basic subscription to use filters. Please upgrade your subscription plan.",
      },
    },
  },

  // System colon-separated keys for compatibility
  "system:language.selector.title": "Select Language",
  "system:language.selector.label": "Language",
  "system:language.selector.available": "Available Languages",
  "system:auth.required": "Login required to view coin summary",
  "system:auth.loginButton": "Login",

  // Sidebar namespace
  sidebar: {
    home: "Home",
    coins: "Coins",
    topMovers: "Top Movers",
    watchlist: "Watchlist",
    aiPortfolio: "AI Portfolio",
    portfolioCheckup: "Portfolio Checkup",
    compareCoins: "Compare Coins",
    upcomingIDOs: "Upcoming IDOs",
    aiAssistant: "AI Assistant",
    airdrops: "Airdrops",
    gemScout: "Gem Scout",
    soon: "Soon",
    cryptoRating: "Crypto Rating",
    idoRating: "IDO Rating",
    recentListings: "Recent Listings",
    portfolioAudit: "Portfolio Audit",
    launchpads: "Launchpads",
    lock: "Lock Sidebar",
  },

  // Highlights - separate string values for each key
  topGainers: "Top Movers",
  newListings: "New Listings",
  upcomingIDOs: "Upcoming IDOs",
  score: "Score",

  // Common translations
  common: {
    searching: "Searching...",
    cancel: "Cancel",
    more: "more",
    selected: "selected",
    loading: "Loading...",
    upcomingIdos: "Upcoming IDOs",
  },

  // Main coinlist translations
  aiPoweredTitle: "AI-Powered & Data-Driven Crypto Fundamental Ratings",
  highlights: "Highlights",
  nextDataUpdate: "Next Data Update",
  allCoins: "All Coins",
  coinDetailDescription: "Click on any coin for detailed analysis",
  filtersButton: "Filters",
  alertsTitle: "Alerts",

  // Table column headers
  name: "Name",
  tokenomics: "Tokenomics",
  security: "Security",
  social: "Social",
  market: "Market",
  insights: "Insights",
  totalScore: "Total Score",
  sevenDayChange: "7D Change",

  // Score rating colon-separated keys for compatibility
  "score:excellent": "Excellent",
  "score:positive": "Positive",
  "score:average": "Average",
  "score:weak": "Weak",
  "score:critical": "Critical",

  // Alerts namespace
  alerts: {
    price: "Price",
    currentPrice: "Current Price",
    priceGoesAbove: "Price goes above",
    priceGoesBelow: "Price goes below",
    title: "Crypto Alerts",
    description: "Get notified when prices change or AI scores update",
    createNewAlert: "Create New Alert",
    activeAlerts: "Active Alerts",
    notifications: "Notifications",
    aiScore: "AI Score",
    coin: "Coin",
    priceAbove: "Price goes above",
    priceBelow: "Price goes below",
    aiScoreAbove: "Get notified when AI score exceeds your set threshold",
    aiScoreBelow: "Get notified when AI score falls below your set threshold",
    priceAboveDesc: "Get notified when price exceeds your set threshold",
    priceBelowDesc: "Get notified when price falls below your set threshold",
    selectCoin: "Select coin...",
    targetPriceAbove: "Target Price Above",
    targetPriceBelow: "Target Price Below",
    enterUpperTargetPrice: "Enter upper target price",
    enterLowerTargetPrice: "Enter lower target price",
    notificationType: "Notification Type",
    browserNotification: "Browser Notification",
    cancel: "Cancel",
    save: "Save",
    back: "Back",
  },

  marketData: {
    priceChange: "Price Change",
    priceMovement: "Price Movement",
    marketCap: "Market Cap",
    fullyDilute: "Fully Diluted",
    fdv: "FDV",
    tradeVolume24h: "24h Volume",
    marketCapDetails: "Market Cap Details",
    volumeMarketCapRatio: "Volume / Market Cap Ratio",
    marketCapRank: "Market Cap Rank",
    currentRank: "Current rank",
    updatedHourly: "Updated hourly from multiple exchanges",
  },

  // Top Movers namespace
  topMovers: {
    title: "Top Movers",
    description:
      "View cryptocurrencies with the highest upward score changes in the past 7 days",
    allCoins: "All Coins",
    clickForAnalysis: "Click on any coin for detailed analysis",
    searchPlaceholder: "Search coins...",
    filters: "Filters",
  },

  // Filters namespace
  filters: {
    title: "Filter Options",
    description: "Customize your view with advanced filtering options",
    marketCapRange: "Market Cap Range (Million)",
    projectScoreRange: "Project Score Range",
    categories: "Categories",
    chains: "Chains",
    selectCategories: "Select categories",
    selectChains: "Select chains",
    listingDate: "Listing Date",
    chainEcosystem: "Chain / Ecosystem",
    moveSliderToAdjust: "Move the slider to adjust the range:",
    applyFilters: "Apply Filters",
    resetFilters: "Reset Filters",
    loading: "Loading...",
    noCategoriesFound: "No categories found",
    noChainsFound: "No chains found",
    cancel: "Cancel",
    loginRequired: "You need to log in first to use filters.",
    subscriptionRequired:
      "You need at least a Basic plan to use filters. Please upgrade your subscription plan.",
    login: "Login",
    upgradePlan: "Upgrade Plan",
  },

  // Newly Listed Coins namespace
  newlyListed: {
    title: "Newly Listed Coins",
    description: "Discover all newly listed cryptocurrencies",
    cardTitle: "Newly Listed Coins",
    clickForAnalysis: "Click on any coin for detailed analysis",
    searchPlaceholder: "Search coins...",
    selectTimeframe: "Select timeframe",
    last24Hours: "Last 24 Hours",
    last7Days: "Last 7 Days",
    last14Days: "Last 14 Days",
    last30Days: "Last 30 Days",
    last90Days: "Last 90 Days",
    loading: "Loading newly listed coins...",
    noCoinsFound: "No newly listed coins found.",
    filters: "Filters",
  },

  // Coin Age namespace
  coinAge: {
    comingSoon: "Coming soon",
    comingInDays: "Coming in {days} days",
    listedToday: "Listed today",
    oneDayAgo: "1 day ago",
    daysAgo: "{days} days ago",
  },

  // Watchlist namespace
  watchlist: {
    add: "Add To Watchlist",
    addTowatchlist: "Add To Watchlist",
    addToWatchlist: "Add To Watchlist",
    inWatchlist: "In Watchlist",
    watchers: "Watchers",
    alerts: "Alerts",
    share: "Share",
    advancedView: "Advanced View",
    enable: "Enable",
    disable: "Disable",
    enableAdvancedView: "Enable advanced view",
    disableAdvancedView: "Disable advanced view",
    favorites: "Favorites",
    addToFavorites: "Add cryptocurrencies to your favorites",
    error: "Error",
    success: "Success",
    noWatchlistSelected: "No watchlist selected",
    removedFromWatchlist: "Removed from watchlist",
    coinRemovedFromWatchlist: "{coinName} removed from watchlist",
    failedToRemoveCoin: "Failed to remove coin from watchlist",
    removeFromWatchlist: "Remove from watchlist",
    removeCoinFromWatchlist: "Remove {coinName} from watchlist",
    title: "Watchlists",
    description:
      "Track your favorite cryptocurrencies and receive real-time updates",
    portfolioScorePerformance: "Portfolio Score Performance",
    createNewWatchlist: "Create New Watchlist",
    editWatchlist: "Edit Watchlist",
    deleteWatchlist: "Delete Watchlist",
    confirmDelete: "Are you sure you want to delete this watchlist?",
    confirmDeleteDescription:
      "This action cannot be undone. This will permanently delete your watchlist and all its contents.",
    cancel: "Cancel",
    delete: "Delete",
    shareDescription: "Share your watchlist with friends and community",
    copyLink: "Copy Link",
    linkCopied: "Link copied to clipboard!",
    watchlistName: "Watchlist Name",
    watchlistDescription: "Watchlist Description (Optional)",
    enterWatchlistName: "Enter watchlist name",
    enterDescription: "Enter description for your watchlist",
    createDescription:
      "Create a new watchlist to organize your favorite cryptocurrencies",
    editDescription: "Update your watchlist details",
    create: "Create",
    save: "Save Changes",
    searchPlaceholder: "Search coins in watchlist...",
    noCoinsFound: "No coins found in this watchlist",
    addFirstCoin: "Add your first coin to get started",
    sortBy: "Sort by",
    viewMode: "View Mode",
    displayMode: "Display Mode",
    simple: "Simple",
    advanced: "Advanced",
    grid: "Grid",
    list: "List",
    untitled: "Untitled Watchlist",
    preview: "Preview",
    shareNote:
      "Note: By sharing this link, you make your watchlist publicly viewable",
    aiPoweredTitle: "AI-Powered & Data-Driven Crypto Ratings",
    trackDescription:
      "Track your favorite cryptocurrencies and receive real-time updates",
    coins: "Coins",
    idos: "IDOs",
  },

  notifications: {
    title: "Notifications",
  },

  coinDetail: {
    tokenSupplyDynamics: "Token Supply Dynamics",
    overallCategoriesMetricsBreakdown: "Overall Categories Metrics Breakdown",
    categories: "Categories",
    backToPreviousPage: "Back to Previous Page",
    rankPrefix: "Rank #",
    nextUnlock: "Next Unlock",
    unlockInDays: "Unlock in {days} days",
    about: "About",
    marketStatistics: "Market Statistics",
    allTimeHighLow: "All-Time High/Low",
    allTimeHigh: "All-Time High",
    allTimeLow: "All-Time Low",
    fromATH: "From ATH",
    fromATL: "From ATL",
    maxSupply: "Max Supply",
    circulatingSupply: "Circulating Supply",
    totalSupply: "Total Supply",
    ofMaxSupply: "of Maximum Supply",
  },

  priceChart: {
    title: "Price Chart",
    current: "Current",
    high: "High",
    low: "Low",
    sevenDays: "7D ",
    fourteenDays: "14D ",
    thirtyDays: "30D ",
  },

  scoreChart: {
    title: "Score Chart",
    current: "Current",
    high: "High",
    low: "Low",
    oneMonth: "1M ",
    threeMonths: "3M ",
    sixMonths: "6M ",
    oneyear: "1Y ",
    all: "All",
  },

  externalLinks: {
    title: "External Links",
  },

  format: {
    trillion: "T",
    billion: "B",
    million: "M",
    thousand: "K",
  },

  // Price Changes section
  priceChanges: {
    title: "Price Changes",
    "1d": "1 Day",
    "1day": "1 Day",
    "1w": "1 Week",
    "1week": "1 Week",
    "1m": "1 Month",
    "1month": "1 Month",
    "3m": "3 Months",
    "3months": "3 Months",
    "6m": "6 Months",
    "1y": "1 Year",
    "1year": "1 Year",
  },

  // Tokenomics namespace
  tokenomicsNamespace: {
    metricsBreakdown: "Tokenomics Metrics Breakdown",
    metricsBreakdownGeneric: "Metrics Breakdown",
    weight: "Weight",
    requestFeature: "Request Feature",
    reportError: "Report Error",
  },

  // Coin Health Score
  coinHealth: {
    title: "Coin Health Score",
    scoreRange: "Score Range",
    critical: "Critical",
    average: "Average",
    excellent: "Excellent",
    positive: "Positive",
  },

  // Score ratings namespace
  scoreRatings: {
    excellent: "Excellent",
    positive: "Positive",
    average: "Average",
    weak: "Weak",
    critical: "Critical",
  },

  // Methodology namespace
  methodology: {
    whatAreWeScoring: "What Are We Scoring?",
    whyIsThisImportant: "Why Is This Important?",
    scoringLevels: "Scoring Levels",
  },

  // Homepage namespace
  homepage: {
    // Hero section
    hero: {
      badge: "AI-Powered Crypto Analysis Platform",
      title: "Advanced AI-Driven Cryptocurrency Analysis",
      titleHighlight: "Platform",
      description:
        "Discover the future of cryptocurrency analysis with our comprehensive AI-powered platform. Get detailed insights, accurate ratings, and data-driven recommendations for smarter investment decisions.",
      ctaButton: "Start Analyzing",
      stats: {
        cryptocurrencies: "Cryptocurrencies",
        analysisMetrics: "Analysis Metrics",
        moreAccuracy: "More Accuracy",
      },
    },

    // Features sectionx
    features: {
      title: "Powerful Features for Every Crypto Investor",
      subtitle:
        "Comprehensive tools and AI-driven insights to help you make informed cryptocurrency investment decisions.",
      comprehensiveScoring: "Comprehensive AI scoring system",
      aiRating: {
        title: "AI-Enhanced Score Analysis",
        description:
          "Advanced machine learning algorithms analyze multiple data points to provide accurate cryptocurrency ratings and investment insights.",
        bullets: [
          "Real-time AI scoring across 40+ metrics",
          "Machine learning prediction models",
        ],
      },
      idoRating: {
        title: "IDO Rating System",
        description:
          "Comprehensive Initial DEX Offering analysis with AI-powered risk assessment and potential evaluation.",
        bullets: [
          "Pre-launch project evaluation",
          "Team and tokenomics analysis",
          "Launch potential scoring",
        ],
      },
      compareCoins: {
        title: "Advanced Coin Comparison",
        description:
          "Side-by-side comparison of cryptocurrencies with detailed metrics and AI-generated insights.",
        bullets: [
          "Multi-metric comparison dashboard",
          "AI-powered similarity analysis",
          "Risk-reward assessment",
        ],
      },
      portfolioGenerator: {
        title: "Smart Portfolio Generator",
        description:
          "AI-driven portfolio optimization based on your risk tolerance and investment goals.",
        bullets: [
          "Automated portfolio creation",
          "Risk-adjusted allocations",
          "Diversification optimization",
        ],
      },
      portfolioAnalysis: {
        title: "Portfolio Health Analysis",
        description:
          "Comprehensive analysis of your current portfolio with optimization recommendations.",
        bullets: [
          "Performance tracking",
          "Rebalancing suggestions",
          "Risk exposure analysis",
        ],
      },
      launchpads: {
        title: "Launchpad Integration",
        description:
          "Direct access to top cryptocurrency launchpads with curated project selections.",
        bullets: [
          "Verified launchpad projects",
          "Early access opportunities",
          "Due diligence reports",
        ],
      },
      aiAssistant: {
        title: "AI Trading Assistant",
        description:
          "Intelligent trading assistant powered by advanced AI to guide your investment decisions.",
        bullets: [
          "Real-time market insights",
          "Personalized recommendations",
          "Trading signal analysis",
        ],
      },
      airdropScore: {
        title: "Airdrop Score Analysis",
        description:
          "Evaluate airdrop opportunities with AI-powered scoring and eligibility assessment.",
        bullets: [
          "Airdrop potential scoring",
          "Eligibility requirements",
          "Historical success rates",
        ],
      },
      gemScout: {
        title: "Gem Scout Discovery",
        description:
          "Discover hidden cryptocurrency gems using advanced AI pattern recognition and market analysis.",
        bullets: [
          "Early-stage project discovery",
          "Market pattern analysis",
          "Growth potential scoring",
        ],
      },
    },

    // Badges
    badges: {
      betaTestingLive: "Beta Testing Live",
      betaTestingSoon: "Beta Testing Soon",
      comingSoon: "Soon",
    },

    // Trusted By section
    trustedBy: {
      title: "Trusted by Industry Leaders",
      description:
        "Our platform integrates with leading cryptocurrency data providers and blockchain explorers to deliver the most accurate and comprehensive analysis.",
      stats: {
        coinsAnalyzed: "Coins Analyzed",
        dataPoints: "Data Points",
        apiCalls: "API Calls",
        dataSources: "Data Sources",
      },
      features: {
        aiPowered: "AI-Powered Analysis",
        multiLayer: "Multi-Layer Verification",
        enterprise: "Enterprise Grade",
      },
    },

    // Intelligence section
    intelligence: {
      title: "AI-Driven Intelligence",
      subtitle:
        "Experience the power of artificial intelligence in cryptocurrency analysis with real-time insights and predictive modeling.",
    },

    // Call to Action
    callToAction: {
      primary:
        "Experience the power of artificial intelligence in cryptocurrency analysis with real-time insights and predictive modeling.",
      secondary:
        "Join thousands of investors who trust our AI-powered analysis",
    },

    // Benefits
    benefits: {
      "0": {
        title: "Real-Time Analysis",
      },
      "1": {
        title: "AI-Powered Insights",
      },
      "2": {
        title: "Risk Assessment",
      },
    },

    // Buttons
    buttons: {
      getStarted: "Get Started",
      viewAllQuestions: "View All Questions",
    },

    // FAQ section
    faq: {
      title: "Frequently Asked Questions",
      questions: {
        "0": {
          question: "How accurate are the AI-powered cryptocurrency ratings?",
          answer:
            "Our AI models achieve over 85% accuracy in trend prediction by analyzing 40+ metrics including technical indicators, fundamental analysis, social sentiment, and market data from multiple sources.",
        },
        "1": {
          question:
            "What makes CoinScout different from other crypto analysis platforms?",
          answer:
            "CoinScout combines advanced AI algorithms with real-time data from multiple sources, providing comprehensive analysis that goes beyond basic price tracking to include tokenomics, team evaluation, and market sentiment analysis.",
        },
        "2": {
          question:
            "Is the platform suitable for both beginners and experienced traders?",
          answer:
            "Yes, our platform is designed with multiple complexity levels. Beginners can use simplified views and AI recommendations, while experienced traders can access detailed metrics, custom analysis tools, and advanced comparison features.",
        },
      },
    },

    // Testimonials
    testimonials: {
      title: "What Our Users Say",
      "0": {
        text: "CoinScout's AI analysis helped me identify profitable opportunities I would have missed otherwise. The comprehensive scoring system is incredibly accurate.",
      },
    },
  },

  // Error namespace
  error: {
    somethingWentWrong: "Something went wrong",
    refreshPage: "Refresh Page",
    goToHome: "Go to Home",
    clearErrorLogs: "Clear Error Logs",
  },

  // Footer namespace
  footer: {
    description:
      "Advanced AI-powered cryptocurrency analysis platform providing comprehensive insights, ratings, and data-driven recommendations for smarter investment decisions.",
    allRightsReserved: "All rights reserved.",
    categories: {
      product: "Product",
      learn: "Learn",
      community: "Community",
      legal: "Legal",
    },
    links: {
      cryptoRatings: "Crypto Ratings",
      idoRatings: "IDO Ratings",
      aiPortfolioStrategist: "AI Portfolio Strategist",
      aiPortfolioCheckup: "AI Portfolio Checkup",
      compareCoins: "Compare Coins",
      recentListings: "Recent Listings",
      topMovers: "Top Movers",
      airdropsHub: "Airdrops Hub",
      scoutAI: "Scout AI",
      aiAssistant: "AI Assistant",
      pricing: "Pricing",
      academy: "Academy",
      documentation: "Documentation",
      blog: "Blog",
      faq: "FAQ",
      forum: "Forum",
      telegram: "Telegram",
      discord: "Discord",
      twitter: "Twitter",
      publicPortfolios: "Public Portfolios",
      communityGuidelines: "Community Guidelines",
      userTestimonials: "User Testimonials",
      privacyPolicy: "Privacy Policy",
      termsOfService: "Terms of Service",
      cookiePolicy: "Cookie Policy",
      disclaimer: "Disclaimer",
      advertisingPolicy: "Advertising Policy",
      careers: "Careers",
      soon: "Soon",
    },
  },

  // Footer colon-separated keys for compatibility
  "footer:description":
    "Advanced AI-powered cryptocurrency analysis platform providing comprehensive insights, ratings, and data-driven recommendations for smarter investment decisions.",
  "footer:allRightsReserved": "All rights reserved.",
  "footer:categories.product": "Product",
  "footer:categories.learn": "Learn",
  "footer:categories.community": "Community",
  "footer:categories.legal": "Legal",
  "footer:links.cryptoRatings": "Crypto Ratings",
  "footer:links.idoRatings": "IDO Ratings",
  "footer:links.aiPortfolioStrategist": "AI Portfolio Strategist",
  "footer:links.aiPortfolioCheckup": "AI Portfolio Checkup",
  "footer:links.compareCoins": "Compare Coins",
  "footer:links.recentListings": "Recent Listings",
  "footer:links.topMovers": "Top Movers",
  "footer:links.airdropsHub": "Airdrops Hub",
  "footer:links.scoutAI": "Scout AI",
  "footer:links.aiAssistant": "AI Assistant",
  "footer:links.pricing": "Pricing",
  "footer:links.academy": "Academy",
  "footer:links.documentation": "Documentation",
  "footer:links.blog": "Blog",
  "footer:links.faq": "FAQ",
  "footer:links.forum": "Forum",
  "footer:links.telegram": "Telegram",
  "footer:links.discord": "Discord",
  "footer:links.twitter": "Twitter",
  "footer:links.publicPortfolios": "Public Portfolios",
  "footer:links.communityGuidelines": "Community Guidelines",
  "footer:links.userTestimonials": "User Testimonials",
  "footer:links.privacyPolicy": "Privacy Policy",
  "footer:links.termsOfService": "Terms of Service",
  "footer:links.cookiePolicy": "Cookie Policy",
  "footer:links.disclaimer": "Disclaimer",
  "footer:links.advertisingPolicy": "Advertising Policy",
  "footer:links.careers": "Careers",
  "footer:links.soon": "Coming Soon",

  // Upcoming IDO translations
  upcoming: {
    title: "Upcoming Token Sales",
    subtitle: "Discover and evaluate new tokens before launch",
    search: "Search upcoming token sales...",
    noResults: "No upcoming token sales found",
    loading: "Loading upcoming token sales...",
    error: "Error loading upcoming token sales",
    retryButton: "Retry",
    tba: "TBA",
    rank: "Rank #{number}",
    saleType: "Sale Type",
    points: "points",
    tokenomics: "Tokenomics",
    security: "Security",
    social: "Social",
    market: "Market",
    insights: "Insights",
    totalAiScore: "Total AI Score",
    filters: {
      title: "Filters",
      description: "Filter upcoming token sales by various criteria",
      projectScore: "Project Score",
      saleType: "Sale Type",
      category: "Category",
      blockchain: "Blockchain",
      allTypes: "All Types",
      allCategories: "All Categories",
      allBlockchains: "All Blockchains",
      searchCategories: "Search categories...",
      searchChains: "Search blockchains...",
      selectDateRange: "Select date range",
      last24Hours: "Last 24 Hours",
      last7Days: "Last 7 Days",
      last14Days: "Last 14 Days",
      last30Days: "Last 30 Days",
      last90Days: "Last 90 Days",
      reset: "Reset Filters",
      apply: "Apply Filters",
    },
    table: {
      name: "Name",
      date: "Launch Date",
      launchDate: "Launch Date",
      initialCap: "Initial Cap",
      totalRaised: "Total Raised",
      score: "Score",
      actions: "Actions",
      imcScore: "Initial Cap",
      fundingScore: "Financing",
      launchpadScore: "Launchpad",
      investorScore: "Investors",
      socialScore: "Social",
      totalAiScore: "Total AI Score",
    },
    tooltips: {
      rank: {
        title: "Rank",
        description: "Project ranking based on current sort order.",
      },
      watchlist: {
        title: "Watchlist",
        description: "Add or remove projects from your personal watchlist.",
      },
      projectName: {
        title: "Project Name",
        description:
          "Official name of the cryptocurrency project and its ticker symbol.",
      },
      launchDate: {
        title: "Launch Date",
        description:
          "Scheduled or confirmed date for the token's public launch and listing on exchanges.",
      },
      initialMarketCap: {
        title: "Initial Market Cap",
        description:
          "Initial Market Cap metric evaluates the projected market capitalization at token launch, considering factors like token supply, initial price, and market conditions.",
      },
      financing: {
        title: "Financing Analysis",
        description:
          "Financing Score metric analyzes the project's funding history, investor quality, and financial sustainability.",
      },
      launchpad: {
        title: "Launchpad Analysis",
        description:
          "Launchpad Score metric evaluates the reputation and track record of the platform conducting the token sale.",
      },
      investors: {
        title: "Investor Analysis",
        description:
          "Investor Score metric assesses the quality and reputation of backing investors and venture capital firms.",
      },
      socialMedia: {
        title: "Social Media Analysis",
        description:
          "Social Score metric quantifies community interest by analyzing follower counts, engagement rates, and growth trends across a project's social media presence.",
      },
      coinScoutAiScore: {
        title: "CoinScout AI Score",
        description: "The overall rating calculated from all scoring metrics.",
      },
      projectDetails: {
        title: "Project Details",
        description:
          "View comprehensive analysis and detailed metrics for this project.",
      },
    },
  },

  // Sale type translations
  saleType: {
    IDO: "IDO",
    IEO: "IEO",
    ICO: "ICO",
    SHO: "SHO",
    Seed: "Seed",
    IGO: "IGO",
    ISO: "ISO",
  },

  // Logs translations (for development)
  logs: {
    usingIdoWatchlists: "Using IDO watchlists:",
    fetchInitialDataStarted: "Fetch initial data started",
    rawAPIData: "Raw API data:",
    apiDataLength: "API data length:",
    fetchInitialDataCompleted: "Fetch initial data completed",
    firstRecordDetails: "First record details:",
    imageUrlChecks: "Image URL checks",
    allProjectsFromAPI: "All projects from API",
    usingDirectAPI: "Using direct API",
    rawAPIDataFirst5: "Raw API data first 5:",
    firstRecordSocialScore: "First record social score:",
    clickedProjectInfo: "Clicked project info:",
    redirectingWithDirectID: "Redirecting with direct ID:",
  },

  // Empty state translations
  emptyState: {
    noData: "No data available",
    noDescription: "No description available",
    noTeamInfo: "No team information available",
    noFundingInfo: "No funding information available",
    noTokenomics: "No tokenomics data available",
    noPriceAnalysis: "No price analysis available",
    noProjectDetails: "No project details available",
    noInvestorInfo: "No investor information available",
  },

  // IDO feature request translations
  ido: {
    featureRequest: {
      title: "Request a Feature",
      description: "Tell us what feature you'd like to see in CoinScout.",
    },
  },

  // Common translations with colon-separated keys for compatibility
  "common:logs.firstRecordSocialScore": "First record social score:",
  "common:logs.clickedProjectInfo": "Clicked project info:",
  "common:logs.redirectingWithDirectID": "Redirecting with direct ID:",
  "common:emptyState.noData": "No data available",
  "common:emptyState.noInvestorInfo": "No investor information available",
};
