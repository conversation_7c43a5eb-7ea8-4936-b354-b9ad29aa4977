/**
 * Turkish localization strings
 */

export default {
  // Coin list related translations
  coinlist: {
    title: "<PERSON>üm Coinler",
    search: "Coin ara...",
    filters: "<PERSON>lt<PERSON><PERSON>",
    viewAll: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>",
    headers: {
      name: "<PERSON><PERSON><PERSON>",
      tokenomics: "Tokenomik",
      security: "Güvenlik",
      social: "Sosyal",
      market: "Pazar",
      insights: "Analiz",
      totalAIScore: "Toplam AI Skoru",
      "7dChange": "7G Değişim",
    },
  },

  // Compare page translations
  compare: {
    title: "Kripto Para Karşılaştırması",
    overview: "Genel Bakış",
    metrics: "Metrikler",
    categorySelection: "Kategori Seçimi",
    gettingStarted: "Başlangıç",
    selectInstruction: "Performans metriklerini karşılaştırmak ve en güçlü yatırım seçeneklerini belirlemek için 4 adede kadar kripto para seçin.",
    selectCoin: "Coin seç",
    bestPerformer: "En İyi Performans",
    overallScore: "Genel Skor",
    noCoinsSelected: "Karşılaştırma için coin seçilmedi",
    selectCoinsForBest: "En iyi performansı görmek için coin seçin",
    selectCoinsForMetrics: "Metrikleri görüntülemek için coin seçin",
    metric: "Metrik",
    winner: "Kazanan",
    price: "Fiyat",
    marketCap: "Piyasa Değeri",
    overallRating: "Genel Değerlendirme",
    tokenomics: "Tokenomik",
    security: "Güvenlik",
    socials: "Sosyal Medya",
    market: "Piyasa",
    insights: "İçgörüler",
  },

  // Pagination strings
  pagination: {
    showing: "Gösterilen",
    of: "toplam",
    rows: "satır",
  },

  // Authentication namespace with colon-separated keys
  "auth:fields.password": "Şifre",
  "auth:forgotPassword": "Şifremi Unuttum?",
  "auth:remember": "30 gün boyunca beni hatırla",
  "auth:signin.securely": "Güvenli giriş yap",
  "auth:signin.loading": "Giriş yapılıyor...",
  "auth:continueWith": "Veya şununla devam et",
  "auth:termsAccept": "",
  "auth:terms.service": "Hizmet Şartları",
  "auth:terms.privacy": "Gizlilik Politikası",
  "auth:terms.and": "ve",
  "auth:resetPassword": "Şifrenizi Sıfırlayın",
  "auth:backToLogin": "Girişe Dön",
  "auth:email": "E-posta",
  "auth:password": "Şifre",
  "auth:login": "Giriş Yap",
  "auth:register": "Kayıt Ol",
  "auth:login.title": "CoinScout Giriş",
  "auth:register.title": "Hesap Oluştur",

  // Password validation
  "auth:password.criteria.length": "En az 8 karakter",
  "auth:password.criteria.uppercase": "En az bir büyük harf",
  "auth:password.criteria.lowercase": "En az bir küçük harf",
  "auth:password.criteria.number": "En az bir rakam",
  "auth:password.criteria.special": "En az bir özel karakter",

  // Common auth validation
  "common:auth:validation.username.min":
    "Kullanıcı adı en az 3 karakter olmalı",
  "common:auth:validation.email.invalid": "Geçerli bir e-posta adresi girin",
  "common:auth:validation.password.min": "Şifre en az 8 karakter olmalı",
  "common:auth:validation.password.uppercase": "En az bir büyük harf içermeli",
  "common:auth:validation.password.lowercase": "En az bir küçük harf içermeli",
  "common:auth:validation.password.number": "En az bir rakam içermeli",
  "common:auth:validation.password.special": "En az bir özel karakter içermeli",
  "common:auth:validation.terms": "Hizmet şartlarını kabul etmelisiniz",
  "common:auth:validation.password.match": "Şifreler eşleşmiyor",

  // Register form fields
  "common:auth:username": "Kullanıcı Adı",
  "common:auth:username.placeholder": "Kullanıcı adınızı seçin",
  "common:auth:email.placeholder": "E-posta adresinizi girin",
  "common:auth:email.description":
    "E-posta adresinizi asla kimseyle paylaşmayız",
  "common:auth:password.create": "Şifre oluşturun",
  "common:auth:password.show": "Şifreyi göster",
  "common:auth:password.confirm": "Şifreyi Onayla",
  "common:auth:password.confirm.placeholder": "Şifrenizi onaylayın",

  // Register form additional text
  "auth:terms.agree": "Hizmet Şartları ve Gizlilik Politikasını kabul ediyorum",
  "auth:captcha.protected": "Bu form reCAPTCHA ile korunmaktadır",
  "auth:register.create": "Hesap oluştur",
  "auth:register.haveAccount": "Zaten hesabınız var mı?",
  "auth:login.noAccount": "Hesabınız yok mu?",

  // Common namespace with colon-separated keys
  "common:back.home": "Ana Sayfaya Dön",

  // Nav strings
  "nav:home": "Ana Sayfa",
  "nav:coins": "Coinler",
  "nav:idos": "IDO'lar",
  "nav:portfolio": "Portföy",
  "nav:profile": "Profil",
  "nav:login": "Giriş Yap",
  "nav:register": "Kayıt Ol",
  "nav:trending": "Trendler",
  "nav:favorites": "Favoriler",
  "nav:watchlist": "İzleme Listesi",

  // Navigation namespace
  navigation: {
    pricing: "Fiyatlandırma",
    goToApp: "Uygulamaya Git",
    Pricing: "Fiyatlandırma",
    Documentation: "Dokümantasyon",
    goToHomepage: "Ana Sayfaya Git",
    coinScoutAlt: "CoinScout - AI Destekli Kripto Analizi",
    login: "Giriş Yap",
    signUp: "Kayıt Ol",
    membershipManagement: "Üyelik Yönetimi",
  },

  // Navigation colon-separated keys for compatibility
  "navigation:pricing": "Fiyatlandırma",
  "navigation:goToApp": "Uygulamaya Git",
  "navigation:Pricing": "Fiyatlandırma",
  "navigation:Documentation": "Dokümantasyon",
  "navigation:goToHomepage": "Ana Sayfaya Git",
  "navigation:coinScoutAlt": "CoinScout - AI Destekli Kripto Analizi",
  "navigation:login": "Giriş Yap",
  "navigation:signUp": "Kayıt Ol",
  "navigation:membershipManagement": "Üyelik Yönetimi",

  // System namespace
  system: {
    language: {
      selector: {
        title: "Dil Seçin",
        label: "Dil",
        available: "Mevcut Diller",
      },
    },
    auth: {
      required: "Coin özetini görüntülemek için giriş yapmanız gerekli",
      loginButton: "Giriş Yap",
    },
    subscription: {
      filterRestriction: {
        title: "Abonelik Gerekli",
        message:
          "Filtreleri kullanmak için en az Basic pakete sahip olmanız gerekiyor. Lütfen abonelik planınızı yükseltin.",
      },
    },
  },

  // System colon-separated keys for compatibility
  "system:language.selector.title": "Dil Seçin",
  "system:language.selector.label": "Dil",
  "system:language.selector.available": "Mevcut Diller",
  "system:auth.required":
    "Coin özetini görüntülemek için giriş yapmanız gerekli",
  "system:auth.loginButton": "Giriş Yap",

  // Sidebar namespace
  sidebar: {
    home: "Ana Sayfa",
    coins: "Coinler",
    topMovers: "En Çok Hareket Edenler",
    watchlist: "İzleme Listesi",
    aiPortfolio: "AI Portföyü",
    portfolioCheckup: "Portföy Kontrolü",
    compareCoins: "Coinleri Karşılaştır",
    upcomingIDOs: "Yaklaşan IDO'lar",
    aiAssistant: "AI Asistanı",
    airdrops: "Airdrop'lar",
    gemScout: "Gem Scout",
    soon: "Yakında",
    cryptoRating: "Kripto Derecelendirme",
    idoRating: "IDO Derecelendirme",
    recentListings: "Son Listelenenler",
    portfolioAudit: "Portföy Denetimi",
    launchpads: "Launchpad'ler",
    lock: "Kenar Çubuğunu Kilitle",
  },

  // Highlights - separate string values for each key
  topGainers: "En Çok Yükselenler",
  newListings: "Yeni Listeler",
  upcomingIDOs: "Yaklaşan IDO'lar",
  score: "Skor",

  // Main coinlist translations
  aiPoweredTitle: "AI Destekli ve Veri Odaklı Kripto Temel Derecelendirmeleri",
  highlights: "Öne Çıkanlar",
  nextDataUpdate: "Sonraki Veri Güncellemesi",
  allCoins: "Tüm Coinler",
  coinDetailDescription: "Detaylı analiz için herhangi bir coin'e tıklayın",
  filtersButton: "Filtreler",
  alertsTitle: "Alarmlar",

  // Table column headers
  name: "İsim",
  tokenomics: "Tokenomik",
  security: "Güvenlik",
  social: "Sosyal",
  market: "Piyasa",
  insights: "Öngörüler",
  totalScore: "Toplam Skor",
  sevenDayChange: "7G Değişim",

  // Score rating colon-separated keys for compatibility
  "score:excellent": "Mükemmel",
  "score:positive": "Pozitif",
  "score:average": "Ortalama",
  "score:weak": "Zayıf",
  "score:critical": "Kritik",

  // Alerts namespace
  alerts: {
    price: "Fiyat",
    currentPrice: "Mevcut Fiyat",
    priceGoesAbove: "Fiyat şunun üzerine çıktığında",
    priceGoesBelow: "Fiyat şunun altına düştüğünde",
    title: "Kripto Alarmları",
    description:
      "Fiyat değişiklikleri veya AI skor güncellemeleri için bildirim alın",
    createNewAlert: "Yeni Alarm Oluştur",
    activeAlerts: "Aktif Alarmlar",
    notifications: "Bildirimler",
    aiScore: "AI Skor",
    coin: "Coin",
    priceAbove: "Fiyat şunun üzerine çıktığında",
    priceBelow: "Fiyat şunun altına düştüğünde",
    aiScoreAbove: "AI skoru belirlediğiniz eşiği aştığında bildirim alın",
    aiScoreBelow:
      "AI skoru belirlediğiniz eşiğin altına düştüğünde bildirim alın",
    priceAboveDesc: "Fiyat belirlediğiniz eşiği aştığında bildirim alın",
    priceBelowDesc:
      "Fiyat belirlediğiniz eşiğin altına düştüğünde bildirim alın",
    selectCoin: "Coin seçin...",
    targetPriceAbove: "Hedef Fiyat Üstü",
    targetPriceBelow: "Hedef Fiyat Altı",
    enterUpperTargetPrice: "Üst hedef fiyatı girin",
    enterLowerTargetPrice: "Alt hedef fiyatı girin",
    notificationType: "Bildirim Türü",
    browserNotification: "Tarayıcı Bildirimi",
    cancel: "İptal",
    save: "Kaydet",
    back: "Geri",
  },

  marketData: {
    priceChange: "Fiyat Değişimi",
    priceMovement: "Fiyat Hareketi",
    marketCap: "Piyasa Değeri",
    fullyDilute: "Tam Seyreltilmiş",
    fdv: "TSV",
    tradeVolume24h: "24s Hacim",
    volumeMarketCapRatio: "Hacim/Piyasa Değeri Oranı",
    marketCapDetails: "Piyasa Değeri Detayları",
    marketCapRank: "Piyasa Değeri Sıralaması",
    currentRank: "Mevcut sıralama",
    updatedHourly: "Birden fazla borsadan saatlik güncellenir",
  },

  // Top Movers namespace
  topMovers: {
    title: "En Çok Hareket Edenler",
    description:
      "Son 7 günde en yüksek skor artışı gösteren kripto paraları görüntüleyin",
    allCoins: "Tüm Coinler",
    clickForAnalysis: "Detaylı analiz için herhangi bir coine tıklayın",
    searchPlaceholder: "Coin ara...",
    filters: "Filtreler",
  },

  // Filters namespace
  filters: {
    title: "Filtre Seçenekleri",
    description: "Gelişmiş filtreleme seçenekleriyle görünümünüzü özelleştirin",
    marketCapRange: "Piyasa Değeri Aralığı (Milyon)",
    projectScoreRange: "Proje Skor Aralığı",
    categories: "Kategoriler",
    chains: "Zincirler",
    selectCategories: "Kategorileri seçin",
    selectChains: "Zincirleri seçin",
    listingDate: "Listeleme Tarihi",
    chainEcosystem: "Zincir / Ekosistem",
    moveSliderToAdjust: "Aralığı ayarlamak için kaydırıcıyı hareket ettirin:",
    applyFilters: "Filtreleri Uygula",
    resetFilters: "Filtreleri Sıfırla",
    loading: "Yükleniyor...",
    noCategoriesFound: "Kategori bulunamadı",
    noChainsFound: "Zincir bulunamadı",
    cancel: "İptal",
    loginRequired: "Filtreleri kullanmak için önce giriş yapmanız gerekiyor.",
    subscriptionRequired:
      "Filtreleri kullanmak için en az Basic pakete sahip olmanız gerekiyor. Lütfen abonelik planınızı yükseltin.",
    login: "Giriş Yap",
    upgradePlan: "Paket Yükselt",
  },

  // Newly Listed Coins namespace
  newlyListed: {
    title: "Yeni Listelenen Coinler",
    description: "Yeni listelenen tüm kripto paraları keşfedin",
    cardTitle: "Yeni Listelenen Coinler",
    clickForAnalysis: "Detaylı analiz için herhangi bir coine tıklayın",
    searchPlaceholder: "Coin ara...",
    selectTimeframe: "Zaman dilimi seçin",
    last24Hours: "Son 24 Saat",
    last7Days: "Son 7 Gün",
    last14Days: "Son 14 Gün",
    last30Days: "Son 30 Gün",
    last90Days: "Son 90 Gün",
    loading: "Yeni listelenen coinler yükleniyor...",
    noCoinsFound: "Yeni listelenen coin bulunamadı.",
    filters: "Filtreler",
  },

  // Coin Age namespace
  coinAge: {
    comingSoon: "Yakında",
    comingInDays: "{days} gün sonra",
    listedToday: "Bugün listelendi",
    oneDayAgo: "1 gün önce",
    daysAgo: "{days} gün önce",
  },

  // Watchlist namespace
  watchlist: {
    add: "İzleme Listesine Ekle",
    addTowatchlist: "İzleme Listesine Ekle",
    addToWatchlist: "İzleme Listesine Ekle",
    inWatchlist: "İzleme Listesinde",
    watchers: "Takipçi",
    alerts: "Alarmlar",
    share: "Paylaş",
    advancedView: "Gelişmiş Görünüm",
    enable: "Etkinleştir",
    disable: "Devre Dışı Bırak",
    enableAdvancedView: "Gelişmiş görünümü etkinleştir",
    disableAdvancedView: "Gelişmiş görünümü devre dışı bırak",
    favorites: "Favoriler",
    addToFavorites: "Kripto paraları favorilerinize ekleyin",
    error: "Hata",
    success: "Başarılı",
    noWatchlistSelected: "İzleme listesi seçilmedi",
    removedFromWatchlist: "İzleme listesinden çıkarıldı",
    coinRemovedFromWatchlist: "{coinName} izleme listesinden çıkarıldı",
    failedToRemoveCoin: "Coin izleme listesinden çıkarılamadı",
    removeFromWatchlist: "İzleme listesinden çıkar",
    removeCoinFromWatchlist: "{coinName} izleme listesinden çıkar",
    title: "İzleme Listeleri",
    description:
      "Favori kripto paralarınızı takip edin ve gerçek zamanlı güncellemeler alın",
    portfolioScorePerformance: "Portföy Skor Performansı",
    createNewWatchlist: "Yeni İzleme Listesi Oluştur",
    editWatchlist: "İzleme Listesini Düzenle",
    deleteWatchlist: "İzleme Listesini Sil",
    confirmDelete: "Bu izleme listesini silmek istediğinizden emin misiniz?",
    confirmDeleteDescription:
      "Bu işlem geri alınamaz. Bu işlem izleme listenizi ve tüm içeriğini kalıcı olarak silecektir.",
    cancel: "İptal",
    delete: "Sil",
    shareDescription: "İzleme listenizi arkadaşlarınız ve toplulukla paylaşın",
    copyLink: "Bağlantıyı Kopyala",
    linkCopied: "Bağlantı panoya kopyalandı!",
    watchlistName: "İzleme Listesi Adı",
    watchlistDescription: "İzleme Listesi Açıklaması (Opsiyonel)",
    enterWatchlistName: "İzleme listesi adını girin",
    enterDescription: "İzleme listeniz için açıklama girin",
    createDescription:
      "Favori kripto paralarınızı organize etmek için yeni bir izleme listesi oluşturun",
    editDescription: "İzleme listesi detaylarınızı güncelleyin",
    create: "Oluştur",
    save: "Değişiklikleri Kaydet",
    searchPlaceholder: "İzleme listesinde coin ara...",
    noCoinsFound: "Bu izleme listesinde coin bulunamadı",
    addFirstCoin: "Başlamak için ilk coininizi ekleyin",
    sortBy: "Sırala",
    viewMode: "Görünüm Modu",
    displayMode: "Gösterim Modu",
    simple: "Basit",
    advanced: "Gelişmiş",
    grid: "Izgara",
    list: "Liste",
    untitled: "İsimsiz İzleme Listesi",
    preview: "Önizleme",
    shareNote:
      "Not: Bu bağlantıyı paylaşarak izleme listenizi herkese açık hale getirirsiniz",
    aiPoweredTitle: "AI Destekli ve Veri Odaklı Kripto Derecelendirmeleri",
    trackDescription:
      "Favori kripto paralarınızı takip edin ve gerçek zamanlı güncellemeler alın",
    coins: "Coinler",
    idos: "IDO'lar",
  },

  notifications: {
    title: "Bildirimler",
  },

  coinDetail: {
    overallCategoriesMetricsBreakdown: "Genel Kategori Metrik Dağılımları",
    categories: "Kategoriler",
    backToPreviousPage: "Önceki Sayfaya Dön",
    rankPrefix: "Sıralama #",
    nextUnlock: "Sonraki Kilit Açma",
    unlockInDays: "{days} gün içinde kilit açma",
    about: "Hakkında",
    marketStatistics: "Piyasa İstatistikleri",
    tokenSupplyDynamics: "Token Tedarik Dinamikleri",
    maxSupply: "Maksimum Tedarik",
    totalSupply: "Toplam Tedarik",
    circulatingSupply: "Dolaşımdaki Tedarik",
    burned: "Yakılan",
    locked: "Kilitli",
    lockedPercentage: "Kilitli Yüzde",
    supplyRatioNA: "Tedarik Oranı Mevcut Değil",
    allTimeHigh: "Tüm Zamanların Yüksek",
    allTimeLow: "Tüm Zamanların Düşük",
    marketCap: "Piyasa Değeri",
    volume: "Hacim",
    allTimeHighLow: "Tüm Zamanların İstatistikleri",
    fromATH: "En Yüksekten Şimdiye",
    fromATL: "En Düşükten Şimdiye",
  },

  priceChart: {
    title: "Fiyat Grafiği",
    current: "Mevcut",
    high: "Yüksek",
    low: "Düşük",
    sevenDays: "7 Gün",
    fourteenDays: "14 Gün",
    thirtyDays: "30 Gün",
    ninetyDays: "90 Gün",
    oneYear: "1 Yıl",
    threeYears: "3 Yıl",
    fiveYears: "5 Yıl",
    allTime: "Tüm Zamanlar",
  },

  scoreChart: {
    title: "Skor Grafiği",
    current: "Mevcut",
    high: "Yüksek",
    low: "Düşük",
    oneMonth: "1 Ay",
    threeMonths: "3 Ay",
    sixMonths: "6 Ay",
    oneYear: "1 Yıl",
    threeYears: "3 Yıl",
    fiveYears: "5 Yıl",
  },

  externalLinks: {
    title: "Dış Bağlantılar",
  },

  format: {
    trillion: "Tr",
    billion: "M",
    million: "Mn",
    thousand: "B",
  },

  // Price Changes section
  priceChanges: {
    title: "Fiyat Değişimleri",
    "1d": "1 Gün",
    "1day": "1 Gün",
    "1w": "1 Hafta",
    "1week": "1 Hafta",
    "1m": "1 Ay",
    "1month": "1 Ay",
    "3m": "3 Ay",
    "3months": "3 Ay",
    "6m": "6 Ay",
    "1y": "1 Yıl",
    "1year": "1 Yıl",
  },

  // Tokenomics namespace
  tokenomicsNamespace: {
    metricsBreakdown: "Tokenomik Metrik Dağılımları",
    metricsBreakdownGeneric: "Metrik Dağılımları",
    weight: "Ağırlık",
    requestFeature: "Öneride Bulun",
    reportError: "Hata Bildir",
  },

  // Coin Health Score
  coinHealth: {
    title: "Coin Sağlık Skoru",
    scoreRange: "Skor Aralığı",
    critical: "Kritik",
    average: "Ortalama",
    excellent: "Mükemmel",
    positive: "Pozitif",
  },

  // Score ratings namespace
  scoreRatings: {
    excellent: "Mükemmel",
    positive: "Pozitif",
    average: "Ortalama",
    weak: "Zayıf",
    critical: "Kritik",
  },

  // Methodology namespace
  methodology: {
    whatAreWeScoring: "Neyi Puanlıyoruz?",
    whyIsThisImportant: "Bu Neden Önemli?",
  },

  // Homepage namespace
  homepage: {
    // Hero section
    hero: {
      badge: "AI Destekli Kripto Analiz Platformu",
      title: "Gelişmiş AI Destekli Kripto Para Analizi",
      titleHighlight: "Platformu",
      description:
        "Kapsamlı AI destekli platformumuzla kripto para analizinin geleceğini keşfedin. Detaylı görüşler, doğru puanlar ve daha akıllı yatırım kararları için veri odaklı öneriler alın.",
      ctaButton: "Analize Başla",
      stats: {
        cryptocurrencies: "Kripto Paralar",
        analysisMetrics: "Analiz Metrikleri",
        moreAccuracy: "Daha Fazla Doğruluk",
      },
    },

    // Features section
    features: {
      title: "Her Kripto Yatırımcısı için Güçlü Özellikler",
      subtitle:
        "Bilinçli kripto para yatırım kararları vermenize yardımcı olacak kapsamlı araçlar ve AI destekli görüşler.",
      comprehensiveScoring: "Kapsamlı AI puanlama sistemi",
      aiRating: {
        title: "AI Geliştirilmiş Skor Analizi",
        description:
          "Gelişmiş makine öğrenimi algoritmaları, doğru kripto para derecelendirmeleri ve yatırım görüşleri sağlamak için birden fazla veri noktasını analiz eder.",
        bullets: [
          "40+ metrik boyunca gerçek zamanlı AI puanlaması",
          "Makine öğrenimi tahmin modelleri",
        ],
      },
      idoRating: {
        title: "IDO Derecelendirme Sistemi",
        description:
          "AI destekli risk değerlendirmesi ve potansiyel değerlendirmesi ile kapsamlı İlk DEX Arzı analizi.",
        bullets: [
          "Lansmanöncesi proje değerlendirmesi",
          "Takım ve tokenomik analizi",
          "Lansman potansiyeli puanlaması",
        ],
      },
      compareCoins: {
        title: "Gelişmiş Coin Karşılaştırması",
        description:
          "Detaylı metrikler ve AI üretilmiş görüşlerle kripto paraların yan yana karşılaştırılması.",
        bullets: [
          "Çoklu metrik karşılaştırma panosu",
          "AI destekli benzerlik analizi",
          "Risk-getiri değerlendirmesi",
        ],
      },
      portfolioGenerator: {
        title: "Akıllı Portföy Üreticisi",
        description:
          "Risk toleransınıza ve yatırım hedeflerinize göre AI destekli portföy optimizasyonu.",
        bullets: [
          "Otomatik portföy oluşturma",
          "Risk ayarlı tahsisler",
          "Çeşitlendirme optimizasyonu",
        ],
      },
      portfolioAnalysis: {
        title: "Portföy Sağlık Analizi",
        description:
          "Optimizasyon önerileriyle mevcut portföyünüzün kapsamlı analizi.",
        bullets: [
          "Performans takibi",
          "Yeniden dengeleme önerileri",
          "Risk maruziyeti analizi",
        ],
      },
      launchpads: {
        title: "Launchpad Entegrasyonu",
        description:
          "Seçilmiş proje seçimleriyle en iyi kripto para launchpad'lerine doğrudan erişim.",
        bullets: [
          "Doğrulanmış launchpad projeleri",
          "Erken erişim fırsatları",
          "Durum tespiti raporları",
        ],
      },
      aiAssistant: {
        title: "AI Ticaret Asistanı",
        description:
          "Yatırım kararlarınıza rehberlik etmek için gelişmiş AI ile desteklenen akıllı ticaret asistanı.",
        bullets: [
          "Gerçek zamanlı pazar görüşleri",
          "Kişiselleştirilmiş öneriler",
          "Ticaret sinyali analizi",
        ],
      },
      airdropScore: {
        title: "Airdrop Skor Analizi",
        description:
          "AI destekli puanlama ve uygunluk değerlendirmesi ile airdrop fırsatlarını değerlendirin.",
        bullets: [
          "Airdrop potansiyeli puanlaması",
          "Uygunluk gereksinimleri",
          "Geçmiş başarı oranları",
        ],
      },
      gemScout: {
        title: "Gem Scout Keşfi",
        description:
          "Gelişmiş AI desen tanıma ve pazar analizi kullanarak gizli kripto para değerlerini keşfedin.",
        bullets: [
          "Erken aşama proje keşfi",
          "Pazar desen analizi",
          "Büyüme potansiyeli puanlaması",
        ],
      },
    },

    // Badges
    badges: {
      betaTestingLive: "Beta Test Canlı",
      betaTestingSoon: "Beta Test Yakında",
      comingSoon: "Yakında",
    },

    // Trusted By section
    trustedBy: {
      title: "Sektör Liderleri Tarafından Güvenilen",
      description:
        "Platformumuz, en doğru ve kapsamlı analizi sunmak için önde gelen kripto para veri sağlayıcıları ve blockchain gezginleriyle entegre olur.",
      stats: {
        coinsAnalyzed: "Analiz Edilen Coinler",
        dataPoints: "Veri Noktaları",
        apiCalls: "API Çağrıları",
        dataSources: "Veri Kaynakları",
      },
      features: {
        aiPowered: "AI Destekli Analiz",
        multiLayer: "Çok Katmanlı Doğrulama",
        enterprise: "Kurumsal Seviye",
      },
    },

    // Intelligence section
    intelligence: {
      title: "AI Destekli Zeka",
      subtitle:
        "Gerçek zamanlı görüşler ve tahmin modellemesi ile kripto para analizinde yapay zekanın gücünü deneyimleyin.",
    },

    // Call to Action
    callToAction: {
      primary:
        "Gerçek zamanlı görüşler ve tahmin modellemesi ile kripto para analizinde yapay zekanın gücünü deneyimleyin.",
      secondary: "AI destekli analizimize güvenen binlerce yatırımcıya katılın",
    },

    // Benefits
    benefits: {
      "0": {
        title: "Gerçek Zamanlı Analiz",
      },
      "1": {
        title: "AI Destekli Görüşler",
      },
      "2": {
        title: "Risk Değerlendirmesi",
      },
    },

    // Buttons
    buttons: {
      getStarted: "Başlayın",
      viewAllQuestions: "Tüm Soruları Görüntüle",
    },

    // FAQ section
    faq: {
      title: "Sıkça Sorulan Sorular",
      questions: {
        "0": {
          question:
            "AI destekli kripto para derecelendirmeleri ne kadar doğru?",
          answer:
            "AI modellerimiz, teknik göstergeler, temel analiz, sosyal duyarlılık ve birden fazla kaynaktan pazar verisi dahil olmak üzere 40+ metriği analiz ederek trend tahmininde %85'in üzerinde doğruluk elde eder.",
        },
        "1": {
          question:
            "CoinScout'u diğer kripto analiz platformlarından farklı kılan nedir?",
          answer:
            "CoinScout, gelişmiş AI algoritmalarını birden fazla kaynaktan gerçek zamanlı verilerle birleştirerek, temel fiyat takibinin ötesine geçerek tokenomik, takım değerlendirmesi ve pazar duyarlılığı analizini içeren kapsamlı analiz sağlar.",
        },
        "2": {
          question:
            "Platform hem yeni başlayanlar hem de deneyimli tüccarlar için uygun mu?",
          answer:
            "Evet, platformumuz birden fazla karmaşıklık seviyesi ile tasarlanmıştır. Yeni başlayanlar basitleştirilmiş görünümler ve AI önerilerini kullanabilirken, deneyimli tüccarlar detaylı metriklere, özel analiz araçlarına ve gelişmiş karşılaştırma özelliklerine erişebilir.",
        },
      },
    },

    // Testimonials
    testimonials: {
      title: "Kullanıcılarımız Ne Diyor",
      "0": {
        text: "CoinScout'un AI analizi, başka türlü kaçıracağım karlı fırsatları belirlememe yardımcı oldu. Kapsamlı puanlama sistemi inanılmaz derecede doğru.",
      },
    },
  },

  // Error namespace
  error: {
    somethingWentWrong: "Bir şeyler ters gitti",
    refreshPage: "Sayfayı Yenile",
    goToHome: "Ana Sayfaya Git",
    clearErrorLogs: "Hata Kayıtlarını Temizle",
  },

  // Footer namespace
  footer: {
    description:
      "Akıllı yatırım kararları için kapsamlı görüşler, derecelendirmeler ve veri odaklı öneriler sağlayan gelişmiş AI destekli kripto para analiz platformu.",
    allRightsReserved: "Tüm hakları saklıdır.",
    categories: {
      product: "Ürün",
      learn: "Öğren",
      community: "Topluluk",
      legal: "Yasal",
    },
    links: {
      cryptoRatings: "Kripto Derecelendirmeleri",
      idoRatings: "IDO Derecelendirmeleri",
      aiPortfolioStrategist: "AI Portföy Stratejisti",
      aiPortfolioCheckup: "AI Portföy Kontrolü",
      compareCoins: "Coinleri Karşılaştır",
      recentListings: "Son Listelenenler",
      topMovers: "En Çok Hareket Edenler",
      airdropsHub: "Airdrop Merkezi",
      scoutAI: "Scout AI",
      aiAssistant: "AI Asistanı",
      pricing: "Fiyatlandırma",
      academy: "Akademi",
      documentation: "Dokümantasyon",
      blog: "Blog",
      faq: "SSS",
      forum: "Forum",
      telegram: "Telegram",
      discord: "Discord",
      twitter: "Twitter",
      publicPortfolios: "Herkese Açık Portföyler",
      communityGuidelines: "Topluluk Kuralları",
      userTestimonials: "Kullanıcı Yorumları",
      privacyPolicy: "Gizlilik Politikası",
      termsOfService: "Hizmet Şartları",
      cookiePolicy: "Çerez Politikası",
      disclaimer: "Sorumluluk Reddi",
      advertisingPolicy: "Reklam Politikası",
      careers: "Kariyer",
      soon: "Yakında",
    },
  },

  // Footer colon-separated keys for compatibility
  "footer:description":
    "Akıllı yatırım kararları için kapsamlı görüşler, derecelendirmeler ve veri odaklı öneriler sağlayan gelişmiş AI destekli kripto para analiz platformu.",
  "footer:allRightsReserved": "Tüm hakları saklıdır.",
  "footer:categories.product": "Ürün",
  "footer:categories.learn": "Öğren",
  "footer:categories.community": "Topluluk",
  "footer:categories.legal": "Yasal",
  "footer:links.cryptoRatings": "Kripto Derecelendirmeleri",
  "footer:links.idoRatings": "IDO Derecelendirmeleri",
  "footer:links.aiPortfolioStrategist": "AI Portföy Stratejisti",
  "footer:links.aiPortfolioCheckup": "AI Portföy Kontrolü",
  "footer:links.compareCoins": "Coinleri Karşılaştır",
  "footer:links.recentListings": "Son Listelenenler",
  "footer:links.topMovers": "En Çok Hareket Edenler",
  "footer:links.airdropsHub": "Airdrop Merkezi",
  "footer:links.scoutAI": "Scout AI",
  "footer:links.aiAssistant": "AI Asistanı",
  "footer:links.pricing": "Fiyatlandırma",
  "footer:links.academy": "Akademi",
  "footer:links.documentation": "Dokümantasyon",
  "footer:links.blog": "Blog",
  "footer:links.faq": "SSS",
  "footer:links.forum": "Forum",
  "footer:links.telegram": "Telegram",
  "footer:links.discord": "Discord",
  "footer:links.twitter": "Twitter",
  "footer:links.publicPortfolios": "Herkese Açık Portföyler",
  "footer:links.communityGuidelines": "Topluluk Kuralları",
  "footer:links.userTestimonials": "Kullanıcı Yorumları",
  "footer:links.privacyPolicy": "Gizlilik Politikası",
  "footer:links.termsOfService": "Hizmet Şartları",
  "footer:links.cookiePolicy": "Çerez Politikası",
  "footer:links.disclaimer": "Sorumluluk Reddi",
  "footer:links.advertisingPolicy": "Reklam Politikası",
  "footer:links.careers": "Kariyer",
  "footer:links.soon": "Yakında",

  // Upcoming IDO translations
  upcoming: {
    title: "Yaklaşan Token Satışları",
    subtitle: "Lansmanından önce yeni tokenları keşfedin ve değerlendirin",
    search: "Yaklaşan token satışlarını ara...",
    noResults: "Yaklaşan token satışı bulunamadı",
    loading: "Yaklaşan token satışları yükleniyor...",
    error: "Yaklaşan token satışları yüklenirken hata oluştu",
    retryButton: "Tekrar Dene",
    tba: "Açıklanacak",
    rank: "Sıra #{number}",
    saleType: "Satış Türü",
    points: "puan",
    tokenomics: "Tokenomik",
    security: "Güvenlik",
    social: "Sosyal",
    market: "Pazar",
    insights: "Analiz",
    totalAiScore: "Toplam AI Skoru",
    filters: {
      title: "Filtreler",
      description:
        "Yaklaşan token satışlarını çeşitli kriterlere göre filtreleyin",
      projectScore: "Proje Skoru",
      saleType: "Satış Türü",
      category: "Kategori",
      blockchain: "Blokzincir",
      allTypes: "Tüm Türler",
      allCategories: "Tüm Kategoriler",
      allBlockchains: "Tüm Blokzincirler",
      searchCategories: "Kategorileri ara...",
      searchChains: "Blokzincirleri ara...",
      selectDateRange: "Tarih aralığı seçin",
      last24Hours: "Son 24 Saat",
      last7Days: "Son 7 Gün",
      last14Days: "Son 14 Gün",
      last30Days: "Son 30 Gün",
      last90Days: "Son 90 Gün",
      reset: "Filtreleri Sıfırla",
      apply: "Filtreleri Uygula",
    },
    table: {
      name: "İsim",
      date: "Lansman Tarihi",
      launchDate: "Lansman Tarihi",
      initialCap: "Başlangıç Limitı",
      totalRaised: "Toplam Toplanan",
      score: "Skor",
      actions: "İşlemler",
      imcScore: "Piyasa Değeri",
      fundingScore: "Finansman",
      launchpadScore: "Launchpad",
      investorScore: "Yatırımcılar",
      socialScore: "Sosyal",
      totalAiScore: "Toplam AI Skoru",
    },
    tooltips: {
      rank: {
        title: "Sıralama",
        description: "Mevcut sıralama düzenine göre proje sıralaması.",
      },
      watchlist: {
        title: "İzleme Listesi",
        description: "Projeleri kişisel izleme listenize ekleyin veya çıkarın.",
      },
      projectName: {
        title: "Proje Adı",
        description: "Kripto para projesinin resmi adı ve sembol kısaltması.",
      },
      launchDate: {
        title: "Lansman Tarihi",
        description:
          "Token'ın halka açık lansmanı ve borsalarda listelenmesi için planlanan veya onaylanmış tarih.",
      },
      initialMarketCap: {
        title: "Başlangıç Piyasa Değeri",
        description:
          "Başlangıç Piyasa Değeri metriği, token arzı, başlangıç fiyatı ve piyasa koşullarını dikkate alarak token lansmanındaki öngörülen piyasa değerini değerlendirir.",
      },
      financing: {
        title: "Finansman Analizi",
        description:
          "Finansman Skoru metriği projenin finansman geçmişini, yatırımcı kalitesini ve finansal sürdürülebilirliğini analiz eder.",
      },
      launchpad: {
        title: "Launchpad Analizi",
        description:
          "Launchpad Skoru metriği token satışını gerçekleştiren platformun itibarını ve başarı geçmişini değerlendirir.",
      },
      investors: {
        title: "Yatırımcı Analizi",
        description:
          "Yatırımcı Skoru metriği destekleyen yatırımcıların ve girişim sermayesi firmalarının kalitesini ve itibarını değerlendirir.",
      },
      socialMedia: {
        title: "Sosyal Medya Analizi",
        description:
          "Sosyal Skor metriği, projenin sosyal medya varlığındaki takipçi sayıları, etkileşim oranları ve büyüme trendlerini analiz ederek topluluk ilgisini ölçer.",
      },
      coinScoutAiScore: {
        title: "CoinScout AI Skoru",
        description:
          "Tüm puanlama metriklerinden hesaplanan genel derecelendirme.",
      },
      projectDetails: {
        title: "Proje Detayları",
        description:
          "Bu proje için kapsamlı analiz ve detaylı metrikleri görüntüleyin.",
      },
    },
  },

  // Sale type translations
  saleType: {
    IDO: "IDO",
    IEO: "IEO",
    ICO: "ICO",
    SHO: "SHO",
    Seed: "Tohum",
    IGO: "IGO",
    ISO: "ISO",
  },

  // Common translations
  common: {
    searching: "Aranıyor...",
    cancel: "İptal",
    more: "daha fazla",
    selected: "seçili",
    loading: "Yükleniyor...",
    upcomingIdos: "Yaklaşan IDO'lar",
  },

  // Logs translations (for development)
  logs: {
    usingIdoWatchlists: "IDO izleme listeleri kullanılıyor:",
    fetchInitialDataStarted: "İlk veri çekme başladı",
    rawAPIData: "Ham API verisi:",
    apiDataLength: "API veri uzunluğu:",
    fetchInitialDataCompleted: "İlk veri çekme tamamlandı",
    firstRecordDetails: "İlk kayıt detayları:",
    imageUrlChecks: "Resim URL kontrolleri",
    allProjectsFromAPI: "API'den tüm projeler",
    usingDirectAPI: "Doğrudan API kullanılıyor",
    rawAPIDataFirst5: "Ham API verisi ilk 5:",
    firstRecordSocialScore: "İlk kayıt sosyal skoru:",
    clickedProjectInfo: "Tıklanan proje bilgisi:",
    redirectingWithDirectID: "Doğrudan ID ile yönlendiriliyor:",
  },

  // Empty state translations
  emptyState: {
    noData: "Veri mevcut değil",
    noDescription: "Açıklama mevcut değil",
    noTeamInfo: "Takım bilgisi mevcut değil",
    noFundingInfo: "Fonlama bilgisi mevcut değil",
    noTokenomics: "Tokenomik verisi mevcut değil",
    noPriceAnalysis: "Fiyat analizi mevcut değil",
    noProjectDetails: "Proje detayları mevcut değil",
    noInvestorInfo: "Yatırımcı bilgisi mevcut değil",
  },

  // IDO feature request translations
  ido: {
    featureRequest: {
      title: "Özellik Talep Et",
      description: "CoinScout'ta görmek istediğiniz özelliği bize söyleyin.",
    },
    noLaunchpadInfo: "Launchpad Bilgisi Yok",
  },

  // Common translations with colon-separated keys for compatibility
  "common:logs.firstRecordSocialScore": "İlk kayıt sosyal skoru:",
  "common:logs.clickedProjectInfo": "Tıklanan proje bilgisi:",
  "common:logs.redirectingWithDirectID": "Doğrudan ID ile yönlendiriliyor:",
  "common:emptyState.noData": "Veri mevcut değil",
  "common:emptyState.noInvestorInfo": "Yatırımcı bilgisi mevcut değil",
};
