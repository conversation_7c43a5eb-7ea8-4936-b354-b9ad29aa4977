/**
 * Portuguese localization strings
 */
const pt = {
  // Common strings
  "common": {
    "loading": "Carregando...",
    "error": "Erro",
    "retry": "Tentar novamente",
    "save": "<PERSON><PERSON>",
    "cancel": "<PERSON>celar",
    "close": "<PERSON><PERSON><PERSON>",
    "success": "Sucesso",
    "viewMore": "Ver mais",
    "back": "Voltar",
    "next": "Próximo",
    "search": "Buscar",
    "searchCoinsAndTokens": "Buscar por Moedas & Projetos Futuros",
    "searching": "Buscando...",
    "noResults": "Nenhum resultado encontrado",
    "coins": "Moedas",
    "upcomingIdos": "Próximos IDOs",
  },

  // Nav related strings
  "nav": {
    "home": "Início",
    "portfolio": "Portfólio",
    "explore": "Explorar",
    "news": "Notícias",
    "learn": "Aprender",
    "profile": "Perfil",
    "settings": "Configurações",
    "logout": "Sair",
    "login": "Entrar",
    "register": "Registrar",
    "trending": "Em alta",
    "favorites": "Favoritos",
    "watchlist": "Lista de observação",
  },

  // System related strings
  "system": {
    "choose": "Escolher idioma",
    "current": "Idioma atual",
    "searching": "Buscando...",
  },

  // Error related strings
  "error": {
    "criticalError": "Erro Crítico",
    "somethingWentWrong": "Algo deu errado",
    "criticalErrorMessage": "Ocorreu um erro crítico. Por favor, atualize a página ou retorne à página inicial.",
    "returnToHome": "Voltar ao Início",
    "multipleErrorsDetected": "Múltiplos Erros Detectados",
    "unexpectedError": "Ocorreu um erro inesperado",
    "refreshPage": "Atualizar Página",
    "goToHome": "Ir ao Início",
    "clearErrorLogs": "Limpar Logs de Erro",
    "anErrorOccurred": "Ocorreu um erro",
  },

  // Data related strings
  "data": {
    "loading": "Carregando dados",
    "empty": "Sem dados disponíveis",
    "error": "Erro ao carregar dados",
  },

  // Auth related strings
  "auth": {
    "email": "E-mail",
    "emailPlaceholder": "Digite seu e-mail",
    "emailDescription": "Seu e-mail nunca será compartilhado com ninguém",
    "password": "Senha",
    "passwordPlaceholder": "Digite sua senha",
    "passwordCreate": "Criar senha",
    "passwordConfirm": "Confirmar senha",
    "passwordConfirmPlaceholder": "Confirme sua senha",
    "passwordShow": "Mostrar senha",
    "passwordHide": "Ocultar senha",
    "passwordStrength": "Força da senha",
    "passwordStrengthWeak": "Fraca",
    "passwordStrengthGood": "Boa",
    "passwordStrengthStrong": "Forte",
    "passwordResetMessage": "Recurso de redefinição de senha estará disponível em breve",
    "forgotPassword": "Esqueceu a senha?",
    "resetPassword": "Redefinir senha",
    "signin": "Entrar",
    "signinLoading": "Entrando...",
    "signinSecurely": "Entre com segurança",
    "signup": "Registrar",
    "signout": "Sair",
    "accountCreated": "Conta criada com sucesso",
    "passwordResetSent": "E-mail de redefinição de senha enviado",
    "invalidCredentials": "E-mail ou senha inválidos",
    "continueWith": "Continuar com",
    "username": "Nome de usuário",
    "usernamePlaceholder": "Escolha um nome de usuário",
    "agree": "Eu concordo com",
    "service": "Termos de Serviço",
    "and": "e",
    "privacy": "Política de Privacidade",
    "email.placeholder": "Digite seu e-mail",
    "email.description": "Seu e-mail nunca será compartilhado com ninguém",
    "password.placeholder": "Digite sua senha",
    "password.create": "Criar senha",
    "password.confirm": "Confirmar senha",
    "password.confirm.placeholder": "Confirme sua senha",
    "password.show": "Mostrar senha",
    "password.hide": "Ocultar senha",
    "password.strength": "Força da senha",
    "password.strength.weak": "Fraca",
    "password.strength.good": "Boa",
    "password.strength.strong": "Forte",
    "password.reset.message": "Recurso de redefinição de senha estará disponível em breve",
    "signin.loading": "Entrando...",
    "signin.securely": "Entre com segurança",
    "username.placeholder": "Escolha um nome de usuário",
    "password.criteria.length": "[MISSING] At least 8 characters",
    "password.criteria.uppercase": "[MISSING] At least one uppercase letter",
    "password.criteria.lowercase": "[MISSING] At least one lowercase letter",
    "password.criteria.number": "[MISSING] At least one number",
    "password.criteria.special": "[MISSING] At least one special character",
    "captcha.protected": "[MISSING] This form is protected by reCAPTCHA",
    "terms.service": "Termos de Serviço",
    "terms.and": "e",
    "terms.privacy": "Política de Privacidade",
  },

  // Login related strings
  "login": {
    "prompt": "Insira suas credenciais para entrar",
  },

  // Success related strings
  "success": {
    "title": "Sucesso",
    "description": "Login bem-sucedido",
  },

  // Register related strings
  "register": {
    "title": "Criar conta",
    "create": "Criar conta",
    "creating": "Criando conta...",
    "haveAccount": "Já tem uma conta?",
    "success": "Registro bem-sucedido",
    "successDetail": "Conta criada com sucesso",
    "successLogin": "Conta criada. Por favor, faça login.",
    "failed": "Falha no registro",
    "failedGeneric": "Ocorreu um erro durante o registro. Por favor, tente novamente.",
    "generic": "Ocorreu um erro. Por favor, tente novamente.",
    "success.detail": "[MISSING] Your account has been created successfully",
    "success.login": "[MISSING] Your account has been created, please login.",
    "failed.generic": "[MISSING] An error occurred during registration. Please try again.",
    "success.email_verify": "[MISSING] Registration successful. Please check your email to verify your account.",
  },

  // Form related strings
  "form": {
    "invalidFields": "Por favor, preencha todos os campos obrigatórios corretamente",
  },

  // Validation related strings
  "validation": {
    "email": "Por favor, insira um endereço de e-mail válido",
    "length": "A senha deve ter pelo menos 6 caracteres",
  },

  // Coinlist related strings
  "coinlist": {
    "allCoins": "Todas as moedas",
    "coinDetailDescription": "Clique em qualquer moeda para análise detalhada",
    "searchCoins": "Buscar moedas...",
    "ariaLabel": "Buscar moedas",
  },

  // Filters related strings
  "filters": {
    "title": "Opções de Filtro",
    "description": "Use estes filtros para encontrar tipos específicos de vendas de tokens que correspondam aos seus critérios de investimento.",
    "details": "Filtre por tipo de venda, plataforma de lançamento, categoria, blockchain ou investidores.",
    "action": "Tente selecionar um filtro para ver como ele atualiza a lista.",
  },

  // Columns related strings
  "columns": {
    "name": "Nome",
    "tokenomics": "Tokenomics",
    "security": "Segurança",
    "social": "Social",
    "market": "Mercado",
    "insights": "Insights",
    "totalScore": "Pontuação total de IA",
    "sevenDayChange": "Variação 7D",
    "price": "Preço",
  },

  // Highlights related strings
  "highlights": {
    "topGainers": "Moedas com Maior Ganho",
    "newListings": "Novas Listagens",
    "upcomingIDOs": "Próximos IDOs",
    "gemCoins": "Moedas Promissoras",
    "topAirdrops": "Melhores Airdrops",
    "score": "Pontuação",
  },

  // Footer related strings
  "footer": {
    "description": "Plataforma avançada de análise de criptomoedas e gestão de portfólio alimentada por inteligência artificial.",
    "bankGradeSecurity": "Segurança de Nível Bancário",
    "allRightsReserved": "Todos os direitos reservados",
    "product": "Produto",
    "learn": "Aprender",
    "community": "Comunidade",
    "legal": "Legal",
  },

  // Links related strings
  "links": {
    "privacyPolicy": "Política de Privacidade",
    "termsOfService": "Termos de Serviço",
    "cookiePolicy": "Política de Cookies",
    "disclaimer": "Isenção de Responsabilidade",
    "advertisingPolicy": "Política de Publicidade",
    "careers": "Carreiras",
    "soon": "em breve",
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "Buscar Moedas e Projetos Futuros",
    "goToHomepage": "Ir para a página inicial",
    "coinScoutAlt": "CoinScout",
    "pricing": "Preços",
    "goToApp": "Ir para o App",
    "login": "Entrar",
    "signUp": "Cadastrar",
    "profile": "Perfil",
    "membershipManagement": "Gestão de Assinatura",
    "feedback": "Feedback",
    "adminDashboard": "Painel Administrativo",
    "logout": "Sair",
    "premium": "Premium",
    "pro": "Pro",
    "free": "Gratuito",
  },

  // Watchlist related strings
  "watchlist": {
    "title": "Sua Lista de Observação está vazia",
    "description": "Você ainda não tem nenhuma lista de observação. Crie uma lista de observação para acompanhar suas criptomoedas.",
    "createWatchlist": "Criar Lista de Observação",
    "addCoins": "Adicionar Moedas",
  },

  // SaleType related strings
  "saleType": {
    "IDO": "Oferta Inicial DEX - Venda de tokens realizada em uma exchange descentralizada (DEX)",
    "IEO": "Oferta Inicial de Exchange - Venda de tokens hospedada em uma exchange de criptomoedas centralizada",
    "ICO": "Oferta Inicial de Moeda - Primeira etapa de captação de recursos onde novos projetos oferecem tokens para investidores iniciais",
    "SHO": "Oferta de Detentor Forte - Venda de tokens que dá prioridade aos detentores de tokens de longo prazo",
    "Seed": "Rodada Semente - Rodada de financiamento privado inicial antes da venda pública",
    "IGO": "Oferta Inicial de Jogo - Captação de recursos focada em projetos de jogos blockchain",
    "ISO": "Oferta Inicial de Staking - Distribuição de tokens através do mecanismo de staking",
  },

  // ListingDate related strings
  "listingDate": {
    "24hours": "Últimas 24 Horas",
    "7days": "Últimos 7 Dias",
    "14days": "Últimos 14 Dias",
    "30days": "Últimos 30 Dias",
    "90days": "Últimos 90 Dias",
  },

  // UpcomingTour related strings
  "upcomingTour": {
    "title": "Bem-vindo aos IDOs Futuros",
    "description": "Gostaria de um tour rápido dos recursos dos IDOs Futuros?",
    "info": "Aprenda como filtrar vendas de tokens futuras, entender os detalhes do projeto e avaliar projetos antes do lançamento.",
    "dontShowAgain": "Não mostrar isso novamente",
    "skipButton": "Pular por agora",
    "startButton": "Iniciar Tour",
  },

  // Steps related strings
  "steps": {
    "title": "Visão Geral dos IDOs Futuros",
    "description": "Bem-vindo à página de IDOs Futuros, onde você pode descobrir e avaliar novos lançamentos de tokens antes de ficarem ativos.",
    "details": "Navegue, filtre e analise vendas de tokens futuras em diferentes blockchains e plataformas de lançamento.",
  },

  // Search related strings
  "search": {
    "title": "Buscar e Encontrar",
    "description": "Busque rapidamente qualquer venda de token futura por nome ou símbolo.",
    "details": "Os resultados são atualizados em tempo real conforme você digita.",
  },

  // ProjectInfo related strings
  "projectInfo": {
    "title": "Informações do Projeto",
    "description": "Cada linha contém informações detalhadas sobre uma venda de token futura.",
    "details": "Clique em qualquer linha para ver a análise detalhada desse projeto.",
    "action": "Tente passar o mouse sobre um projeto para ver mais informações.",
  },

  // InitialCap related strings
  "initialCap": {
    "title": "Capitalização Inicial",
    "description": "A capitalização de mercado inicial esperada do token no momento da listagem.",
    "details": "Calculado como preço do token × fornecimento circulante no TGE.",
  },

  // Score related strings
  "score": {
    "title": "Pontuação CoinScout",
    "description": "Nossa pontuação proprietária avalia a qualidade geral e o potencial do projeto.",
    "details": "Baseado em tokenomics, segurança, atividade de mídia social e mais.",
  },

  // LaunchDate related strings
  "launchDate": {
    "title": "Data de Lançamento",
    "description": "A data programada quando o token estará disponível para negociação.",
    "details": "Mantenha-se atualizado com os próximos lançamentos para preparar sua estratégia de investimento.",
  },

  // Pagination related strings
  "pagination": {
    "title": "Navegação de Página",
    "description": "Navegue pela lista de vendas de tokens futuras.",
    "details": "Ajuste o número de projetos exibidos por página.",
  },

  // Completion related strings
  "completion": {
    "title": "Você está Pronto!",
    "description": "Você completou o tour da página de IDOs Futuros.",
    "details": "Comece a explorar e analisar vendas de tokens futuras para encontrar sua próxima oportunidade de investimento.",
    "help": "Você pode reiniciar este tour a qualquer momento clicando no botão Tour Guiado.",
  },

  // Methodology related strings
  "methodology": {
    "whatAreWeScoring": "O Que Estamos Pontuando",
    "whyIsThisImportant": "Por Que Isso É Importante",
    "scoringLevels": "Níveis de Pontuação",
  },

  // Profile related strings
  "profile": {
    "yourProfile": "Seu Perfil",
    "howOthersSeeYou": "É assim que outros irão vê-lo na plataforma.",
    "verified": "Verificado",
    "unverified": "Não verificado",
    "memberSince": "Membro desde",
    "unknown": "Desconhecido",
    "status": "Status",
    "active": "Ativo",
    "plan": "Plano",
    "unknownPlan": "Plano Desconhecido",
    "planStatus": "Status do Plano",
    "started": "Iniciado",
    "expires": "Expira",
    "lastLogin": "Último login",
    "never": "Nunca",
    "profileCompletion": "[MISSING] Profile completion",
    "improveTip": "[MISSING] Add a bio and profile picture to improve your profile.",
    "signOut": "[MISSING] Sign Out",
    "areYouSure": "[MISSING] Are you sure?",
    "signOutConfirmation": "[MISSING] You'll be signed out from your account. You can sign back in at any time.",
    "cancel": "[MISSING] Cancel",
    "personalInformation": "[MISSING] Personal Information",
    "updateDescription": "[MISSING] Update your personal information and how your profile appears.",
    "username": "[MISSING] Username",
    "email": "[MISSING] Email",
    "emailPlaceholder": "[MISSING] <EMAIL>",
    "emailSent": "[MISSING] Email sent",
    "verificationEmailSent": "[MISSING] Verification email sent successfully. Please check your inbox.",
    "error": "[MISSING] Error",
    "emailSendError": "[MISSING] An error occurred while sending email. Please try again.",
    "resendVerification": "[MISSING] Resend verification email",
    "membershipTier": "[MISSING] Membership Tier",
    "pro": "[MISSING] Pro",
    "premium": "[MISSING] Premium",
    "free": "[MISSING] Free",
    "manageSubscription": "[MISSING] Manage your subscription in the",
    "membership": "[MISSING] Membership",
    "tab": "[MISSING] tab",
    "downloadData": "[MISSING] Download Your Data",
    "deleteAccount": "[MISSING] Delete Account",
    "absolutelySure": "[MISSING] Are you absolutely sure?",
    "deleteWarning": "[MISSING] This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    "title": "[MISSING] Profile",
    "description": "[MISSING] Manage your account settings and preferences.",
    "saveChanges": "[MISSING] Save Changes",
    "profile": "[MISSING] Profile",
    "notifications": "[MISSING] Notifications",
  },

  // Logs related strings
  "logs": {
    "usingDirectAPIData": "[MISSING] 🔄 Using direct API data, data length:",
    "imageLoadError": "[MISSING] 🖼️ Error loading image:",
    "usingIdoWatchlists": "[MISSING] Using IDO watchlists for upcoming projects:",
    "rawAPIData": "[MISSING] 🟡 Raw data from API:",
    "apiDataLength": "[MISSING] 🟡 API data length:",
    "filteredData": "[MISSING] 🔍 Filtered data:",
    "filteredDataLength": "[MISSING] 🔍 Filtered data length:",
    "imageUrlChecks": "[MISSING] 🖼️ Image URL checks:",
    "allProjectsFromAPI": "[MISSING] 📌 All projects from API:",
    "usingDirectAPI": "[MISSING] 🟢 Using direct API data",
    "rawAPIDataFirst5": "[MISSING] 🔍 Raw API data (first 5 items):",
    "emptyAPIResult": "[MISSING] ⚠️ API returned empty result, using empty array",
    "errorUsingEmptyArray": "[MISSING] ❌ Using empty array due to error",
    "totalAIScoreClicked": "[MISSING] Total AI Score clicked:",
    "filtersChanged": "[MISSING] Filters changed:",
    "upcomingIDOFirstUseEffect": "[MISSING] 🔄 UpcomingIDO - First useEffect triggered",
    "fetchInitialDataStarted": "[MISSING] 🔄 fetchInitialData started",
    "fetchInitialDataCompleted": "[MISSING] 🔄 fetchInitialData completed",
    "filterRunning": "[MISSING] 🔍 Filter running, coins length:",
    "paginatedData": "[MISSING] 📄 Paginated data:",
    "first2Records": "[MISSING] 📄 First 2 records:",
    "paginatedDataImageFields": "[MISSING] 📄 Image fields in paginated data:",
    "firstRecordDetails": "[MISSING] 🧩 First record details:",
    "firstRecordSocialScore": "[MISSING] 📢 First record social score value:",
    "clickedProjectInfo": "[MISSING] Clicked project info:",
    "redirectingWithDirectID": "[MISSING] Redirecting with direct ID:",
  },

  // Homepage related strings
  "homepage": {
    "badge": "[MISSING] AI-Powered Crypto Analysis",
    "title": "[MISSING] Make smarter crypto investments",
    "titleHighlight": "[MISSING] with AI insights",
    "description": "[MISSING] CoinScout analyzes over 3,000 cryptocurrencies across 40+ metrics to help you identify the best investment opportunities before others see them.",
    "ctaButton": "[MISSING] Start Free Analysis",
    "cryptocurrencies": "[MISSING] Cryptocurrencies",
    "analysisMetrics": "[MISSING] Analysis Metrics",
    "moreAccuracy": "[MISSING] More Accuracy",
  },

  // Features related strings
  "features": {
    "title": "[MISSING] AI-Enhanced Crypto Rating",
    "subtitle": "[MISSING] Our comprehensive suite of AI-powered tools helps you navigate the crypto market with confidence",
    "description": "[MISSING] Proprietary algorithms assess 50+ metrics across tokenomics, security, social engagement, and market factors",
  },

  // IdoRating related strings
  "idoRating": {
    "title": "[MISSING] IDO Rating",
    "description": "[MISSING] Discover and evaluate promising token offerings before their launch with detailed project analysis and scoring",
  },

  // CompareCoins related strings
  "compareCoins": {
    "title": "[MISSING] Compare Coins",
    "description": "[MISSING] Side-by-side analysis of multiple cryptocurrencies to identify the best investment opportunities based on your criteria",
  },

  // PortfolioGenerator related strings
  "portfolioGenerator": {
    "title": "[MISSING] Custom Portfolio Generator",
    "description": "[MISSING] AI-powered recommendations to build a diversified portfolio based on your risk tolerance and investment goals",
  },

  // PortfolioAnalysis related strings
  "portfolioAnalysis": {
    "title": "[MISSING] AI Portfolio Analysis",
    "description": "[MISSING] Gain AI-powered insights into your existing portfolio with automated risk analysis, diversification score, and optimization tips",
  },

  // Launchpads related strings
  "launchpads": {
    "title": "[MISSING] Top Launchpads",
    "description": "[MISSING] Discover the best platforms for new token offerings with performance metrics, success rates, and investor returns",
  },

  // AiAssistant related strings
  "aiAssistant": {
    "title": "[MISSING] AI Assistant",
    "description": "[MISSING] Get instant answers to your crypto questions with our advanced AI assistant that provides personalized guidance and insights",
  },

  // AirdropScore related strings
  "airdropScore": {
    "title": "[MISSING] Airdrop Score",
    "description": "[MISSING] Find legitimate airdrops with the highest potential value and lowest participation barriers through our verification system",
  },

  // GemScout related strings
  "gemScout": {
    "title": "[MISSING] Gem Scout",
    "description": "[MISSING] Discover early-stage projects with exceptional growth potential before they hit mainstream markets and major exchanges",
  },

  // Badges related strings
  "badges": {
    "betaTestingLive": "[MISSING] Beta Testing Live",
    "betaTestingSoon": "[MISSING] Beta Testing Soon",
    "comingSoon": "[MISSING] Coming Soon",
  },

  // Intelligence related strings
  "intelligence": {
    "title": "[MISSING] AI-Driven Intelligence for Smarter Crypto Decisions",
  },

  // Faq related strings
  "faq": {
    "title": "[MISSING] Frequently Asked Questions",
    "question": "[MISSING] What makes CoinScout's analysis different from other platforms?",
    "answer": "[MISSING] CoinScout leverages AI-driven analytics to assess over 50 key factors across tokenomics, security, social sentiment, market performance, and project fundamentals. Unlike traditional platforms that primarily provide raw data, we transform complex metrics into clear, actionable insights—making it easier for investors at all levels to understand and make informed decisions.",
  },

  // Upcoming related strings
  "upcoming": {
    "title": "Vendas de Tokens Futuras",
    "subtitle": "Descubra e avalie novos tokens antes do lançamento",
    "saleType": "Tipo de Venda",
    "allTypes": "[MISSING] All Types",
    "launchpad": "[MISSING] Launchpad",
    "allLaunchpads": "[MISSING] All Launchpads",
    "category": "[MISSING] Category",
    "allCategories": "[MISSING] All Categories",
    "blockchain": "[MISSING] Blockchain",
    "allBlockchains": "[MISSING] All Blockchains",
    "investor": "[MISSING] Investor",
    "allInvestors": "[MISSING] All Investors",
    "projectScore": "[MISSING] Project Score",
    "listingDate": "[MISSING] Listing Date",
    "reset": "[MISSING] Reset Filters",
    "apply": "[MISSING] Apply Filters",
    "searchCategories": "[MISSING] Search categories...",
    "searchChains": "[MISSING] Search chains...",
    "selectDateRange": "[MISSING] Select date range",
    "last24Hours": "[MISSING] Last 24 Hours",
    "last7Days": "[MISSING] Last 7 Days",
    "last14Days": "[MISSING] Last 14 Days",
    "last30Days": "[MISSING] Last 30 Days",
    "last90Days": "[MISSING] Last 90 Days",
  },

  // Table related strings
  "table": {
    "name": "[MISSING] Name",
    "launchDate": "[MISSING] Launch Date",
    "initialCap": "[MISSING] Initial Cap",
    "totalRaised": "[MISSING] Total Raised",
    "score": "[MISSING] Score",
    "actions": "[MISSING] Actions",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "Carregando...",
  "common:error": "Erro",
  "common:retry": "Tentar novamente",
  "common:save": "Salvar",
  "common:cancel": "Cancelar",
  "common:close": "Fechar",
  "common:success": "Sucesso",
  "common:viewMore": "Ver mais",
  "common:back": "Voltar",
  "common:next": "Próximo",
  "common:search": "Buscar",
  "common:searchCoinsAndTokens": "Buscar por Moedas & Projetos Futuros",
  "common:searching": "Buscando...",
  "common:noResults": "Nenhum resultado encontrado",
  "common:coins": "Moedas",
  "common:upcomingIdos": "Próximos IDOs",

  // Flat format for nav (for backward compatibility)
  "nav:home": "Início",
  "nav:portfolio": "Portfólio",
  "nav:explore": "Explorar",
  "nav:news": "Notícias",
  "nav:learn": "Aprender",
  "nav:profile": "Perfil",
  "nav:settings": "Configurações",
  "nav:logout": "Sair",
  "nav:login": "Entrar",
  "nav:register": "Registrar",
  "nav:trending": "Em alta",
  "nav:favorites": "Favoritos",
  "nav:watchlist": "Lista de observação",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "Buscando...",
  "system:error.translation": "Erro de tradução",
  "system:data.loading": "Carregando dados",
  "system:data.empty": "Sem dados disponíveis",
  "system:data.error": "Erro ao carregar dados",

  // Flat format for auth (for backward compatibility)
  "auth:email": "E-mail",
  "auth:email.placeholder": "Digite seu e-mail",
  "auth:email.description": "Seu e-mail nunca será compartilhado com ninguém",
  "auth:password": "Senha",
  "auth:password.placeholder": "Digite sua senha",
  "auth:password.create": "Criar senha",
  "auth:password.confirm": "Confirmar senha",
  "auth:password.confirm.placeholder": "Confirme sua senha",
  "auth:password.show": "Mostrar senha",
  "auth:password.hide": "Ocultar senha",
  "auth:password.strength": "Força da senha",
  "auth:password.strength.weak": "Fraca",
  "auth:password.strength.good": "Boa",
  "auth:password.strength.strong": "Forte",
  "auth:password.reset.message": "Recurso de redefinição de senha estará disponível em breve",
  "auth:forgotPassword": "Esqueceu a senha?",
  "auth:resetPassword": "Redefinir senha",
  "auth:signin": "Entrar",
  "auth:signin.loading": "Entrando...",
  "auth:signin.securely": "Entre com segurança",
  "auth:signup": "Registrar",
  "auth:signout": "Sair",
  "auth:accountCreated": "Conta criada com sucesso",
  "auth:passwordResetSent": "E-mail de redefinição de senha enviado",
  "auth:invalidCredentials": "E-mail ou senha inválidos",
  "auth:continueWith": "Continuar com",
  "auth:username": "Nome de usuário",
  "auth:username.placeholder": "Escolha um nome de usuário",
  "auth:terms.agree": "Eu concordo com",
  "auth:terms.service": "Termos de Serviço",
  "auth:terms.and": "e",
  "auth:terms.privacy": "Política de Privacidade",
  "auth:termsAccept": "Ao continuar, você concorda com nossos Termos de Serviço e Política de Privacidade",
  "auth:remember": "Mantenha-me conectado por 30 dias",
  "auth:welcome.back": "Bem-vindo de volta",
  "auth:login.credential.prompt": "Insira suas credenciais para entrar",
  "auth:login.success.title": "Sucesso",
  "auth:login.success.description": "Login bem-sucedido",
  "auth:login.error.title": "Erro de login",
  "auth:login.error.unknown": "Algo deu errado. Por favor, tente novamente.",
  "auth:register.title": "Criar conta",
  "auth:register.create": "Criar conta",
  "auth:register.creating": "Criando conta...",
  "auth:register.haveAccount": "Já tem uma conta?",
  "auth:register.success": "Registro bem-sucedido",
  "auth:register.success.detail": "Conta criada com sucesso",
  "auth:register.success.login": "Conta criada. Por favor, faça login.",
  "auth:register.success.email_verify": "Registro bem-sucedido. Por favor, verifique seu e-mail para ativar sua conta.",
  "auth:register.failed": "Falha no registro",
  "auth:register.failed.generic": "Ocorreu um erro durante o registro. Por favor, tente novamente.",
  "auth:register.error.generic": "Ocorreu um erro. Por favor, tente novamente.",
  "auth:register.description": "Crie uma conta para começar",
  "auth:form.invalidFields": "Por favor, preencha todos os campos obrigatórios corretamente",
  "auth:validation.email": "Por favor, insira um endereço de e-mail válido",
  "auth:validation.password.length": "A senha deve ter pelo menos 6 caracteres",
  "auth:authentication.required": "Autenticação Obrigatória",
  "auth:authentication.required.description": "Por favor, faça login para acessar este recurso",
  "auth:authentication.signin": "Entrar",
  "auth:authentication.continueWithEmail": "Continuar com email",
  "auth:authentication.goBack": "Voltar",
  "auth:authentication.signInPrompt": "Faça login para acessar recursos personalizados, salvar suas preferências e desbloquear todas as capacidades do CoinScout.",
  "auth:authentication.comparisonPrompt": "Faça login para acessar recursos personalizados, salvar suas preferências e desbloquear todas as capacidades do CoinScout para comparação.",
  "auth:backToHome": "Voltar ao Início",
  "auth:validation.email.required": "[MISSING] Email is required",
  "auth:validation.email.invalid": "[MISSING] Please enter a valid email address",
  "auth:validation.email.complete": "[MISSING] Please enter a complete email address",
  "auth:validation.password.required": "[MISSING] Password is required",
  "auth:validation.password.uppercase": "[MISSING] Password must contain at least one uppercase letter",
  "auth:validation.password.lowercase": "[MISSING] Password must contain at least one lowercase letter",
  "auth:validation.password.number": "[MISSING] Password must contain at least one number",
  "auth:validation.password.special": "[MISSING] Password must contain at least one special character",
  "auth:login.failed": "[MISSING] Login Failed",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "Todas as moedas",
  "coinlist:coinDetailDescription": "Clique em qualquer moeda para análise detalhada",
  "coinlist:searchCoins": "Buscar moedas...",
  "coinlist:searchBox.ariaLabel": "Buscar moedas",
  "coinlist:aiPoweredTitle": "Avaliações fundamentais de criptomoedas por IA e dados",
  "coinlist:comprehensiveAnalysis": "Análise abrangente e pontuação de criptomoedas em",
  "coinlist:multipleMetrics": "múltiplas métricas",
  "coinlist:highlights": "Destaques",
  "coinlist:viewAll": "Ver tudo",
  "coinlist:currentPrice": "Preço atual",
  "coinlist:marketCap": "Capitalização de mercado",
  "coinlist:rank": "Classificação",
  "coinlist:filters.button": "Filtros",
  "coinlist:filters.title": "Opções de filtro",
  "coinlist:filters.description": "Personalize sua visualização com opções de filtro avançadas",
  "coinlist:filters.sortBy": "Ordenar por",
  "coinlist:filters.reset": "Redefinir filtros",
  "coinlist:columns.name": "Nome",
  "coinlist:columns.tokenomics": "Tokenomics",
  "coinlist:columns.security": "Segurança",
  "coinlist:columns.social": "Social",
  "coinlist:columns.market": "Mercado",
  "coinlist:columns.insights": "Insights",
  "coinlist:columns.totalScore": "Pontuação total de IA",
  "coinlist:columns.sevenDayChange": "Variação 7D",
  "coinlist:columns.price": "Preço",
  "coinlist:tooltips.name.title": "[MISSING] Coin Name & Symbol",
  "coinlist:tooltips.name.description": "[MISSING] The official name and symbol of the cryptocurrency as listed on exchanges.",
  "coinlist:tooltips.tokenomics.title": "[MISSING] Tokenomics Analysis",
  "coinlist:tooltips.tokenomics.description": "[MISSING] Measures token supply mechanisms, inflation risks, and vesting structures.",
  "coinlist:tooltips.security.title": "[MISSING] Security Analysis",
  "coinlist:tooltips.security.description": "[MISSING] Security audit results and risk assessment metrics.",
  "coinlist:tooltips.social.title": "[MISSING] Social Analysis",
  "coinlist:tooltips.social.description": "[MISSING] Social media presence, community engagement and sentiment analysis.",
  "coinlist:tooltips.market.title": "[MISSING] Market Performance Analysis",
  "coinlist:tooltips.market.description": "[MISSING] Measures trading volume, liquidity and overall market health.",
  "coinlist:tooltips.insights.title": "[MISSING] AI Insights Analysis",
  "coinlist:tooltips.insights.description": "[MISSING] AI-powered project insights, sentiment analysis and forecasting metrics.",
  "coinlist:tooltips.totalScore.title": "[MISSING] Total Score",
  "coinlist:tooltips.totalScore.description": "[MISSING] The overall rating calculated from all scoring metrics.",
  "coinlist:tooltips.sevenDayChange.title": "[MISSING] 7 Day Change",
  "coinlist:tooltips.sevenDayChange.description": "[MISSING] Percentage price change over the last 7 days.",
  "coinlist:search.inProgress": "[MISSING] Search in progress...",
  "coinlist:search.resultsUpdate": "[MISSING] Results will update automatically",
  "coinlist:search.clearSearch": "[MISSING] Clear search",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "Moedas com Maior Ganho",
  "highlights:newListings": "Novas Listagens",
  "highlights:upcomingIDOs": "Próximos IDOs",
  "highlights:gemCoins": "Moedas Promissoras",
  "highlights:topAirdrops": "Melhores Airdrops",
  "highlights:score": "Pontuação",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "Visão geral",
  "coindetail:fundamentals": "Fundamentos",
  "coindetail:technicals": "Técnicos",
  "coindetail:news": "Notícias",
  "coindetail:social": "Social",
  "coindetail:developers": "Desenvolvedores",
  "coindetail:analysis": "Análise",
  "coindetail:price": "Preço",
  "coindetail:volume": "Volume",
  "coindetail:marketCap": "Capitalização de mercado",
  "coindetail:circulatingSupply": "Fornecimento em circulação",
  "coindetail:totalSupply": "Fornecimento total",
  "coindetail:maxSupply": "Fornecimento máximo",
  "coindetail:allTimeHigh": "Máxima histórica",
  "coindetail:allTimeLow": "Mínima histórica",
  "coindetail:pricePrediction": "Previsão de preço",
  "coindetail:addToWatchlist": "Adicionar à lista de observação",
  "coindetail:removeFromWatchlist": "Remover da lista de observação",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "Valor total",
  "portfolio:recentActivity": "Atividade recente",
  "portfolio:performance": "Desempenho",
  "portfolio:holdings": "Ativos",
  "portfolio:addAsset": "Adicionar ativo",
  "portfolio:editAsset": "Editar ativo",
  "portfolio:noAssets": "Sem ativos no portfólio",
  "portfolio:24hChange": "Variação 24h",
  "portfolio:allocation": "Alocação",
  "portfolio:profit": "Lucro/Prejuízo",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "Aparência",
  "settings:language": "Idioma",
  "settings:notifications": "Notificações",
  "settings:security": "Segurança",
  "settings:preferences": "Preferências",
  "settings:theme": "Tema",
  "settings:lightMode": "Modo claro",
  "settings:darkMode": "Modo escuro",
  "settings:systemDefault": "Padrão do sistema",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "Travar",
  "sidebar:unlock": "Destravar",
  "sidebar:collapse": "Recolher",
  "sidebar:cryptoRating": "Avaliação de Criptos",
  "sidebar:idoRating": "Avaliação de IDO",
  "sidebar:compareCoins": "Comparar Moedas",
  "sidebar:recentListings": "Listagens Recentes",
  "sidebar:topMovers": "Maiores Movimentações",
  "sidebar:watchlist": "Minha Lista de Observação",
  "sidebar:aiPortfolio": "Guia de Portfólio IA",
  "sidebar:portfolioAudit": "Auditoria de Portfólio",
  "sidebar:launchpads": "Plataformas de Lançamento",
  "sidebar:airdrops": "Centro de Airdrops",
  "sidebar:aiAssistant": "Assistente IA",
  "sidebar:gemScout": "Buscador de Gemas",
  "sidebar:soon": "EM BREVE",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "Algo deu errado",
  "error:tryAgain": "Por favor, tente novamente",
  "error:networkIssue": "Problema de conexão de rede",
  "error:dataFetch": "Não foi possível buscar dados",
  "error:timeOut": "Tempo limite esgotado",
  "error:invalidInput": "Entrada inválida",
  "error:pageNotFound": "Página não encontrada",

  // Flat format for format (for backward compatibility)
  "format:thousand": "[MISSING] K",
  "format:million": "[MISSING] M",
  "format:billion": "[MISSING] B",
  "format:trillion": "[MISSING] T",

  // Flat format for coin (for backward compatibility)
  "coin:age": "[MISSING] Age:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "Excelente",
  "score:positive": "Positivo",
  "score:average": "Médio",
  "score:weak": "Fraco",
  "score:critical": "Crítico",

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "Vendas de Tokens Futuras",
  "upcoming:subtitle": "Descubra e avalie novos tokens antes do lançamento",
  "upcoming:filters.title": "Filtros",
  "upcoming:filters.saleType": "Tipo de Venda",
  "upcoming:filters.allTypes": "Todos os Tipos",
  "upcoming:filters.launchpad": "Plataforma de Lançamento",
  "upcoming:filters.allLaunchpads": "Todas as Plataformas",
  "upcoming:filters.category": "Categoria",
  "upcoming:filters.allCategories": "Todas as Categorias",
  "upcoming:filters.blockchain": "Blockchain",
  "upcoming:filters.allBlockchains": "Todas as Blockchains",
  "upcoming:filters.investor": "Investidor",
  "upcoming:filters.allInvestors": "Todos os Investidores",
  "upcoming:filters.projectScore": "Pontuação do Projeto",
  "upcoming:filters.listingDate": "Data de Listagem",
  "upcoming:filters.reset": "Resetar Filtros",
  "upcoming:filters.apply": "Aplicar Filtros",
  "upcoming:table.name": "Nome",
  "upcoming:table.launchDate": "Data de Lançamento",
  "upcoming:table.initialCap": "Cap Inicial",
  "upcoming:table.totalRaised": "Total Arrecadado",
  "upcoming:table.score": "Pontuação",
  "upcoming:table.actions": "Ações",
  "upcoming:search": "Buscar vendas de tokens futuras...",
  "upcoming:noResults": "Nenhuma venda de token futura encontrada",
  "upcoming:loading": "Carregando vendas de tokens futuras...",
  "upcoming:error": "Erro ao carregar vendas de tokens futuras",
  "upcoming:retryButton": "Tentar Novamente",
  "upcoming:tba": "A Ser Anunciado",
  "upcoming:rank": "Ranking #{number}",
  "upcoming:saleType": "Tipo de Venda",
  "upcoming:totalAiScore": "Pontuação Total de IA",
  "upcoming:points": "Pontos",
  "upcoming:tokenomics": "Tokenomics",
  "upcoming:security": "Segurança",
  "upcoming:social": "Social",
  "upcoming:market": "Mercado",
  "upcoming:insights": "Insights",

};

export default pt;