/**
 * Notifications Hook - <PERSON><PERSON> Pattern
 *
 * React hook for managing real-time crypto alert notifications
 * Uses singleton pattern to ensure only one WebSocket connection
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import CoinScoutWebSocketClient, { getWebSocketUrl, type Notification } from '@/lib/CoinScoutWebSocketClient';
import { refreshToken, getAllNotifications } from '@/lib/api';

interface NotificationState {
  notifications: Notification[]; // WebSocket unread notifications only
  allNotifications: Notification[]; // All notifications from API
  unreadCount: number;
  isConnected: boolean;
  isAuthenticated: boolean;
  isPopupOpen: boolean; // Track popup state
  frozenNotifications: Notification[] | null; // Frozen list while popup is open
  allNotificationsLoading: boolean; // Loading state for API notifications
  allNotificationsLoaded: boolean; // Track if all notifications have been loaded
}

interface UseNotificationsReturn extends NotificationState {
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  removeNotification: (notificationId: string) => void;
  clearAll: () => void;
  showBrowserNotification: (notification: Notification) => void;
  setPopupOpen: (isOpen: boolean) => void;
  getDisplayNotifications: () => Notification[];
  loadAllNotifications: () => Promise<void>;
  refreshAllNotifications: () => Promise<void>;
}

// Global WebSocket singleton
let globalWebSocketClient: CoinScoutWebSocketClient | null = null;
let globalStateUpdaters: ((state: Partial<NotificationState>) => void)[] = [];
let isInitialized = false;

// Initialize global WebSocket connection
function initializeGlobalWebSocket(authToken?: string) {
  if (globalWebSocketClient && globalWebSocketClient.getConnectionStatus().isConnected) {
    console.log('🔄 [Singleton] Reusing existing WebSocket connection');
    return globalWebSocketClient;
  }

  if (globalWebSocketClient) {
    console.log('🔌 [Singleton] Disconnecting existing WebSocket connection');
    globalWebSocketClient.disconnect();
    globalWebSocketClient = null;
  }

  const storedToken = localStorage.getItem('auth_token');
  const token = authToken || storedToken || `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const wsUrl = getWebSocketUrl();

  console.log('🔌 [Singleton] Creating NEW global WebSocket connection');
  console.log(`🔢 [Singleton] This should be the only WebSocket connection created!`);

  globalWebSocketClient = new CoinScoutWebSocketClient(wsUrl, token, {
    debug: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5
  });

  // Set up event listeners only once
  if (!isInitialized) {
    setupGlobalEventListeners();
    isInitialized = true;
  }

  return globalWebSocketClient;
}

// Setup event listeners for the global WebSocket
function setupGlobalEventListeners() {
  if (!globalWebSocketClient) return;

  console.log('🎧 [Singleton] Setting up global event listeners');

  // Connection events
  globalWebSocketClient.on('connect', () => {
    console.log('✅ [Singleton] WebSocket connected');
    updateAllStates({ isConnected: true });
  });

  globalWebSocketClient.on('disconnect', () => {
    console.log('❌ [Singleton] WebSocket disconnected');
    updateAllStates({ isConnected: false, isAuthenticated: false });
  });

  globalWebSocketClient.on('auth_success', (data: any) => {
    console.log('🔐 [Singleton] WebSocket authentication successful');
    updateAllStates({ isAuthenticated: true });
  });

  globalWebSocketClient.on('auth_error', (error: any) => {
    console.error('🚫 [Singleton] WebSocket authentication failed');
    updateAllStates({ isAuthenticated: false });
  });

  // Notification events
  globalWebSocketClient.on('notification', (notification: Notification) => {
    console.log('🔔 [Singleton] New notification received');
    
    const wsNotification = {
      ...notification,
      status: 'unread' as const,
      source: 'websocket',
      timestamp: new Date(notification.timestamp)
    };

    // Update all hook instances
    globalStateUpdaters.forEach(updateState => {
      updateState({
        notifications: [wsNotification],
        addToUnreadCount: 1 // Special flag to add to existing count
      });
    });
  });

  globalWebSocketClient.on('unread_notifications', (data: any) => {
    console.log('📬 [Singleton] Unread notifications batch received');
    
    const processedNotifications = (data.notifications || []).map((notif: any) => ({
      ...notif,
      status: 'unread',
      source: 'websocket',
      timestamp: new Date(notif.timestamp)
    }));

    updateAllStates({
      notifications: processedNotifications,
      unreadCount: data.count || 0
    });
  });

  globalWebSocketClient.on('notification_read_confirmed', (data: any) => {
    console.log('✅ [Singleton] Notification marked as read confirmed');
    // Update notifications to mark as read
    globalStateUpdaters.forEach(updateState => {
      updateState({ readConfirmation: data.notificationId });
    });
  });

  globalWebSocketClient.on('unread_count_update', (data: any) => {
    console.log('📊 [Singleton] Unread count updated');
    updateAllStates({ unreadCount: data.count || 0 });
  });

  // Token refresh events
  globalWebSocketClient.on('token_refresh_failed', (error: any) => {
    console.error('❌ [Singleton] Token refresh failed');
    localStorage.removeItem('auth_token');
    window.location.href = '/login?session=expired';
  });

  // Start token refresh
  globalWebSocketClient.startTokenRefresh(async () => {
    try {
      const newToken = await refreshToken();
      return newToken;
    } catch (error) {
      throw error;
    }
  }, 4 * 60 * 1000);
}

// Update all hook instances with new state
function updateAllStates(newState: Partial<NotificationState>) {
  globalStateUpdaters.forEach(updateState => {
    updateState(newState);
  });
}

export function useNotifications(authToken?: string): UseNotificationsReturn {
  const [state, setState] = useState<NotificationState>({
    notifications: [],
    allNotifications: [],
    unreadCount: 0,
    isConnected: false,
    isAuthenticated: false,
    isPopupOpen: false,
    frozenNotifications: null,
    allNotificationsLoading: false,
    allNotificationsLoaded: false
  });

  // Register this hook instance for global updates
  useEffect(() => {
    const updateState = (newState: any) => {
      setState(prev => {
        if (newState.notifications && Array.isArray(newState.notifications)) {
          // Add new notifications to existing ones
          return {
            ...prev,
            notifications: [...newState.notifications, ...prev.notifications.slice(0, 49)],
            unreadCount: newState.addToUnreadCount ? prev.unreadCount + newState.addToUnreadCount : (newState.unreadCount ?? prev.unreadCount)
          };
        } else if (newState.readConfirmation) {
          // Mark notification as read
          return {
            ...prev,
            notifications: prev.notifications.map(notif =>
              notif.id === newState.readConfirmation
                ? { ...notif, status: 'read' as const }
                : notif
            )
          };
        } else {
          // Regular state update
          return { ...prev, ...newState };
        }
      });
    };

    globalStateUpdaters.push(updateState);

    return () => {
      globalStateUpdaters = globalStateUpdaters.filter(updater => updater !== updateState);
    };
  }, []);

  // Initialize WebSocket connection
  useEffect(() => {
    console.log('🔄 [Hook] Initializing WebSocket connection');
    const client = initializeGlobalWebSocket(authToken);
    
    // Update connection status from global client
    setState(prev => ({
      ...prev,
      isConnected: client.getConnectionStatus().isConnected,
      isAuthenticated: client.getConnectionStatus().isAuthenticated
    }));

    return () => {
      // Don't disconnect here - let singleton manage the connection
      console.log('🧹 [Hook] Hook cleanup (not disconnecting global WebSocket)');
    };
  }, [authToken]);

  // Listen for auth:logout event
  useEffect(() => {
    const handleAuthLogout = () => {
      console.log('🔐 [Hook] Received auth:logout event');
      
      if (globalWebSocketClient) {
        globalWebSocketClient.isAuthenticated = false;
        globalWebSocketClient.disconnect();
        globalWebSocketClient = null;
        isInitialized = false;
      }
      
      updateAllStates({
        notifications: [],
        allNotifications: [],
        unreadCount: 0,
        isConnected: false,
        isAuthenticated: false,
        isPopupOpen: false,
        frozenNotifications: null,
        allNotificationsLoading: false,
        allNotificationsLoaded: false
      });
    };

    window.addEventListener('auth:logout', handleAuthLogout as EventListener);
    
    return () => {
      window.removeEventListener('auth:logout', handleAuthLogout as EventListener);
    };
  }, []);

  // Notification management functions
  const markAsRead = useCallback((notificationId: string) => {
    if (globalWebSocketClient && globalWebSocketClient.getConnectionStatus().isAuthenticated) {
      globalWebSocketClient.markNotificationAsRead(notificationId);
    } else {
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(notif =>
          notif.id === notificationId
            ? { ...notif, status: 'read' as const }
            : notif
        ),
        unreadCount: Math.max(0, prev.unreadCount - 1)
      }));
    }
  }, []);

  const markAllAsRead = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.map(notif => ({ ...notif, status: 'read' as const })),
      unreadCount: 0
    }));
  }, []);

  const removeNotification = useCallback((notificationId: string) => {
    setState(prev => {
      const notification = prev.notifications.find(n => n.id === notificationId);
      const wasUnread = notification?.status === 'unread';
      return {
        ...prev,
        notifications: prev.notifications.filter(notif => notif.id !== notificationId),
        unreadCount: wasUnread ? Math.max(0, prev.unreadCount - 1) : prev.unreadCount
      };
    });
  }, []);

  const clearAll = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: [],
      unreadCount: 0
    }));
  }, []);

  const showBrowserNotification = useCallback((notification: Notification) => {
    if (!('Notification' in window)) return;

    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.priority === 'high'
      });

      browserNotification.onclick = () => {
        window.focus();
        if (notification.link) {
          window.location.href = notification.link;
        }
        browserNotification.close();
      };

      if (notification.priority !== 'high') {
        setTimeout(() => browserNotification.close(), 5000);
      }
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          showBrowserNotification(notification);
        }
      });
    }
  }, []);

  const setPopupOpen = useCallback((isOpen: boolean) => {
    setState(prev => ({
      ...prev,
      isPopupOpen: isOpen,
      frozenNotifications: isOpen ? prev.notifications : null
    }));
  }, []);

  const getDisplayNotifications = useCallback(() => {
    return state.frozenNotifications || state.notifications;
  }, [state.frozenNotifications, state.notifications]);

  const loadAllNotifications = useCallback(async () => {
    if (state.allNotificationsLoading || state.allNotificationsLoaded) return;

    setState(prev => ({ ...prev, allNotificationsLoading: true }));

    try {
      const response = await getAllNotifications();
      const notifications = response?.notifications || [];
      setState(prev => ({
        ...prev,
        allNotifications: notifications,
        allNotificationsLoading: false,
        allNotificationsLoaded: true
      }));
    } catch (error) {
      console.error('Failed to load all notifications:', error);
      setState(prev => ({
        ...prev,
        allNotificationsLoading: false
      }));
    }
  }, [state.allNotificationsLoading, state.allNotificationsLoaded]);

  const refreshAllNotifications = useCallback(async () => {
    setState(prev => ({
      ...prev,
      allNotificationsLoaded: false,
      allNotificationsLoading: false
    }));
    await loadAllNotifications();
  }, [loadAllNotifications]);

  return {
    ...state,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    showBrowserNotification,
    setPopupOpen,
    getDisplayNotifications,
    loadAllNotifications,
    refreshAllNotifications
  };
}