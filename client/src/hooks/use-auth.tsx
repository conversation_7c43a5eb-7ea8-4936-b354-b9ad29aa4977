import { createContext, useContext, useState, ReactNode, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import AuthService, { UserResponse, RegisterResponse } from "@/lib/services/AuthService";
import { useToast } from "@/hooks/use-toast";
import { decodeToken, extractPermissions, hasPermission, hasWildcardPermission } from "@/lib/jwtUtils";
import { useComparisonStore } from "@/stores/comparisonStore";

// Define the User type based on API response structure
interface User {
  id: number;
  username: string;
  email: string;
  email_verified?: number;
  isAdmin?: boolean;
  avatarUrl?: string;
  avatarId?: number;
  avatar_id?: number; // Backend'den gelen raw field
  bio?: string;
  membershipTier?: string;
  createdAt?: string;
  lastLogin?: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  notificationPreferences?: {
    email?: boolean;
    push?: boolean;
    telegram?: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  error: Error | null;
  login: (email: string, password: string, remember?: boolean) => Promise<void>;
  register: (
    email: string,
    password: string,
    username: string,
    termsAccepted?: boolean,
    captchaToken?: string | null
  ) => Promise<any>;
  logout: () => Promise<void>;
  // Add permission related functionality
  permissions: string[];
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  tokenInfo: {
    token: string | null;
    decodedToken: any | null;
    isExpired: boolean;
    expiresAt: Date | null;
  };
}

// Create a default value for the context to avoid null issues
const defaultAuthContext: AuthContextType = {
  user: null,
  isLoggedIn: false,
  isLoading: false,
  error: null,
  login: async () => {},
  register: async (email: string, password: string, username: string, termsAccepted, captchaToken) =>
    ({ success: false, error: "Not implemented" }),
  logout: async () => {},
  // Default permission related values
  permissions: [],
  hasPermission: () => false,
  hasAnyPermission: () => false,
  hasAllPermissions: () => false,
  tokenInfo: {
    token: null,
    decodedToken: null,
    isExpired: true,
    expiresAt: null
  }
};

// Type guard to verify we have a complete auth context (for type safety)
const isAuthContextComplete = (ctx: Partial<AuthContextType>): ctx is AuthContextType => {
  return !!(
    ctx.user !== undefined &&
    ctx.isLoggedIn !== undefined &&
    ctx.isLoading !== undefined &&
    ctx.error !== undefined &&
    ctx.login !== undefined &&
    ctx.register !== undefined &&
    ctx.logout !== undefined &&
    ctx.permissions !== undefined &&
    ctx.hasPermission !== undefined &&
    ctx.hasAnyPermission !== undefined &&
    ctx.hasAllPermissions !== undefined &&
    ctx.tokenInfo !== undefined
  );
};

const AuthContext = createContext<AuthContextType>(defaultAuthContext);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [, setLocation] = useLocation();
  const [authError, setAuthError] = useState<Error | null>(null);
  const [initialCheck, setInitialCheck] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { clearOnUserChange } = useComparisonStore();

  // Initial synchronous check for token existence
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    const bypassAuth = process.env.NODE_ENV === 'development' && import.meta.env.VITE_BYPASS_AUTH === 'true';

    // Mark initial check as complete regardless of token existence
    // This prevents the loading state from blocking authentication checks
    setInitialCheck(true);
  }, []);

  // Query for getting the current user
  const {
    data: authData,
    isLoading: queryLoading,
    error,
    refetch: refetchUser
  } = useQuery({
    queryKey: ["currentUser"],
    queryFn: async () => {
      try {
        // Check for token in localStorage first
        const token = localStorage.getItem('auth_token');
        if (!token && !(process.env.NODE_ENV === 'development' && import.meta.env.VITE_BYPASS_AUTH === 'true')) {
          // No token exists and not bypassing auth
          return { success: false, output: null };
        }

        // In development environment, check if we should bypass authentication
        if (process.env.NODE_ENV === 'development' && import.meta.env.VITE_BYPASS_AUTH === 'true') {
          console.log('Auth check bypassed for development');
          // Return a mock user for development that matches our expected structure
          return {
            success: true,
            output: {
              user: {
                id: 1,
                username: 'dev_user',
                email: '<EMAIL>',
                isAdmin: true,
                avatarUrl: 'https://ui-avatars.com/api/?name=Dev+User&background=random',
                bio: 'Development user for testing',
                membershipTier: 'premium',
                createdAt: '2024-01-01 12:00:00',
                lastLogin: '2024-03-14 08:30:00',
                notificationPreferences: {
                  email: true,
                  push: false,
                  telegram: true
                }
              }
            }
          } as UserResponse;
        }

        // Using our AuthService to get the current user
        const response = await AuthService.getCurrentUser();
        console.log("Raw backend response:", response);
        console.log("User data from response:", response?.output?.user);
        return response;
      } catch (error) {
        console.error("Failed to fetch current user:", error);
        // Return null (not logged in) in case of error
        return { success: false, output: null };
      }
    },
    retry: 1,
    retryDelay: 1000,
    refetchOnWindowFocus: true, // Allow refetching when window regains focus
    refetchOnMount: true,
    refetchOnReconnect: true,
    staleTime: 5 * 60 * 1000, // 5 minutes - data stays fresh
    gcTime: 10 * 60 * 1000, // 10 minutes - data stays in cache
  });

  // Calculate actual loading state - only show loading if we have a token or bypass auth
  const hasToken = !!localStorage.getItem('auth_token');
  const bypassAuth = process.env.NODE_ENV === 'development' && import.meta.env.VITE_BYPASS_AUTH === 'true';
  const shouldCheckAuth = hasToken || bypassAuth;

  const isLoading = shouldCheckAuth && queryLoading && !initialCheck;

  // Mutation for login
  const loginMutation = useMutation({
    mutationFn: async (credentials: { email: string; password: string; remember?: boolean }) => {
      const { email, password, remember } = credentials;
      const response = await AuthService.login(email, password, remember);
      return response;
    },
    onSuccess: (data) => {
      // Detaylı loglama: yanıt detaylarını yazdır
      console.log("Login response:", data);

      if (!data) {
        console.error("Empty response from login API");
        toast({
          title: "Login error",
          description: "No response received from server. Please try again.",
          variant: "destructive",
        });
        return;
      }

      // Login başarılı mı kontrol et
      if (!data.success) {
        console.error("Login failed:", data.errormsg || data.error || "Unknown error");

        // Check if email verification is required
        if (data.data?.email_not_verified === true) {
          console.log("Email not verified, redirecting to verification request page");
          setLocation("/request-email-verification");
          return;
        }

        toast({
          title: "Login failed",
          description: data.errormsg || data.error || "Authentication failed. Please check your credentials.",
          variant: "destructive",
        });
        return;
      }

      // Token kontrolü
      if (!data.output?.token) {
        console.error("Token not received in successful response");
        toast({
          title: "Authentication error",
          description: "Session could not be established. Please try again.",
          variant: "destructive",
        });
        return;
      }

      // Giriş başarılı, token ve kullanıcı adını depola
      const token = data.output.token;
      const userEmail = data.output.user;

      localStorage.setItem('auth_token', token);
      if (userEmail) {
        localStorage.setItem('user_email', userEmail);
      }

      // Kullanıcı bilgilerini yenile
      queryClient.invalidateQueries({ queryKey: ["currentUser"] });

      // Başarılı mesajı göster
      toast({
        title: "Login successful",
        description: "Welcome back!",
        variant: "default",
      });

      // Check for returnTo parameter to redirect to the original page
      const urlParams = new URLSearchParams(window.location.search);
      const returnTo = urlParams.get('returnTo');
      
      console.log("Login redirect debug:", {
        currentUrl: window.location.href,
        searchParams: window.location.search,
        returnTo: returnTo,
        decodedReturnTo: returnTo ? decodeURIComponent(returnTo) : null
      });
      
      if (returnTo) {
        // Redirect to the original page
        const decodedReturnTo = decodeURIComponent(returnTo);
        console.log("Redirecting to:", decodedReturnTo);
        setLocation(decodedReturnTo);
      } else {
        // Default to coinlist page if no returnTo parameter
        console.log("No returnTo parameter found, redirecting to coinlist");
        setLocation("/coinlist");
      }
    },
    onError: (error: Error) => {
      console.error("Login error:", error);
      setAuthError(error);

      toast({
        title: "Login failed",
        description: error.message || "Could not log in. Please check your credentials.",
        variant: "destructive",
      });
    },
  });

  // Mutation for registration
  const registerMutation = useMutation({
    mutationFn: async (credentials: {
      email: string;
      password: string;
      username: string;
      termsAccepted?: boolean;
      captchaToken?: string | null;
    }) => {
      const { email, password, username, termsAccepted = true, captchaToken = null } = credentials;
      const response = await AuthService.register(email, password, username, termsAccepted, captchaToken);
      return response;
    },
    onSuccess: (data) => {
      console.log("Register response:", data);

      if (!data) {
        console.error("Empty response from register API");
        toast({
          title: "Registration error",
          description: "No response received from server. Please try again.",
          variant: "destructive",
        });
        return;
      }

      // Check if registration was successful
      if (!data.success) {
        console.error("Registration failed:", data.error || data.errormsg || "Unknown error");
        toast({
          title: "Registration failed",
          description: data.error || data.errormsg || "Registration failed. Please try again.",
          variant: "destructive",
        });
        return;
      }

      // Check if token was received
      if (!data.output?.token) {
        console.error("Token not received in successful registration response");
        toast({
          title: "Registration error",
          description: "Account was created but session could not be established. Please log in.",
          variant: "destructive",
        });
        return;
      }

      // Store auth data
      const token = data.output.token;
      const userEmail = data.output.user;

      localStorage.setItem('auth_token', token);
      if (userEmail) {
        localStorage.setItem('user_email', userEmail);
      }

      // Refresh user data
      queryClient.invalidateQueries({ queryKey: ["currentUser"] });

      // Show success message
      toast({
        title: "Registration successful",
        description: "Registration successful. Please check your email to verify your account.",
        variant: "default",
      });

      // Check for returnTo parameter to redirect to the original page
      const urlParams = new URLSearchParams(window.location.search);
      const returnTo = urlParams.get('returnTo');
      
      console.log("Registration redirect debug:", {
        currentUrl: window.location.href,
        searchParams: window.location.search,
        returnTo: returnTo,
        decodedReturnTo: returnTo ? decodeURIComponent(returnTo) : null
      });
      
      if (returnTo) {
        // Redirect to the original page
        const decodedReturnTo = decodeURIComponent(returnTo);
        console.log("Registration redirecting to:", decodedReturnTo);
        setLocation(decodedReturnTo);
      } else {
        // Default to coinlist page if no returnTo parameter
        console.log("Registration: No returnTo parameter found, redirecting to coinlist");
        setLocation("/coinlist");
      }
    },
    onError: (error: Error) => {
      console.error("Registration error:", error);
      setAuthError(error);

      toast({
        title: "Registration failed",
        description: error.message || "Could not register. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Mutation for logout
  const logoutMutation = useMutation({
    mutationFn: async () => {
      const response = await AuthService.logout();
      return response;
    },
    onSuccess: (data) => {
      if (data.success) {
        // Dispatch auth:logout event to notify other components (including WebSocket)
        const logoutEvent = new CustomEvent('auth:logout', { 
          detail: { manual: true } 
        });
        window.dispatchEvent(logoutEvent);

        // Clear any auth data from the cache
        queryClient.invalidateQueries({ queryKey: ["currentUser"] });
        queryClient.setQueryData(["currentUser"], { success: false, output: null });

        toast({
          title: "Logged out",
          description: "You have been successfully logged out.",
          variant: "default",
        });

        // Redirect to home page
        setLocation("/");
      }
    },
    onError: (error: Error) => {
      console.error("Logout error:", error);
      setAuthError(error);

      // Dispatch auth:logout event even on error to ensure WebSocket cleanup
      const logoutEvent = new CustomEvent('auth:logout', { 
        detail: { manual: true } 
      });
      window.dispatchEvent(logoutEvent);

      toast({
        title: "Logout error",
        description: "An error occurred during logout. Please try again.",
        variant: "destructive",
      });

      // Even if server-side logout fails, clear the local state
      queryClient.setQueryData(["currentUser"], { success: false, output: null });
      setLocation("/");
    },
  });

  // Login function
  const login = async (email: string, password: string, remember?: boolean) => {
    try {
      await loginMutation.mutateAsync({ email, password, remember });
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unknown error occurred during login");
    }
  };

  // Register function
  const register = async (
    email: string,
    password: string,
    username: string,
    termsAccepted: boolean = true,
    captchaToken: string | null = null
  ) => {
    try {
      // Bu çağrı, onSuccess içindeki kodu tetikleyecek (toast ve yönlendirme dahil)
      // Ama biz de yanıtı almak istiyoruz ki form içinde de işleyebilelim
      const response = await AuthService.register(
        email,
        password,
        username,
        termsAccepted,
        captchaToken
      );

      // Eğer başarılı kayıt olduysa ve token varsa, queryClient vs. işlemleri yapalım
      if (response.success && response.output?.token) {
        // Otomatik olarak giriş yapmış sayılalım - token ve kullanıcı bilgilerini saklayalım
        localStorage.setItem('auth_token', response.output.token);
        if (response.output.user) {
          localStorage.setItem('user_email', response.output.user);
        }

        // Kullanıcı verilerini güncelleyelim
        queryClient.invalidateQueries({ queryKey: ["currentUser"] });
      }

      return response; // Yanıtı doğrudan formda kullanmak için geri döndürelim
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unknown error occurred during registration");
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await logoutMutation.mutateAsync();
    } catch (error) {
      console.error("Logout error:", error);

      // Even if the API call fails, clear the local auth state
      queryClient.setQueryData(["currentUser"], { success: false, output: null });

      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unknown error occurred during logout");
    }
  };

  // Extract user from authData based on our new structure and map avatar_id to avatarId
  const user = authData?.success && authData?.output?.user ? {
    ...authData.output.user,
    avatarId: (authData.output.user as any).avatar_id || authData.output.user.avatarId
  } : null;
  const isLoggedIn = !!user;

  // Get token and permission information
  const token = localStorage.getItem('auth_token');
  const decodedToken = token ? decodeToken(token) : null;
  const permissions = decodedToken ? extractPermissions(decodedToken) : [];

  // Token expiry information
  const isExpired = decodedToken?.exp ? (decodedToken.exp * 1000) < Date.now() : true;
  const expiresAt = decodedToken?.exp ? new Date(decodedToken.exp * 1000) : null;

  // For development only - log authentication status
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Auth state:', {
        isLoggedIn,
        user
      });
    }
  }, [isLoggedIn, user]);

  // Listen for auth:logout event triggered by token refresh failures
  useEffect(() => {
    const handleAuthLogout = (event: CustomEvent) => {
      console.log('Auth logout event detected:', event.detail);

      // Clear auth data
      localStorage.removeItem("auth_token");
      localStorage.removeItem("user");

      // Update query client
      queryClient.setQueryData(["currentUser"], { success: false, output: null });

      // Show toast notification
      toast({
        title: "Session expired",
        description: "Your session has expired. Please log in again.",
        variant: "destructive",
      });

      // Redirect to login page if not already there
      if (window.location.pathname !== "/login" && window.location.pathname !== "/register") {
        setLocation("/login?session=expired");
      }
    };

    // Add event listener
    window.addEventListener('auth:logout', handleAuthLogout as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('auth:logout', handleAuthLogout as EventListener);
    };
  }, [queryClient, setLocation, toast]);

  // Permission check functions - reuse jwtUtils functions
  const checkHasPermission = (permission: string) => {
    return hasPermission(permissions, permission);
  };

  const checkHasAnyPermission = (requiredPermissions: string[]) => {
    for (const permission of requiredPermissions) {
      if (hasPermission(permissions, permission)) {
        return true;
      }
    }
    return false;
  };

  const checkHasAllPermissions = (requiredPermissions: string[]) => {
    for (const permission of requiredPermissions) {
      if (!hasPermission(permissions, permission)) {
        return false;
      }
    }
    return true;
  };

  // Package token information
  const tokenInfo = {
    token,
    decodedToken,
    isExpired,
    expiresAt
  };

  // Enhanced development logging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Token and permissions:', {
        token: token ? 'present' : 'not found',
        decodedToken,
        permissions,
        isExpired,
        expiresAt
      });
    }
  }, [token, decodedToken, permissions, isExpired, expiresAt]);

  // Clear comparison data when user changes
  useEffect(() => {
    const userId = user?.id?.toString() || null;
    clearOnUserChange(userId);
  }, [user, clearOnUserChange]);

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoggedIn,
        isLoading,
        error: authError || (error as Error | null),
        login,
        register,
        logout,
        // Add permission related values
        permissions,
        hasPermission: checkHasPermission,
        hasAnyPermission: checkHasAnyPermission,
        hasAllPermissions: checkHasAllPermissions,
        tokenInfo
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);

  // Make sure the context is properly initialized
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  // Ensure context has all required properties
  if (!isAuthContextComplete(context)) {
    console.error("Auth context is missing required properties:", context);
    return defaultAuthContext; // Return default context as fallback
  }

  return context;
}