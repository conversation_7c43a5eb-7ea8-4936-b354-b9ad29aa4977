/**
 * Notifications Hook
 *
 * React hook for managing real-time crypto alert notifications
 * Integrates with CoinScout WebSocket client
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import CoinScoutWebSocketClient, { getWebSocketUrl, type Notification } from '@/lib/CoinScoutWebSocketClient';
import { refreshToken, getAllNotifications } from '@/lib/api';

// Global WebSocket singleton
let globalWebSocketClient: CoinScoutWebSocketClient | null = null;
let globalWebSocketListeners: (() => void)[] = [];

interface NotificationState {
  notifications: Notification[]; // WebSocket unread notifications only
  allNotifications: Notification[]; // All notifications from API
  unreadCount: number;
  isConnected: boolean;
  isAuthenticated: boolean;
  isPopupOpen: boolean; // Track popup state
  frozenNotifications: Notification[] | null; // Frozen list while popup is open
  allNotificationsLoading: boolean; // Loading state for API notifications
  allNotificationsLoaded: boolean; // Track if all notifications have been loaded
}

interface UseNotificationsReturn extends NotificationState {
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  removeNotification: (notificationId: string) => void;
  clearAll: () => void;
  showBrowserNotification: (notification: Notification) => void;
  setPopupOpen: (isOpen: boolean) => void;
  getDisplayNotifications: () => Notification[];
  loadAllNotifications: () => Promise<void>;
  refreshAllNotifications: () => Promise<void>;
}

export function useNotifications(authToken?: string): UseNotificationsReturn {
  const wsClient = useRef<CoinScoutWebSocketClient | null>(null);
  const [state, setState] = useState<NotificationState>({
    notifications: [],
    allNotifications: [],
    unreadCount: 0,
    isConnected: false,
    isAuthenticated: false,
    isPopupOpen: false,
    frozenNotifications: null,
    allNotificationsLoading: false,
    allNotificationsLoaded: false
  });

  // Get or initialize global WebSocket connection
  const initializeWebSocket = useCallback(() => {
    console.log('🔗 Requesting WebSocket connection...');
    
    // If global WebSocket already exists and connected, reuse it
    if (globalWebSocketClient && globalWebSocketClient.getConnectionStatus().isConnected) {
      console.log('🔄 Reusing existing global WebSocket connection');
      wsClient.current = globalWebSocketClient;
      return globalWebSocketClient;
    }
    
    // If global WebSocket exists but not connected, disconnect it
    if (globalWebSocketClient) {
      console.log('🔌 Disconnecting existing global WebSocket connection');
      globalWebSocketClient.disconnect();
      globalWebSocketClient = null;
    }
    
    // Try to get real auth token from localStorage or use a generated one
    const storedToken = localStorage.getItem('auth_token');
    const token = authToken || storedToken || `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    console.log('🔌 Using WebSocket token:', token);
    const wsUrl = getWebSocketUrl();

    console.log('🔌 Creating NEW global WebSocket connection for notifications');

    // Initialize global WebSocket client
    globalWebSocketClient = new CoinScoutWebSocketClient(wsUrl, token, {
      debug: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5
    });

    // Set local reference to global client
    wsClient.current = globalWebSocketClient;

    return globalWebSocketClient;

    // Notification events
    wsClient.current.on('notification', (notification: Notification) => {
      console.log('🔔 [WebSocket] New notification received:', notification);
      console.log('📱 [WebSocket] Adding to unread notifications list');

      // Ensure notification has proper unread status and WebSocket source marker
      const wsNotification = {
        ...notification,
        status: 'unread' as const,
        source: 'websocket', // Mark as WebSocket notification
        timestamp: new Date(notification.timestamp)
      };

      // Add to notifications list
      setState(prev => ({
        ...prev,
        notifications: [wsNotification, ...prev.notifications.slice(0, 49)],
        unreadCount: prev.unreadCount + 1
      }));

      console.log('📊 [WebSocket] Notification added to unread list');

      // Show browser notification
      showBrowserNotification(wsNotification);
    });

    wsClient.current.on('unread_notifications', (data: any) => {
      console.log('📬 [WebSocket] Unread notifications batch received:', data);
      console.log('📊 [WebSocket] Unread count:', data.count);
      console.log('📋 [WebSocket] Notifications list:', data.notifications);

      // Check notification status
      if (data.notifications) {
        data.notifications.forEach((notif: any, index: number) => {
          console.log(`📝 [WebSocket] Notification ${index + 1} status:`, notif.status);
        });
      }

      // Process and mark notifications as WebSocket sourced
      const processedNotifications = (data.notifications || []).map((notif: any) => ({
        ...notif,
        status: 'unread', // Explicitly set as unread since these are unread notifications
        source: 'websocket', // Mark as WebSocket notification
        timestamp: new Date(notif.timestamp)
      }));

      // Update state with processed notifications
      setState(prev => ({
        ...prev,
        notifications: processedNotifications,
        unreadCount: data.count || 0
      }));

      console.log('✅ [WebSocket] Updated unread notifications list with', processedNotifications.length, 'items');
      
      // Log processed notifications with status
      processedNotifications.forEach((notif: any, index: number) => {
        console.log(`📋 [WebSocket] Processed notification ${index + 1}:`, {
          id: notif.id,
          title: notif.title,
          status: notif.status,
          source: notif.source
        });
      });
    });

    wsClient.current.on('error', (error: any) => {
      console.error('⚠️ WebSocket error:', error);
    });

    // Handle token refresh events
    wsClient.current.on('token_refreshed', () => {
      console.log('✅ [WebSocket] Token refreshed successfully');
    });

    wsClient.current.on('token_refresh_failed', (error: any) => {
      console.error('❌ [WebSocket] Token refresh failed:', error);
      console.log('🚪 [WebSocket] Redirecting to login due to token refresh failure');
      
      // Clear auth state and redirect to login
      localStorage.removeItem('auth_token');
      window.location.href = '/login?session=expired';
    });

    // Start token refresh mechanism (every 4 minutes)
    wsClient.current.startTokenRefresh(async () => {
      try {
        console.log('🔄 [WebSocket] Starting token refresh process...');
        const newToken = await refreshToken();
        console.log('✅ [WebSocket] Token refreshed successfully');
        return newToken;
      } catch (error) {
        console.error('❌ [WebSocket] Token refresh failed:', error);
        throw error;
      }
    }, 4 * 60 * 1000); // 4 minutes

    // Handle notification read confirmations
    wsClient.current.on('notification_read_confirmed', (data: any) => {
      console.log('✅ Notification marked as read confirmed:', data.notificationId);
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(notif =>
          notif.id === data.notificationId
            ? { ...notif, status: 'read' as const }
            : notif
        )
      }));
    });

    // Handle unread count updates
    wsClient.current.on('unread_count_update', (data: any) => {
      console.log('📊 Unread count updated:', data.count);
      setState(prev => ({
        ...prev,
        unreadCount: data.count || 0
      }));
    });

    return wsClient.current;
  }, [authToken]);

  // Browser notification function
  const showBrowserNotification = useCallback((notification: Notification) => {
    if (!('Notification' in window)) {
      console.log('Browser does not support notifications');
      return;
    }

    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.priority === 'high'
      });

      // Handle notification click
      browserNotification.onclick = () => {
        window.focus();
        if (notification.link) {
          window.location.href = notification.link;
        }
        browserNotification.close();
      };

      // Auto close after 5 seconds for non-high priority
      if (notification.priority !== 'high') {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }
    } else if (Notification.permission !== 'denied') {
      // Request permission
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          showBrowserNotification(notification);
        }
      });
    }
  }, []);

  // Notification management functions with WebSocket integration
  const markAsRead = useCallback((notificationId: string) => {
    console.log('🔔 [NotificationRead] Marking notification as read:', notificationId);
    
    // Send mark as read request to WebSocket server
    if (wsClient.current && wsClient.current.getConnectionStatus().isAuthenticated) {
      console.log('📡 [NotificationRead] Sending mark as read via WebSocket');
      wsClient.current.markNotificationAsRead(notificationId);
    } else {
      console.log('📱 [NotificationRead] WebSocket not available, using local fallback');
      // Fallback to local state update if WebSocket not available
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(notif =>
          notif.id === notificationId
            ? { ...notif, status: 'read' as const }
            : notif
        ),
        unreadCount: Math.max(0, prev.unreadCount - 1)
      }));
    }
  }, []);

  const markAllAsRead = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.map(notif => ({ ...notif, status: 'read' as const })),
      unreadCount: 0
    }));
  }, []);

  const removeNotification = useCallback((notificationId: string) => {
    setState(prev => {
      const notification = prev.notifications.find(n => n.id === notificationId);
      const wasUnread = notification?.status === 'unread';

      return {
        ...prev,
        notifications: prev.notifications.filter(notif => notif.id !== notificationId),
        unreadCount: wasUnread ? Math.max(0, prev.unreadCount - 1) : prev.unreadCount
      };
    });
  }, []);

  const clearAll = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: [],
      unreadCount: 0
    }));
  }, []);

  // Initialize WebSocket on mount and when authToken changes
  useEffect(() => {
    console.log('🔄 [useEffect] WebSocket initialization triggered');
    const client = initializeWebSocket();

    return () => {
      console.log('🧹 [useEffect] Cleanup - disconnecting WebSocket');
      if (client) {
        client.disconnect();
      }
    };
  }, [authToken]); // Only re-initialize when authToken changes

  // Listen for auth:logout event to disconnect WebSocket on logout
  useEffect(() => {
    const handleAuthLogout = (event: CustomEvent) => {
      console.log('🔐 [Auth] Received auth:logout event in notifications hook');
      
      // Set WebSocket as unauthenticated immediately to block incoming messages
      if (wsClient.current) {
        console.log('🔌 [WebSocket] Setting isAuthenticated to false and disconnecting');
        wsClient.current.isAuthenticated = false; // Block any incoming messages immediately
        wsClient.current.disconnect();
        wsClient.current = null;
      }
      
      // Reset notification state
      setState(prev => ({
        ...prev,
        notifications: [],
        allNotifications: [],
        unreadCount: 0,
        isConnected: false,
        isAuthenticated: false,
        isPopupOpen: false,
        frozenNotifications: null,
        allNotificationsLoading: false,
        allNotificationsLoaded: false
      }));
    };

    // Listen for the custom auth:logout event
    window.addEventListener('auth:logout', handleAuthLogout as EventListener);
    
    return () => {
      window.removeEventListener('auth:logout', handleAuthLogout as EventListener);
    };
  }, []);

  // Request browser notification permission on first load
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      console.log('Requesting browser notification permission');
      Notification.requestPermission().then(permission => {
        console.log('Notification permission:', permission);
      });
    }
  }, []);

  // Popup state management
  const setPopupOpen = useCallback((isOpen: boolean) => {
    setState(prev => {
      if (isOpen && !prev.isPopupOpen) {
        // Opening popup - freeze current notification list
        console.log('🔒 [Popup] Opening popup, freezing notification list');
        return {
          ...prev,
          isPopupOpen: true,
          frozenNotifications: [...prev.notifications] // Create frozen snapshot
        };
      } else if (!isOpen && prev.isPopupOpen) {
        // Closing popup - unfreeze and update with latest notifications
        console.log('🔓 [Popup] Closing popup, unfreezing notification list');
        return {
          ...prev,
          isPopupOpen: false,
          frozenNotifications: null // Clear frozen snapshot
        };
      }
      return prev;
    });
  }, []);

  // Get notifications to display (frozen when popup is open)
  const getDisplayNotifications = useCallback(() => {
    return state.isPopupOpen && state.frozenNotifications 
      ? state.frozenNotifications 
      : state.notifications;
  }, [state.isPopupOpen, state.frozenNotifications, state.notifications]);

  // Load all notifications from API (always fetch fresh data)
  const loadAllNotifications = useCallback(async (forceRefresh = false) => {
    if (state.allNotificationsLoading && !forceRefresh) {
      console.log('🔔 [API] All notifications already loading, skipping...');
      return;
    }

    setState(prev => ({
      ...prev,
      allNotificationsLoading: true
    }));

    try {
      console.log('🔔 [API] Loading all notifications from server...');
      const response = await getAllNotifications();
      
      if (response.success) {
        const apiNotifications = (response.notifications || []).map((notif: any) => ({
          ...notif,
          source: 'api', // Mark as API notification
          timestamp: new Date(notif.timestamp)
        }));

        console.log('🔔 [API] Loaded', apiNotifications.length, 'notifications from server');
        
        setState(prev => ({
          ...prev,
          allNotifications: apiNotifications,
          allNotificationsLoading: false,
          allNotificationsLoaded: true
        }));
      }
    } catch (error) {
      console.error('🔔 [API] Failed to load all notifications:', error);
      setState(prev => ({
        ...prev,
        allNotificationsLoading: false
      }));
    }
  }, [state.allNotificationsLoading]);

  // Force refresh all notifications (reset loaded flag and reload)
  const refreshAllNotifications = useCallback(async () => {
    setState(prev => ({
      ...prev,
      allNotificationsLoaded: false,
      allNotifications: []
    }));
    await loadAllNotifications();
  }, [loadAllNotifications]);



  return {
    ...state,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    showBrowserNotification,
    setPopupOpen,
    getDisplayNotifications,
    loadAllNotifications,
    refreshAllNotifications
  };
}

export default useNotifications;