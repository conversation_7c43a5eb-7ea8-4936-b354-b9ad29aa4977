import { CoinStatus } from "../types/CoinStatus";
import { useLanguage } from "../contexts/LanguageContext";

/**
 * Score değerine göre status metinlerini ve görsel stillerini hesaplayan utility fonksiyonları
 */

/**
 * Skor değerine göre standart bir durum (status) metni döndürür
 * @param score - 0-100 arası skor değeri
 * @returns CoinStatus tipinde durum metni
 */
export function getStatusFromScore(score: number): CoinStatus {
  if (score >= 90) return "Excellent";
  if (score >= 75) return "Good"; // İç sistemde "Good" değerini kullanıyoruz, çeviri için "Positive" anahtar adını kullanıyoruz
  if (score >= 65) return "Fair"; // İç sistemde "Fair" değerini kullanıyoruz, çeviri için "Average" anahtar adını kullanıyoruz
  if (score >= 50) return "Poor"; // İç sistemde "Poor" değerini kullanı<PERSON>ruz, çeviri için "Weak" anahtar adını kullanıyoruz
  return "Bad"; // İç sistemde "Bad" değerini kullanıyoruz, çeviri için "Critical" anahtar adını kullanıyoruz
}

/**
 * Skor durumunun çevirisini getiren fonksiyon
 * @param status - Skor durumu
 * @param t - Çeviri fonksiyonu, opsiyonel, verilmezse varsayılan İngilizce metinler kullanılır
 * @returns Çevrilmiş durum metni
 */
export function getStatusTranslation(status: CoinStatus, t?: Function): string {
  // t fonksiyonu sağlanmışsa kullan
  if (t) {
    // Önemli: t() fonksiyonu ile çeviri yaparken, anahtar adları küçük harflerle olmalı
    switch (status) {
      case "Excellent":
        return t("score:excellent", "score", "Excellent");
      case "Good":
        return t("score:positive", "score", "Positive");
      case "Fair":
        return t("score:average", "score", "Average");
      case "Poor":
        return t("score:weak", "score", "Weak");
      case "Bad":
        return t("score:critical", "score", "Critical");
      default:
        return status;
    }
  }
  
  // t fonksiyonu verilmemişse varsayılan İngilizce değeri döndür
  switch (status) {
    case "Excellent":
      return "Excellent";
    case "Good":
      return "Positive";
    case "Fair":
      return "Average";
    case "Poor":
      return "Weak";
    case "Bad":
      return "Critical";
    default:
      return status;
  }
}

/**
 * Bileşen içinde kullanmak için skor durumu çevirisi - Hook kullanımı gereken bileşenler için
 * Not: Bu fonksiyon React hook kullandığı için sadece React bileşenleri içinde kullanılabilir
 * @param status - Skor durumu
 * @param t - Çeviri fonksiyonu (useLanguage hook'undan alınmalı)
 * @returns Çevrilmiş durum metni
 */
export function getStatusTranslationInComponent(status: CoinStatus, t: Function): string {
  // Önemli: t() fonksiyonu ile çeviri yaparken, anahtar adları küçük harflerle olmalı
  switch (status) {
    case "Excellent":
      return t("score:excellent", "score", "Excellent");
    case "Good":
      return t("score:positive", "score", "Positive");
    case "Fair":
      return t("score:average", "score", "Average");
    case "Poor":
      return t("score:weak", "score", "Weak");
    case "Bad":
      return t("score:critical", "score", "Critical");
    default:
      return status;
  }
}

/**
 * Skor değerine göre renk sınıfları ve metin içeren bir kapsamlı stil nesnesi döndürür
 * @param score - 0-100 arası skor değeri
 * @returns Renk sınıfları ve metin içeren bir nesne
 */
export function getScoreRatingStyles(score: number): {
  text: string;
  bgClass: string;
  colorClass: string;
  borderClass: string;
  bgLightClass: string;
  colorWithOpacity80: string;
  colorWithOpacity60: string;
  bgWithOpacity10: string;
  bgWithOpacity20: string;
} {
  if (score >= 90)
    return {
      text: "Excellent",
      bgClass: "bg-[#00D88A]",
      colorClass: "text-[#00D88A]",
      borderClass: "border-[#00D88A]",
      bgLightClass: "bg-[#00D88A]/10",
      colorWithOpacity80: "text-[#00D88A]/80 group-hover/row:text-[#00D88A]",
      colorWithOpacity60: "text-[#00D88A]/60 group-hover/row:text-[#00D88A]",
      bgWithOpacity10: "bg-[#00D88A]/10",
      bgWithOpacity20: "bg-[#00D88A]/10 group-hover/row:bg-[#00D88A]/20"
    };
  if (score >= 75)
    return {
      text: "Positive",
      bgClass: "bg-[#00B8D9]",
      colorClass: "text-[#00B8D9]", 
      borderClass: "border-[#00B8D9]",
      bgLightClass: "bg-[#00B8D9]/10",
      colorWithOpacity80: "text-[#00B8D9]/80 group-hover/row:text-[#00B8D9]",
      colorWithOpacity60: "text-[#00B8D9]/60 group-hover/row:text-[#00B8D9]",
      bgWithOpacity10: "bg-[#00B8D9]/10",
      bgWithOpacity20: "bg-[#00B8D9]/10 group-hover/row:bg-[#00B8D9]/20"
    };
  if (score >= 65)
    return {
      text: "Average",
      bgClass: "bg-[#FFAB00]",
      colorClass: "text-[#FFAB00]",
      borderClass: "border-[#FFAB00]",
      bgLightClass: "bg-[#FFAB00]/10",
      colorWithOpacity80: "text-[#FFAB00]/80 group-hover/row:text-[#FFAB00]",
      colorWithOpacity60: "text-[#FFAB00]/60 group-hover/row:text-[#FFAB00]",
      bgWithOpacity10: "bg-[#FFAB00]/10",
      bgWithOpacity20: "bg-[#FFAB00]/10 group-hover/row:bg-[#FFAB00]/20"
    };
  if (score >= 50)
    return {
      text: "Weak",
      bgClass: "bg-[#FF5630]",
      colorClass: "text-[#FF5630]",
      borderClass: "border-[#FF5630]",
      bgLightClass: "bg-[#FF5630]/10",
      colorWithOpacity80: "text-[#FF5630]/80 group-hover/row:text-[#FF5630]",
      colorWithOpacity60: "text-[#FF5630]/60 group-hover/row:text-[#FF5630]",
      bgWithOpacity10: "bg-[#FF5630]/10",
      bgWithOpacity20: "bg-[#FF5630]/10 group-hover/row:bg-[#FF5630]/20"
    };
  return {
    text: "Critical",
    bgClass: "bg-[#FF3B3B]",
    colorClass: "text-[#FF3B3B]",
    borderClass: "border-[#FF3B3B]",
    bgLightClass: "bg-[#FF3B3B]/10",
    colorWithOpacity80: "text-[#FF3B3B]/80 group-hover/row:text-[#FF3B3B]",
    colorWithOpacity60: "text-[#FF3B3B]/60 group-hover/row:text-[#FF3B3B]",
    bgWithOpacity10: "bg-[#FF3B3B]/10",
    bgWithOpacity20: "bg-[#FF3B3B]/10 group-hover/row:bg-[#FF3B3B]/20"
  };
}

/**
 * Skor tabanlı bir CoinScore nesnesini günceller, API'den gelen status'u yok sayar
 * @param scoreObj - Güncellenecek skor nesnesi
 * @returns Güncellenmiş skor nesnesi
 */
export function normalizeScoreObject(scoreObj: {
  score: number;
  label?: string;
  status?: CoinStatus | null;
}): {
  score: number;
  label: string;
  status: CoinStatus;
} {
  // Skor değerine göre status hesapla
  const status = getStatusFromScore(scoreObj.score);

  // Güncellenmiş nesneyi döndür
  return {
    score: scoreObj.score,
    label: scoreObj.label || "",
    status,
  };
}
