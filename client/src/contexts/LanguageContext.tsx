import { createContext, useContext, useEffect, useState, useCallback } from "react";
import { getFontConfig, generateFontCustomProperties } from "@/lib/fonts";
import { useToast } from "@/hooks/use-toast";
import { availableLanguages as locales, getTranslations, defaultLocale } from "@/locales";

// Type for language object
export interface Language {
  id: number;
  code: string;
  name: string;
  nativeName: string;
  isRTL: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Language context type definition
interface LanguageContextType {
  currentLanguage: Language | null;
  availableLanguages: Language[];
  isLoading: boolean;
  setLanguage: (languageCode: string) => Promise<void>;
  translations: Record<string, string>;
  t: (key: string, namespace?: string, fallback?: string) => string;
  clearTranslationCache: () => void;
  refreshTranslations: () => Promise<void>;
  translationStats: {
    total: number;
    missing: string[];
    lastUpdated: string;
  };
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [currentLanguage, setCurrentLanguage] = useState<Language | null>(null);
  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [missingKeys, setMissingKeys] = useState<string[]>([]);
  const [lastUpdated, setLastUpdated] = useState<string>(new Date().toISOString());
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Available languages from locales - only show active languages
  const availableLanguages: Language[] = locales.filter(lang => lang.isActive);
  
  // Apply font configuration when language changes
  useEffect(() => {
    if (!currentLanguage) return;
    
    const fontConfig = getFontConfig(currentLanguage);
    const customProperties = generateFontCustomProperties(fontConfig);

    // Apply custom properties to document root
    Object.entries(customProperties).forEach(([property, value]) => {
      document.documentElement.style.setProperty(property, value);
    });

    // Set text direction on body
    document.body.dir = fontConfig.direction;
    
    // Load translations for the current language
    const langTranslations = getTranslations(currentLanguage.code);
    setTranslations(langTranslations);
    setIsLoading(false);
  }, [currentLanguage]);

  // Initialize language from localStorage or browser preference
  useEffect(() => {
    if (currentLanguage !== null) return;

    const savedLanguage = localStorage.getItem("preferredLanguage");
    const browserLanguage = navigator.language.split("-")[0];

    // Find language by code in available languages
    const findLanguage = (code: string) =>
      availableLanguages.find((lang) => lang.code === code);

    // Default to English (code 'en') if available, otherwise use first language
    const englishLanguage = availableLanguages.find((lang) => lang.code === 'en');
    const defaultLanguage = englishLanguage || availableLanguages[0];
    
    // Try to find saved language or browser language, fallback to default
    const language = savedLanguage
      ? findLanguage(savedLanguage) || defaultLanguage
      : findLanguage(browserLanguage) || defaultLanguage;

    if (language) {
      console.log(`Setting current language: ${language.name} (${language.code}, ID: ${language.id})`);
      setCurrentLanguage(language);
      localStorage.setItem("preferredLanguage", language.code);
    }
  }, [availableLanguages, currentLanguage]);

  // Function to change the language
  const setLanguage = useCallback(async (languageCode: string): Promise<void> => {
    console.log("setLanguage called with:", languageCode);
    const language = availableLanguages.find((lang) => lang.code === languageCode);
    if (!language) {
      console.error("Language not found:", languageCode);
      return;
    }

    console.log("Changing to language:", language.name);
    setIsLoading(true);

    try {
      // Update the current language and local storage
      setCurrentLanguage(language);
      localStorage.setItem("preferredLanguage", languageCode);
      localStorage.setItem("language_code", languageCode);
      
      // Reset translations temporarily to avoid showing stale content
      setTranslations({});
      
      console.log("Language changed successfully to:", language.name);
    } catch (error) {
      console.error("Error changing language:", error);
      setIsLoading(false);
    }
  }, [availableLanguages]);

  // Clear translation cache and force refresh
  const clearTranslationCache = useCallback(() => {
    if (!currentLanguage) return;
    
    // Clear in-memory translations
    setTranslations({});
    
    // Load translations again
    const langTranslations = getTranslations(currentLanguage.code);
    setTranslations(langTranslations);
    
    toast({
      title: "Translation cache cleared",
      description: "Reloaded translations",
      duration: 3000
    });
  }, [currentLanguage, toast]);
  
  // Manually refresh translations
  const refreshTranslations = useCallback(async () => {
    if (!currentLanguage) return;
    
    setLastUpdated(new Date().toISOString());
    
    try {
      // Load translations again
      const langTranslations = getTranslations(currentLanguage.code);
      setTranslations(langTranslations);
      
      toast({
        title: "Translations refreshed",
        description: `Loaded ${Object.keys(langTranslations).length} translations`,
        duration: 3000
      });
    } catch (error) {
      console.error("Failed to refresh translations:", error);
      toast({
        title: "Failed to refresh translations",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
        duration: 5000
      });
    }
  }, [currentLanguage, toast]);

  // Enhanced translation function with better support for nested keys
  const t = useCallback((key: string, namespace: string = "common", fallback?: string) => {
    // Get current language code
    const langCode = currentLanguage?.code || defaultLocale;
    
    // Step 1: Try accessing nested objects first (new format)
    if (namespace && key) {
      // Check if namespace exists as direct object property
      if (translations[namespace] && typeof translations[namespace] === 'object') {
        const namespaceObj = translations[namespace] as Record<string, any>;
        
        // Handle dot notation for deeper nesting
        if (key.includes('.')) {
          const keyParts = key.split('.');
          let currentObj: any = namespaceObj;
          let found = true;
          
          // Navigate through nested objects
          for (let i = 0; i < keyParts.length - 1; i++) {
            if (currentObj[keyParts[i]] && typeof currentObj[keyParts[i]] === 'object') {
              currentObj = currentObj[keyParts[i]];
            } else {
              found = false;
              break;
            }
          }
          
          // Check if we can get the final value
          const finalKey = keyParts[keyParts.length - 1];
          if (found && currentObj[finalKey]) {
            return currentObj[finalKey];
          }
        } else if (namespaceObj[key]) {
          // Simple namespace.key access
          return namespaceObj[key];
        }
      }
    }
    
    // Step 2: Try with namespace:key format (old format)
    const colonFormat = `${namespace}:${key}`;
    if (translations[colonFormat]) {
      return translations[colonFormat];
    }
    
    // Step 3: Try with namespace.key format (flattened format)
    const dotFormat = `${namespace}.${key}`;
    if (translations[dotFormat]) {
      return translations[dotFormat];
    }
    
    // Step 4: Try direct key without namespace
    if (translations[key]) {
      return translations[key];
    }
    
    // Step 5: Check for direct access but with dot notation (flattened nested objects)
    if (key.includes('.')) {
      const fullKey = key.includes(':') ? key : `${namespace}:${key}`;
      if (translations[fullKey]) {
        return translations[fullKey];
      }
    }
    
    // Step 6: Use provided fallback
    if (fallback !== undefined) {
      return fallback;
    }
    
    // Track missing keys
    setTimeout(() => {
      const keyToTrack = namespace ? `${namespace}:${key}` : key;
      if (!missingKeys.includes(keyToTrack)) {
        setMissingKeys(prev => [...prev, keyToTrack]);
        console.warn(`Missing translation key: ${keyToTrack}`);
      }
    }, 0);
    
    // Return key as last resort
    return key;
  }, [translations, missingKeys, currentLanguage]);

  return (
    <LanguageContext.Provider
      value={{
        currentLanguage,
        availableLanguages,
        isLoading,
        setLanguage,
        translations,
        t,
        clearTranslationCache,
        refreshTranslations,
        translationStats: {
          total: Object.keys(translations).length,
          missing: missingKeys,
          lastUpdated
        }
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

// Define the hook as a named function declaration instead of an arrow function
// This helps with HMR (Hot Module Replacement) compatibility
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}