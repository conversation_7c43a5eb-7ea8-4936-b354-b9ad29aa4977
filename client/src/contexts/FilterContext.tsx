import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { FilterControlsState } from '@/components/FilterControls';
import { CoinFilters } from '@/lib/services/CoinService';
import { useAuth } from '@/hooks/use-auth';

// Default filter values - using null for API to indicate no filtering
const DEFAULT_FILTERS: FilterControlsState = {
  marketCapRange: [1000000, 100000000000], // Default range for UI display
  projectScoreRange: [0, 100], // Default range for UI display
  categories: [],
  chains: [],
};

// Context type definitions
interface FilterContextType {
  filters: FilterControlsState;
  apiFilters: CoinFilters;
  updateFilters: (newFilters: FilterControlsState, isReset?: boolean) => void;
  resetFilters: () => void;
}

// Create the context
const FilterContext = createContext<FilterContextType | undefined>(undefined);

// Provider component
export const FilterProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Get auth state for monitoring login changes
  const { isLoggedIn } = useAuth();
  
  // State for the filters
  const [filters, setFilters] = useState<FilterControlsState>(DEFAULT_FILTERS);
  
  // Derived API filters - all null by default (no filtering)
  const [apiFilters, setApiFilters] = useState<CoinFilters>({
    marketcap_min: null,
    marketcap_max: null,
    score_min: null,
    score_max: null,
    selectedcategories: null,
    selectedchains: null,
    change: null,
  });

  // No localStorage loading - filters reset on each session

  // Reset filters when login status changes
  useEffect(() => {
    console.log("Login status changed:", isLoggedIn, "- Resetting filters to default (all null)");
    // Reset to default filters with null API values
    setFilters(DEFAULT_FILTERS);
    setApiFilters({
      marketcap_min: null,
      marketcap_max: null,
      score_min: null,
      score_max: null,
      selectedcategories: null,
      selectedchains: null,
      change: null,
    });
  }, [isLoggedIn]);

  // Function to update filters
  const updateFilters = (newFilters: FilterControlsState, isReset = false) => {
    if (isReset) {
      setFilters(DEFAULT_FILTERS);
      setApiFilters({
        marketcap_min: null,
        marketcap_max: null,
        score_min: null,
        score_max: null,
        selectedcategories: null,
        selectedchains: null,
        change: null,
      });
      return;
    }

    // Update state filters
    setFilters(newFilters);
    
    // Convert to API filters format - send null if default values are used
    const isDefaultMarketCap = 
      newFilters.marketCapRange[0] === DEFAULT_FILTERS.marketCapRange[0] && 
      newFilters.marketCapRange[1] === DEFAULT_FILTERS.marketCapRange[1];
    
    const isDefaultScore = 
      newFilters.projectScoreRange[0] === DEFAULT_FILTERS.projectScoreRange[0] && 
      newFilters.projectScoreRange[1] === DEFAULT_FILTERS.projectScoreRange[1];
    
    const newApiFilters: CoinFilters = {
      marketcap_min: isDefaultMarketCap ? null : newFilters.marketCapRange[0],
      marketcap_max: isDefaultMarketCap ? null : newFilters.marketCapRange[1],
      score_min: isDefaultScore ? null : newFilters.projectScoreRange[0],
      score_max: isDefaultScore ? null : newFilters.projectScoreRange[1],
      selectedcategories: newFilters.categories.length > 0 ? newFilters.categories : null,
      selectedchains: newFilters.chains.length > 0 ? newFilters.chains : null,
      change: null,
    };
    
    // Update API filters
    setApiFilters(newApiFilters);
    
    // No longer saving to localStorage - filters are session-only
  };

  // Function to reset filters
  const resetFilters = () => {
    updateFilters(DEFAULT_FILTERS, true);
  };

  return (
    <FilterContext.Provider value={{ filters, apiFilters, updateFilters, resetFilters }}>
      {children}
    </FilterContext.Provider>
  );
};

// Hook to use the filter context
export const useFilters = () => {
  const context = useContext(FilterContext);
  if (context === undefined) {
    throw new Error('useFilters must be used within a FilterProvider');
  }
  return context;
};