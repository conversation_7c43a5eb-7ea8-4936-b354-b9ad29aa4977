import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { useQuery } from '@tanstack/react-query';
import { getUserSubscription } from '@/services/subscriptionService';

// Subscription types
export interface SubscriptionPlan {
  id: string;
  name: string;
  level: number;
  price: number;
  currency: string;
  interval: 'monthly' | 'annually' | 'lifetime';
  features: string[];
  isPopular?: boolean;
}

export interface UserSubscription {
  id: string;
  planId: string;
  planName: string;
  status: 'active' | 'canceled' | 'past_due' | 'trialing' | 'incomplete' | 'incomplete_expired';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  level: number;
  price: number;
  currency: string;
  interval: 'monthly' | 'annually' | 'lifetime';
}

// Subscription context type
export interface SubscriptionContextType {
  subscription: UserSubscription | null;
  isLoading: boolean;
  error: Error | null;
  refetchSubscription: () => Promise<void>;
  hasSubscription: boolean;
  hasPaidSubscription: boolean;
  subscriptionLevel: number;
  canAccessFeature: (requiredLevel: number) => boolean;
}

// Create context with default values
const SubscriptionContext = createContext<SubscriptionContextType>({
  subscription: null,
  isLoading: false,
  error: null,
  refetchSubscription: async () => {},
  hasSubscription: false,
  hasPaidSubscription: false,
  subscriptionLevel: 0,
  canAccessFeature: () => false,
});

// Provider component
export const SubscriptionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isLoggedIn, user } = useAuth();
  const { toast } = useToast();
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);

  // Use React Query to fetch subscription data
  const { 
    data, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['/api/user/subscription'],
    queryFn: async () => {
      const response = await getUserSubscription();
      
      // Handle the specific case where API returns {active: false, message: "User has no Stripe customer ID"}
      if (response.success && response.data && typeof response.data === 'object' && 'active' in response.data && response.data.active === false) {
        console.log("User has no active subscription from context:", (response.data as any).message || "No Stripe customer ID");
        return { success: true, data: null, message: (response.data as any).message };
      }
      
      return response;
    },
    enabled: isLoggedIn, // Only fetch if user is logged in
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update subscription state when data changes
  useEffect(() => {
    if (data && data.success && data.data && typeof data.data === 'object' && 'level' in data.data) {
      setSubscription(data.data as UserSubscription);
      console.log("Subscription data loaded:", data.data);
      console.log("Subscription level:", (data.data as UserSubscription).level);
      console.log("Has paid subscription:", (data.data as UserSubscription).level > 0);
    } else {
      setSubscription(null);
      console.log("No subscription data available", data);
    }
  }, [data]);

  // Clear subscription data when user logs out
  useEffect(() => {
    if (!isLoggedIn) {
      setSubscription(null);
      console.log("User logged out, clearing subscription data");
    }
  }, [isLoggedIn]);

  // Refetch subscription data
  const refetchSubscription = async () => {
    if (isLoggedIn) {
      try {
        await refetch();
      } catch (err) {
        toast({
          title: "Error",
          description: "Failed to refresh subscription data",
          variant: "destructive",
        });
      }
    }
  };

  // Determine if user has any subscription
  const hasSubscription = !!subscription;

  // Determine if user has a paid subscription (not free tier)
  const hasPaidSubscription = !!(subscription && subscription.level > 0);

  // Get the subscription level (0 for free, higher numbers for paid tiers)
  const subscriptionLevel = subscription?.level || 0;

  // Function to check if user can access a feature based on subscription level
  const canAccessFeature = (requiredLevel: number) => {
    return subscriptionLevel >= requiredLevel;
  };

  // Create the context value
  const contextValue: SubscriptionContextType = {
    subscription,
    isLoading,
    error: error as Error | null,
    refetchSubscription,
    hasSubscription,
    hasPaidSubscription,
    subscriptionLevel,
    canAccessFeature,
  };

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}
    </SubscriptionContext.Provider>
  );
};

// Custom hook to use the subscription context
export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};