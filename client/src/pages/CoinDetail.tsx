import React, { useState, useEffect, useCallback } from "react";
import { use<PERSON>out<PERSON>, Link, useLocation } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { ProgressCircle } from "@/components/ProgressCircle";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from "framer-motion";
import ScoreGauge from "@/components/coin-detail/ScoreGaugeNew";
import { useAuth } from "@/hooks/use-auth";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  ChevronLeft,
  Sparkles,
  Shield,
  Users,
  BarChart4,
  BarChart3,
  Lightbulb,
  HeartPulse,
  DollarSign,
  Building2,
  Clock,
  LineChart,
  TrendingDown,
  TrendingUp,
  HelpCircle,
  LayoutGrid,
  Link as LinkIcon,
  Coins,
  Database,
  Layers,
  Award,
  Share,
  AlertCircle,
} from "lucide-react";
import { WatchlistButton } from "@/components/watchlists/WatchlistButton";
import { ContextAwareWatchlistButton } from "@/components/watchlists/ContextAwareWatchlistButton";
import { ContextAwareAlertButton } from "@/components/alerts/ContextAwareAlertButton";
import DownloadPdfButton from "@/components/reports/DownloadPdfButton";
import ReportErrorButton from "@/components/coin-detail/ReportErrorButton";
import { ScoreIndicator } from "@/components/ScoreIndicator";
import { CoinListScoreIndicator } from "@/components/CoinListScoreIndicator";
import ShareMechanism from "@/components/share/ShareMechanism";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Import types from scoreMethodology.ts to maintain compatibility
import { CoinData } from "@/lib/scoreMethodology";

// Import coin detail models
import { CoinDetailModel, CoinAdvancedDetailModel } from "@/types/coinDetail";

// Import coin detail service
import { CoinDetailService } from "@/lib/services/CoinDetailService";

// Import language context for localization (already imported above)

// Import our components
import DetailedAnalysis from "@/components/coin-detail/DetailedAnalysis";
import LinksCard from "@/components/coin-detail/LinksCard";
import SocialsCard from "@/components/coin-detail/SocialsCard";
import CoinImage from "@/components/coin-detail/CoinImage";
import PriceChart from "@/components/charts/PriceChart";
import ScoreChart from "@/components/charts/ScoreChart";
import LaunchpadInfo from "@/components/coin-detail/LaunchpadInfo";
import {
  CoinDetailData,
  mockBitcoinDetailData,
  getScoreColor,
  getScoreName,
  getDefaultScoreForCategory,
  generateRealMethodologyData,
  ProjectLinks,
} from "@/lib/coinDetailModel";
import {
  enhanceMetricsWithMethodology,
  getMethodologyKeyForMetric,
} from "@/lib/methodologyUtils";
import { allMethodologies } from "@/lib/methodologies";

/**
 * Format a currency value with robust fallbacks and configuration options
 * @param value The numeric value to format
 * @param options Configuration options for the formatter
 * @returns A formatted currency string
 */
interface CurrencyFormatOptions {
  /** Currency symbol to use (default: '$') */
  currency?: string;
  /** Locale for number formatting (default: 'en-US') */
  locale?: string;
  /** Whether to use compact notation (e.g., 1K, 1M) (default: false) */
  compact?: boolean;
  /** Fixed precision to use, overrides min/max logic if provided */
  precision?: number;
  /** Minimum precision to use (default: 2) */
  minPrecision?: number;
  /** Maximum precision to use (default: 6) */
  maxPrecision?: number;
  /** Whether to show zero values as formatted zeros (default: true) */
  showZero?: boolean;
  /** Display value for zero/undefined/null values when showZero is false (default: 'N/A') */
  zeroValue?: string;
  /** Whether to show negative zeros as negative (default: false) */
  negativeZero?: boolean;
}

/**
 * Format a currency value with robust fallbacks and accessibility features
 * Uses different decimal places based on value magnitude
 * @param value The numeric value to format
 * @param options Configuration options for the formatter
 * @param t Translation function (from useLanguage hook)
 * @param currentLanguage Current language code
 * @returns A formatted currency string
 */
const formatCurrencyValue = (
  value: number | string | undefined | null,
  options: CurrencyFormatOptions = {},
  t?: Function,
  currentLanguage?: string,
): string => {

  // Default options with improved defaults
  const {
    currency = "$",
    locale = "tr-TR", // Default to Turkish locale for this use case
    compact = false,
    precision,
    minPrecision = 2,
    maxPrecision = 6,
    showZero = true, // Changed default to true for better UX
    zeroValue = "N/A",
    negativeZero = false, // Whether to show negative zero values as negative
  } = options;

  // Handle undefined, null, NaN, or empty cases with better fallbacks
  if (value === undefined || value === null) {
    return showZero ? `${currency}0.00` : zeroValue;
  }

  // Convert string to number if it's a string
  if (typeof value === "string") {
    const parsed = parseFloat(value);
    if (isNaN(parsed)) {
      return showZero ? `${currency}0.00` : zeroValue;
    }
    value = parsed;
  }

  // Handle negative zero case
  if (value === 0 || (Math.abs(value) < 0.000001 && !negativeZero)) {
    return showZero ? `${currency}0.00` : zeroValue;
  }

  try {
    // Format based on price ranges with different decimal places
    if (compact) {
      // Use custom compact notation with language-specific abbreviations
      const trillion = 1_000_000_000_000;
      const billion = 1_000_000_000;
      const million = 1_000_000;
      const thousand = 1_000;

      // Get the number value
      const numValue = Number(value);
      const absValue = Math.abs(numValue);

      // Format with appropriate abbreviation based on language
      if (absValue >= trillion) {
        const formatted = (numValue / trillion).toFixed(
          precision !== undefined ? precision : 2,
        );
        // Use translated abbreviation with fallback
        return `${currency}${formatted}${t ? t("format:trillion", "format", "T") : "T"}`;
      } else if (absValue >= billion) {
        const formatted = (numValue / billion).toFixed(
          precision !== undefined ? precision : 2,
        );
        // Use translated abbreviation with fallback
        return `${currency}${formatted}${t ? t("format:billion", "format", "B") : "B"}`;
      } else if (absValue >= million) {
        const formatted = (numValue / million).toFixed(
          precision !== undefined ? precision : 2,
        );
        // Use translated abbreviation with fallback
        return `${currency}${formatted}${t ? t("format:million", "format", "M") : "M"}`;
      } else if (absValue >= thousand) {
        const formatted = (numValue / thousand).toFixed(
          precision !== undefined ? precision : 2,
        );
        // Use translated abbreviation with fallback
        return `${currency}${formatted}${t ? t("format:thousand", "format", "K") : "K"}`;
      } else {
        // Format smaller numbers without abbreviation
        return `${currency}${numValue.toFixed(precision !== undefined ? precision : 2)}`;
      }
    } else {
      // Format with variable precision based on value magnitude
      if (value < 1) {
        // Less than $1: show 5 decimal places
        return `${currency}${value.toLocaleString(locale, {
          minimumFractionDigits: 5,
          maximumFractionDigits: 5,
        })}`;
      } else if (value < 100) {
        // Between $1 and $100: show 2 decimal places
        return `${currency}${value.toLocaleString(locale, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`;
      } else if (value < 1000) {
        // Between $100 and $1000: show 2 decimal places
        return `${currency}${value.toLocaleString(locale, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`;
      } else if (value < 10000) {
        // Between $1000 and $10000: show 1 decimal place
        return `${currency}${value.toLocaleString(locale, {
          minimumFractionDigits: 1,
          maximumFractionDigits: 1,
        })}`;
      } else {
        // Greater than $10000: show no decimal places
        return `${currency}${Math.round(value).toLocaleString(locale, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        })}`;
      }
    }
  } catch (error) {
    // Fallback for formatting errors
    console.warn("Currency formatting error:", error);
    return `${currency}${Number(value).toFixed(2)}`;
  }
};

// Formatted date string for easy reuse - in DD/MM format
const formatDateDDMM = (dateString: string) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  return `${day}/${month}`;
};

export default function CoinDetail() {
  // Get the coin ID from the URL
  const [coinMatch, coinParams] = useRoute("/coin/:id");
  const [detailMatch, detailParams] = useRoute("/coindetail/:id");
  const { isLoggedIn } = useAuth();
  const [, setLocation] = useLocation();
  const { t, currentLanguage } = useLanguage();

  // Function to handle back navigation using browser history
  const handleBackNavigation = () => {
    // Check if there's a previous page in browser history
    if (window.history.length > 1) {
      window.history.back();
    } else {
      // Fallback to coin list if no history available
      setLocation("/coinlist");
    }
  };

  // Kullanıcı giriş yapmamışsa login sayfasına yönlendir
  useEffect(() => {
    if (!isLoggedIn) {
      // Kullanıcı giriş yaptıktan sonra bu sayfaya geri dönebilmesi için mevcut URL'i de gönder
      const currentPath = window.location.pathname;
      setLocation(`/login?returnTo=${encodeURIComponent(currentPath)}`);
    }
  }, [isLoggedIn, setLocation]);

  // Use the ID from either route, or default to bitcoin
  const coinId = coinParams?.id || detailParams?.id || "bitcoin";

  // State for enhanced CoinDetailData - mockBitcoinDetailData yerine boş bir başlangıç state'i kullanacağız
  const [coinDetailData, setCoinDetailData] = useState<CoinDetailData>(() => {
    // Boş başlangıç state'i (temel API veri yapısına uygun)
    const initialData: CoinDetailData = {
      id: "",
      name: "",
      symbol: "",
      image: "",
      rank: 0,
      total_score: 0,
      description: "",
      geckoslug: "",
      watchlist_count: 0, // Watchlist sayısı için başlangıç değeri
      total_supply: null,
      max_supply: null,
      circulating_supply: null,
      ath: undefined,
      ath_change: undefined,
      atl: undefined,
      atl_change: undefined,
      marketData: {
        H24Volume: 0,
        MarketCap: 0,
        FullyDilutedValuation: 0,
        priceChanges: [],
        priceChangesMap: {
          "24h": 0,
          "7d": 0,
          "30d": 0,
          "90d": 0,
          "1y": 0,
        },
      },
      links: {
        homepage: [],
        blockchain_site: [],
        official_forum_url: [],
        chat_url: [],
        announcement_url: [],
        repos_url: {
          github: [],
          bitbucket: [],
        },
      },
      scores: [],
      socials: {},
      risk: {},
      metrics: {},
      categoryNames: ["Cryptocurrency"], // CoinDetailData'da gerekli bir alan
    };

    return initialData;
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // State for current price data (separate API call)
  const [currentPrice, setCurrentPrice] = useState<number | null>(null);
  const [isPriceLoading, setIsPriceLoading] = useState<boolean>(false);
  const [priceError, setPriceError] = useState<string | null>(null);

  // State for UI interaction
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState<number>(0);
  const [selectedMetricIndex, setSelectedMetricIndex] = useState<number | null>(
    null,
  );
  const [isFullDescriptionVisible, setIsFullDescriptionVisible] =
    useState<boolean>(false);
  const [showAdvancedView, setShowAdvancedView] = useState<boolean>(false);

  // State for interactive charts (simplified)
  const [priceHoverPosition, setPriceHoverPosition] = useState<number | null>(
    null,
  );
  const [scoreHoverPosition, setScoreHoverPosition] = useState<number | null>(
    null,
  );
  const [scoreTimeframe, setScoreTimeframe] = useState<"1M" | "3M" | "6M">(
    "3M",
  );
  const [priceTimeframe, setPriceTimeframe] = useState<"7D" | "14D" | "30D">(
    "7D",
  );
  // HTML tooltip state for chart (overlay outside of SVG)
  const [tooltipStyle, setTooltipStyle] = useState<{
    left: number;
    top: number;
    display: string;
  }>({
    left: 0,
    top: 0,
    display: "none",
  });
  const [tooltipData, setTooltipData] = useState<{
    date: string;
    value: number;
  } | null>(null);
  const [scoreTooltipStyle, setScoreTooltipStyle] = useState<{
    left: number;
    top: number;
    display: string;
  }>({
    left: 0,
    top: 0,
    display: "none",
  });
  const [scoreTooltipData, setScoreTooltipData] = useState<{
    date: string;
    value: number;
  } | null>(null);

  // State for action buttons
  const [inPortfolio, setInPortfolio] = useState<boolean>(false);
  const [alertCreated, setAlertCreated] = useState<boolean>(false);

  // Handle portfolio toggle action
  const handlePortfolioToggle = useCallback(() => {
    const newState = !inPortfolio;
    setInPortfolio(newState);
    // In a real implementation, this would call an API to add/remove from portfolio
    console.log(
      `${newState ? "Added to" : "Removed from"} portfolio: ${coinId}`,
    );
  }, [inPortfolio, coinId]);

  // Handle alert toggle action
  const handleAlertToggle = useCallback(() => {
    const newState = !alertCreated;
    setAlertCreated(newState);
    // In a real implementation, this would call an API to create/remove alert
    console.log(
      `${newState ? "Created" : "Removed"} price alert for: ${coinId}`,
    );
  }, [alertCreated, coinId]);

  // PDF download functionality now handled by DownloadPdfButton component

  // Share functionality now handled by ShareMechanism component

  // Function to handle category selection
  const handleCategorySelect = useCallback((index: number) => {
    setSelectedCategoryIndex(index);
    setSelectedMetricIndex(null); // Reset sub-score selection when category changes
  }, []);

  // Function to handle metric selection
  const handleMetricSelect = useCallback((index: number) => {
    setSelectedMetricIndex(index);
  }, []);

  // Function to handle advanced view toggle
  const handleAdvancedViewChange = useCallback((isAdvanced: boolean) => {
    setShowAdvancedView(isAdvanced);
  }, []);

  // Helper function to get progress color based on score - aligned with ScoreIndicator colors
  const getProgressColor = (score: number) => {
    if (score >= 85) return "#00D88A"; // Excellent - same as ScoreIndicator
    if (score >= 75) return "#00B8D9"; // Good - same as ScoreIndicator
    if (score >= 65) return "#FFAB00"; // Fair - same as ScoreIndicator
    if (score >= 50) return "#FF5630"; // Poor - same as ScoreIndicator
    return "#FF3B3B"; // Bad - same as ScoreIndicator
  };

  // Helper function to get RGB values from score for styling
  const getColorRGB = (score: number): string => {
    const color = getProgressColor(score);
    // Map hex colors to RGB values
    switch (color) {
      case "#00D88A":
        return "0, 216, 138"; // Emerald/Excellent
      case "#00B8D9":
        return "0, 184, 217"; // Blue/Good
      case "#FFAB00":
        return "255, 171, 0"; // Yellow/Fair
      case "#FF5630":
        return "255, 86, 48"; // Orange/Poor
      default:
        return "255, 59, 59"; // Red/Bad
    }
  };

  // Real-time price data from API
  const [priceHistory, setPriceHistory] = useState<{
    "7D": Array<{ date: string; value: number }>;
    "14D": Array<{ date: string; value: number }>;
    "30D": Array<{ date: string; value: number }>;
  }>({
    "7D": [],
    "14D": [],
    "30D": [],
  });

  // Load price history data when coin changes or advanced view is toggled
  useEffect(() => {
    const fetchPriceHistory = async () => {
      if (!coinId) return;

      try {
        console.log("Fetching price history data for coinId:", coinId);
        console.log(
          "Advanced view:",
          showAdvancedView ? "enabled" : "disabled",
        );

        // Make different API requests based on user preference
        let coinDetail;
        if (showAdvancedView) {
          // Get advanced coin details if advanced view is enabled
          coinDetail =
            await CoinDetailService.getAdvancedCoinDetailById(coinId);
          console.log("Advanced view API response received");
        } else {
          // Get standard coin details otherwise
          coinDetail = await CoinDetailService.getCoinDetailById(coinId);
          console.log("Standard view API response received");
        }

        // Log API response for debugging
        console.log("API Response received:", {
          success: true,
          endpoint: showAdvancedView
            ? "get_adv_coin_detail_by_id"
            : "get_coin_detail_by_id",
          coinId: coinId,
          dataAvailable: !!coinDetail,
        });

        // Process sparkline data if available (only in advanced mode)
        if (coinDetail && showAdvancedView && (coinDetail as any).sparkline) {
          let sparklineData: any[] = [];

          // Parse sparkline data if it's a string
          if (typeof (coinDetail as any).sparkline === "string") {
            try {
              sparklineData = JSON.parse((coinDetail as any).sparkline);
              console.log("Successfully parsed sparkline JSON data");
            } catch (err) {
              console.error("Error parsing sparkline JSON:", err);
              sparklineData = [];
            }
          } else if (Array.isArray((coinDetail as any).sparkline)) {
            // If it's already an array, use it directly
            sparklineData = (coinDetail as any).sparkline;
            console.log("Using sparkline array data directly");
          }

          // If we have sparkline data, process it
          if (Array.isArray(sparklineData) && sparklineData.length > 0) {
            console.log("Sparkline data found, length:", sparklineData.length);

            // Transform API data into the format needed for our charts
            const pricePoints = sparklineData
              .filter(
                (point: any) =>
                  point && point.timestamp && !isNaN(parseFloat(point.value)),
              )
              .map((point: any) => {
                try {
                  // Normalize the date format
                  const date = new Date(point.timestamp);
                  return {
                    date: date.toISOString().split("T")[0],
                    value: parseFloat(point.value),
                  };
                } catch (err) {
                  console.error("Error parsing point:", point, err);
                  // Return a valid object instead of null to avoid type errors
                  return {
                    date: new Date().toISOString().split("T")[0],
                    value: 0,
                  };
                }
              });

            console.log("Processed price points, count:", pricePoints.length);

            if (pricePoints.length > 0) {
              // Update state with the real data
              setPriceHistory({
                "7D":
                  pricePoints.length >= 7 ? pricePoints.slice(-7) : pricePoints,
                "14D":
                  pricePoints.length >= 14
                    ? pricePoints.slice(-14)
                    : pricePoints,
                "30D":
                  pricePoints.length >= 30
                    ? pricePoints.slice(-30)
                    : pricePoints,
              });
              return; // Exit early if we found valid data
            }
          }
        }

        // If advanced mode didn't have sparkline data, try standard mode sparkline7d
        if (!showAdvancedView && coinDetail && coinDetail.sparkline7d) {
          let sparklineData: any[] = [];

          // Parse sparkline7d data if it's a string
          if (typeof coinDetail.sparkline7d === "string") {
            try {
              sparklineData = JSON.parse(coinDetail.sparkline7d);
              console.log("Successfully parsed sparkline7d JSON data");
            } catch (err) {
              console.error("Error parsing sparkline7d JSON:", err);
              sparklineData = [];
            }
          } else if (Array.isArray(coinDetail.sparkline7d)) {
            // If it's already an array, use it directly
            sparklineData = coinDetail.sparkline7d;
            console.log("Using sparkline7d array data directly");
          }

          // If we have sparkline7d data, process it
          if (Array.isArray(sparklineData) && sparklineData.length > 0) {
            console.log("Sparkline7d data found, length:", sparklineData.length);

            // Transform API data into the format needed for our charts
            const pricePoints = sparklineData
              .filter(
                (point: any) =>
                  point && point.timestamp && !isNaN(parseFloat(point.value)),
              )
              .map((point: any) => {
                try {
                  // Normalize the date format
                  const date = new Date(point.timestamp);
                  return {
                    date: date.toISOString().split("T")[0],
                    value: parseFloat(point.value),
                  };
                } catch (err) {
                  console.error("Error parsing point:", point, err);
                  // Return a valid object instead of null to avoid type errors
                  return {
                    date: new Date().toISOString().split("T")[0],
                    value: 0,
                  };
                }
              });

            console.log("Processed price points from sparkline7d, count:", pricePoints.length);

            if (pricePoints.length > 0) {
              // Update state with the real data
              setPriceHistory({
                "7D":
                  pricePoints.length >= 7 ? pricePoints.slice(-7) : pricePoints,
                "14D":
                  pricePoints.length >= 14
                    ? pricePoints.slice(-14)
                    : pricePoints,
                "30D":
                  pricePoints.length >= 30
                    ? pricePoints.slice(-30)
                    : pricePoints,
              });
              return; // Exit early if we found valid data
            }
          }
        }

        // If we get to this point, we couldn't find any valid sparkline data
        console.warn("No valid sparkline data found in API response");

        // Set empty arrays for all timeframes - the generatePriceData function will create minimal fallback data
        setPriceHistory({
          "7D": [],
          "14D": [],
          "30D": [],
        });
      } catch (error) {
        console.error("Error fetching price history:", error);
      }
    };

    fetchPriceHistory();
  }, [coinId, showAdvancedView]);

  // For debugging - show when price history is updated
  useEffect(() => {
    console.log("Price history updated:", priceHistory);
  }, [priceHistory]);

  // Get price data based on selected timeframe
  // Use useMemo to cache price data and prevent recalculations during hover
  const cachedPriceData = React.useMemo(() => {
    return {
      "7D": priceHistory["7D"] || [],
      "14D": priceHistory["14D"] || [],
      "30D": priceHistory["30D"] || [],
    };
  }, [priceHistory]);

  const generatePriceData = useCallback(
    (timeframe: "7D" | "14D" | "30D" = "7D") => {
      // Use the cached data to ensure consistency during hover
      const apiData = cachedPriceData[timeframe];

      // If we have actual API data, return it exactly as received from API
      if (apiData && apiData.length > 0) {
        // Return original API data without any modifications
        return apiData;
      }

      // If no data available for this timeframe, return empty array
      // This ensures PriceChart receives authentic data only
      return [];
    },
    [cachedPriceData],
  );

  // Generate score data for score charts
  const generateScoreData = useCallback((timeframe: "1M" | "3M" | "6M") => {
    // This would normally come from an API with different timeframes
    if (timeframe === "1M") {
      return [
        { date: "2025-02-10", value: 76.5 },
        { date: "2025-02-17", value: 77.2 },
        { date: "2025-02-24", value: 77.8 },
        { date: "2025-03-03", value: 78.1 },
        { date: "2025-03-10", value: 78.5 },
      ];
    } else if (timeframe === "3M") {
      return [
        { date: "2024-12-10", value: 72.0 },
        { date: "2024-12-24", value: 73.5 },
        { date: "2025-01-07", value: 74.8 },
        { date: "2025-01-21", value: 75.7 },
        { date: "2025-02-04", value: 76.3 },
        { date: "2025-02-18", value: 77.4 },
        { date: "2025-03-04", value: 78.2 },
        { date: "2025-03-10", value: 78.5 },
      ];
    } else {
      return [
        { date: "2024-09-10", value: 65.8 },
        { date: "2024-10-10", value: 68.2 },
        { date: "2024-11-10", value: 70.5 },
        { date: "2024-12-10", value: 72.0 },
        { date: "2025-01-10", value: 75.2 },
        { date: "2025-02-10", value: 76.7 },
        { date: "2025-03-10", value: 78.5 },
      ];
    }
  }, []);

  // Format date for tooltip display - in DD/MM format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    return `${day}/${month}`;
  };

  // Calculate price change percentage
  const calculatePriceChange = (data: { date: string; value: number }[]) => {
    if (data.length < 2) return 0;
    const firstPrice = data[0].value;
    const lastPrice = data[data.length - 1].value;
    return ((lastPrice - firstPrice) / firstPrice) * 100;
  };

  // Reset price state when coinId changes
  useEffect(() => {
    console.log(`Coin ID changed to: ${coinId} - resetting price state`);
    setCurrentPrice(null);
    setPriceError(null);
    setIsPriceLoading(false);
  }, [coinId]);

  // Fetch current price data separately using geckoslug
  useEffect(() => {
    // Create a flag to prevent state updates after unmount
    let isMounted = true;

    // Skip if missing data (but allow during loading for view mode change)
    if (!coinDetailData || !coinId) {
      console.log(`Skipping price fetch - prerequisite data missing`);
      return;
    }

    // Skip if already fetching price and not a view mode change
    if (isPriceLoading && currentPrice !== null) {
      console.log(
        `Skipping price fetch - already loading and not a view mode change`,
      );
      return;
    }

    const fetchCurrentPrice = async () => {
      // Don't set state if component is unmounted
      if (!isMounted) return;

      setIsPriceLoading(true);
      setPriceError(null);

      try {
        // Input validation
        if (!coinId || typeof coinId !== "string") {
          throw new Error("Invalid coin ID provided");
        }

        // Extract or use geckoslug from API response if available, otherwise fallback to coinId
        const geckoslug = coinDetailData?.geckoslug || coinId;

        console.log(`Fetching price for ${geckoslug} (one-time call)`);

        // Get current price using dedicated API call with geckoslug
        const price =
          await CoinDetailService.getCurrentPriceByCoinId(geckoslug);

        // Only update state if component is still mounted
        if (isMounted) {
          setCurrentPrice(price);
          console.log(`Price data received: ${price}`);
        }
      } catch (error) {
        console.error("Error fetching price data:", error);

        if (isMounted) {
          if (error instanceof Error) {
            setPriceError(error.message);
          } else {
            setPriceError("An unexpected error occurred fetching price data");
          }
        }
      } finally {
        if (isMounted) {
          setIsPriceLoading(false);
        }
      }
    };

    // Execute price data fetch
    fetchCurrentPrice();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [coinId, coinDetailData?.geckoslug, showAdvancedView]); // Also trigger when advanced view changes

  // Fetch coin data with robust error handling - Optimize to reduce duplicate API calls
  // This effect will run when:
  // 1. coinId changes (user navigates to a different coin)
  // 2. showAdvancedView changes (user toggles between standard and advanced view)
  // 3. isLoading is added to prevent redundant API calls
  useEffect(() => {
    // Create a flag to track mounted state to prevent state updates after unmount
    let isMounted = true;

    const fetchCoinData = async () => {
      // Prevent duplicate calls by checking if we're already loading
      if (isLoading) {
        console.log(`Skipping API call because we're already loading data`);
        return;
      }

      setIsLoading(true);
      setError(null); // Reset any previous errors

      try {
        // Input validation
        if (!coinId || typeof coinId !== "string") {
          throw new Error("Invalid coin ID provided");
        }

        console.log(
          `Fetching ${showAdvancedView ? "ADVANCED" : "STANDARD"} data for coin ${coinId}`,
        );

        // Always fetch fresh data when view mode changes (standard vs advanced)
        // Zorla veri çekmek için forceRefresh parametresini true olarak gönderiyoruz
        console.log(
          "🚀 Making API call with forceRefresh=true, showAdvancedView:",
          showAdvancedView,
        );
        const fetchPromise = showAdvancedView
          ? CoinDetailService.getAdvancedCoinDetailById(coinId, true) // Her zaman taze veri
          : CoinDetailService.getCoinDetailById(coinId, true); // Her zaman taze veri

        // Simulate network request with timeout protection
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Request timed out")), 8000),
        );

        // Race between actual request and timeout
        const result = (await Promise.race([fetchPromise, timeoutPromise])) as
          | CoinDetailModel
          | CoinAdvancedDetailModel;

        // Debug: Check if total_score_history exists in the API result
        console.log(
          "🔍 API RESULT total_score_history:",
          (result as any).total_score_history,
        );
        console.log("🔍 API RESULT keys:", Object.keys(result));

        // Check for network connectivity issues
        if (!navigator.onLine) {
          throw new Error(
            "You appear to be offline. Please check your internet connection and try again.",
          );
        }

        // API call successful, process the data - only log essential info to reduce console noise
        console.log(
          `Successfully fetched ${showAdvancedView ? "ADVANCED" : "STANDARD"} data for coin ${coinId}`,
        );

        // Only log data structure in development mode
        if (process.env.NODE_ENV === "development") {
          console.log("API Response Structure:", {
            id: result.id,
            name: result.name,
            hasScores: !!result.scores?.length,
            scoreCount: result.scores?.length || 0,
          });
        }

        // Define a mapping for time periods to make API values more understandable
        const timeFrameMap: Record<string, string> = {
          "24H": "24h",
          "7D": "7d",
          "30D": "30d",
          "90D": "90d",
          "1Y": "1y",
        };

        // Define a mapping for display names
        const displayNameMap: Record<string, string> = {
          "24h": "1 Gün",
          "7d": "1 Hafta",
          "30d": "1 Ay",
          "90d": "3 Ay",
          "1y": "1 Yıl",
        };

        // Initialize default priceChanges array
        let priceChanges = [
          { name: "24h", value: 0, displayName: displayNameMap["24h"] },
          { name: "7d", value: 0, displayName: displayNameMap["7d"] },
          { name: "30d", value: 0, displayName: displayNameMap["30d"] },
          { name: "90d", value: 0, displayName: displayNameMap["90d"] },
          { name: "1y", value: 0, displayName: displayNameMap["1y"] },
        ];

        // Extract available price change data if it exists and map to our standardized format
        if (
          result.marketData &&
          result.marketData.Changes &&
          Array.isArray(result.marketData.Changes)
        ) {
          // Convert API Changes array to priceChanges object format
          const formattedPriceChanges: Record<string, number> = {};

          result.marketData.Changes.forEach((change: any) => {
            if (change.name && typeof change.value === "number") {
              // Map the original time frame name (e.g. "24H") to our standardized format (e.g. "24h")
              const standardizedName =
                timeFrameMap[change.name] || change.name.toLowerCase();
              formattedPriceChanges[standardizedName] = change.value;

              console.log(
                `Mapped price change: ${change.name} -> ${standardizedName} = ${change.value}`,
              );
            }
          });

          // Update priceChanges array with values from API
          priceChanges = [
            {
              name: "24h",
              value: formattedPriceChanges["24h"] || 0,
              displayName: displayNameMap["24h"],
            },
            {
              name: "7d",
              value: formattedPriceChanges["7d"] || 0,
              displayName: displayNameMap["7d"],
            },
            {
              name: "30d",
              value: formattedPriceChanges["30d"] || 0,
              displayName: displayNameMap["30d"],
            },
            {
              name: "90d",
              value: formattedPriceChanges["90d"] || 0,
              displayName: displayNameMap["90d"],
            },
            {
              name: "1y",
              value: formattedPriceChanges["1y"] || 0,
              displayName: displayNameMap["1y"],
            },
          ];
        }

        // Convert result to CoinDetailData format with more robust mapping
        const apiData: CoinDetailData = {
          // Basic information
          id: result.id || "unknown",
          name: result.name || "Unknown Coin",
          symbol: result.symbol || "XXX",
          image: result.image || "",
          rank: parseInt(result.marketcap_rank) || 0,
          total_score: result.total_score || 50,
          description: showAdvancedView ? (result.description || "No description available") : "",
          summary: showAdvancedView ? (result.summary || "") : "", // Only use summary in advanced mode
          geckoslug: result.geckoslug || coinId, // Use geckoslug from API response or fallback to coinId
          socialLinks: showAdvancedView ? (result.socialLinks || []) : [], // Only use in advanced mode
          otherLinks: showAdvancedView ? (result.otherLinks || []) : [], // Only use in advanced mode

          // Add supply metrics from API response
          total_supply:
            result.total_supply !== undefined ? result.total_supply : null,
          max_supply:
            result.max_supply !== undefined ? result.max_supply : null,
          circulating_supply:
            result.circulating_supply !== undefined
              ? result.circulating_supply
              : null,

          // ATH/ATL bilgilerini API'den alalım - yalnızca number veya undefined olabilir
          ath:
            typeof result.ath === "string"
              ? parseFloat(result.ath)
              : typeof result.ath === "number"
                ? result.ath
                : undefined,
          ath_change:
            typeof result.ath_change_percentage === "string"
              ? parseFloat(result.ath_change_percentage)
              : typeof result.ath_change_percentage === "number"
                ? result.ath_change_percentage
                : typeof (result as any).ath_change === "number"
                  ? (result as any).ath_change
                  : undefined,
          atl:
            typeof result.atl === "string"
              ? parseFloat(result.atl)
              : typeof result.atl === "number"
                ? result.atl
                : undefined,
          atl_change:
            typeof result.atl_change_percentage === "string"
              ? parseFloat(result.atl_change_percentage)
              : typeof result.atl_change_percentage === "number"
                ? result.atl_change_percentage
                : typeof (result as any).atl_change === "number"
                  ? (result as any).atl_change
                  : undefined,

          // CoinDetailData'daki DetailedMarketData tipine uygun şekilde oluştur
          marketData: {
            // API'den gelen fiyat bilgisi
            Price: result.marketData?.Price || 0,
            // Diğer piyasa bilgileri
            MarketCap: result.marketData?.MarketCap || 0,
            H24Volume: result.marketData?.H24Volume || 0,
            FullyDilutedValuation:
              result.marketData?.FullyDilutedValuation || 0,
            // priceChanges dizisi ve değişim verileri
            priceChanges: priceChanges,
            // Geriye dönük uyumluluk için priceChangesMap'i de ekliyoruz
            priceChangesMap: {
              "24h": priceChanges.find((pc) => pc.name === "24h")?.value || 0,
              "7d": priceChanges.find((pc) => pc.name === "7d")?.value || 0,
              "30d": priceChanges.find((pc) => pc.name === "30d")?.value || 0,
              "90d": priceChanges.find((pc) => pc.name === "90d")?.value || 0,
              "1y": priceChanges.find((pc) => pc.name === "1y")?.value || 0,
            },
          },

          // CoinDetailData'daki ProjectLinks yapısına uygun şekilde çevir
          links:
            typeof result.links === "string"
              ? result.links
              : result.links || {},

          // Scores dizisini boş bir dizi olarak başlat (API'den gelirse güncellenecek)
          scores: Array.isArray(result.scores) ? result.scores : [],

          // Optional fields for enriched data
          socials: result.socials || {},
          risk: result.risk || {},
          metrics: result.metrics || {},

          // CoinDetailData modeli categoryNames gerektiriyor - only use in advanced mode
          categoryNames: showAdvancedView ? (result.categories || ["Cryptocurrency"]) : ["Cryptocurrency"],

          // Watchlist count - kaç kullanıcının izlediği
          watchlist_count:
            typeof result.watchlist_count === "number"
              ? result.watchlist_count
              : undefined,

          // Next unlock bilgileri
          unlock_amount: (result as any).unlock_amount || null,
          nextunlock: (result as any).nextunlock || null,

          // Launchpad information from API
          launchpad: Array.isArray((result as any).launchpad) ? (result as any).launchpad : undefined,

          // Historical score data from API
          total_score_history: (result as any).total_score_history || [],
        };

        // Log API data before processing
        console.log("API Data before processing:", {
          max_supply: apiData.max_supply,
          total_supply: apiData.total_supply,
          circulating_supply: apiData.circulating_supply,
          max_supply_type: typeof apiData.max_supply,
          total_supply_type: typeof apiData.total_supply,
          circulating_supply_type: typeof apiData.circulating_supply,
        });

        // Log API data before processing
        console.log(
          "🔍 BEFORE generateRealMethodologyData - apiData total_score_history:",
          apiData.total_score_history,
        );
        console.log(
          "🔍 BEFORE generateRealMethodologyData - apiData keys:",
          Object.keys(apiData),
        );

        // Process only scoring data to preserve original supply values
        const methodologyData = generateRealMethodologyData(apiData);

        // Log API data to check if total_score_history exists
        console.log(
          "API Data total_score_history:",
          apiData.total_score_history,
        );
        console.log("Full API Data keys:", Object.keys(apiData));

        // Manual merge to ensure supply values are preserved from original API data
        const processedData = {
          ...apiData,
          scores: methodologyData.scores,
          total_score: methodologyData.total_score,
          categoryScores: methodologyData.categoryScores,
          metrics: methodologyData.metrics,
          risk: methodologyData.risk,
        };

        // Log data after processing
        console.log("Processed data:", {
          max_supply: processedData.max_supply,
          total_supply: processedData.total_supply,
          circulating_supply: processedData.circulating_supply,
          unlock_amount: processedData.unlock_amount,
          nextunlock: processedData.nextunlock,
          max_supply_type: typeof processedData.max_supply,
          total_supply_type: typeof processedData.total_supply,
          circulating_supply_type: typeof processedData.circulating_supply,
        });

        // Debug Next Unlock values
        console.log("DEBUG NEXT UNLOCK VALUES:", {
          unlock_amount: processedData.unlock_amount,
          unlock_amount_type: typeof processedData.unlock_amount,
          nextunlock: processedData.nextunlock,
          nextunlock_type: typeof processedData.nextunlock,
        });

        setCoinDetailData(processedData);
      } catch (error) {
        console.error("Error fetching coin data:", error);

        // Structured error handling with specific error messages
        if (error instanceof TypeError) {
          setError(
            "There was a problem processing the data. Please try again later.",
          );
        } else if (error instanceof Error) {
          if (error.message.includes("timed out")) {
            setError(
              "Request timed out. Please check your internet connection and try again.",
            );
          } else if (!navigator.onLine) {
            setError(
              "You appear to be offline. Please check your internet connection and try again.",
            );
          } else {
            setError(error.message);
          }
        } else {
          setError("An unexpected error occurred. Please try again later.");
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchCoinData();
    // Reset UI state when coinId changes
    setSelectedCategoryIndex(0);
    setSelectedMetricIndex(null);
    setIsFullDescriptionVisible(false);

    // Cleanup function to prevent memory leaks and updates after unmount
    return () => {
      isMounted = false;
      console.log(
        `Cleanup for coin ${coinId} - preventing further state updates`,
      );
    };
  }, [coinId, showAdvancedView, currentLanguage?.code]); // Language dependency added to refresh metrics when language changes

  // Fetch fresh price data when showAdvancedView changes
  useEffect(() => {
    console.log(
      `View mode changed to: ${showAdvancedView ? "ADVANCED" : "STANDARD"} - forcing a data refresh`,
    );

    // Doğrudan verilerimizi temizleyerek ve loading durumunu ayarlayarak yenilemeyi tetikleyelim
    setIsLoading(true);

    // Tüm verilerimizi sıfırlayalım ki API isteği zorunlu olsun
    setCurrentPrice(null);

    // Bu effect sadece görünüm modu değiştiğinde çalışacak
    // Ana veri alma effect'i otomatik olarak yeni veriyi getirecek
  }, [showAdvancedView]);

  // Effect to scroll to top when page loads and determine referring page
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [coinId]);

  // Use current data for reference
  const coinCurrentData = {
    name: coinDetailData.name || "Loading...",
    symbol: coinDetailData.symbol || "...",
  };

  // Loading state UI
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 flex flex-col items-center justify-center">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-slate-200">Loading coin data...</p>
      </div>
    );
  }

  // Error state UI
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 flex flex-col items-center justify-center">
        <div className="bg-red-500/10 p-3 rounded-full mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-red-400"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        </div>
        <h2 className="text-xl font-bold text-red-400 mb-2">
          Error Loading Coin Data
        </h2>
        <p className="text-slate-300 mb-6 text-center max-w-md">{error}</p>
        <Link
          to={`/coinlist?coinId=${coinId}`}
          className="bg-slate-800 hover:bg-slate-700 text-slate-200 px-4 py-2 rounded-md text-sm flex items-center transition-colors border border-slate-700/30 shadow-sm hover:shadow-md"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Return to Coin List
        </Link>
      </div>
    );
  }

  // Prepare data for DetailedAnalysis component
  // Return the original category name without transformation
  const getCategoryDisplayName = (categoryName: string): string => {
    // Simply return the original name from the API
    return categoryName;
  };

  // API'den gelen score objelerini konsola yazdırarak kontrol edelim
  // Konsol logları kaldırıldı

  const detailedAnalysisCategories = coinDetailData.scores.map((score) => {
    const category = score.name;
    const displayCategory = getCategoryDisplayName(category);

    // First create the metrics with basic properties
    const metrics = score.subScores.map((subScore) => {
      // API'den gelen subscore'larda methodology alanı yok
      // getMethodologyKeyForMetric kullanmaya gerek yok

      // Map the score name to a valid CoinStatus value
      const scoreName = getScoreName(subScore.value);
      let validStatus:
        | "Excellent"
        | "Positive"
        | "Average"
        | "Weak"
        | "Critical" = "Fair"; // Default fallback

      // Map the score names to valid CoinStatus values
      if (scoreName === "Excellent") validStatus = "Excellent";
      else if (scoreName === "Positive") validStatus = "Positive";
      else if (scoreName === "Average") validStatus = "Average";
      else if (scoreName === "Weak") validStatus = "Weak";
      else if (scoreName === "Critical") validStatus = "Critical";

      // Create a unique identifier for each metric to avoid duplicate keys
      // Use the id if available, otherwise create a unique id based on name and value
      const uniqueId =
        subScore.id ||
        `${category}-${subScore.name}-${subScore.value}`
          .replace(/\s+/g, "-")
          .toLowerCase();

      return {
        name: subScore.name,
        value: subScore.value,
        score: subScore.value, // Score is used for color status
        label: getScoreName(subScore.value), // Label is the text status
        id: uniqueId, // Use the unique identifier
        uniqueKey: uniqueId, // Add an extra uniqueKey property for React keys
        category: category,
        description:
          subScore.description || `${subScore.name} metrics breakdown`,
        status: validStatus, // Use the mapped valid status
        dataPoints: subScore.dataPoints || [
          `${subScore.name} analysis`,
          `Historical performance`,
          `Comparative metrics`,
        ],
        // methodology ve methodologyData alanlarını kaldırdık, API'den isteyeceğiz
      };
    });

    // Map the score name to a valid CoinStatus value for the category
    const scoreName = getScoreName(score.total);
    let validStatus: "Excellent" | "Good" | "Fair" | "Poor" | "Bad" = "Fair"; // Default fallback

    // Map the score names to valid CoinStatus values
    if (scoreName === "Excellent") validStatus = "Excellent";
    else if (scoreName === "Positive") validStatus = "Good";
    else if (scoreName === "Average") validStatus = "Fair";
    else if (scoreName === "Weak") validStatus = "Poor";
    else if (scoreName === "Critical") validStatus = "Bad";

    return {
      name: displayCategory, // Use the mapped display name
      originalName: category, // Keep the original name for reference if needed
      score: score.total,
      status: validStatus, // Use the mapped valid status
      description: `${displayCategory} metrics breakdown`,
      // Sadece score.weight değerini doğrudan kullanıyoruz (herhangi bir manipülasyon yapmadan)
      weight: score.weight || 0, // Provide a default value of 0 if weight is undefined
      metrics: metrics,
    };
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800">
      <div className="container mx-auto pt-6 px-4">
        <button
          onClick={handleBackNavigation}
          className="inline-flex items-center text-blue-500 hover:text-blue-400 mb-6 transition-colors px-3 py-1 rounded-md hover:bg-blue-500/10"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          <span>{t('coinDetail.backToPreviousPage')}</span>
        </button>

        {/* Single Unified Card with Perfect Layout */}
        <Card className="bg-slate-900/80 border border-slate-800 shadow-lg overflow-hidden mb-8">
          {/* Top Action Bar with Buttons and Toggle */}
          <div className="p-4 pb-4 border-b border-slate-800/30 bg-slate-800/30 backdrop-blur-sm">
            {/* Single row layout with perfect alignment */}
            <div className="flex items-center justify-between w-full px-2">
              {/* Left side - Bitcoin card with integrated watchlist button */}
              <div className="flex-shrink-0 bg-slate-800/90 hover:bg-slate-800/80 text-slate-200 rounded-md border border-slate-700/30 shadow-sm hover:shadow-md group ml-2">
                <div className="flex items-center px-4 py-3">
                  <CoinImage
                    src={coinDetailData.image}
                    alt={coinCurrentData.name}
                    symbol={coinCurrentData.symbol}
                    size="sm"
                  />
                  <div className="ml-2.5 flex items-center">
                    <span className="text-sm font-bold text-white whitespace-nowrap">
                      {coinCurrentData.name}
                    </span>
                    <span className="text-xs text-slate-400 ml-1.5">
                      {coinCurrentData.symbol}
                    </span>
                    <span className="text-xs text-amber-300 ml-1.5 font-medium">
                      {t('coinDetail.rankPrefix')}{coinDetailData.rank || "N/A"}
                    </span>
                    <span className="text-xs text-blue-400 ml-1.5 font-medium flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      {coinDetailData.watchlist_count || 0} {t('watchlist.watchers')}
                    </span>
                  </div>
                  {/* Integrated Watchlist Button - no border */}
                  <div className="flex items-center gap-2 ml-3">
                    {/* Watchlist Button - Using ContextAwareWatchlistButton for cross-page synchronization */}
                    <ContextAwareWatchlistButton
                      coinId={coinId}
                      pageType="coindetail"
                      variant="default"
                      className="text-xs bg-transparent hover:bg-slate-700/30 text-slate-200 px-2 h-7 rounded-md flex items-center justify-center gap-1.5 transition-all duration-200 font-medium shadow-none hover:shadow-none border-none focus:shadow-none"
                    />

                    {/* Portfolio Button - First in sequence after watchlist
                    <button
                      className="text-xs bg-transparent hover:bg-slate-700/30 text-slate-200 px-2 h-7 rounded-md flex items-center justify-center gap-1.5 transition-all duration-200 font-medium"
                      onClick={handlePortfolioToggle}
                      aria-label={
                        inPortfolio
                          ? "Remove from portfolio"
                          : "Add to portfolio"
                      }
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={`${
                          inPortfolio
                            ? "text-green-400 fill-green-400/30"
                            : "text-green-400"
                        } hover:text-green-300 transition-colors`}
                      >
                        <path d="M21 12V7H5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16v-5" />
                        <path d="M9 22V16a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v6" />
                        <path d="M14 12V3.6a.6.6 0 0 0-.6-.6H7.6a.6.6 0 0 0-.6.6v8.4" />
                      </svg>
                      <span className="whitespace-nowrap">
                        {inPortfolio ? "In Your Portfolio" : "Add to Portfolio"}
                      </span>
                    </button>
                    */}
                    {/* Alert Button - Using ContextAwareAlertButton for cross-page synchronization */}
                    <ContextAwareAlertButton
                      coinId={coinId}
                      variant="ghost"
                      size="sm"
                      className="text-xs bg-transparent hover:bg-slate-700/30 text-slate-200 px-2 h-7 rounded-md flex items-center justify-center gap-1.5 transition-all duration-200 font-medium shadow-none hover:shadow-none border-none focus:shadow-none"
                      onToggleComplete={handleAlertToggle}
                    />
                    {/* Download PDF Button - Third in sequence
                    <DownloadPdfButton
                      coinData={coinDetailData}
                      showAdvancedView={showAdvancedView}
                    />
                    */}
                    {/* Report Error Button - Fourth in sequence */}

                    {/* Share Button - Using ShareMechanism for cross-page synchronization */}
                    <ShareMechanism
                      itemId={coinId}
                      itemType="coin"
                      itemName={coinCurrentData.name}
                      itemIcon={coinCurrentData.symbol.charAt(0)}
                    >
                      <button
                        className="text-xs bg-transparent hover:bg-slate-700/30 text-slate-200 px-2 h-7 rounded-md flex items-center justify-center gap-1.5 transition-all duration-200 font-medium"
                        aria-label="Share this coin"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-indigo-400 hover:text-indigo-300 transition-colors"
                        >
                          <circle cx="18" cy="5" r="3" />
                          <circle cx="6" cy="12" r="3" />
                          <circle cx="18" cy="19" r="3" />
                          <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" />
                          <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" />
                        </svg>
                        <span className="whitespace-nowrap">{t('watchlist.share')}</span>
                      </button>
                    </ShareMechanism>
                  </div>
                </div>
              </div>

              {/* Right side with toggle */}

              <div className="flex items-center gap-2 ml-auto">
                {/* Advanced View Toggle - Enhanced with better animations and accessibility */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-slate-300 font-medium">
                    {t('watchlist.advancedView')}
                  </span>
                  <button
                    role="switch"
                    aria-checked={showAdvancedView}
                    aria-label={showAdvancedView ? t('watchlist.disableAdvancedView') : t('watchlist.enableAdvancedView')}
                    className={`relative w-12 h-6 rounded-full flex items-center px-0.5 cursor-pointer transition-all duration-300 ${
                      showAdvancedView
                        ? "bg-blue-600 shadow-inner shadow-blue-900/50"
                        : "bg-slate-700 shadow-inner shadow-slate-900/30"
                    } focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-400 focus:ring-offset-slate-900`}
                    onClick={() => {
                      console.log(
                        `Toggling view mode from ${showAdvancedView ? "advanced" : "standard"} to ${!showAdvancedView ? "advanced" : "standard"}`,
                      );

                      // Clear any existing error states
                      setError(null);
                      setPriceError(null);

                      // Toggle the view state - bu değişim useEffect'i tetikleyecek
                      setShowAdvancedView(!showAdvancedView);

                      // Diğer işlemleri useEffect içinde hallediyoruz
                      // Bu şekilde bağımlılık dizileri doğru çalışabilir
                    }}
                  >
                    <span className="sr-only">
                      {showAdvancedView ? t('watchlist.disableAdvancedView') : t('watchlist.enableAdvancedView')}
                    </span>
                    <motion.div
                      className={`w-5 h-5 rounded-full absolute left-0.5 ${
                        showAdvancedView ? "bg-blue-200" : "bg-slate-400"
                      }`}
                      animate={{
                        x: showAdvancedView ? 24 : 0,
                        backgroundColor: showAdvancedView
                          ? "rgb(219, 234, 254)"
                          : "rgb(148, 163, 184)",
                      }}
                      transition={{
                        type: "spring",
                        stiffness: 500,
                        damping: 30,
                        duration: 0.2,
                      }}
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Card Content Area */}
          <div className="px-6 py-6 flex flex-col md:flex-row gap-8 items-stretch">
            {/* Left Column: Score - Modified for proper height alignment */}
            <div className="bg-slate-800/70 rounded-xl min-w-[30%] p-5 min-h-[100%] border border-slate-700/40 shadow-md shadow-slate-900/40 flex flex-col justify-between transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden">
              <div className="flex justify items-center">
                <div className="bg-blue-500/10 p-1 rounded-md group-hover:bg-blue-500/20 transition-all duration-300 flex items-center justify-center">
                  <HeartPulse className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
                </div>
                <span className="text-md ml-3 font-medium text-slate-300">
                  {t('coinHealth.title')}
                </span>
              </div>
              <div className="flex  items-center justify-between relative z-10">
                <div className="flex items-center justify-center">
                  <div className="bg-slate-800/90 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 p-2.5 transition-all duration-300 hover:border-blue-500/30">
                    <div
                      className={`rounded-xl py-1.5 px-4 flex items-center whitespace-nowrap ${
                        coinDetailData.total_score >= 90
                          ? "bg-[#00D88A]/20 text-[#00D88A] border border-[#00D88A]/20"
                          : coinDetailData.total_score >= 75
                            ? "bg-[#00B8D9]/20 text-[#00B8D9] border border-[#00B8D9]/20"
                            : coinDetailData.total_score >= 65
                              ? "bg-[#FFAB00]/20 text-[#FFAB00] border border-[#FFAB00]/20"
                              : coinDetailData.total_score >= 50
                                ? "bg-[#FF5630]/20 text-[#FF5630] border border-[#FF5630]/20"
                                : "bg-[#FF3B3B]/20 text-[#FF3B3B] border border-[#FF3B3B]/20"
                      }`}
                    >
                      <HeartPulse className="h-4 w-4 mr-2" />
                      <span className="text-sm font-medium">
                        {coinDetailData.total_score >= 90
                          ? t('coinHealth.excellent')
                          : coinDetailData.total_score >= 75
                            ? t('coinHealth.positive')
                            : coinDetailData.total_score >= 65
                              ? t('coinHealth.average')
                              : coinDetailData.total_score >= 50
                                ? t('score:weak')
                                : t('coinHealth.critical')}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  {/* Score Gauge directly in the flex container with responsive sizing from Tailwind */}
                  <ScoreGauge
                    className="shadow-md shadow-slate-900/40 rounded-xl transition-all duration-300"
                    score={coinDetailData.total_score}
                    id="ido-health"
                    showLabel={false}
                    animated={false}
                  />
                </div>
              </div>

              {/* Score Range Indicator */}
              <div className="mt-3 relative z-10">
                <p className="text-sm text-slate-300 mb-1.5 font-medium">
                  {t('coinHealth.scoreRange')}
                </p>
                <div className="h-2 w-full bg-slate-800/90 rounded-full relative overflow-hidden">
                  {/* Background gradient - smoother transitions */}
                  <div
                    className="absolute left-0 top-0 h-full"
                    style={{
                      width: "100%",
                      background: `linear-gradient(to right,
                        #FF3B3B 0%,
                        #FF3B3B 47.5%,
                        #FF5630 52.5%,
                        #FF5630 62.5%,
                        #FFAB00 67.5%,
                        #FFAB00 72.5%,
                        #00B8D9 77.5%,
                        #00B8D9 87.5%,
                        #00D88A 92.5%,
                        #00D88A 100%)`,
                    }}
                  />
                  {/* Position indicator dot */}
                  <div
                    className="absolute top-0 h-4 w-2 bg-white rounded-full transform -translate-y-1"
                    style={{
                      left: `${Math.max(0, Math.min(100, coinDetailData.total_score))}%`,
                    }}
                  ></div>
                </div>
                <div className="flex justify-between mt-1.5 text-xs text-slate-400">
                  <span>{t('coinHealth.critical')}</span>
                  <span>{t('coinHealth.excellent')}</span>
                </div>
              </div>
            </div>
            {/* Right Column: Market Stats & Price Changes */}
            <div className="md:w-2/3 lg:w-2/3 flex flex-col h-full">
              {/* Stats Cards Grid - Improved responsive breakpoints */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 mb-6 md:mb-8 pb-3">
                {/* Price Card - Enhanced with standardized styling and better user experience */}
                <div
                  className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg py-3 px-4 shadow-md flex flex-col border border-slate-700/20 hover:border-blue-500/20 hover:bg-slate-800/80 transition-all duration-300 group relative"
                  aria-label={`Current price of ${coinCurrentData.name}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-1.5">
                      <div className="bg-blue-500/10 p-1.5 rounded-md group-hover:bg-blue-500/20 transition-all duration-300 flex items-center justify-center">
                        <DollarSign className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
                      </div>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="text-sm font-medium text-slate-400 group-hover:text-blue-400 transition-colors duration-300">
                              {t('alerts.price')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                          >
                            <p className="font-medium text-blue-400 mb-1">
                              Current Market Price
                            </p>
                            <p>
                              Live / Current USD price from multiple exchanges.
                            </p>
                            <p className="text-slate-400 mt-1.5 text-[10px]">
                              Last updated: {new Date().toLocaleTimeString()}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    {/* 24h badge removed as requested */}
                  </div>
                  <div className="mt-2">
                    <div className="flex flex-wrap items-baseline gap-2">
                      <span className="text-xl font-bold text-white group-hover:text-blue-50 transition-colors duration-300">
                        {formatCurrencyValue(currentPrice, {
                          currency: "$",
                          compact: false,
                          showZero: true,
                        }, t, currentLanguage)}
                      </span>
                    </div>
                    {/* Rank badge removed as requested */}
                  </div>
                </div>

                {/* Market Cap Card - Enhanced with standardized styling */}
                <div
                  className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg py-3 px-4 shadow-md flex flex-col border border-slate-700/20 hover:border-green-500/20 hover:bg-slate-800/80 transition-all duration-300 group relative"
                  aria-label={`${coinCurrentData.name} market capitalization`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-1.5">
                      <div className="bg-green-500/10 p-1.5 rounded-md group-hover:bg-green-500/20 transition-all duration-300 flex items-center justify-center">
                        <BarChart3 className="h-4 w-4 text-green-400 group-hover:text-green-300 transition-colors duration-300" />
                      </div>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="text-sm font-medium text-slate-400 group-hover:text-green-400 transition-colors duration-300 ">
                              {t('marketData.marketCap')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                          >
                            <p className="font-medium text-green-400 mb-1">
                              Market Capitalization
                            </p>
                            <p>
                              Total market value of circulating supply,
                              calculated using the current price.
                            </p>
                            <p className="text-slate-400 mt-1.5 text-[10px]">
                              Last updated: {new Date().toLocaleTimeString()}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    {/* Rank badge removed as requested */}
                  </div>
                  <div className="mt-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="text-xl font-bold text-white group-hover:text-green-50 transition-colors duration-300 block">
                            {formatCurrencyValue(
                              coinDetailData.marketData?.MarketCap,
                              {
                                currency: "$",
                                compact: true,
                                precision: 1,
                                zeroValue: "Data unavailable",
                              },
                              t,
                              currentLanguage,
                            )}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent
                          side="bottom"
                          className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                        >
                          <p className="font-medium text-green-400 mb-1">
                            {t('marketData.marketCapDetails')}
                          </p>
                          <p>
                            {t('marketData.marketCapRank')}: #{coinDetailData.rank || "N/A"}
                          </p>
                          <p className="mt-1.5 text-slate-400 text-[10px]">
                            {t('marketData.updatedHourly')}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>

                {/* Fully Diluted Value Card - Enhanced with standardized styling */}
                <div
                  className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg py-3 px-4 shadow-md flex flex-col border border-slate-700/20 hover:border-purple-500/20 hover:bg-slate-800/80 transition-all duration-300 group relative"
                  aria-label={`${coinCurrentData.name} fully diluted valuation`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-1.5">
                      <div className="bg-purple-500/10 p-1.5 rounded-md group-hover:bg-purple-500/20 transition-all duration-300 flex items-center justify-center">
                        <Coins className="h-4 w-4 text-purple-400 group-hover:text-purple-300 transition-colors duration-300" />
                      </div>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="text-sm font-medium text-slate-400 group-hover:text-purple-400 transition-colors duration-300 ">
                              {t('marketData.fullyDilute')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                          >
                            <p className="font-medium text-purple-400 mb-1">
                              Fully Diluted Valuation
                            </p>
                            <p>
                              Theoretical market cap if all tokens were in
                              circulation.
                            </p>
                            <p className="text-slate-400 mt-1.5 text-[10px]">
                              Last updated: {new Date().toLocaleTimeString()}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    {/* Circ badge removed as requested */}
                  </div>
                  <div className="mt-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="text-xl font-bold text-white group-hover:text-purple-50 transition-colors duration-300 block">
                            {formatCurrencyValue(
                              coinDetailData.marketData?.FullyDilutedValuation,
                              {
                                currency: "$",
                                compact: true,
                                precision: 1,
                                zeroValue: "Data unavailable",
                              },
                              t,
                              currentLanguage,
                            )}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent
                          side="bottom"
                          className="bg-slate-800 text-slate-200 border-slate-700 text-xs max-w-[220px] p-3 shadow-xl"
                        >
                          <p className="font-medium text-purple-400 mb-1">
                            Supply Analysis
                          </p>
                          {coinDetailData.marketData?.MarketCap &&
                          coinDetailData.marketData?.FullyDilutedValuation ? (
                            <p>
                              {Math.round(
                                (coinDetailData.marketData.MarketCap /
                                  coinDetailData.marketData
                                    .FullyDilutedValuation) *
                                  100,
                              )}
                              % of maximum supply is currently in circulation
                            </p>
                          ) : (
                            <p>Supply data not available</p>
                          )}
                          {coinDetailData.total_supply && (
                            <p className="mt-1 text-sm">
                              <span className="text-purple-400 font-medium">
                                Total Supply:
                              </span>{" "}
                              {coinDetailData.total_supply &&
                              coinDetailData.total_supply !== "null"
                                ? typeof coinDetailData.total_supply ===
                                  "number"
                                  ? coinDetailData.total_supply.toLocaleString()
                                  : parseInt(
                                      String(coinDetailData.total_supply),
                                    ).toLocaleString()
                                : "N/A"}
                            </p>
                          )}
                          <p className="mt-1.5 text-slate-400 text-[10px]">
                            Important for assessing future supply dilution
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              </div>

              {/* Price Changes Section */}
              <div className="bg-gradient-to-br from-slate-800/60 to-slate-900/60 rounded-lg border border-slate-700/20 p-5 h-full flex flex-col justify-between">
                <div className="flex items-center justify-between mb-3.5 md:mb-5">
                  <div className="flex items-center gap-1.5 md:gap-2">
                    <div className="bg-indigo-500/10 p-1 md:p-1.5 rounded-md flex items-center justify-center">
                      <LineChart className="h-3.5 w-3.5 md:h-4 md:w-4 text-indigo-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-white">
                      {t('priceChanges.title')}
                    </h3>
                  </div>
                </div>

                {/* Equal width grid for 5 time period boxes */}
                <div className="grid grid-cols-5 gap-2 md:gap-3">
                  {/* 1 Day - Enhanced with better mobile responsive design and accessibility */}
                  {(() => {
                    // Yeni veri modelimizden priceChanges değerini alıyoruz
                    const priceChange =
                      coinDetailData.marketData?.priceChanges?.find(
                        (pc) => pc.name === "24h",
                      );
                    const change = priceChange?.value ?? 0;
                    // displayName is not used
                    const isPositive = change >= 0;
                    const changeLabel = `${
                      isPositive ? "Up" : "Down"
                    } ${Math.abs(
                      typeof change === "number" ? change : 0,
                    ).toFixed(2)}% in the last 24 hours`;
                    return (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-lg border border-slate-700/20 ${
                                isPositive
                                  ? "hover:border-green-500/20 hover:bg-slate-700/60"
                                  : "hover:border-red-500/20 hover:bg-slate-700/60"
                              } transition-all duration-200 flex flex-col items-center py-2 md:py-3.5 px-2 md:px-3 shadow-md group cursor-pointer`}
                              aria-label={changeLabel}
                            >
                              <span
                                className={`text-[10px] md:text-xs text-slate-400 font-medium mb-0.5 md:mb-1.5 ${
                                  isPositive
                                    ? "group-hover:text-green-400"
                                    : "group-hover:text-red-400"
                                } transition-colors`}
                              >
                                {t('priceChanges.1day')}
                              </span>
                              <span
                                className={`text-[10px] md:text-sm font-medium ${
                                  isPositive
                                    ? "text-green-400 bg-green-500/10"
                                    : "text-red-400 bg-red-500/10"
                                } flex items-center rounded-md px-1 md:px-2 py-0.5 md:py-1`}
                              >
                                {isPositive ? (
                                  <TrendingUp className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                ) : (
                                  <TrendingDown className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                )}
                                {typeof change === "number"
                                  ? change.toFixed(2)
                                  : change}
                                %
                              </span>
                            </div>
                          </TooltipTrigger>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })()}

                  {/* 1 Week - Enhanced with better mobile responsive design and accessibility */}
                  {(() => {
                    // Yeni veri modelimizden priceChanges değerini alıyoruz
                    const priceChange =
                      coinDetailData.marketData?.priceChanges?.find(
                        (pc) => pc.name === "7d",
                      );
                    const change = priceChange?.value ?? 0;
                    // displayName is not used
                    const isPositive = change >= 0;
                    const changeLabel = `${
                      isPositive ? "Up" : "Down"
                    } ${Math.abs(
                      typeof change === "number" ? change : 0,
                    ).toFixed(2)}% in the last week`;
                    return (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-lg border border-slate-700/20 ${
                                isPositive
                                  ? "hover:border-green-500/20 hover:bg-slate-700/60"
                                  : "hover:border-red-500/20 hover:bg-slate-700/60"
                              } transition-all duration-200 flex flex-col items-center py-2 md:py-3.5 px-2 md:px-3 shadow-md group cursor-pointer`}
                              aria-label={changeLabel}
                            >
                              <span
                                className={`text-[10px] md:text-xs text-slate-400 font-medium mb-0.5 md:mb-1.5 ${
                                  isPositive
                                    ? "group-hover:text-green-400"
                                    : "group-hover:text-red-400"
                                } transition-colors`}
                              >
                                {t('priceChanges.1week')}
                              </span>
                              <span
                                className={`text-[10px] md:text-sm font-medium ${
                                  isPositive
                                    ? "text-green-400 bg-green-500/10"
                                    : "text-red-400 bg-red-500/10"
                                } flex items-center rounded-md px-1 md:px-2 py-0.5 md:py-1`}
                              >
                                {isPositive ? (
                                  <TrendingUp className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                ) : (
                                  <TrendingDown className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                )}
                                {typeof change === "number"
                                  ? change.toFixed(2)
                                  : change}
                                %
                              </span>
                            </div>
                          </TooltipTrigger>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })()}

                  {/* 1 Month - Enhanced with better mobile responsive design and accessibility */}
                  {(() => {
                    // Yeni veri modelimizden priceChanges değerini alıyoruz
                    const priceChange =
                      coinDetailData.marketData?.priceChanges?.find(
                        (pc) => pc.name === "30d",
                      );
                    const change = priceChange?.value ?? 0;
                    // displayName is not used
                    const isPositive = change >= 0;
                    const changeLabel = `${
                      isPositive ? "Up" : "Down"
                    } ${Math.abs(
                      typeof change === "number" ? change : 0,
                    ).toFixed(2)}% in the last month`;
                    return (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-lg border border-slate-700/20 ${
                                isPositive
                                  ? "hover:border-green-500/20 hover:bg-slate-700/60"
                                  : "hover:border-red-500/20 hover:bg-slate-700/60"
                              } transition-all duration-200 flex flex-col items-center py-2 md:py-3.5 px-2 md:px-3 shadow-md group cursor-pointer`}
                              aria-label={changeLabel}
                            >
                              <span
                                className={`text-[10px] md:text-xs text-slate-400 font-medium mb-0.5 md:mb-1.5 ${
                                  isPositive
                                    ? "group-hover:text-green-400"
                                    : "group-hover:text-red-400"
                                } transition-colors`}
                              >
                                {t('priceChanges.1month')}
                              </span>
                              <span
                                className={`text-[10px] md:text-sm font-medium ${
                                  isPositive
                                    ? "text-green-400 bg-green-500/10 border border-green-500/20"
                                    : "text-red-400 bg-red-500/10 border border-red-500/20"
                                } flex items-center rounded-md px-1 md:px-2 py-0.5 md:py-1`}
                              >
                                {isPositive ? (
                                  <TrendingUp className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                ) : (
                                  <TrendingDown className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                )}
                                {typeof change === "number"
                                  ? change.toFixed(2)
                                  : change}
                                %
                              </span>
                            </div>
                          </TooltipTrigger>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })()}

                  {/* 3 Months - Enhanced with better mobile responsive design and accessibility */}
                  {(() => {
                    // Yeni veri modelimizden priceChanges değerini alıyoruz
                    const priceChange =
                      coinDetailData.marketData?.priceChanges?.find(
                        (pc) => pc.name === "90d",
                      );
                    const change = priceChange?.value ?? 0;
                    // displayName is not used
                    const isPositive = change >= 0;
                    const changeLabel = `${
                      isPositive ? "Up" : "Down"
                    } ${Math.abs(
                      typeof change === "number" ? change : 0,
                    ).toFixed(2)}% in the last quarter`;
                    return (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-lg border border-slate-700/20 ${
                                isPositive
                                  ? "hover:border-green-500/20 hover:bg-slate-700/60"
                                  : "hover:border-red-500/20 hover:bg-slate-700/60"
                              } transition-all duration-200 flex flex-col items-center py-2 md:py-3.5 px-2 md:px-3 shadow-md group cursor-pointer`}
                              aria-label={changeLabel}
                            >
                              <span
                                className={`text-[10px] md:text-xs text-slate-400 font-medium mb-0.5 md:mb-1.5 ${
                                  isPositive
                                    ? "group-hover:text-green-400"
                                    : "group-hover:text-red-400"
                                } transition-colors`}
                              >
                                {t('priceChanges.3months')}
                              </span>
                              <span
                                className={`text-[10px] md:text-sm font-medium ${
                                  isPositive
                                    ? "text-green-400 bg-green-500/10 border border-green-500/20"
                                    : "text-red-400 bg-red-500/10 border border-red-500/20"
                                } flex items-center rounded-md px-1 md:px-2 py-0.5 md:py-1`}
                              >
                                {isPositive ? (
                                  <TrendingUp className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                ) : (
                                  <TrendingDown className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                )}
                                {typeof change === "number"
                                  ? change.toFixed(2)
                                  : change}
                                %
                              </span>
                            </div>
                          </TooltipTrigger>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })()}

                  {/* 1 Year - Enhanced with better mobile responsive design and accessibility */}
                  {(() => {
                    // Yeni veri modelimizden priceChanges değerini alıyoruz
                    const priceChange =
                      coinDetailData.marketData?.priceChanges?.find(
                        (pc) => pc.name === "1y",
                      );
                    const change = priceChange?.value ?? 0;
                    // displayName is not used
                    const isPositive = change >= 0;
                    const changeLabel = `${
                      isPositive ? "Up" : "Down"
                    } ${Math.abs(
                      typeof change === "number" ? change : 0,
                    ).toFixed(2)}% in the last year`;
                    return (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-lg border border-slate-700/20 ${
                                isPositive
                                  ? "hover:border-green-500/20 hover:bg-slate-700/60"
                                  : "hover:border-red-500/20 hover:bg-slate-700/60"
                              } transition-all duration-200 flex flex-col items-center py-2 md:py-3.5 px-2 md:px-3 shadow-md group cursor-pointer`}
                              aria-label={changeLabel}
                            >
                              <span
                                className={`text-[10px] md:text-xs text-slate-400 font-medium mb-0.5 md:mb-1.5 ${
                                  isPositive
                                    ? "group-hover:text-green-400"
                                    : "group-hover:text-red-400"
                                } transition-colors`}
                              >
                                {t('priceChanges.1year')}
                              </span>
                              <span
                                className={`text-[10px] md:text-sm font-medium ${
                                  isPositive
                                    ? "text-green-400 bg-green-500/10 border border-green-500/20"
                                    : "text-red-400 bg-red-500/10 border border-red-500/20"
                                } flex items-center rounded-md px-1 md:px-2 py-0.5 md:py-1`}
                              >
                                {isPositive ? (
                                  <TrendingUp className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                ) : (
                                  <TrendingDown className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
                                )}
                                {typeof change === "number"
                                  ? change.toFixed(2)
                                  : change}
                                %
                              </span>
                            </div>
                          </TooltipTrigger>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Removed category tabs as we use the side category cards to navigate */}

        {/* Category Metrics Breakdown Section - Only show when not in advanced view */}
        <AnimatePresence mode="wait">
          {!showAdvancedView && coinDetailData.scores.length > 0 && (
            <motion.div
              className="mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.4,
                ease: [0.25, 0.1, 0.25, 1.0],
              }}
              key="normal-view"
            >
              {/* DetailedAnalysis component moved to advanced view section */}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main content - Reorganized as 2-column layout with 50/50 split */}
        {showAdvancedView && (
          <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            {/* LEFT COLUMN - 50% Width - Contains About (Advanced mode only), Market Stats, and Token Supply */}
            <div className="space-y-6">
              {/* About section - Only show in advanced mode when description/summary data is available */}
              {showAdvancedView && (
                <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg border border-slate-700/20 hover:border-blue-500/30 px-9 py-7 shadow-lg transition-all duration-300 relative overflow-hidden group">
                  <div className="absolute inset-0 bg-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                  <div className="absolute top-0 right-0 w-20 h-20 bg-blue-600/10 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>

                  <div className="flex items-center gap-4 mb-6">
                    <div className="bg-blue-500/10 p-3 rounded-md group-hover:bg-blue-500/20 transition-colors duration-300">
                      <HelpCircle className="h-6 w-6 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
                    </div>
                    <h3 className="text-2xl font-semibold text-white group-hover:text-blue-100 transition-colors duration-300">
                      {coinCurrentData.name} {t('coinDetail.about')}
                    </h3>
                  </div>

                  <div className="text-sm md:text-[15px] text-slate-300 mb-4 max-h-[350px] overflow-y-auto pr-2 custom-scrollbar">
                    <div className="bg-gradient-to-br from-slate-800/70 to-slate-800/30 backdrop-blur-sm p-4 rounded-lg border border-slate-700/40 shadow-inner">
                      {/* Summary (shown always) */}
                      <div className="leading-relaxed text-slate-200 tracking-wide coin-about-content">
                        {coinDetailData.summary ? (
                          <div
                            dangerouslySetInnerHTML={{
                              __html: coinDetailData.summary,
                            }}
                          />
                        ) : coinDetailData.description ? (
                          <div
                            dangerouslySetInnerHTML={{
                              __html: coinDetailData.description,
                            }}
                          />
                        ) : (
                          <p>{`${coinCurrentData.name} coin information is currently not available. We are working on updating our data.`}</p>
                        )}
                      </div>

                      {/* Full description (shown when isFullDescriptionVisible is true) */}
                      {isFullDescriptionVisible && coinDetailData.description && coinDetailData.summary && (
                        <div className="mt-5 space-y-4 animate-fadeIn">
                          <h4 className="text-blue-400 font-medium text-base border-b border-blue-500/20 pb-1 mb-2">
                            Detailed Information
                          </h4>
                          <div className="leading-relaxed text-slate-300 coin-about-content">
                            <div
                              dangerouslySetInnerHTML={{
                                __html: coinDetailData.description,
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Only show the button if both summary and description are available */}
                  {coinDetailData.summary && coinDetailData.description && (
                    <div className="flex justify-center mt-4">
                      <button
                        onClick={() =>
                          setIsFullDescriptionVisible(!isFullDescriptionVisible)
                        }
                        className="group text-xs md:text-[0.95rem] text-blue-400 hover:text-blue-200 bg-gradient-to-r from-blue-500/10 to-blue-600/10 hover:from-blue-500/20 hover:to-blue-600/20 px-5 py-2.5 rounded-full transition-all duration-300 flex items-center gap-2.5 border border-blue-500/20 hover:border-blue-400/30 shadow-sm hover:shadow-md hover:shadow-blue-500/5"
                      >
                        {isFullDescriptionVisible ? (
                        <>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="md:w-4.5 md:h-4.5 group-hover:-translate-y-0.5 transition-transform duration-300"
                          >
                            <polyline points="18 15 12 9 6 15"></polyline>
                          </svg>
                          <span>Show Less</span>
                        </>
                      ) : (
                        <>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="md:w-4.5 md:h-4.5 group-hover:translate-y-0.5 transition-transform duration-300"
                          >
                            <polyline points="6 9 12 15 18 9"></polyline>
                          </svg>
                          <span>Show More</span>
                        </>
                      )}
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* BTC Market Statistics Card - Top Card */}
              <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg border border-slate-700/20 hover:border-green-500/30 px-9 py-7 shadow-lg transition-all duration-300 relative overflow-hidden group">
                <div className="absolute inset-0 bg-green-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-green-600/10 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>

                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-green-500/10 p-3 rounded-md group-hover:bg-green-500/20 transition-colors duration-300">
                    <BarChart4 className="h-6 w-6 text-green-400 group-hover:text-green-300 transition-colors duration-300" />
                  </div>
                  <h3 className="text-2xl font-semibold text-white group-hover:text-green-100 transition-colors duration-300">
                    {coinDetailData.symbol
                      ? `${coinDetailData.symbol.toUpperCase()} ${t('coinDetail.marketStatistics')}`
                      : t('coinDetail.marketStatistics')}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="flex items-center justify-between group/metric">
                    <div className="flex items-center gap-1.5 md:gap-2">
                      <div className="bg-slate-700/50 p-1 md:p-1.5 rounded-md group-hover/metric:bg-green-500/20 transition-all duration-300">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-slate-400 sm:w-4 sm:h-4 group-hover/metric:text-green-400 transition-colors duration-300"
                        >
                          <rect
                            x="2"
                            y="7"
                            width="20"
                            height="14"
                            rx="2"
                            ry="2"
                          ></rect>
                          <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                        </svg>
                      </div>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="text-xs md:text-sm text-slate-300 group-hover/metric:text-green-300 transition-colors duration-300">
                              {t('marketData.marketCap')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            className="bg-slate-800 text-slate-200 border-slate-700 text-[13px] max-w-[240px] p-3 shadow-xl"
                          >
                            <p className="font-medium text-green-400 mb-1.5">
                              Market Capitalization
                            </p>
                            <p>
                              Total market value of circulating supply,
                              calculated using the current price.
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="text-xs md:text-sm font-semibold text-white group-hover/metric:text-green-100 transition-colors duration-300 ">
                            {coinDetailData.marketData &&
                            typeof coinDetailData.marketData.MarketCap ===
                              "number"
                              ? formatCurrencyValue(
                                  coinDetailData.marketData.MarketCap,
                                  { compact: true, precision: 2 },
                                  t,
                                  currentLanguage,
                                )
                              : "Veri yok"}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          className="bg-slate-800 text-slate-200 border-slate-700 text-[13px] max-w-[240px] p-3 shadow-xl"
                        >
                          <p className="font-medium text-green-400 mb-1.5">
                            {t('marketData.marketCapDetails')}
                          </p>
                          <p>{t('marketData.currentRank')}: #{coinDetailData.rank || "N/A"}</p>
                          <p className="mt-1.5 text-slate-400 text-[11px]">
                            {t('marketData.updatedHourly')}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5 md:gap-2">
                      <div className="bg-slate-700/50 p-1 md:p-1.5 rounded-md">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-slate-400 sm:w-4 sm:h-4"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <path d="M12 6v6l4 2"></path>
                        </svg>
                      </div>
                      <span className="text-xs md:text-sm text-slate-300">
                        {t('marketData.fdv')}
                      </span>
                    </div>
                    <span className="text-xs md:text-sm font-semibold text-white">
                      {coinDetailData.marketData &&
                      typeof coinDetailData.marketData.FullyDilutedValuation ===
                        "number"
                        ? formatCurrencyValue(
                            coinDetailData.marketData.FullyDilutedValuation,
                            { compact: true, precision: 2 },
                            t,
                            currentLanguage,
                          )
                        : "Veri yok"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5 md:gap-2">
                      <div className="bg-slate-700/50 p-1 md:p-1.5 rounded-md">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-slate-400 sm:w-4 sm:h-4"
                        >
                          <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                        </svg>
                      </div>
                      <span className="text-xs md:text-sm text-slate-300">
                        {t('marketData.tradeVolume24h')}
                      </span>
                    </div>
                    <span className="text-xs md:text-sm font-semibold text-white">
                      {coinDetailData.marketData &&
                      typeof coinDetailData.marketData.H24Volume === "number"
                        ? formatCurrencyValue(
                            coinDetailData.marketData.H24Volume,
                            { compact: true, precision: 1 },
                            t,
                            currentLanguage,
                          )
                        : "Veri yok"}
                    </span>
                  </div>

                  {/* ATH Market Cap alanı kaldırıldı - gerçek veri olmadığı için */}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5 md:gap-2">
                      <div className="bg-slate-700/50 p-1 md:p-1.5 rounded-md">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-slate-400 sm:w-4 sm:h-4"
                        >
                          <line x1="12" y1="1" x2="12" y2="23"></line>
                          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                      </div>
                      <span className="text-xs md:text-sm text-slate-300">
                        {t('marketData.volumeMarketCapRatio')}
                      </span>
                    </div>
                    <span className="text-xs md:text-sm font-semibold text-white">
                      {coinDetailData.marketData &&
                      typeof coinDetailData.marketData.H24Volume === "number" &&
                      typeof coinDetailData.marketData.MarketCap === "number" &&
                      coinDetailData.marketData.MarketCap !== 0
                        ? (
                            (coinDetailData.marketData.H24Volume /
                              coinDetailData.marketData.MarketCap) *
                            100
                          ).toFixed(2) + "%"
                        : "Veri yok"}
                    </span>
                  </div>
                </div>
              </div>

              {/* All-Time High/Low Card - New Bottom Card */}
              <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg border border-slate-700/20 hover:border-purple-500/30 px-9 py-7 shadow-lg transition-all duration-300 relative overflow-hidden group mt-4">
                <div className="absolute inset-0 bg-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-purple-600/10 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>

                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-purple-500/10 p-3 rounded-md group-hover:bg-purple-500/20 transition-colors duration-300">
                    <LineChart className="h-6 w-6 text-purple-400 group-hover:text-purple-300 transition-colors duration-300" />
                  </div>
                  <h3 className="text-2xl font-semibold text-white group-hover:text-purple-100 transition-colors duration-300">
                    {t('coinDetail.allTimeHighLow')}
                  </h3>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="text-sm text-slate-400 mb-2">
                      {t('coinDetail.allTimeHigh')}
                    </div>
                    <div className="text-lg font-semibold text-white mb-1">
                      {coinDetailData.ath
                        ? formatCurrencyValue(coinDetailData.ath, {
                            currency: "USD ",
                            compact: false,
                            precision: 2,
                            minPrecision: 2,
                            maxPrecision: 6,
                          }, t, currentLanguage)
                        : "Veri yok"}
                    </div>
                    <div className="text-sm font-semibold text-red-400">
                      {(() => {
                        // ATH değişim hesaplama
                        if (coinDetailData.ath && currentPrice) {
                          // ATH'dan ne kadar aşağıdayız hesaplama
                          const changeFromATH =
                            ((currentPrice - coinDetailData.ath) /
                              coinDetailData.ath) *
                            100;
                          return `${changeFromATH.toFixed(2)}%`;
                        } else if (coinDetailData.ath_change) {
                          // Eğer hesaplanan bir değer varsa kullan
                          return `${coinDetailData.ath_change.toFixed(2)}%`;
                        } else {
                          return "Veri yok";
                        }
                      })()}{" "}
                      {t('coinDetail.fromATH')}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-slate-400 mb-2">
                      {t('coinDetail.allTimeLow')}
                    </div>
                    <div className="text-lg font-semibold text-white mb-1">
                      {coinDetailData.atl
                        ? formatCurrencyValue(coinDetailData.atl, {
                            currency: "USD ",
                            compact: false,
                            precision: 2,
                            minPrecision: 2,
                            maxPrecision: 6,
                          }, t, currentLanguage)
                        : "Veri yok"}
                    </div>
                    <div className="text-sm font-semibold text-green-400">
                      {(() => {
                        // ATL değişim hesaplama
                        if (coinDetailData.atl && currentPrice) {
                          // ATL'den ne kadar yukarıdayız hesaplama
                          const changeFromATL =
                            ((currentPrice - coinDetailData.atl) /
                              coinDetailData.atl) *
                            100;
                          const prefix = changeFromATL > 0 ? "+" : "";
                          return `${prefix}${changeFromATL.toFixed(2)}%`;
                        } else if (coinDetailData.atl_change) {
                          // Eğer hesaplanan bir değer varsa kullan
                          const prefix =
                            coinDetailData.atl_change > 0 ? "+" : "";
                          return `${prefix}${coinDetailData.atl_change.toFixed(2)}%`;
                        } else {
                          return "Veri yok";
                        }
                      })()}{" "}
                      {t('coinDetail.fromATL')}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* RIGHT COLUMN - 50% Width - Contains Price Chart, Historical Score Chart, and Categories */}
            <div className="space-y-6">
              {/* Price Chart */}
              <PriceChart
                data={{
                  "7D": generatePriceData("7D"),
                  "14D": generatePriceData("14D"),
                  "30D": generatePriceData("30D"),
                }}
                currentPrice={currentPrice}
                coinSymbol={coinDetailData.symbol}
              />
              {/* New ScoreChart Component - Uses real API data */}
              {showAdvancedView && coinDetailData.total_score_history && (
                <ScoreChart
                  data={coinDetailData.total_score_history}
                  currentScore={coinDetailData.total_score}
                  coinSymbol={coinDetailData.symbol}
                />
              )}

              {/* External Links Card - Show coin links only in advanced mode */}
              {showAdvancedView && 
                ((Array.isArray(coinDetailData.socialLinks) &&
                  coinDetailData.socialLinks.length > 0) ||
                (Array.isArray(coinDetailData.otherLinks) &&
                  coinDetailData.otherLinks.length > 0)) ? (
                <LinksCard
                  socialLinks={coinDetailData.socialLinks || []}
                  otherLinks={coinDetailData.otherLinks || []}
                  coinName={coinDetailData.name}
                  className="mt-6"
                />
              ) : null}

              {/* Token Supply Dynamics Card - Relocated to right column for balanced layout */}
              <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg border border-slate-700/20 hover:border-blue-500/30 px-9 py-7 shadow-lg transition-all duration-300 relative overflow-hidden group mt-6">
                <div className="absolute inset-0 bg-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-blue-600/10 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>

                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-blue-500/10 p-3 rounded-md group-hover:bg-blue-500/20 transition-colors duration-300">
                    <Coins className="h-6 w-6 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
                  </div>
                  <h3 className="text-2xl font-semibold text-white group-hover:text-blue-100 transition-colors duration-300">
                    {t('coinDetail.tokenSupplyDynamics')}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5 md:gap-2">
                      <div className="bg-slate-700/50 p-1 md:p-1.5 rounded-md">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-slate-400 sm:w-4 sm:h-4"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="12"></line>
                          <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                      </div>
                      <span className="text-xs md:text-sm text-slate-300">
                        {t('coinDetail.maxSupply')}
                      </span>
                    </div>
                    <span className="text-xs md:text-sm font-semibold text-white">
                      {typeof coinDetailData.max_supply === "number"
                        ? formatCurrencyValue(coinDetailData.max_supply, {
                            currency: "",
                            compact: true,
                            precision: 2,
                            showZero: false,
                            zeroValue: "N/A",
                          }, t, currentLanguage)
                        : typeof coinDetailData.max_supply === "string" &&
                            coinDetailData.max_supply !== "null"
                          ? formatCurrencyValue(
                              parseInt(coinDetailData.max_supply),
                              {
                                currency: "",
                                compact: true,
                                precision: 2,
                                showZero: false,
                                zeroValue: "N/A",
                              },
                            )
                          : "N/A"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5 md:gap-2">
                      <div className="bg-slate-700/50 p-1 md:p-1.5 rounded-md">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-slate-400 sm:w-4 sm:h-4"
                        >
                          <rect
                            x="2"
                            y="7"
                            width="20"
                            height="14"
                            rx="2"
                            ry="2"
                          ></rect>
                          <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                        </svg>
                      </div>
                      <span className="text-xs md:text-sm text-slate-300">
                        {t('coinDetail.totalSupply')}
                      </span>
                    </div>
                    <span className="text-xs md:text-sm font-semibold text-white">
                      {typeof coinDetailData.total_supply === "number"
                        ? formatCurrencyValue(coinDetailData.total_supply, {
                            currency: "",
                            compact: true,
                            precision: 2,
                            showZero: false,
                            zeroValue: "N/A",
                          }, t, currentLanguage)
                        : typeof coinDetailData.total_supply === "string" &&
                            coinDetailData.total_supply !== "null"
                          ? formatCurrencyValue(
                              parseInt(coinDetailData.total_supply),
                              {
                                currency: "",
                                compact: true,
                                precision: 2,
                                showZero: false,
                                zeroValue: "N/A",
                              },
                            )
                          : "N/A"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5 md:gap-2">
                      <div className="bg-slate-700/50 p-1 md:p-1.5 rounded-md">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-slate-400 sm:w-4 sm:h-4"
                        >
                          <circle cx="9" cy="21" r="1"></circle>
                          <circle cx="20" cy="21" r="1"></circle>
                          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                        </svg>
                      </div>
                      <span className="text-xs md:text-sm text-slate-300">
                        {t('coinDetail.circulatingSupply')}
                      </span>
                    </div>
                    <span className="text-xs md:text-sm font-semibold text-white">
                      {typeof coinDetailData.circulating_supply === "number"
                        ? formatCurrencyValue(
                            coinDetailData.circulating_supply,
                            {
                              currency: "",
                              compact: true,
                              precision: 2,
                              showZero: false,
                              zeroValue: "N/A",
                            },
                            t,
                            currentLanguage,
                          )
                        : typeof coinDetailData.circulating_supply ===
                              "string" &&
                            coinDetailData.circulating_supply !== "null"
                          ? formatCurrencyValue(
                              parseInt(coinDetailData.circulating_supply),
                              {
                                currency: "",
                                compact: true,
                                precision: 2,
                                showZero: false,
                                zeroValue: "N/A",
                              },
                              t,
                              currentLanguage,
                            )
                          : "N/A"}
                    </span>
                  </div>

                  <div className="w-full h-2 mt-4 bg-slate-700/30 rounded-full">
                    <div
                      className="h-2 rounded-full bg-blue-500"
                      style={{
                        width: (() => {
                          // Calculate the ratio of circulating supply to max supply
                          let circulatingValue =
                            typeof coinDetailData.circulating_supply ===
                            "number"
                              ? coinDetailData.circulating_supply
                              : typeof coinDetailData.circulating_supply ===
                                    "string" &&
                                  coinDetailData.circulating_supply !== "null"
                                ? parseInt(coinDetailData.circulating_supply)
                                : 0;

                          let maxValue =
                            typeof coinDetailData.max_supply === "number"
                              ? coinDetailData.max_supply
                              : typeof coinDetailData.max_supply === "string" &&
                                  coinDetailData.max_supply !== "null"
                                ? parseInt(coinDetailData.max_supply)
                                : 0;

                          // If we have valid values for both, calculate the percentage
                          if (circulatingValue > 0 && maxValue > 0) {
                            const percentage = Math.min(
                              100,
                              (circulatingValue / maxValue) * 100,
                            );
                            return `${percentage}%`;
                          }

                          // Fallback to 100% if we can't calculate
                          return "100%";
                        })(),
                      }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-slate-400 mt-1">
                    <span>{t('coinDetail.circulatingSupply')}</span>
                    <span>
                      {(() => {
                        // Calculate the ratio of circulating supply to max supply
                        let circulatingValue =
                          typeof coinDetailData.circulating_supply === "number"
                            ? coinDetailData.circulating_supply
                            : typeof coinDetailData.circulating_supply ===
                                  "string" &&
                                coinDetailData.circulating_supply !== "null"
                              ? parseInt(coinDetailData.circulating_supply)
                              : 0;

                        let maxValue =
                          typeof coinDetailData.max_supply === "number"
                            ? coinDetailData.max_supply
                            : typeof coinDetailData.max_supply === "string" &&
                                coinDetailData.max_supply !== "null"
                              ? parseInt(coinDetailData.max_supply)
                              : 0;

                        // If we have valid values for both, calculate the percentage
                        if (circulatingValue > 0 && maxValue > 0) {
                          const percentage =
                            (circulatingValue / maxValue) * 100;
                          return `${percentage.toFixed(2)}% ${t('coinDetail.ofMaxSupply')}`;
                        }

                        // Fallback text if we can't calculate
                        return t('coinDetail.supplyRatioNA');
                      })()}
                    </span>
                  </div>

                  {/* Next Unlock bölümü - İki farklı koşul için ayrı kontroller */}
                  <div className="mt-6 md:mt-8 grid gap-3 md:gap-4">
                    {/* Next Unlock Date - nextunlock değeri varsa göster */}
                    {coinDetailData.nextunlock && (
                        <div>
                          <div className="text-xs text-slate-400 mb-1 md:mb-1.5">
                            {t('coinDetail.nextUnlock')}
                          </div>
                          <div className="text-sm md:text-base font-semibold text-white">
                            {(() => {
                              try {
                                const unlockDate = new Date(coinDetailData.nextunlock);

                                // Format the date
                                const formattedDate = unlockDate.toLocaleDateString('en-US', {
                                  year: 'numeric',
                                  month: 'short',
                                  day: 'numeric'
                                });

                                return formattedDate;
                              } catch (e) {
                                return coinDetailData.nextunlock;
                              }
                            })()}
                          </div>
                        </div>
                      )}

                    {/* Unlock Date - nextunlock değeri varsa her zaman göster */}
                    {coinDetailData.nextunlock && (
                      <div className="flex items-center gap-1.5 md:gap-2 text-xs md:text-sm text-blue-400">
                        <div className="bg-blue-500/10 p-1 rounded-md">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-blue-400 md:w-3.5 md:h-3.5"
                          >
                            <rect
                              x="3"
                              y="11"
                              width="18"
                              height="11"
                              rx="2"
                              ry="2"
                            ></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                          </svg>
                        </div>
                        <span>
                          {(() => {
                            try {
                              const unlockDate = new Date(
                                coinDetailData.nextunlock,
                              );
                              const today = new Date();
                              const diffTime =
                                unlockDate.getTime() - today.getTime();
                              const diffDays = Math.ceil(
                                diffTime / (1000 * 60 * 60 * 24),
                              );

                              if (diffDays <= 0) return t('coinDetail.unlockToday');
                              if (diffDays === 1) return t('coinDetail.unlockTomorrow');
                              return t('coinDetail.unlockInDays').replace('{{days}}', diffDays.toString());
                            } catch (e) {
                              return t('coinDetail.upcomingUnlock');
                            }
                          })()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Launchpad Information Section */}
        {coinDetailData.launchpad && coinDetailData.launchpad.length > 0 && (
          <motion.div
            className="mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <LaunchpadInfo launchpads={coinDetailData.launchpad} />
          </motion.div>
        )}

        {/* Category Metrics Breakdown Section - Significantly enhanced for visual consistency */}
        <AnimatePresence mode="wait">
          {coinDetailData.scores.length > 0 && (
            <motion.div
              className="mt-16"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.4,
                ease: [0.25, 0.1, 0.25, 1.0],
              }}
              key="advanced-view"
            >
              <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-lg border border-slate-700/20 hover:border-slate-600/30 px-9 py-7 shadow-lg transition-all duration-300 relative overflow-hidden group mb-8">
                <div className="absolute inset-0 bg-slate-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-slate-600/10 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>

                {/* Main header section with balanced spacing and alignment */}
                <div className="flex items-center justify-between mb-6">
                  {/* Left side - Title with icon */}
                  <motion.div
                    className="flex items-center gap-2.5"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1, duration: 0.3 }}
                  >
                    <div className="bg-blue-500/10 p-1.5 rounded-md group-hover:bg-blue-500/20 transition-colors duration-300 flex-shrink-0">
                      <Sparkles className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
                    </div>
                    <h2 className="text-xl font-semibold text-white group-hover:text-white transition-colors duration-300">
                      {t('coinDetail.overallCategoriesMetricsBreakdown')}
                    </h2>
                  </motion.div>
                </div>
                <DetailedAnalysis
                  categories={detailedAnalysisCategories}
                  activeCategoryIndex={selectedCategoryIndex}
                  onCategoryChange={handleCategorySelect}
                  overallScore={coinDetailData.total_score}
                  coinId={coinId} // Pass the coin ID variable, not "id"
                  coinSymbol={coinDetailData.symbol}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
