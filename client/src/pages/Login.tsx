import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { LoginForm } from "@/components/auth/LoginForm";
import { RegisterForm } from "@/components/auth/RegisterForm";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ThirdPartyAuth } from "@/components/auth/ThirdPartyAuth";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Shield } from "lucide-react";
import { useLocation } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

// Completely new, distinctive CoinScout logo
const CoinScoutLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50">
    <circle cx="50" cy="50" r="25" fill="#e6f7ff" />

    <circle cx="50" cy="50" r="12" fill="#ffffff" />

    <circle cx="50" cy="50" r="7" fill="#09a9e6" />
  </svg>
);

export default function LoginPage() {
  // The active tab - login or register, initially set to login
  const [activeTab, setActiveTab] = useState<"login" | "register">("login");
  const [location] = useLocation();
  const [returnTo, setReturnTo] = useState<string | null>(null);
  const { t } = useLanguage();

  // Check URL parameters and set the active tab when the component mounts
  useEffect(() => {
    // Log full URL information for debugging
    console.log('%c *** Login Page Mount - URL Debugging ***', 'background: #222; color: #bada55');
    console.log('Full URL at page mount:', window.location.href);
    console.log('Search params at mount:', window.location.search);

    // Check if user is already logged in
    const authToken = localStorage.getItem('auth_token');
    if (authToken) {
      // User is already logged in, check for redirect
      const urlParams = new URLSearchParams(window.location.search);
      const redirectParam = urlParams.get('redirect');

      // If there's a redirect parameter, go there directly
      if (redirectParam) {
        console.log('User already logged in, redirecting to:', redirectParam);
        window.location.href = redirectParam;
        return;
      }
    }

    // Check for mode parameter in URL - direct DOM access is most reliable
    if (window.location.href.includes('mode=register')) {
      console.log('🎯 Found mode=register in URL - Setting register tab');
      // Directly click the register tab
      setTimeout(() => {
        const registerTab = document.querySelector('[value="register"]') as HTMLElement;
        if (registerTab) {
          console.log('Register tab found, clicking it:', registerTab);
          registerTab.click();
        } else {
          console.log('Register tab element not found - setting state directly');
          setActiveTab('register');
        }
      }, 50); // Small delay to ensure DOM is ready
    }

    // Handle returnTo parameter if present
    const urlParams = new URLSearchParams(window.location.search);
    const returnParam = urlParams.get('returnTo');
    if (returnParam) {
      console.log('Setting returnTo:', returnParam);
      setReturnTo(returnParam);
    }
  }, []); // Empty dependency array means this runs once when component mounts

  // Also listen for location changes to handle navigation within the SPA
  useEffect(() => {
    // Get URL search params directly from window.location
    const urlParams = new URLSearchParams(window.location.search);

    // Check if there's a mode parameter requesting register tab
    const modeParam = urlParams.get('mode');
    if (modeParam === 'register') {
      console.log('Navigation detected with mode=register - Setting active tab to register');
      setActiveTab('register');
    } else if (modeParam === 'login') {
      console.log('Navigation detected with mode=login - Setting active tab to login');
      setActiveTab('login');
    }

  }, [location]);

  return (
    <div className="container relative min-h-screen flex flex-col items-center justify-center py-8 bg-auth-gradient dark:bg-auth-gradient">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute w-[500px] h-[500px] rounded-full bg-primary/5 blur-3xl top-1/4 -left-1/4 animate-pulse"></div>
        <div className="absolute w-[400px] h-[400px] rounded-full bg-secondary/5 blur-3xl bottom-0 right-1/3 animate-pulse delay-700"></div>
      </div>
      <motion.div
        className="w-full max-w-[520px] relative z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="border-border/40 fluent-shadow backdrop-blur-sm bg-card/80">
          <CardHeader className="space-y-1 pb-2 px-8">
            <motion.div
              className="flex items-center justify-center mb-4"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1, type: "spring" }}
            >
              <div className="w-16 h-16 flex items-center justify-center">
                <CoinScoutLogo />
              </div>
            </motion.div>
            <CardTitle className="text-2xl font-bold text-center">
              {activeTab === "login"
                ? t("welcome.back", "auth", "Welcome back")
                : t("register.title", "auth", "Create an account")}
            </CardTitle>
            <CardDescription className="text-center">
              {activeTab === "login"
                ? t("login.credential.prompt", "auth", "Enter your credentials to sign in to your account")
                : t("register.description", "auth", "Create an account to get started")}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-3 px-8">
            <Tabs
              defaultValue={activeTab} /* This sets the initial tab */
              value={activeTab} /* This controls the current active tab */
              onValueChange={(value) => {
                setActiveTab(value as "login" | "register");
              }}
              className="w-full"
            >
              <TabsList className="grid grid-cols-2 mb-4">
                <TabsTrigger value="login">{t("signin", "auth", "Sign In")}</TabsTrigger>
                <TabsTrigger value="register">{t("signup", "auth", "Sign Up")}</TabsTrigger>
              </TabsList>
              <TabsContent value="login">
                <LoginForm
                  returnTo={returnTo || undefined}
                  onSuccess={() => {
                    if (returnTo) {
                      window.location.href = decodeURIComponent(returnTo);
                    }
                  }}
                />
              </TabsContent>
              <TabsContent value="register">
                <RegisterForm onToggleView={() => setActiveTab("login")} />
              </TabsContent>
            </Tabs>

            <div className="relative mt-4 mb-4">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  {t("continueWith", "auth", "Continue with")}
                </span>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex flex-col space-y-4 pt-0 px-8">
            <div className="text-center text-sm text-muted-foreground mt-2">
              {t("termsAccept", "auth", "By continuing, you agree to our")}{" "}
              <a
                href="/terms"
                className="underline underline-offset-4 hover:text-primary"
              >
                {t("terms.service", "auth", "Terms of Service")}
              </a>{" "}
              {t("terms.and", "auth", "and")}{" "}
              <a
                href="/privacy"
                className="underline underline-offset-4 hover:text-primary"
              >
                {t("terms.privacy", "auth", "Privacy Policy")}
              </a>
            </div>
          </CardFooter>
        </Card>

        <div className="flex justify-center mt-6">
          <Button
            variant="ghost"
            size="sm"
            className="text-muted-foreground"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("auth:backToHome", "auth", "Back to Home")}
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
