/**
 * Test Alerts Page
 *
 * Test interface for crypto alert WebSocket notifications
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Bell, TrendingUp, AlertTriangle, Info, Shield, CheckCircle2 } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { getWebSocketUrl } from '@/lib/CoinScoutWebSocketClient';

const TestAlertsPage = () => {
  const notifications = useNotifications();
  const [testForm, setTestForm] = useState({
    title: 'Bitcoin Fiyat Uyarısı',
    message: 'Bitcoin 70,000 USD seviyesini aştı!',
    type: 'price',
    priority: 'high',
    coinId: 'bitcoin',
    coinName: 'Bitcoin',
    coinSymbol: 'BTC'
  });

  const handleSendTestNotification = async () => {
    try {
      // Mock test bildirimi oluştur (API endpoint olmadığı için)
      const mockNotification = {
        id: `test-${Date.now()}`,
        title: testForm.title,
        message: testForm.message,
        type: testForm.type,
        priority: testForm.priority,
        status: 'unread',
        timestamp: new Date(),
        coinId: testForm.coinId,
        coinName: testForm.coinName,
        coinSymbol: testForm.coinSymbol
      };

      // Direkt notification hook'una ekle
      console.log('🧪 Mock test notification created:', mockNotification);

      // useNotifications hook'undan addNotification fonksiyonu çağırılabilir
      // Şimdilik console'da gösterelim
      alert(`Test bildirimi oluşturuldu!\n\nBaşlık: ${mockNotification.title}\nMesaj: ${mockNotification.message}`);

    } catch (error) {
      console.error('Error creating test notification:', error);
    }
  };

  const handleBroadcastNotification = async () => {
    try {
      const response = await fetch('https://api.coinscout.app/api/notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: 'Sistem Duyurusu',
          message: 'Yeni özellikler eklendi! Portföy takibi artık daha gelişmiş.',
          type: 'system',
          priority: 'medium'
        })
      });

      const result = await response.json();
      console.log('Broadcast notification sent:', result);
    } catch (error) {
      console.error('Error sending broadcast notification:', error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'price': return TrendingUp;
      case 'alert': return AlertTriangle;
      case 'security': return Shield;
      case 'system': return CheckCircle2;
      default: return Info;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-3">
        <Bell className="h-8 w-8 text-primary" />
        <div>
          <h1 className="text-3xl font-bold">Crypto Alert Test</h1>
          <p className="text-muted-foreground">WebSocket bildirim sistemi test arayüzü</p>
        </div>
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle>Bağlantı Durumu</CardTitle>
          <CardDescription>WebSocket bağlantı durumu ve kimlik doğrulama bilgileri</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${notifications.isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span>WebSocket: {notifications.isConnected ? 'Bağlı' : 'Bağlantısız'}</span>
            {notifications.isConnected && <Badge variant="secondary" className="text-xs">Aktif</Badge>}
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${notifications.isAuthenticated ? 'bg-green-500' : 'bg-red-500'}`} />
            <span>Kimlik Doğrulama: {notifications.isAuthenticated ? 'Başarılı' : 'Beklemede'}</span>
            {notifications.isAuthenticated && <Badge variant="secondary" className="text-xs">Doğrulandı</Badge>}
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">{notifications.unreadCount} okunmamış bildirim</Badge>
          </div>
          <div className="text-xs text-muted-foreground space-y-1">
            <div>WebSocket URL: <code className="bg-muted-foreground text-black px-1 rounded">{getWebSocketUrl()}</code></div>
            <div>Konsolu kontrol ederek detaylı bağlantı loglarını görebilirsiniz.</div>
          </div>
        </CardContent>
      </Card>

      {/* Test Notification Form */}
      <Card>
        <CardHeader>
          <CardTitle>Test Bildirimi Gönder</CardTitle>
          <CardDescription>Crypto alert bildirimi test etmek için aşağıdaki formu kullanın</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Başlık</Label>
              <Input
                id="title"
                value={testForm.title}
                onChange={(e) => setTestForm(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Bildirim başlığı"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="message">Mesaj</Label>
              <Input
                id="message"
                value={testForm.message}
                onChange={(e) => setTestForm(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Bildirim mesajı"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Tip</Label>
              <Select value={testForm.type} onValueChange={(value) => setTestForm(prev => ({ ...prev, type: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="price">Fiyat Uyarısı</SelectItem>
                  <SelectItem value="alert">Genel Uyarı</SelectItem>
                  <SelectItem value="security">Güvenlik</SelectItem>
                  <SelectItem value="system">Sistem</SelectItem>
                  <SelectItem value="update">Güncelleme</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="priority">Öncelik</Label>
              <Select value={testForm.priority} onValueChange={(value) => setTestForm(prev => ({ ...prev, priority: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">Yüksek</SelectItem>
                  <SelectItem value="medium">Orta</SelectItem>
                  <SelectItem value="low">Düşük</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="coinName">Coin Adı</Label>
              <Input
                id="coinName"
                value={testForm.coinName}
                onChange={(e) => setTestForm(prev => ({ ...prev, coinName: e.target.value }))}
                placeholder="Bitcoin"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="coinSymbol">Coin Sembolü</Label>
              <Input
                id="coinSymbol"
                value={testForm.coinSymbol}
                onChange={(e) => setTestForm(prev => ({ ...prev, coinSymbol: e.target.value }))}
                placeholder="BTC"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleSendTestNotification} className="flex-1">
              Test Bildirimi Gönder
            </Button>
            <Button onClick={handleBroadcastNotification} variant="outline" className="flex-1">
              Sistem Duyurusu Gönder
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Live Notifications */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Canlı Bildirimler</CardTitle>
            <CardDescription>WebSocket üzerinden gelen gerçek zamanlı bildirimler</CardDescription>
          </div>
          {notifications.unreadCount > 0 && (
            <Button onClick={notifications.markAllAsRead} variant="outline" size="sm">
              Tümünü Okundu İşaretle
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {notifications.notifications.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Henüz bildirim yok. Test bildirimi göndererek başlayın.
              </div>
            ) : (
              notifications.notifications.map((notification) => {
                const IconComponent = getNotificationIcon(notification.type);
                return (
                  <div
                    key={notification.id}
                    className={`flex items-start gap-3 p-3 rounded-lg border ${
                      notification.status === 'unread' ? 'bg-muted/50' : 'bg-background'
                    }`}
                  >
                    <IconComponent className="h-5 w-5 mt-1 text-primary" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{notification.title}</h4>
                        <Badge variant={getPriorityColor(notification.priority as any)} className="text-xs">
                          {notification.priority}
                        </Badge>
                        {notification.status === 'unread' && (
                          <Badge variant="secondary" className="text-xs">Yeni</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{notification.message}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{new Date(notification.timestamp).toLocaleTimeString('tr-TR')}</span>
                        <span className="capitalize">{notification.type}</span>
                        {notification.coinSymbol && (
                          <span className="font-mono">{notification.coinSymbol}</span>
                        )}
                      </div>
                    </div>
                    {notification.status === 'unread' && (
                      <Button
                        onClick={() => notifications.markAsRead(notification.id)}
                        variant="ghost"
                        size="sm"
                        className="shrink-0"
                      >
                        Okundu
                      </Button>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestAlertsPage;