import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { motion } from 'framer-motion';
import { cn } from "@/lib/utils";
import { useLanguage } from '@/contexts/LanguageContext';
import { useFavorites } from "@/hooks/useFavorites";
import { useToast } from "@/hooks/use-toast";
import { useWatchlist } from "@/components/watchlists/WatchlistProvider";
import { 
  LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, 
  Tooltip as RechartsTooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell 
} from 'recharts';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger
} from "@/components/ui/dialog";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { ScoreIndicator } from "@/components/ScoreIndicator";
import { CreateAlertButton } from '@/components/alerts/CreateAlertButton';
import { 
  Star, 
  Plus, 
  PlusCircle,
  Pencil,
  Trash2,
  Check,
  CheckCircle,
  Filter,
  RefreshCw,
  Settings,
  MoreHorizontal,
  ChevronDown,
  Bell,
  Share,
  Copy,
  Folder,
  ArrowUpDown,
  BarChart3,
  ListFilter,
  Search,
  GanttChart,
  ArrowUp,
  ArrowDown,
  Eye,
  EyeOff,
  Coins,
  Info,
  ArrowRight,
  Link,
  LineChart as LineChartIcon
} from "lucide-react";

interface CoinScore {
  score: number;
  label: string;
  status: 'Excellent' | 'Good' | 'Fair';
}

interface Coin {
  id: string;
  rank: number;
  name: string;
  symbol: string;
  price?: number;
  marketCap?: string;
  priceChanges?: {
    "24h": number;
    "7d": number;
    "30d": number;
    "90d": number;
    "1y": number;
  };
  tokenomics: CoinScore;
  security: CoinScore;
  social: CoinScore;
  market: CoinScore;
  insights: CoinScore;
  totalScore: CoinScore;
  sevenDayChange: number;
}

interface WatchlistProps {
  id: number; 
  name: string;
  description: string;
  icon: string;
  isDefault: boolean;
  itemCount: number;
  items: string[];
}

// Reusable Performance Chart component
const PerformanceChart = () => {
  const { t } = useLanguage();
  
  return (
  <Card className="bg-card/50 backdrop-blur-sm">
    <CardHeader className="pb-4">
      <div className="flex items-center justify-between">
        <CardTitle className="text-lg">{t('watchlist.portfolioScorePerformance')}</CardTitle>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          <LineChartIcon className="h-3.5 w-3.5" />
          <span>7D</span>
        </Button>
      </div>
    </CardHeader>
    <CardContent className="pb-4">
      <div className="h-[200px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={[
              { day: 'Mon', score: 72 },
              { day: 'Tue', score: 76 },
              { day: 'Wed', score: 78 },
              { day: 'Thu', score: 75 },
              { day: 'Fri', score: 82 },
              { day: 'Sat', score: 84 },
              { day: 'Sun', score: 85 },
            ]}
            margin={{ top: 5, right: 20, left: 0, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#333" opacity={0.15} />
            <XAxis dataKey="day" stroke="#888" opacity={0.5} />
            <YAxis domain={[60, 90]} stroke="#888" opacity={0.5} />
            <RechartsTooltip
              contentStyle={{
                backgroundColor: 'rgba(18, 18, 18, 0.8)',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '6px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.25)',
              }}
              labelStyle={{ color: '#fff' }}
              itemStyle={{ color: '#7ee787' }}
            />
            <Line
              type="monotone"
              dataKey="score"
              stroke="#7ee787"
              strokeWidth={2}
              dot={{ r: 4, fill: '#1e1e1e', strokeWidth: 2 }}
              activeDot={{ r: 6, fill: '#7ee787', strokeWidth: 0 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </CardContent>
  </Card>
  );
};

// Static data for now
const mockCoins: Coin[] = Array.from({ length: 20 }, (_, i) => ({
  id: `coin-${i + 1}`,
  rank: i + 1,
  name: `Crypto ${i + 1}`,
  symbol: ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'ADA', 'AVAX', 'MATIC', 'DOT', 'UNI'][i % 10],
  price: Math.random() * (i < 3 ? 10000 : i < 8 ? 1000 : 100),
  marketCap: `$${(Math.random() * (i < 5 ? 100 : 10)).toFixed(2)}B`,
  priceChanges: {
    "24h": (Math.random() * 20) - 10,
    "7d": (Math.random() * 30) - 15,
    "30d": (Math.random() * 50) - 25,
    "90d": (Math.random() * 100) - 50,
    "1y": (Math.random() * 200) - 50,
  },
  tokenomics: {
    score: Math.floor(Math.random() * 40) + 60,
    label: 'Fair',
    status: 'Fair',
  },
  security: {
    score: Math.floor(Math.random() * 30) + 70,
    label: 'Good',
    status: 'Good',
  },
  social: {
    score: Math.floor(Math.random() * 30) + 60,
    label: 'Fair',
    status: 'Fair',
  },
  market: {
    score: Math.floor(Math.random() * 20) + 75,
    label: 'Good',
    status: 'Good',
  },
  insights: {
    score: Math.floor(Math.random() * 25) + 70,
    label: 'Good',
    status: 'Good',
  },
  totalScore: {
    score: Math.floor(Math.random() * 25) + 70,
    label: 'Good',
    status: 'Good',
  },
  sevenDayChange: (Math.random() * 20) - 10,
}));

// Utility formatters
const formatPercentage = (change?: number) => {
  if (change === undefined) return "-";
  
  return (
    <span className={cn(
      "font-medium",
      change > 0 ? "text-green-500" : change < 0 ? "text-red-500" : ""
    )}>
      {change > 0 ? "+" : ""}{change.toFixed(2)}%
    </span>
  );
};

const formatPrice = (price?: number) => {
  if (price === undefined) return "-";
  
  if (price < 0.00001) {
    return `$${price.toFixed(6)}`;
  } else if (price < 0.001) {
    return `$${price.toFixed(4)}`;
  } else if (price < 1) {
    return `$${price.toFixed(2)}`;
  } else if (price < 1000) {
    return `$${price.toFixed(2)}`;
  } else if (price < 1000000) {
    return `$${price.toLocaleString('en-US', { maximumFractionDigits: 2 })}`;
  } else {
    return `$${price.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
  }
};

export default function WatchlistDashboard() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { t } = useLanguage();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [displayMode, setDisplayMode] = useState<'simple' | 'advanced'>('simple');
  const [search, setSearch] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' }>({ 
    key: 'rank', 
    direction: 'asc' 
  });
  const [selectedCoins, setSelectedCoins] = useState<string[]>([]);
  const [selectAllChecked, setSelectAllChecked] = useState(false);
  const [activeTimeframe, setActiveTimeframe] = useState('24h');
  const [showIntroduction, setShowIntroduction] = useState(true);
  const [showPerformance, setShowPerformance] = useState(false);
  
  // State for watchlist management
  const [watchlists, setWatchlists] = useState<WatchlistProps[]>([
    { id: 1, name: 'Default Watchlist', icon: '⭐', description: '', isDefault: true, itemCount: 5, items: mockCoins.slice(0, 5).map(c => c.id) },
    { id: 2, name: 'DeFi Gems', icon: '💎', description: 'Top DeFi projects to watch', isDefault: false, itemCount: 3, items: mockCoins.slice(2, 5).map(c => c.id) },
    { id: 3, name: 'Layer 1 Projects', icon: '🔗', description: 'Leading Layer 1 blockchains', isDefault: false, itemCount: 4, items: mockCoins.slice(5, 9).map(c => c.id) },
    { id: 4, name: 'High-Risk High-Reward', icon: '🚀', description: 'Speculative opportunities', isDefault: false, itemCount: 2, items: mockCoins.slice(10, 12).map(c => c.id) },
  ]);
  const [activeWatchlist, setActiveWatchlist] = useState<WatchlistProps>(watchlists[0]);
  
  // Dialog states
  const [watchlistDialogOpen, setWatchlistDialogOpen] = useState(false);
  const [isCreateMode, setIsCreateMode] = useState(true);
  const [watchlistIcon, setWatchlistIcon] = useState('⭐');
  const [watchlistName, setWatchlistName] = useState('');
  const [watchlistDescription, setWatchlistDescription] = useState('');
  const [editingWatchlistId, setEditingWatchlistId] = useState<number | null>(null);
  
  // Bulk action states
  const [bulkActionDialogOpen, setBulkActionDialogOpen] = useState(false);
  const [bulkActionTarget, setBulkActionTarget] = useState<WatchlistProps | null>(null);
  const [bulkActionType, setBulkActionType] = useState<'add' | 'remove'>('add');
  
  // Filter coins based on search and active watchlist
  const filteredCoins = mockCoins
    .filter(coin => {
      if (!activeWatchlist) return true;
      
      // Filter by watchlist
      return activeWatchlist.items.includes(coin.id);
    })
    .filter(coin => {
      if (!search) return true;
      
      return (
        coin.name.toLowerCase().includes(search.toLowerCase()) ||
        coin.symbol.toLowerCase().includes(search.toLowerCase())
      );
    })
    .sort((a, b) => {
      let result = 0;
      
      switch (sortConfig.key) {
        case 'rank':
          result = a.rank - b.rank;
          break;
        case 'name':
          result = a.name.localeCompare(b.name);
          break;
        case 'price':
          result = (a.price || 0) - (b.price || 0);
          break;
        case 'priceChange':
          result = (a.priceChanges?.[activeTimeframe as keyof typeof a.priceChanges] || 0) - 
                  (b.priceChanges?.[activeTimeframe as keyof typeof b.priceChanges] || 0);
          break;
        case 'totalScore':
          result = a.totalScore.score - b.totalScore.score;
          break;
        case 'marketCap':
          // Simple parsing for demo purposes
          const aVal = Number(a.marketCap?.replace('$', '').replace('B', '')) || 0;
          const bVal = Number(b.marketCap?.replace('$', '').replace('B', '')) || 0;
          result = aVal - bVal;
          break;
        // Sort by advanced metrics
        case 'tokenomics':
          result = a.tokenomics.score - b.tokenomics.score;
          break;
        case 'security':
          result = a.security.score - b.security.score;
          break;
        case 'social':
          result = a.social.score - b.social.score;
          break;
        case 'market':
          result = a.market.score - b.market.score;
          break;
        default:
          result = 0;
      }
      
      return sortConfig.direction === 'asc' ? result : -result;
    });
  
  // Functions for watchlist management
  const handleSort = (key: string) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc',
    }));
  };
  
  const handleSelectAll = (checked: boolean) => {
    setSelectAllChecked(checked);
    if (checked) {
      setSelectedCoins(filteredCoins.map(coin => coin.id));
    } else {
      setSelectedCoins([]);
    }
  };
  
  const handleSelectCoin = (coinId: string) => {
    setSelectedCoins(prev => {
      if (prev.includes(coinId)) {
        return prev.filter(id => id !== coinId);
      } else {
        return [...prev, coinId];
      }
    });
  };
  
  const handleCreateWatchlist = () => {
    setIsCreateMode(true);
    setWatchlistIcon('⭐');
    setWatchlistName('');
    setWatchlistDescription('');
    setEditingWatchlistId(null);
    setWatchlistDialogOpen(true);
  };
  
  const handleEditWatchlist = (watchlist: WatchlistProps) => {
    setIsCreateMode(false);
    setWatchlistIcon(watchlist.icon);
    setWatchlistName(watchlist.name);
    setWatchlistDescription(watchlist.description);
    setEditingWatchlistId(watchlist.id);
    setWatchlistDialogOpen(true);
  };
  
  const handleSaveWatchlist = () => {
    if (!watchlistName.trim()) {
      toast({
        title: "Watchlist name is required",
        variant: "destructive"
      });
      return;
    }
    
    if (isCreateMode) {
      // Create new watchlist
      const newWatchlist: WatchlistProps = {
        id: Date.now(),
        name: watchlistName,
        icon: watchlistIcon,
        description: watchlistDescription,
        isDefault: watchlists.length === 0, // First watchlist is default
        itemCount: 0,
        items: []
      };
      
      setWatchlists(prev => [...prev, newWatchlist]);
      setActiveWatchlist(newWatchlist);
      
      toast({
        title: "Watchlist created",
        description: `${watchlistName} has been created successfully`
      });
    } else {
      // Update existing watchlist
      setWatchlists(prev => 
        prev.map(w => w.id === editingWatchlistId ? {
          ...w,
          name: watchlistName,
          icon: watchlistIcon,
          description: watchlistDescription,
        } : w)
      );
      
      // Update active watchlist if it's the one being edited
      if (activeWatchlist.id === editingWatchlistId) {
        setActiveWatchlist(prev => ({
          ...prev,
          name: watchlistName,
          icon: watchlistIcon,
          description: watchlistDescription,
        }));
      }
      
      toast({
        title: "Watchlist updated",
        description: `${watchlistName} has been updated successfully`
      });
    }
    
    setWatchlistDialogOpen(false);
  };
  
  const handleSetAsDefault = (watchlist: WatchlistProps) => {
    if (watchlist.isDefault) return;
    
    setWatchlists(prev => 
      prev.map(w => ({
        ...w,
        isDefault: w.id === watchlist.id
      }))
    );
    
    toast({
      title: "Default watchlist changed",
      description: `${watchlist.name} is now your default watchlist`
    });
  };
  
  const handleDeleteWatchlist = (watchlist: WatchlistProps) => {
    if (watchlists.length <= 1) {
      toast({
        title: "Cannot delete last watchlist",
        description: "You need to have at least one watchlist",
        variant: "destructive"
      });
      return;
    }
    
    if (watchlist.isDefault) {
      toast({
        title: "Cannot delete default watchlist",
        description: "Please set another watchlist as default first",
        variant: "destructive"
      });
      return;
    }
    
    // Store watchlist before deletion for potential undo functionality
    const watchlistToRestore = { ...watchlist };
    
    // Remove the watchlist and all its items
    const updatedLists = watchlists.filter(w => w.id !== watchlist.id);
    setWatchlists(updatedLists);
    
    // Also update local storage to persist changes and ensure all items are cleaned up
    localStorage.setItem('coinscout_watchlists', JSON.stringify(updatedLists));
    
    // Switch active watchlist if the deleted one was active
    if (activeWatchlist.id === watchlist.id) {
      setActiveWatchlist(updatedLists[0]);
    }
    
    // Show success toast with undo option
    toast({
      title: "Watchlist deleted",
      description: `${watchlist.name} has been deleted with all its items`,
      action: (
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => {
            // Restore the deleted watchlist
            const restoredWatchlists = [...updatedLists, watchlistToRestore];
            setWatchlists(restoredWatchlists);
            
            // Update localStorage with the restored watchlist
            localStorage.setItem('coinscout_watchlists', JSON.stringify(restoredWatchlists));
            
            toast({
              title: 'Watchlist Restored',
              description: 'Your watchlist has been restored with all its items',
            });
          }}
        >
          Undo
        </Button>
      ),
    });
  };
  
  const handleBulkAction = (action: 'add' | 'remove', targetWatchlist: WatchlistProps) => {
    if (!selectedCoins.length) return;
    
    setBulkActionType(action);
    setBulkActionTarget(targetWatchlist);
    setBulkActionDialogOpen(true);
  };
  
  const confirmBulkAction = (action: 'add' | 'remove') => {
    if (!bulkActionTarget) return;
    
    let updatedWatchlists: WatchlistProps[] = [];
    
    if (action === 'add') {
      // Add selected coins to target watchlist
      updatedWatchlists = watchlists.map(w => {
        if (w.id === bulkActionTarget.id) {
          const updatedItems = Array.from(new Set([...w.items, ...selectedCoins]));
          return {
            ...w,
            items: updatedItems,
            itemCount: updatedItems.length
          };
        }
        return w;
      });
      
      setWatchlists(updatedWatchlists);
      
      // Store changes to localStorage to ensure persistence
      localStorage.setItem('coinscout_watchlists', JSON.stringify(updatedWatchlists));
      
      toast({
        title: "Coins added to watchlist",
        description: `${selectedCoins.length} coins added to ${bulkActionTarget.name}`
      });
    } else {
      // Remove selected coins from target watchlist
      updatedWatchlists = watchlists.map(w => {
        if (w.id === bulkActionTarget.id) {
          const updatedItems = w.items.filter(id => !selectedCoins.includes(id));
          return {
            ...w,
            items: updatedItems,
            itemCount: updatedItems.length
          };
        }
        return w;
      });
      
      setWatchlists(updatedWatchlists);
      
      // Store changes to localStorage to ensure persistence
      localStorage.setItem('coinscout_watchlists', JSON.stringify(updatedWatchlists));
      
      toast({
        title: "Coins removed from watchlist",
        description: `${selectedCoins.length} coins removed from ${bulkActionTarget.name}`
      });
    }
    
    // Update active watchlist if it was modified (using the updated watchlists)
    if (activeWatchlist.id === bulkActionTarget.id) {
      const updatedActiveWatchlist = updatedWatchlists.find(w => w.id === activeWatchlist.id);
      if (updatedActiveWatchlist) {
        setActiveWatchlist(updatedActiveWatchlist);
      }
    }
    
    // Reset state
    setSelectedCoins([]);
    setSelectAllChecked(false);
    setBulkActionDialogOpen(false);
    setBulkActionTarget(null);
  };
  
  return (
    <div className="container mx-auto py-6 max-w-[1400px]" style={{ opacity: 0.9999 }}>
      <div className="flex flex-col gap-2 mb-8">
        <h1 className="text-2xl font-bold tracking-tight text-primary">{t('watchlist.title')}</h1>
        <p className="text-base text-muted-foreground">
          {t('watchlist.description')}
        </p>
      </div>

      {/* Aktif Watchlist ve Watchlist Menüsü - İdiot-proof tasarım */}
      <div className="flex flex-col gap-4 mb-6">
        {/* Aktif Watchlist Kartı */}
        <Card className="bg-[#132F4C]/80 backdrop-blur-sm border-[#1E4976]/20 border-blue-500/50 transition-all duration-200">
          <CardHeader className="flex flex-row items-center space-y-0 p-3 pb-2">
            <div className="flex items-center space-x-3 flex-1">
              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-blue-900/30 to-blue-800/30 flex items-center justify-center text-xl border border-blue-500/40">
                {activeWatchlist?.icon}
              </div>
              <div>
                <div className="flex items-center">
                  <CardTitle className="text-base font-medium text-blue-400 flex items-center">
                    {activeWatchlist?.name}
                    {activeWatchlist?.isDefault && (
                      <Star className="h-3.5 w-3.5 ml-2 fill-blue-500 text-blue-500" />
                    )}
                  </CardTitle>
                  <Badge className="ml-2 bg-blue-500/20 text-blue-400 border-blue-500/30">
                    {activeWatchlist?.itemCount} {activeWatchlist?.itemCount === 1 ? 'coin' : 'coins'}
                  </Badge>
                </div>
                {activeWatchlist?.description && (
                  <p className="text-xs text-[#E7EBF0]/60 mt-0.5">{activeWatchlist.description}</p>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-1.5">
              {/* Edit Button */}
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-9 w-9 bg-[#0A1929] text-[#E7EBF0]/60 hover:text-[#E7EBF0]/90 hover:bg-[#132F4C]"
                onClick={() => handleEditWatchlist(activeWatchlist)}
              >
                <Pencil className="h-4 w-4" />
              </Button>
              
              {/* Share Button with Dialog */}
              <Dialog>
                <DialogTrigger asChild>
                  <Button 
                    variant="ghost" 
                    className="h-9 bg-[#0A1929] text-[#E7EBF0]/60 hover:text-[#E7EBF0]/90 hover:bg-[#132F4C] flex items-center gap-1.5 px-3"
                  >
                    <Share className="h-4 w-4" />
                    <span>{t('watchlist.share')}</span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[480px] bg-[#0A1929] border-[#1E4976]/20 p-0 overflow-hidden">
                  <DialogHeader className="p-6 pb-4">
                    <DialogTitle className="text-xl text-[#E7EBF0] font-semibold flex items-center gap-2">
                      <Share className="h-5 w-5 text-blue-400" />
                      {t('watchlist.share')} Watchlist
                    </DialogTitle>
                    <DialogDescription className="text-[#E7EBF0]/70 text-sm">
                      {t('watchlist.shareDescription')}
                    </DialogDescription>
                  </DialogHeader>
                  
                  {/* Preview Card */}
                  <div className="px-6">
                    <div className="bg-[#132F4C] rounded-lg overflow-hidden border border-[#1E4976]/30 mb-5 shadow-lg shadow-blue-900/10">
                      {/* Preview Label */}
                      <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-1 text-xs text-white font-medium">
                        {t('watchlist.preview')}
                      </div>
                      
                      {/* Watchlist Preview */}
                      <div className="p-4">
                        <div className="flex items-center gap-3.5">
                          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-900/50 to-blue-800/50 flex items-center justify-center text-2xl text-amber-300 shadow-inner shadow-black/10">
                            {activeWatchlist?.icon}
                          </div>
                          <div>
                            <h3 className="text-[#E7EBF0] font-medium text-lg">{activeWatchlist?.name}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 text-[11px] h-5 px-2">
                                {activeWatchlist?.itemCount} Coin
                              </Badge>
                              <Badge className="bg-indigo-500/20 text-indigo-400 border-indigo-500/30 text-[11px] h-5 px-2">
                                CoinScout
                              </Badge>
                            </div>
                          </div>
                        </div>
                        
                        {/* Coins Preview */}
                        <div className="grid grid-cols-3 gap-3 mt-4 bg-[#0A1929]/70 p-3 rounded-md">
                          {filteredCoins.slice(0, 3).map((coin) => (
                            <div key={coin.id} className="flex items-center gap-2 p-2 hover:bg-[#132F4C]/40 rounded-md transition-colors duration-200">
                              <div className="w-7 h-7 rounded-full flex items-center justify-center bg-blue-500/10 text-blue-400 border border-blue-500/20 text-xs shadow-sm">
                                {coin.symbol.charAt(0)}
                              </div>
                              <div className="overflow-hidden flex-1">
                                <p className="text-xs text-[#E7EBF0] truncate font-medium">{coin.symbol}</p>
                                <p className="text-[10px] text-[#E7EBF0]/50">
                                  Score: <span className="text-emerald-400">{coin.totalScore.score}</span>
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                        
                        {/* Bottom Bar */}
                        <div className="flex items-center justify-between mt-4 border-t border-[#1E4976]/20 pt-3">
                          <span className="text-sm text-[#E7EBF0]/70">
                            via <span className="font-bold text-blue-400">CoinScout</span>
                          </span>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 bg-blue-500 hover:bg-blue-600 text-white px-3 rounded-md shadow-md shadow-blue-900/30"
                          >
                            View
                            <ArrowRight className="h-3.5 w-3.5 ml-1.5" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Share Options */}
                  <div className="bg-[#061528] border-t border-[#1E4976]/20 p-6 pt-5">
                    <h3 className="text-sm font-medium text-[#E7EBF0]/90 mb-4">Share Options</h3>
                    
                    <div className="grid grid-cols-5 gap-3 mb-5">
                      <Button 
                        className="flex flex-col items-center justify-center h-16 bg-[#071a2c] hover:bg-[#071a2c]/90 rounded-md group"
                        onClick={() => {
                          navigator.clipboard.writeText(`${window.location.origin}/watchlist/${activeWatchlist?.id}`);
                          toast({
                            title: "Share ready",
                            description: "Telegram share link copied to clipboard",
                          });
                        }}
                      >
                        <svg className="h-7 w-7 mb-1 text-[#0088cc] transition-all duration-200 group-hover:scale-110 group-hover:text-[#00A0E3]" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2a10 10 0 110 20 10 10 0 010-20zm-3.5 10c-.3.6 3.7 4.5 5 7.5.4 1 1.3-3 2-9.5-7 2.7-7 2-7 2z"/>
                        </svg>
                        <span className="text-[11px] font-medium text-[#E7EBF0] group-hover:text-[#E7EBF0]">Telegram</span>
                      </Button>
                      
                      <Button 
                        className="flex flex-col items-center justify-center h-16 bg-[#071a2c] hover:bg-[#071a2c]/90 rounded-md group"
                        onClick={() => {
                          const url = `https://twitter.com/intent/tweet?url=${encodeURIComponent(`${window.location.origin}/watchlist/${activeWatchlist?.id}`)}&text=${encodeURIComponent(`${activeWatchlist?.name} - My CoinScout Watchlist with ${activeWatchlist?.itemCount} coins`)}`;
                          window.open(url, '_blank');
                        }}
                      >
                        <svg className="h-7 w-7 mb-1 text-[#1DA1F2] transition-all duration-200 group-hover:scale-110 group-hover:text-[#4BB4F8]" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z"/>
                        </svg>
                        <span className="text-[11px] font-medium text-[#E7EBF0] group-hover:text-[#E7EBF0]">Twitter</span>
                      </Button>
                      
                      <Button 
                        className="flex flex-col items-center justify-center h-16 bg-[#071a2c] hover:bg-[#071a2c]/90 rounded-md group"
                        onClick={() => {
                          navigator.clipboard.writeText(`${window.location.origin}/watchlist/${activeWatchlist?.id}`);
                          toast({
                            title: "Link copied",
                            description: "Instagram share link copied to clipboard",
                          });
                        }}
                      >
                        <svg className="h-7 w-7 mb-1 text-[#E1306C] transition-all duration-200 group-hover:scale-110 group-hover:text-[#F56040]" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
                        </svg>
                        <span className="text-[11px] font-medium text-[#E7EBF0] group-hover:text-[#E7EBF0]">Instagram</span>
                      </Button>
                      
                      <Button 
                        className="flex flex-col items-center justify-center h-16 bg-[#071a2c] hover:bg-[#071a2c]/90 rounded-md group"
                        onClick={() => {
                          const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(`${window.location.origin}/watchlist/${activeWatchlist?.id}`)}`;
                          window.open(url, '_blank');
                        }}
                      >
                        <svg className="h-7 w-7 mb-1 text-[#1877F2] transition-all duration-200 group-hover:scale-110 group-hover:text-[#4293FF]" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M23.9981 11.9991C23.9981 5.37216 18.626 0 11.9991 0C5.37216 0 0 5.37216 0 11.9991C0 17.9882 4.38789 22.9522 10.1242 23.8524V15.4676H7.07758V11.9991H10.1242V9.35553C10.1242 6.34826 11.9156 4.68714 14.6564 4.68714C15.9692 4.68714 17.3424 4.92149 17.3424 4.92149V7.87439H15.8294C14.3388 7.87439 13.8739 8.79933 13.8739 9.74824V11.9991H17.2018L16.6698 15.4676H13.8739V23.8524C19.6103 22.9522 23.9981 17.9882 23.9981 11.9991Z"/>
                        </svg>
                        <span className="text-[11px] font-medium text-[#E7EBF0] group-hover:text-[#E7EBF0]">Facebook</span>
                      </Button>
                      
                      <Button 
                        className="flex flex-col items-center justify-center h-16 bg-[#071a2c] hover:bg-[#071a2c]/90 rounded-md group"
                        onClick={() => {
                          navigator.clipboard.writeText(`${window.location.origin}/watchlist/${activeWatchlist?.id}`);
                          toast({
                            title: "Link copied",
                            description: "Discord share link copied to clipboard",
                          });
                        }}
                      >
                        <svg className="h-7 w-7 mb-1 text-[#5865F2] transition-all duration-200 group-hover:scale-110 group-hover:text-[#7983F5]" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                        </svg>
                        <span className="text-[11px] font-medium text-[#E7EBF0] group-hover:text-[#E7EBF0]">Discord</span>
                      </Button>
                    </div>
                    
                    {/* Public Link */}
                    <div className="mt-4">
                      <Label htmlFor="public-link" className="text-[#E7EBF0]/80 text-xs font-medium mb-2 flex items-center gap-1.5">
                        <Link className="h-3.5 w-3.5 text-blue-400" />
                        Watchlist Link
                      </Label>
                      <div className="flex gap-2">
                        <Input
                          id="public-link"
                          readOnly
                          value={`${window.location.origin}/watchlist/${activeWatchlist?.id}`}
                          className="flex-1 bg-[#132F4C]/60 border-[#1E4976]/30 text-[#E7EBF0]/80 h-10 text-sm"
                        />
                        <Button
                          variant="secondary"
                          className="bg-blue-500/20 text-blue-400 border-blue-500/30 hover:bg-blue-500/30 h-10 shadow-sm"
                          onClick={() => {
                            navigator.clipboard.writeText(`${window.location.origin}/watchlist/${activeWatchlist?.id}`);
                            toast({
                              title: t('watchlist.linkCopied'),
                              description: t('watchlist.linkCopied'),
                            });
                          }}
                        >
                          <Copy className="h-4 w-4 mr-1.5" />
                          {t('watchlist.copyLink')}
                        </Button>
                      </div>
                      <p className="text-xs text-[#E7EBF0]/50 mt-2">
                        {t('watchlist.shareNote')}
                      </p>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              
              {/* Watchlist Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="h-9 gap-1 bg-[#132F4C]/80 border-[#1E4976]/20 text-[#E7EBF0]/80 hover:bg-[#132F4C] px-3 ml-1"
                  >
                    <ListFilter className="h-4 w-4 mr-1" />
                    <span>Change Watchlist</span>
                    <ChevronDown className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-64 bg-[#0A1929] border-[#1E4976]/20">
                  <DropdownMenuLabel className="text-[#E7EBF0]/80">Your Watchlists</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-[#1E4976]/20" />
                  
                  {/* Watchlist seçenekleri */}
                  {watchlists.map((watchlist) => (
                    <DropdownMenuItem 
                      key={watchlist.id}
                      className={cn(
                        "flex items-center gap-2 text-[#E7EBF0]/80 hover:bg-[#132F4C] hover:text-[#E7EBF0] py-2.5",
                        activeWatchlist?.id === watchlist.id ? "bg-blue-500/10" : ""
                      )}
                      onClick={() => setActiveWatchlist(watchlist)}
                    >
                      <div className="flex-shrink-0 h-7 w-7 rounded-full bg-gradient-to-br from-blue-900/30 to-blue-800/30 flex items-center justify-center text-lg">
                        {watchlist.icon}
                      </div>
                      <div className="flex-1 overflow-hidden">
                        <div className="flex items-center">
                          <span className="truncate">{watchlist.name}</span>
                          {watchlist.isDefault && (
                            <Star className="h-3 w-3 ml-1.5 flex-shrink-0 fill-blue-500 text-blue-500" />
                          )}
                        </div>
                        {watchlist.description && (
                          <p className="text-xs text-[#E7EBF0]/40 truncate">{watchlist.description}</p>
                        )}
                      </div>
                      <Badge className="ml-auto bg-blue-500/10 text-blue-400 border-blue-500/20">
                        {watchlist.itemCount}
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                  
                  <DropdownMenuSeparator className="bg-[#1E4976]/20" />
                  <DropdownMenuItem 
                    className="flex items-center gap-2 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
                    onClick={handleCreateWatchlist}
                  >
                    <Plus className="h-4 w-4" />
                    <span>{t('watchlist.createNewWatchlist')}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              {/* Delete Button - sadece varsayılan olmayan watchlist'ler için */}
              {!activeWatchlist?.isDefault && watchlists.length > 1 && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-9 w-9 bg-[#0A1929] text-red-400 hover:text-red-300 hover:bg-red-500/10 ml-1"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-[#0A1929] border-[#1E4976]/20">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-[#E7EBF0]">Delete Watchlist</AlertDialogTitle>
                      <AlertDialogDescription className="text-[#E7EBF0]/70">
                        Are you sure you want to delete the <span className="font-semibold text-blue-400">{activeWatchlist?.name}</span> watchlist? This action cannot be undone. You will not lose access to the coins in the list.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel className="bg-[#132F4C]/80 border-[#1E4976]/20 text-[#E7EBF0]/80 hover:bg-[#132F4C] hover:text-[#E7EBF0]">
                        Cancel
                      </AlertDialogCancel>
                      <AlertDialogAction 
                        className="bg-red-500/20 text-red-300 border border-red-500/30 hover:bg-red-500/30"
                        onClick={() => handleDeleteWatchlist(activeWatchlist)}
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </CardHeader>
        </Card>
      </div>
      
      {/* Control Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {/* Display Mode Card */}
        <Card className="bg-[#132F4C]/80 backdrop-blur-sm border-[#1E4976]/20 transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
            <CardTitle className="text-xs md:text-sm font-medium text-[#E7EBF0]/80 flex items-center gap-2">
              <Eye className="h-4 w-4 text-blue-500" />
              Display Mode
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <div className="flex gap-2">
              <Button
                variant={displayMode === 'simple' ? 'default' : 'outline'}
                size="sm"
                className={cn(
                  "flex-1 h-9",
                  displayMode === 'simple' 
                    ? "bg-blue-600 hover:bg-blue-700" 
                    : "bg-[#0A1929] hover:bg-[#132F4C] border-[#1E4976]/20 text-[#E7EBF0]/70"
                )}
                onClick={() => setDisplayMode('simple')}
              >
                Simple
              </Button>
              <Button
                variant={displayMode === 'advanced' ? 'default' : 'outline'}
                size="sm"
                className={cn(
                  "flex-1 h-9",
                  displayMode === 'advanced' 
                    ? "bg-blue-600 hover:bg-blue-700" 
                    : "bg-[#0A1929] hover:bg-[#132F4C] border-[#1E4976]/20 text-[#E7EBF0]/70"
                )}
                onClick={() => setDisplayMode('advanced')}
              >
                Advanced
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {/* Timeframe Card */}
        <Card className="bg-[#132F4C]/80 backdrop-blur-sm border-[#1E4976]/20 transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
            <CardTitle className="text-xs md:text-sm font-medium text-[#E7EBF0]/80 flex items-center gap-2">
              <LineChart className="h-4 w-4 text-blue-500" />
              Timeframe
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <Select 
              value={activeTimeframe}
              onValueChange={setActiveTimeframe}
            >
              <SelectTrigger className="w-full bg-[#0A1929] border-[#1E4976]/20 text-[#E7EBF0]/80">
                <SelectValue placeholder="Select timeframe" />
              </SelectTrigger>
              <SelectContent className="bg-[#0A1929] border-[#1E4976]/20">
                {["24h", "7d", "30d", "90d", "1y"].map((option) => (
                  <SelectItem key={option} value={option} className="text-[#E7EBF0]/80 hover:bg-[#132F4C]">
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* Sort By Card */}
        <Card className="bg-[#132F4C]/80 backdrop-blur-sm border-[#1E4976]/20 transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
            <CardTitle className="text-xs md:text-sm font-medium text-[#E7EBF0]/80 flex items-center gap-2">
              <ArrowUpDown className="h-4 w-4 text-blue-500" />
              Sort By
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <Select 
              value={sortConfig.key}
              onValueChange={(value) => handleSort(value)}
            >
              <SelectTrigger className="w-full bg-[#0A1929] border-[#1E4976]/20 text-[#E7EBF0]/80">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-[#0A1929] border-[#1E4976]/20">
                {[
                  { value: 'rank', label: 'Rank' },
                  { value: 'name', label: 'Name' },
                  { value: 'price', label: 'Price' },
                  { value: 'priceChange', label: `Change (${activeTimeframe})` },
                  { value: 'marketCap', label: 'Market Cap' },
                  { value: 'totalScore', label: 'Total Score' }
                ].map((option) => (
                  <SelectItem key={option.value} value={option.value} className="text-[#E7EBF0]/80 hover:bg-[#132F4C]">
                    <div className="flex items-center justify-between w-full">
                      <span>{option.label}</span>
                      {sortConfig.key === option.value && (
                        sortConfig.direction === 'asc' 
                          ? <ArrowUp className="h-3 w-3" /> 
                          : <ArrowDown className="h-3 w-3" />
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* Search Card */}
        <Card className="bg-[#132F4C]/80 backdrop-blur-sm border-[#1E4976]/20 transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
            <CardTitle className="text-xs md:text-sm font-medium text-[#E7EBF0]/80 flex items-center gap-2">
              <Search className="h-4 w-4 text-blue-500" />
              Search
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-[#E7EBF0]/50" />
              <Input
                type="search"
                placeholder="Search coins..."
                className="pl-9 h-9 bg-[#0A1929] border-[#1E4976]/20 text-[#E7EBF0]/80"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>
      </div>
          
      {/* Coins content */}
      <div className="flex flex-col gap-4 mt-4">
        {/* Show performance chart when enabled */}
        {showPerformance && <PerformanceChart />}
        
        {/* Bulk Actions */}
        <div className="flex justify-end">
          {selectedCoins.length > 0 && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="h-9 px-3 gap-1 items-center bg-[#132F4C]/80 border-[#1E4976]/20 text-[#E7EBF0]/80">
                <Check className="h-3.5 w-3.5" />
                <span>{selectedCoins.length} selected</span>
              </Badge>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="h-9 gap-1 bg-[#132F4C]/80 border-[#1E4976]/20 text-[#E7EBF0]/80 hover:bg-[#132F4C]">
                    <span>Bulk Actions</span>
                    <ChevronDown className="h-3.5 w-3.5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-[#0A1929] border-[#1E4976]/20">
                  <DropdownMenuLabel className="text-[#E7EBF0]/80">Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-[#1E4976]/20" />
                  
                  <DropdownMenuItem 
                    className="flex items-center gap-2 text-[#E7EBF0]/80 hover:bg-[#132F4C] hover:text-[#E7EBF0]"
                    onClick={() => {
                      // Determine target watchlist (other than active)
                      const otherWatchlists = watchlists.filter(w => w.id !== activeWatchlist.id);
                      if (otherWatchlists.length > 0) {
                        handleBulkAction('add', otherWatchlists[0]);
                      } else {
                        toast({
                          title: 'No Other Watchlists',
                          description: 'Create another watchlist first',
                          variant: 'destructive'
                        });
                      }
                    }}
                  >
                    <PlusCircle className="h-4 w-4" />
                    <span>Add to Another Watchlist</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem 
                    className="flex items-center gap-2 text-red-400 hover:bg-[#132F4C] hover:text-red-300"
                    onClick={() => handleBulkAction('remove', activeWatchlist)}
                  >
                    <Trash2 className="h-4 w-4" />
                    <span>Remove from Watchlist</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem 
                    className="flex items-center gap-2 text-[#E7EBF0]/80 hover:bg-[#132F4C] hover:text-[#E7EBF0]"
                    onClick={() => {
                      setSelectedCoins([]);
                      setSelectAllChecked(false);
                    }}
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span>Clear Selection</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
        
        {/* Coins List View */}
        {viewMode === 'list' && (
          <div className="rounded-md border border-[#1E4976]/20 bg-[#132F4C]/80 backdrop-blur-sm overflow-hidden">
            <Table className="border-0">
              <TableHeader className="bg-[#132F4C]/80 border-b border-[#1E4976]/40">
                <TableRow className="hover:bg-transparent">
                  <TableHead className="w-12 text-[#E7EBF0]/80">
                    <Checkbox 
                      checked={selectAllChecked && filteredCoins.length > 0}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all"
                    />
                  </TableHead>
                  <TableHead
                    className="w-16 cursor-pointer text-[#E7EBF0]/80"
                    onClick={() => handleSort('rank')}
                  >
                    <div className="flex items-center gap-1">
                      <span>Rank</span>
                      {sortConfig.key === 'rank' && (
                        sortConfig.direction === 'asc' 
                          ? <ArrowUp className="h-3 w-3 text-blue-500" /> 
                          : <ArrowDown className="h-3 w-3 text-blue-500" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer text-[#E7EBF0]/80"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center gap-1">
                      <span>Coin</span>
                      {sortConfig.key === 'name' && (
                        sortConfig.direction === 'asc' 
                          ? <ArrowUp className="h-3 w-3 text-blue-500" /> 
                          : <ArrowDown className="h-3 w-3 text-blue-500" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer text-[#E7EBF0]/80"
                    onClick={() => handleSort('price')}
                  >
                    <div className="flex items-center gap-1">
                      <span>Price</span>
                      {sortConfig.key === 'price' && (
                        sortConfig.direction === 'asc' 
                          ? <ArrowUp className="h-3 w-3 text-blue-500" /> 
                          : <ArrowDown className="h-3 w-3 text-blue-500" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer text-[#E7EBF0]/80"
                    onClick={() => handleSort('priceChange')}
                  >
                    <div className="flex items-center gap-1">
                      <span>Change ({activeTimeframe})</span>
                      {sortConfig.key === 'priceChange' && (
                        sortConfig.direction === 'asc' 
                          ? <ArrowUp className="h-3 w-3 text-blue-500" /> 
                          : <ArrowDown className="h-3 w-3 text-blue-500" />
                      )}
                    </div>
                  </TableHead>
                  {displayMode === 'advanced' && (
                    <>
                      <TableHead
                        className="cursor-pointer hidden md:table-cell text-[#E7EBF0]/80"
                        onClick={() => handleSort('marketCap')}
                      >
                        <div className="flex items-center gap-1">
                          <span>Market Cap</span>
                          {sortConfig.key === 'marketCap' && (
                            sortConfig.direction === 'asc' 
                              ? <ArrowUp className="h-3 w-3 text-blue-500" /> 
                              : <ArrowDown className="h-3 w-3 text-blue-500" />
                          )}
                        </div>
                      </TableHead>
                      
                      {/* Advanced Mode Metric Columns */}
                      <TableHead
                        className="cursor-pointer hidden md:table-cell text-[#E7EBF0]/80"
                        onClick={() => handleSort('tokenomics')}
                      >
                        <div className="flex items-center justify-center gap-1">
                          <span>Tokenomics</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-3 w-3 ml-1 text-[#E7EBF0]/60 cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="bottom">
                                <p className="text-xs w-48">Analysis of token's economic model and distribution</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {sortConfig.key === 'tokenomics' && (
                            sortConfig.direction === 'asc' 
                              ? <ArrowUp className="h-3 w-3 ml-1 text-blue-500" /> 
                              : <ArrowDown className="h-3 w-3 ml-1 text-blue-500" />
                          )}
                        </div>
                      </TableHead>
                      
                      <TableHead
                        className="cursor-pointer hidden md:table-cell text-[#E7EBF0]/80"
                        onClick={() => handleSort('security')}
                      >
                        <div className="flex items-center justify-center gap-1">
                          <span>Security</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-3 w-3 ml-1 text-[#E7EBF0]/60 cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="bottom">
                                <p className="text-xs w-48">Security audit and risk assessment score</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {sortConfig.key === 'security' && (
                            sortConfig.direction === 'asc' 
                              ? <ArrowUp className="h-3 w-3 ml-1 text-blue-500" /> 
                              : <ArrowDown className="h-3 w-3 ml-1 text-blue-500" />
                          )}
                        </div>
                      </TableHead>
                      
                      <TableHead
                        className="cursor-pointer hidden md:table-cell text-[#E7EBF0]/80"
                        onClick={() => handleSort('social')}
                      >
                        <div className="flex items-center justify-center gap-1">
                          <span>Social</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-3 w-3 ml-1 text-[#E7EBF0]/60 cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="bottom">
                                <p className="text-xs w-48">Social media metrics and community engagement</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {sortConfig.key === 'social' && (
                            sortConfig.direction === 'asc' 
                              ? <ArrowUp className="h-3 w-3 ml-1 text-blue-500" /> 
                              : <ArrowDown className="h-3 w-3 ml-1 text-blue-500" />
                          )}
                        </div>
                      </TableHead>
                    </>
                  )}
                  <TableHead
                    className="cursor-pointer w-[200px] text-[#E7EBF0]/80"
                    onClick={() => handleSort('totalScore')}
                  >
                    <div className="flex items-center gap-1">
                      <span>Total AI Score</span>
                      <div className="relative group">
                        <Info className="h-3 w-3 ml-1 text-[#E7EBF0]/60 cursor-help" />
                        <div className="absolute hidden group-hover:block z-50 bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 rounded bg-[#0A1929] border border-[#1E4976]/20 text-[#E7EBF0]/80 text-xs w-48">
                          <p>Comprehensive AI-generated score based on all metrics</p>
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 bg-[#0A1929] border-r border-b border-[#1E4976]/20"></div>
                        </div>
                      </div>
                      {sortConfig.key === 'totalScore' && (
                        sortConfig.direction === 'asc' 
                          ? <ArrowUp className="h-3 w-3 ml-1 text-blue-500" /> 
                          : <ArrowDown className="h-3 w-3 ml-1 text-blue-500" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="w-[90px] text-center text-[#E7EBF0]/80">
                    <span>Alert</span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="bg-[#132F4C]/80">
                {filteredCoins.length === 0 ? (
                  <TableRow className="hover:bg-blue-900/20">
                    <TableCell colSpan={displayMode === 'advanced' ? 11 : 7} className="h-32 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <Search className="h-8 w-8 mb-3 stroke-[1.25px]" />
                        <p>No coins found in this watchlist</p>
                        <Button
                          variant="link"
                          className="h-8 mt-2 text-primary"
                          onClick={() => {
                            setSearch('');
                          }}
                        >
                          Clear filters
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCoins.map((coin) => (
                    <TableRow 
                      key={coin.id} 
                      className="border-b border-[#1E4976]/40 hover:bg-blue-900/20"
                    >
                      <TableCell className="py-2">
                        <Checkbox 
                          checked={selectedCoins.includes(coin.id)}
                          onCheckedChange={() => handleSelectCoin(coin.id)}
                          aria-label={`Select ${coin.name}`}
                        />
                      </TableCell>
                      <TableCell className="py-2">#{coin.rank}</TableCell>
                      <TableCell className="py-2">
                        <div className="flex items-center space-x-3">
                          <div className="font-medium flex items-center">
                            {/* Coin icon - using real crypto icons with fallback */}
                            <div className="w-8 h-8 mr-3 flex-shrink-0">
                              <img
                                src={`https://cdn.jsdelivr.net/gh/atomiclabs/cryptocurrency-icons@master/128/color/${coin.symbol.toLowerCase()}.png`}
                                alt={coin.symbol}
                                className="w-full h-full object-contain"
                                onError={(e) => {
                                  // Fallback to colored circle with symbol initial if icon fails to load
                                  e.currentTarget.style.display = 'none';
                                  const sibling = e.currentTarget.nextElementSibling;
                                  if (sibling && sibling instanceof HTMLElement) {
                                    sibling.style.display = 'flex';
                                  }
                                }}
                              />
                              <div 
                                className="w-8 h-8 rounded-full flex items-center justify-center bg-primary/10 text-primary font-bold text-xs"
                                style={{ display: 'none' }}
                              >
                                {coin.symbol.charAt(0)}
                              </div>
                            </div>
                            
                            <span 
                              className="font-medium cursor-pointer hover:text-primary hover:underline"
                              onClick={() => setLocation(`/coin/${coin.id}`)}
                            >
                              {coin.name}
                            </span>
                            <span className="text-muted-foreground text-xs ml-2">
                              {coin.symbol}
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-2 font-medium">
                        {formatPrice(coin.price)}
                      </TableCell>
                      <TableCell className="py-2">
                        <span className={cn(
                          (coin.priceChanges?.[activeTimeframe as keyof typeof coin.priceChanges] || 0) < 0 
                            ? "text-red-500" 
                            : "text-green-500"
                        )}>
                          {formatPercentage(coin.priceChanges?.[activeTimeframe as keyof typeof coin.priceChanges])}
                        </span>
                      </TableCell>
                      {displayMode === 'advanced' && (
                        <>
                          <TableCell className="py-2 hidden md:table-cell">
                            {coin.marketCap}
                          </TableCell>
                          
                          {/* Advanced Mode Metric Cells */}
                          <TableCell className="py-2 hidden md:table-cell">
                            <div className="flex justify-center">
                              <ScoreIndicator 
                                score={coin.tokenomics.score} 
                                type="tokenomics" 
                                status={coin.tokenomics.status}
                                size="sm"
                              />
                            </div>
                          </TableCell>
                          
                          <TableCell className="py-2 hidden md:table-cell">
                            <div className="flex justify-center">
                              <ScoreIndicator 
                                score={coin.security.score} 
                                type="security" 
                                status={coin.security.status}
                                size="sm"
                              />
                            </div>
                          </TableCell>
                          
                          <TableCell className="py-2 hidden md:table-cell">
                            <div className="flex justify-center">
                              <ScoreIndicator 
                                score={coin.social.score} 
                                type="social" 
                                status={coin.social.status}
                                size="sm"
                              />
                            </div>
                          </TableCell>
                        </>
                      )}
                      
                      {/* Mobile-only advanced metrics button - only shows in advanced mode on mobile */}
                      {displayMode === 'advanced' && (
                        <TableCell className="py-2 md:hidden">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button 
                                size="sm" 
                                variant="outline" 
                                className="w-full h-8 bg-blue-500/10 border-blue-500/20 text-blue-400 hover:bg-blue-500/20"
                              >
                                <BarChart3 className="h-3.5 w-3.5 mr-1.5" />
                                <span>Metrics</span>
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="sm:max-w-[420px] bg-[#0A1929] border-[#1E4976]/20">
                              <DialogHeader>
                                <DialogTitle className="text-lg text-[#E7EBF0] flex items-center gap-2">
                                  <span>{coin.name}</span>
                                  <span className="text-sm text-muted-foreground">({coin.symbol})</span>
                                </DialogTitle>
                                <DialogDescription className="text-[#E7EBF0]/70">
                                  Detailed metrics analysis
                                </DialogDescription>
                              </DialogHeader>
                              
                              <div className="grid grid-cols-2 gap-4 py-4">
                                <div className="bg-[#132F4C]/50 rounded-xl p-3 flex flex-col items-center">
                                  <div className="text-xs text-[#E7EBF0]/60 mb-2">Tokenomics</div>
                                  <ScoreIndicator 
                                    score={coin.tokenomics.score} 
                                    type="tokenomics" 
                                    status={coin.tokenomics.status}
                                    size="md"
                                  />
                                </div>
                                
                                <div className="bg-[#132F4C]/50 rounded-xl p-3 flex flex-col items-center">
                                  <div className="text-xs text-[#E7EBF0]/60 mb-2">Security</div>
                                  <ScoreIndicator 
                                    score={coin.security.score} 
                                    type="security" 
                                    status={coin.security.status}
                                    size="md"
                                  />
                                </div>
                                
                                <div className="bg-[#132F4C]/50 rounded-xl p-3 flex flex-col items-center">
                                  <div className="text-xs text-[#E7EBF0]/60 mb-2">Social</div>
                                  <ScoreIndicator 
                                    score={coin.social.score} 
                                    type="social" 
                                    status={coin.social.status}
                                    size="md"
                                  />
                                </div>
                                
                                <div className="bg-[#132F4C]/50 rounded-xl p-3 flex flex-col items-center">
                                  <div className="text-xs text-[#E7EBF0]/60 mb-2">Market Cap</div>
                                  <div className="text-sm font-medium text-[#E7EBF0]">{coin.marketCap}</div>
                                </div>
                              </div>
                              
                              <DialogFooter>
                                <Button 
                                  variant="outline"
                                  onClick={() => setLocation(`/coin/${coin.id}`)}
                                  className="w-full bg-blue-500/10 border-blue-500/20 text-blue-400 hover:bg-blue-500/20"
                                >
                                  View Detailed Analysis
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                      )}
                      <TableCell className="py-2">
                        <div className="flex items-center justify-center">
                          <div className="relative w-14 h-14">
                            <svg className="w-full h-full transform -rotate-90">
                              <defs>
                                <linearGradient
                                  id={`scoreGradient-${coin.id}`}
                                  x1="0%"
                                  y1="0%"
                                  x2="100%"
                                  y2="100%"
                                >
                                  <stop
                                    offset="0%"
                                    stopColor={
                                      coin.totalScore.score >= 85
                                        ? "#00FF85"
                                        : coin.totalScore.score >= 75
                                          ? "#3B82F6"
                                          : coin.totalScore.score >= 65
                                            ? "#FFB800"
                                            : coin.totalScore.score >= 50
                                              ? "#FF7A00"
                                              : "#FF3B3B"
                                    }
                                  />
                                  <stop
                                    offset="100%"
                                    stopColor={
                                      coin.totalScore.score >= 85
                                        ? "#00FF85"
                                        : coin.totalScore.score >= 75
                                          ? "#3B82F6"
                                          : coin.totalScore.score >= 65
                                            ? "#FFB800"
                                            : coin.totalScore.score >= 50
                                              ? "#FF7A00"
                                              : "#FF3B3B"
                                    }
                                  />
                                </linearGradient>
                              </defs>
                              <circle
                                cx="27"
                                cy="27"
                                r="22"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="3.5"
                                className="text-muted/10"
                              />
                              <circle
                                cx="27"
                                cy="27"
                                r="22"
                                fill="none"
                                stroke={`url(#scoreGradient-${coin.id})`}
                                strokeWidth="3.5"
                                strokeLinecap="round"
                                strokeDasharray={`${2 * Math.PI * 22}`}
                                strokeDashoffset={2 * Math.PI * 22 * (1 - coin.totalScore.score / 100)}
                              />
                            </svg>
                            <div className="absolute inset-0 flex items-center justify-center">
                              <span
                                className={`text-sm font-medium opacity-90 ${
                                  coin.totalScore.score >= 85 
                                    ? "text-emerald-400" 
                                    : coin.totalScore.score >= 75 
                                      ? "text-blue-400" 
                                      : coin.totalScore.score >= 65 
                                        ? "text-yellow-400" 
                                        : "text-red-400"
                                }`}
                              >
                                {coin.totalScore.score}
                              </span>
                            </div>
                          </div>
                          <span className={`text-xs font-medium ml-2 ${
                            coin.totalScore.score >= 85 
                              ? "text-emerald-400" 
                              : coin.totalScore.score >= 75 
                                ? "text-blue-400" 
                                : coin.totalScore.score >= 65 
                                  ? "text-yellow-400" 
                                  : "text-red-400"
                          }`}>
                            {coin.totalScore.status}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="py-2">
                        <div className="flex justify-center">
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            className="flex items-center gap-1 bg-blue-500/10 border border-blue-500/20 text-blue-500 hover:bg-blue-500/20"
                          >
                            <Bell className="h-3 w-3" />
                            <span>Alerts</span>
                            <div className="ml-1 bg-blue-500 text-white h-4 w-4 rounded-full flex items-center justify-center text-[10px]">
                              3
                            </div>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
      
      {/* Create/Edit Watchlist Dialog */}
      <Dialog open={watchlistDialogOpen} onOpenChange={setWatchlistDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {isCreateMode ? t('watchlist.createNewWatchlist') : t('watchlist.editWatchlist')}
            </DialogTitle>
            <DialogDescription>
              {isCreateMode 
                ? t('watchlist.createDescription')
                : t('watchlist.editDescription')}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            {/* Preview */}
            <div className="bg-muted/30 rounded-lg p-4 flex items-center gap-3 border border-border/40">
              <div className="flex-shrink-0 h-12 w-12 rounded-full bg-gradient-to-br from-blue-100 to-blue-50 dark:from-blue-900/30 dark:to-blue-800/30 flex items-center justify-center text-2xl">
                {watchlistIcon}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-base truncate">
                  {watchlistName || t('watchlist.untitled')}
                </h3>
                {watchlistDescription && (
                  <p className="text-sm text-muted-foreground truncate">
                    {watchlistDescription}
                  </p>
                )}
              </div>
              {isCreateMode ? (
                <Badge>New</Badge>
              ) : (
                <Badge variant="outline">Editing</Badge>
              )}
            </div>
            
            {/* Icon Selection */}
            <div className="space-y-2">
              <Label htmlFor="icon">Choose an Icon</Label>
              <div className="grid grid-cols-8 gap-2">
                {['⭐', '🔥', '💰', '🚀', '📈', '🔍', '💎', '🛡️', '🌟', '🔮', '💹', '📊', '🧿', '🔱', '🧠', '🏆'].map((icon) => (
                  <Button
                    key={icon}
                    type="button"
                    variant={watchlistIcon === icon ? 'default' : 'outline'}
                    className={`h-10 w-10 rounded-md ${
                      watchlistIcon === icon 
                        ? 'ring-2 ring-primary scale-105' 
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => setWatchlistIcon(icon)}
                  >
                    {icon}
                  </Button>
                ))}
              </div>
            </div>
            
            {/* Name Field */}
            <div className="space-y-2">
              <Label htmlFor="name">
                {t('watchlist.watchlistName')} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={watchlistName}
                onChange={(e) => setWatchlistName(e.target.value)}
                placeholder={t('watchlist.enterWatchlistName')}
              />
            </div>
            
            {/* Description Field */}
            <div className="space-y-2">
              <Label htmlFor="description">
                {t('watchlist.watchlistDescription')}
              </Label>
              <Textarea
                id="description"
                value={watchlistDescription}
                onChange={(e) => setWatchlistDescription(e.target.value)}
                placeholder={t('watchlist.enterDescription')}
                rows={3}
                className="resize-none"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setWatchlistDialogOpen(false)}
            >
              {t('watchlist.cancel')}
            </Button>
            <Button onClick={handleSaveWatchlist}>
              {isCreateMode ? t('watchlist.create') : t('watchlist.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Bulk Action Confirmation Dialog */}
      <Dialog open={bulkActionDialogOpen} onOpenChange={setBulkActionDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Action</DialogTitle>
            <DialogDescription>
              {selectedCoins.length === 1
                ? `Are you sure you want to add/remove this coin to/from "${bulkActionTarget?.name}"?`
                : `Are you sure you want to add/remove ${selectedCoins.length} coins to/from "${bulkActionTarget?.name}"?`
              }
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex justify-end gap-2 mt-4">
            <Button
              variant="outline"
              onClick={() => {
                setBulkActionDialogOpen(false);
                setBulkActionTarget(null);
              }}
            >
              Cancel
            </Button>
            
            <Button
              variant="default"
              onClick={() => confirmBulkAction('add')}
              className="gap-1.5"
            >
              <PlusCircle className="h-4 w-4" />
              Add to Watchlist
            </Button>
            
            <Button
              variant="destructive"
              onClick={() => confirmBulkAction('remove')}
              className="gap-1.5"
            >
              <Trash2 className="h-4 w-4" />
              Remove from Watchlist
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}