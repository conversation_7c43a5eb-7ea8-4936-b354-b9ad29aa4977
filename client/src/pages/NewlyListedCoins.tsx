import React, {
  useState,
  useEffect,
  useRef,
  use<PERSON><PERSON>back,
  useMemo,
} from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import { Spark<PERSON>, Filter, Search, X } from "lucide-react";
import { CoinLogo } from "@/components/CoinLogo";
import { RatingBadge } from "@/components/RatingBadge";
import CoinService, { CoinFilters } from "../lib/services/CoinService";
import CoinTableRow from "@/components/CoinTableRow";
import { CoinTableHeader } from "@/components/CoinTableHeader";
import { Coin } from "@/types/CoinTypes";
import { useFavorites } from "@/hooks/useFavorites";
import { useLanguage } from "@/contexts/LanguageContext";
import { type FilterControlsState } from "@/components/FilterControls";

// Listing date options
interface ListingDateOption {
  value: string;
  label: string;
}

// LISTING_DATE_OPTIONS will be generated dynamically with translations
const getListingDateOptions = (t: (key: string) => string): ListingDateOption[] => [
  { value: "1", label: t("newlyListed.last24Hours") },
  { value: "7", label: t("newlyListed.last7Days") },
  { value: "14", label: t("newlyListed.last14Days") },
  { value: "30", label: t("newlyListed.last30Days") },
  { value: "90", label: t("newlyListed.last90Days") }
];

// Define constants
const ITEMS_PER_PAGE = 10;

// Varsayılan filtre değerleri
const defaultMarketCapMin = 1000000; // $1M
const defaultMarketCapMax = 100000000000; // $100B

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { 
  ChevronLeft, 
  ChevronRight, 
  FolderTree, 
  ListFilter,
  Brain, 
  CircleDollarSign, 
  LineChart,
  Calendar,
  Link2,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CreateAlertButton } from '@/components/alerts/CreateAlertButton';
import { Pagination } from "@/components/Pagination";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FilterControls } from "@/components/FilterControls";

export default function NewlyListedCoins() {
  const [, setLocation] = useLocation();
  const { toggleFavorite, isFavorite } = useFavorites();
  const { t } = useLanguage();
  const { isLoggedIn } = useAuth();
  
  // Eğer kullanıcı giriş yapmamışsa login sayfasına yönlendir
  useEffect(() => {
    if (!isLoggedIn) {
      // Kullanıcı giriş yaptıktan sonra bu sayfaya geri dönebilmesi için mevcut URL'i de gönder
      const currentPath = window.location.pathname;
      setLocation(`/login?returnTo=${encodeURIComponent(currentPath)}`);
    }
  }, [isLoggedIn, setLocation]);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [tableCoins, setTableCoins] = useState<Coin[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [selectedListingDateOption, setSelectedListingDateOption] = useState<string | null>(null); // Başlangıçta filtreleme yok
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  
  // Add currentFilters state for tracking applied filters
  const [currentFilters, setCurrentFilters] = useState<NewlyListedFilters>({
    marketCapRange: [1000000, 100000000000], // 1M to 100B
    projectScoreRange: [0, 100],
    categories: [],
    chains: [],
    // Başlangıçta tarih filtresi yok - tüm coinleri göster
  });
  
  // Add sorting state similar to CoinList
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc" | false;
  }>({
    key: "",
    direction: false,
  });
  
  // Handle sort requests
  const requestSort = useCallback((key: string) => {
    setSortConfig((prevState) => {
      // If clicking the same column that's already sorted
      if (prevState.key === key) {
        if (prevState.direction === "asc") {
          return { key, direction: "desc" };
        } else if (prevState.direction === "desc") {
          return { key, direction: false }; // Third click - remove sorting
        } else {
          return { key, direction: "asc" }; // If no direction, set to ascending
        }
      }
      // If clicking a new column, default to ascending
      return { key, direction: "asc" };
    });
  }, []);
  
  // Update search debounce
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(search);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [search]);

  // Apply search on debounce change
  useEffect(() => {
    if (debouncedSearch !== search) {
      setCurrentPage(1); // Reset to first page on new search
    }
  }, [debouncedSearch]);
  
  // Fetch data function with newlyListed=true parameter and filters
  type NewlyListedFilters = FilterControlsState & {listingDate?: string | null};
  
  const fetchData = async (filters: Partial<NewlyListedFilters> = {}) => {
    try {
      setIsLoading(true);
      
      // Temel API filtre nesnesi - SADECE newlyListed parametresi ile başla
      // Diğer parametreleri sadece gerekli olduğunda ekleyelim
      const apiFilters: Partial<CoinFilters> = {
        newlyListed: true, // Always set for this page
      };
      
      // Eğer reset işlemi değilse VE özel filtreler varsa, onları ekle
      if (!filters.isReset) {
        // Market cap filtresi set edilmişse ekle
        if (filters.marketCapRange) {
          apiFilters.marketcap_min = filters.marketCapRange[0];
          apiFilters.marketcap_max = filters.marketCapRange[1];
        }
        
        // Project score filtresi set edilmişse ekle
        if (filters.projectScoreRange) {
          apiFilters.score_min = filters.projectScoreRange[0];
          apiFilters.score_max = filters.projectScoreRange[1];
        }
        
        // Kategori filtresi set edilmişse ekle
        if (filters.categories && filters.categories.length > 0) {
          apiFilters.selectedcategories = filters.categories;
        }
        
        // Zincir filtresi set edilmişse ekle
        if (filters.chains && filters.chains.length > 0) {
          apiFilters.selectedchains = filters.chains;
        }
      }
      
      // Sadece listing date filtresi aktifse, diğer tüm filtreleri null olarak zorla
      if (filters.listingDate && 
          (!filters.marketCapRange && !filters.projectScoreRange && 
           (!filters.categories || filters.categories.length === 0) && 
           (!filters.chains || filters.chains.length === 0))) {
        // Diğer tüm filtreleri temizle, sadece listingDate'i koru
        apiFilters.marketcap_min = null;
        apiFilters.marketcap_max = null;
        apiFilters.score_min = null;
        apiFilters.score_max = null;
        apiFilters.selectedcategories = null;
        apiFilters.selectedchains = null;
      }
      
      // Add listing date filter if available
      if (filters.listingDate) {
        // Convert days to a timestamp
        const days = parseInt(filters.listingDate);
        if (!isNaN(days)) {
          console.log(`Filtering for coins listed in the last ${days} days`);
          
          // Gün sayısını API'ye parametre olarak gönder
          apiFilters.listing_date = filters.listingDate;
          
          // Tarih aralığı hesapla (Unix timestamp)
          const currentDate = new Date();
          const startDate = new Date();
          startDate.setDate(currentDate.getDate() - days);
          
          // Unix timestamp'e çevir (saniye cinsinden)
          const startTimestamp = Math.floor(startDate.getTime() / 1000).toString();
          const endTimestamp = Math.floor(currentDate.getTime() / 1000).toString();
          
          // API'ye tarih aralığı parametrelerini ekle
          apiFilters.listing_date_start = startTimestamp;
          apiFilters.listing_date_end = endTimestamp;
          
          console.log(`Date range: ${new Date(startDate).toLocaleDateString()} - ${new Date(currentDate).toLocaleDateString()}`);
        }
      }
      
      console.log("Fetching newly listed coins with filters:", apiFilters);
      const response = await CoinService.getCoinsList(apiFilters);
      setTableCoins(response);
      
      // Seçilen tarih filtresini kaydet/güncelle
      if (filters.listingDate) {
        setSelectedListingDateOption(filters.listingDate);
      }
      
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching newly listed coins:", error);
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);
  
  // Check for horizontal scroll
  useEffect(() => {
    const checkForHorizontalScroll = () => {
      if (tableContainerRef.current) {
        const { scrollWidth, clientWidth } = tableContainerRef.current;
        setHasHorizontalScroll(scrollWidth > clientWidth);
      }
    };

    // Initial check
    checkForHorizontalScroll();

    // Add resize listener
    window.addEventListener('resize', checkForHorizontalScroll);

    return () => {
      window.removeEventListener('resize', checkForHorizontalScroll);
    };
  }, [tableCoins]);

  // Filter and sort coins
  const filteredAndSortedCoins = useMemo(() => {
    let result = [...tableCoins];

    // Filter by search
    if (search.trim()) {
      const searchLower = search.toLowerCase().trim();
      result = result.filter(
        (coin) =>
          coin.name.toLowerCase().includes(searchLower) ||
          coin.symbol.toLowerCase().includes(searchLower)
      );
    }
    
    // First filter out any future-dated coins (regardless of listing date filter)
    const currentTimestamp = Math.floor(Date.now() / 1000);
    result = result.filter(coin => {
      if (!coin.coin_age) return true; // Keep coins without age
      
      const coinTimestamp = parseInt(coin.coin_age);
      if (isNaN(coinTimestamp)) return true; // Keep coins with invalid timestamps
      
      // Only keep coins with past or current dates (not future dates)
      return coinTimestamp <= currentTimestamp;
    });
    
    // Then sort remaining coins by date (newest first)
    result.sort((a, b) => {
      // Coin_age (tarih) değerlerine göre sırala
      if (!a.coin_age || !b.coin_age) return 0;
      
      const dateA = parseInt(a.coin_age);
      const dateB = parseInt(b.coin_age);
      
      // Büyük tarih değeri daha yeni olduğundan, büyük olan üstte olsun (azalan sıralama)
      return dateB - dateA;
    });
    
    // Client-side filtering for listing date - sadece filtre seçili ise uygula
    if (currentFilters.listingDate) {
      const days = parseInt(currentFilters.listingDate);
      if (!isNaN(days)) {
        // Bugünün tarihini Unix timestamp olarak hesapla (saniye cinsinden)
        const nowTimestamp = Math.floor(Date.now() / 1000);
        
        // API'den gelen coin_age değerlerini inceleyelim (debug amaçlı)
        console.log(`Client-side filtering for coins in the last ${days} days period`);
        console.log(`Current timestamp: ${nowTimestamp}`);
        
        // İlk birkaç coini kontrol edelim (debug amaçlı)
        if (result.length > 0) {
          console.log("First coin age sample:", result[0].coin_age);
          if (result.length > 1) console.log("Second coin age sample:", result[1].coin_age);
          if (result.length > 2) console.log("Third coin age sample:", result[2].coin_age);
        }
        
        // Tarihe göre coin filtreleme
        // coin_age değerleriyle ilgili tarih hesaplaması
        const filteredCoins = result.filter(coin => {
          if (!coin.coin_age) return false;
          
          const coinAgeTimestamp = parseInt(coin.coin_age);
          if (isNaN(coinAgeTimestamp)) return false;
          
          // Calculate the difference between now and the coin age timestamp
          // Get milliseconds difference - positive for past dates, negative for future dates
          const diffMs = nowTimestamp * 1000 - coinAgeTimestamp * 1000;
          
          // Skip future-dated coins completely
          if (diffMs < 0) {
            console.log(`Coin ${coin.name} has a future date (${new Date(coinAgeTimestamp * 1000).toLocaleDateString()}) and is excluded from listing`);
            return false;
          }
          
          // Convert the difference to days for past-dated coins
          const differenceInDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
          
          console.log(`Coin ${coin.name} - age: ${coin.coin_age}, difference: ${differenceInDays} days`);
          
          // Apply filters based on the selected time period
          if (days === 1) { // Last 24 hours
            return differenceInDays < 1;
          } else if (days === 7) { // Last 7 days
            return differenceInDays <= 7;
          } else if (days === 14) { // Last 14 days
            return differenceInDays <= 14;
          } else if (days === 30) { // Last 30 days
            return differenceInDays <= 30;
          } else if (days === 90) { // Last 90 days
            return differenceInDays <= 90;
          } else {
            // For other values - filter by the number of days
            return differenceInDays <= days;
          }
        });
        
        console.log(`Selected ${filteredCoins.length} coins to display for ${days} days filter`);
        
        // Sonuçları güncelle
        result = filteredCoins;
      }
    } else {
      // Filtre yoksa tüm sonuçları göster - reset olmuş durumda
      console.log(`No date filter applied, showing all ${result.length} coins`);
      
      // Burada önemli: reset sonrası bütün coinleri göster, filtreleme yapma!
      // İlgili bir bug var - reset sonrasında filtreleme yapılmamalı
    }

    // Sort based on sortConfig (only if user has selected a sorting option)
    if (sortConfig.key && sortConfig.direction !== false) {
      result = [...result].sort((a, b) => {
        let aValue, bValue;

        switch (sortConfig.key) {
          case "rank":
            aValue = a.rank;
            bValue = b.rank;
            break;
          case "name":
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case "tokenomics":
            aValue = a.tokenomics.score;
            bValue = b.tokenomics.score;
            break;
          case "security":
            aValue = a.security.score;
            bValue = b.security.score;
            break;
          case "social":
            aValue = a.social.score;
            bValue = b.social.score;
            break;
          case "market":
            aValue = a.market.score;
            bValue = b.market.score;
            break;
          case "insights":
            aValue = a.insights.score;
            bValue = b.insights.score;
            break;
          case "totalScore":
            aValue = a.totalScore.score;
            bValue = b.totalScore.score;
            break;
          case "sevenDayChange":
            aValue = a.sevenDayChange;
            bValue = b.sevenDayChange;
            break;
          default:
            aValue = a.rank;
            bValue = b.rank;
        }

        if (sortConfig.direction === "asc") {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }

    return result;
  }, [search, sortConfig, tableCoins, currentFilters.listingDate]);

  // Calculate total pages
  const totalPages = useMemo(
    () => Math.ceil(filteredAndSortedCoins.length / pageSize),
    [filteredAndSortedCoins.length, pageSize]
  );

  // Get paginated coins
  const paginatedCoins = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredAndSortedCoins.slice(startIndex, startIndex + pageSize);
  }, [filteredAndSortedCoins, currentPage, pageSize]);

  // Handler for filter changes from the filter controls
  const handleFilterChange = (filters: NewlyListedFilters & { isReset?: boolean }) => {
    if (filters.isReset) {
      // Reset filters'da sadece newlyListed:true gönder
      // Diğer değerler null olmalı (CoinList sayfasıyla tutarlı olması için)
      // Çok önemli: Reset sırasında listingDate değerini tamamen kaldırmalıyız
      
      // İlgili state'leri sıfırla - önceki filtre değerlerini temizle
      setSelectedListingDateOption(null);
      
      // Filtreleri temizle - Tip hatalarını engellemek için tam bir geçerli obje oluştur
      setCurrentFilters({
        marketCapRange: [defaultMarketCapMin, defaultMarketCapMax],
        projectScoreRange: [0, 100],
        categories: [],
        chains: [],
        listingDate: null
      });
      
      // Sadece newlyListed:true parametresi ile veri çek
      fetchData({
        isReset: true
      });
    } else {
      // Yeni filtreleri uygula
      setCurrentFilters(filters);
      fetchData(filters);
    }
    
    // Reset to first page when applying new filters
    setCurrentPage(1);
  };
  
  // Burada varsayılan değerleri global olarak tanımladık, component içinde tekrar tanımlamamıza gerek yok
  
  // Handler for direct listing date selection from quick filter
  const handleListingDateChange = (days: string) => {
    setSelectedListingDateOption(days);
    
    // Sadece listingDate değerini güncelle, diğerlerini undefined olarak gönder
    // API'ye null olarak gönderilmesi için
    const updatedFilters: Partial<NewlyListedFilters> = {
      listingDate: days
    };
    
    // State'i güncelle - tip hatalarını önlemek için uygun tipte objeler kullan
    setCurrentFilters({
      marketCapRange: [defaultMarketCapMin, defaultMarketCapMax],
      projectScoreRange: [0, 100],
      categories: [],
      chains: [],
      listingDate: days
    });
    
    // Sadece listingDate değeriyle veri çek
    fetchData(updatedFilters);
  };
  
  // Get the display name for the current listing date filter
  const getListingDateDisplay = () => {
    const listingDateOptions = getListingDateOptions(t);
    const option = listingDateOptions.find((opt: ListingDateOption) => opt.value === selectedListingDateOption);
    return option ? option.label : t("newlyListed.selectTimeframe");
  };

  return (
    <div className="container mx-auto p-y">
      <div className="flex flex-col lg:flex-row justify-between items-start gap-4 mb-8">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-primary">{t("newlyListed.title")}</h1>
          <p className="text-base text-muted-foreground">
            {t("newlyListed.description")}
          </p>
        </div>
        
        {/* Normal olarak burada filtre butonları yer alıyor, ancak görsele göre bunlar kaldırılacak */}
      </div>

      {/* Filter bileşenleri kaldırıldı */}

      <Card className="mt-6 max-w-[1600px] mx-auto w-full overflow-hidden">
        <CardHeader className="border-b border-border/40 px-6">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div>
              <CardTitle>{t("newlyListed.cardTitle")}</CardTitle>
              <CardDescription>
                {t("newlyListed.clickForAnalysis")}
              </CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Input
                placeholder={t("newlyListed.searchPlaceholder")}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-[260px]"
              />
              <div className="flex items-center gap-4">
                <Dialog 
                  open={filterDialogOpen} 
                  onOpenChange={setFilterDialogOpen}
                >
                  <DialogTrigger asChild>
                    <div className="filter-container">
                      <Button
                        variant="outline"
                        size="default"
                        className="w-full sm:w-auto filter-button coinlist-filter flex items-center gap-2 h-10 px-4 
                          bg-[#132F4C]/50 border-[#1E4976]/30 text-[#E7EBF0]/80 
                          hover:bg-[#132F4C]/70 hover:text-[#66B2FF] hover:border-[#1E4976]/50 
                          focus:bg-[#132F4C]/70 focus:border-[#1E4976]/50
                          transition-all duration-200 rounded-md"
                      >
                        <Filter className="h-4 w-4" />
                        {t("newlyListed.filters")}
                      </Button>
                    </div>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px] bg-[#0A1929]/95 border border-[#1E4976]/60 backdrop-blur-md">
                    <DialogHeader>
                      <DialogTitle className="text-lg font-semibold text-[#66B2FF]">
                        {t("filters.title")}
                      </DialogTitle>
                      <DialogDescription className="text-[#E7EBF0]/70">
                        {t("filters.description")}
                      </DialogDescription>
                    </DialogHeader>
                    <FilterControls
                      initialFilters={currentFilters}
                      onFilterChange={handleFilterChange}
                      onClose={() => setFilterDialogOpen(false)}
                      showListingDateFilter={true} // Bu sayfada listeleme tarihi filtresini göster
                    />
                  </DialogContent>
                </Dialog>
                <CreateAlertButton 
                  variant="outline" 
                  size="sm" 
                  className="h-10 w-full sm:w-auto flex items-center gap-2 
                    bg-[#132F4C]/50 border-[#1E4976]/30 text-[#E7EBF0]/80 
                    hover:bg-[#132F4C]/70 hover:text-[#66B2FF] hover:border-[#1E4976]/50 
                    focus:bg-[#132F4C]/70 focus:border-[#1E4976]/50
                    transition-all duration-200 rounded-md"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="scrollable-container relative group/scroll w-full mt-2 overflow-hidden">
            {hasHorizontalScroll && (
              <>
                <div className="absolute left-2 top-[45%] z-10 opacity-0 group-hover/scroll:opacity-100 transition-opacity duration-300 bg-[#132F4C]/80 rounded-full p-1 text-white/80 shadow-md">
                  <ChevronLeft className="h-5 w-5" />
                </div>
                <div className="absolute right-2 top-[45%] z-10 opacity-0 group-hover/scroll:opacity-100 transition-opacity duration-300 bg-[#132F4C]/80 rounded-full p-1 text-white/80 shadow-md">
                  <ChevronRight className="h-5 w-5" />
                </div>
              </>
            )}
            <div 
              ref={tableContainerRef}
              className="overflow-hidden w-full rounded-md scrollbar-custom pb-8 mx-auto"
              style={{ 
                display: "grid", 
                gridTemplateColumns: "minmax(0, 1fr)"
              }}>
              <Table
                id="newly-listed-coins-table"
                className="w-[100%] min-w-[940px] text-center mx-auto relative"
                responsive={true}
                containerClassName="overflow-hidden">
                <CoinTableHeader 
                  sortConfig={sortConfig}
                  requestSort={requestSort}
                  id="newly-listed-coins-header"
                  className="coin-list-header bg-[#132F4C]/90 w-full hover:bg-[#132F4C]/90 backdrop-blur-sm mt-2 sticky top-0 z-10 border-b border-[#1E4976]/50 shadow-md"
                />
                <TableBody className="relative">
                  {(() => {
                    if (isLoading) {
                      return (
                        <tr>
                          <td colSpan={8} className="text-center py-6">
                            <div className="space-y-4 text-center">
                              <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
                              <div className="text-primary">{t("newlyListed.loading")}</div>
                            </div>
                          </td>
                        </tr>
                      );
                    } else if (paginatedCoins.length === 0) {
                      return (
                        <tr>
                          <td colSpan={8} className="text-center py-6">
                            <div className="space-y-2 text-center">
                              <Sparkles className="w-8 h-8 text-muted-foreground mx-auto" />
                              <p>{t("newlyListed.noCoinsFound")}</p>
                            </div>
                          </td>
                        </tr>
                      );
                    } else {
                      return paginatedCoins.map((coin, idx) => (
                        <CoinTableRow 
                          key={coin.id} 
                          coin={coin} 
                          index={idx}
                          currentPage={currentPage}
                          pageSize={ITEMS_PER_PAGE}
                          onRowClick={(coinId) => setLocation(`/coin/${coinId}`)}
                          showAgeBadge={true}
                        />
                      ));
                    }
                  })()}
                </TableBody>
              </Table>
            </div>
          </div>
          
          {paginatedCoins.length > 0 && (
            <div className="mt-6 w-full">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                pageSize={pageSize}
                totalItems={filteredAndSortedCoins.length}
                onPageChange={setCurrentPage}
                onPageSizeChange={setPageSize}
                className="justify-between w-full"
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}