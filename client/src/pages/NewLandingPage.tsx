import * as React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { useEffect, useState, FC } from "react";
import {
  ArrowRight,
  Shield,
  ShieldCheck,
  Zap,
  Brain,
  TrendingUp,
  LineChart,
  Star,
  BookOpen,
  Bell,
  Target,
  BarChart2,
  BarChart3,
  BarChart4,
  Wallet,
  Search,
  Activity,
  ChartBar,
  Sparkles,
  Check,
  HelpCircle,
  Rocket,
  Gift,
  Info,
  ExternalLink,
  Gem,
  CreditCard,
  CalendarClock,
  XCircle,
  LayoutGrid as Rows3,
  CircleDot,
  Scale,
  GitCompare,
  Smile,
  Dog,
  Package,
  Heart,
  Users,
  Bot,
  User,
  Lightbulb,
  ChevronDown,
} from "lucide-react";
import { useLocation } from "wouter";
import { Badge } from "@/components/ui/badge";
import HeroTableDisplay from "@/components/HeroTableDisplay";
import { useLanguage } from "@/contexts/LanguageContext";

// Standardized RadialGradient component for consistent design
interface RadialGradientProps {
  center?: string;
  opacity?: number;
  transparentEndpoint?: number;
}

const RadialGradient = ({
  center = "30% 30%",
  opacity = 0.05,
  transparentEndpoint = 80,
}: RadialGradientProps) => {
  return (
    <div
      className="absolute inset-0"
      style={{
        backgroundImage: `radial-gradient(circle at ${center}, rgba(var(--primary-rgb),${opacity}) 0%, transparent ${transparentEndpoint}%)`,
        transition: "all 0.6s ease",
      }}
    />
  );
};

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 },
};

const staggerContainer = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const featureVariants = {
  initial: { opacity: 0, y: 20 },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

// FAQ questions are now handled via translation system

// The footer links have been moved to the global Footer component
// to ensure consistent implementation and avoid duplication

// FAQ Item component with simplified implementation
interface FAQItemProps {
  question: string;
  answer: string;
}

const SimpleFAQItem: FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border border-border/40 rounded-lg bg-muted/5 w-full shadow-sm relative">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 sm:px-5 py-4 text-left gap-2 sm:gap-4"
      >
        <div className="flex items-center gap-2 sm:gap-4">
          <div className="w-10 h-10 rounded-full bg-primary/10 p-2 flex items-center justify-center flex-shrink-0">
            <HelpCircle className="h-5 w-5 text-primary" />
          </div>
          <span className="pt-1.5 text-muted-foreground">{question}</span>
        </div>
        <div>
          <ChevronDown
            className={`h-4 w-4 transition-transform ${isOpen ? "transform rotate-180" : ""}`}
          />
        </div>
      </button>

      {isOpen && (
        <div className="pt-2 pb-4 px-5 ml-6 text-muted-foreground text-sm border-l-2 border-primary">
          <p>{answer}</p>
        </div>
      )}
    </div>
  );
};

interface FloatingParticle {
  id: number;
  x: number;
  y: number;
  size: number;
  delay: number;
  opacity: number;
  color: string;
}

// Component to render floating particles in the background
const FloatingParticles = ({ count = 15 }: { count?: number }) => {
  const [particles, setParticles] = useState<FloatingParticle[]>([]);

  useEffect(() => {
    // Generate random particles
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100, // random position as percentage of parent width
      y: Math.random() * 100, // random position as percentage of parent height
      size: Math.random() * 4 + 2, // random size between 2-6px
      delay: Math.random() * 5, // random delay for animation start
      opacity: Math.random() * 0.5 + 0.1, // random opacity between 0.1-0.6
      color:
        Math.random() > 0.5
          ? "rgba(var(--primary-rgb), $opacity)"
          : "rgba(var(--blue-rgb), $opacity)",
    }));

    setParticles(newParticles);
  }, [count]);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="particle"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            opacity: particle.opacity,
            backgroundColor: particle.color.replace(
              "$opacity",
              particle.opacity.toString(),
            ),
            animationDelay: `${particle.delay}s`,
            animationDuration: `${8 + particle.delay}s`,
          }}
        />
      ))}
    </div>
  );
};

export default function NewLandingPage() {
  const [, setLocation] = useLocation();
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-background w-full">
      {/* Enhanced Hero Section */}
      <section className="pb-20 md:pb-28 pt-4 md:pt-12 w-full relative overflow-hidden">
        {/* Premium animated background with enhanced texture and gradients */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated primary gradient background */}
          <motion.div
            className="absolute inset-0 opacity-30"
            style={{
              backgroundImage: `
                radial-gradient(circle at 20% 30%, rgba(11, 134, 251, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 60%, rgba(138, 58, 251, 0.04) 0%, transparent 60%)
              `,
              pointerEvents: "none",
            }}
            animate={{
              opacity: [0.25, 0.35, 0.25],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
            }}
          />

          {/* Subtle animated particles for modern tech feel */}
          <FloatingParticles count={20} />

          {/* Gradient mesh to create depth */}
          <div
            className="absolute inset-0"
            style={{
              background: `radial-gradient(ellipse at 50% 0%, rgba(var(--primary-rgb), 0.03) 0%, transparent 70%)`,
              pointerEvents: "none",
            }}
          />
        </div>

        {/* Content container with refined spacing */}
        <div className="main-container container mx-auto max-w-[1600px] px-4 sm:px-8 relative z-10">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-12 items-center w-full mx-auto">
            {/* Left column: Hero text and CTA */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              className="relative"
            >
              {/* Premium badge with enhanced styling */}
              <motion.div variants={fadeInUp} className="text-left lg:pr-8">
                <motion.div
                  initial={{ y: -5, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <Badge
                    variant="outline"
                    className="mb-5 text-sm py-1.5 px-4 border-primary/30 bg-gradient-to-r from-primary/10 to-blue-500/5 backdrop-blur-sm shadow-sm inline-flex items-center"
                  >
                    <div className="mr-1.5 h-3 w-3 bg-primary rounded-full animate-pulse opacity-80"></div>
                    <Star className="mr-1.5 h-3.5 w-3.5 text-primary" />
                    <span className="font-medium">
                      {t("hero.badge", "homepage")}
                    </span>
                  </Badge>
                </motion.div>

                {/* Premium headline with enhanced gradient styling */}
                <h1 className="text-4xl md:text-5xl xl:text-6xl font-bold mb-6 tracking-tight leading-tight">
                  <motion.span
                    className="bg-clip-text text-transparent bg-gradient-to-r from-primary via-blue-500 to-violet-500 animate-text-gradient bg-size-200"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: 0.1 }}
                  >
                    {t("hero.title", "homepage")}
                  </motion.span>
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: 0.2 }}
                    className="mt-2"
                  >
                    <span className="text-primary font-extrabold">
                      {t("hero.titleHighlight", "homepage")}
                    </span>
                  </motion.div>
                </h1>

                {/* Enhanced description with better spacing and typography */}
                <motion.p
                  className="text-lg md:text-xl text-muted-foreground mb-8 leading-relaxed max-w-xl"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.3 }}
                >
                  {t("hero.description", "homepage")}
                </motion.p>
              </motion.div>

              {/* Enhanced CTA buttons with more premium effects */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-col sm:flex-row gap-4 mb-12"
              >
                {/* Primary CTA with enhanced animation and glass effect */}
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-700 text-primary-foreground px-8 py-6 group transition-all duration-600 transform hover:scale-105 hover:shadow-lg hover:shadow-primary/20 relative overflow-hidden animate-button-glow border-0"
                  onClick={() => setLocation("/coinlist")}
                >
                  <span className="relative z-10 text-base font-medium">
                    {t("hero.ctaButton", "homepage")}
                  </span>
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-600 relative z-10" />
                  <motion.span
                    className="absolute inset-0 bg-gradient-to-r from-primary/90 via-blue-600/90 to-primary/90 opacity-0 group-hover:opacity-100 transition-all duration-600"
                    animate={{
                      backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      repeatType: "loop",
                      ease: "linear",
                    }}
                  ></motion.span>
                </Button>
                {/* How It Works button removed as requested */}
              </motion.div>

              {/* Metrics display (reverted to original style with gradient text) */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="flex overflow-x-auto pb-[20px] items-center mt-8 space-x-8"
              >
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <div className="text-3xl font-bold text-white">1000+</div>
                    <div className="ml-2 text-xs px-2 py-1 bg-green-500/10 text-green-500 rounded flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" /> +500
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {t("hero.stats.cryptocurrencies", "homepage")}
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <div className="text-3xl font-bold text-white">40+</div>
                    <div className="ml-2 text-xs px-2 py-1 bg-blue-500/10 text-blue-500 rounded flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" /> +12
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {t("hero.stats.analysisMetrics", "homepage")}
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <div className="text-3xl font-bold text-white">500%</div>
                    <div className="ml-2 text-xs px-2 py-1 bg-purple-500/10 text-purple-500 rounded">
                      ↗
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {t("hero.stats.moreAccuracy", "homepage")}
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right column: Enhanced Hero Graphic with clean premium effects */}
            <div className="relative lg:pl-8 mt-8 lg:mt-0 w-full">
              {/* Background glow effect */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute w-[60%] h-[60%] top-0 right-0 bg-primary/5 blur-[120px] rounded-full opacity-70"></div>
                <div className="absolute w-[30%] h-[30%] bottom-10 left-10 bg-blue-500/5 blur-[80px] rounded-full opacity-70"></div>
              </div>

              {/* Table display with clean, no-nested approach */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative z-10 w-full"
              >
                <div className="relative">
                  {/* Simple glow effect */}
                  <div className="absolute -inset-6 rounded-2xl z-10">
                    <div
                      className="absolute inset-0 opacity-60"
                      style={{
                        background:
                          "radial-gradient(circle at center, rgba(var(--primary-rgb), 0.15), transparent 70%)",
                        filter: "blur(15px)",
                      }}
                    />
                  </div>

                  {/* Clean table with internal border */}
                  <div className="relative z-20">
                    <HeroTableDisplay className="xl:max-w-2xl rounded-xl" />
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/5 border-y border-border/30 w-full relative">
        {/* Enhanced background with animated gradients and particles */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Dynamic gradient background */}
          <motion.div
            className="absolute inset-0 opacity-30"
            style={{
              backgroundImage: `
                radial-gradient(circle at 70% 20%, rgba(11, 134, 251, 0.04) 0%, transparent 60%),
                radial-gradient(circle at 30% 70%, rgba(138, 58, 251, 0.03) 0%, transparent 50%)
              `,
              pointerEvents: "none",
            }}
            animate={{
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
            }}
          />

          {/* Subtle animated particles */}
          <div className="absolute top-1/4 left-1/3 w-72 h-72 rounded-full bg-gradient-to-r from-[#0B86FB]/5 to-[#8A3AFB]/5 blur-3xl opacity-30 animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 right-1/3 w-96 h-96 rounded-full bg-gradient-to-r from-[#8A3AFB]/5 to-[#0B86FB]/5 blur-3xl opacity-30 animate-pulse-slow-reverse"></div>

          {/* Grid pattern overlay */}
          <div className="absolute inset-0 bg-grid-pattern opacity-[0.03] pointer-events-none"></div>
        </div>

        <div className="main-container container mx-auto max-w-[1600px] px-4 sm:px-8 relative z-10">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            {/* Enhanced heading with animated underline and glow effect */}
            <motion.div
              className="relative inline-block mb-3"
              initial={{ scale: 0.98, opacity: 0.9 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <motion.h2 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB] animate-gradient-slow pb-3">
                {t("features.title", "homepage")}
              </motion.h2>

              <motion.span
                className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB] opacity-70"
                initial={{ width: "0%" }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 1, delay: 0.2, ease: "easeOut" }}
              />

              {/* Subtle highlight glow */}
              <motion.div
                className="absolute -inset-1 rounded-lg bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-purple-500/0 blur-xl"
                animate={{
                  opacity: [0, 0.2, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
            </motion.div>

            <motion.p
              className="text-base md:text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed"
              initial={{ y: 10, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              {t("features.subtitle", "homepage")}
            </motion.p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mx-auto w-full"
          >
            {[
              {
                title: t("features.aiRating.title", "homepage"),
                description: t("features.aiRating.description", "homepage"),
                icon: (
                  <Brain className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "blue",
                bulletPoints: [
                  t("features.comprehensiveScoring", "homepage"),
                  t("features.aiRating.bullets.0", "homepage"), 
                  t("features.aiRating.bullets.1", "homepage")
                ],
              },
              {
                title: t("features.idoRating.title", "homepage"),
                description: t("features.idoRating.description", "homepage"),
                icon: (
                  <Rocket className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "amber",
                bulletPoints: [
                  t("features.idoRating.bullets.0", "homepage"),
                  t("features.idoRating.bullets.1", "homepage"),
                  t("features.idoRating.bullets.2", "homepage")
                ],
              },
              {
                title: t("features.compareCoins.title", "homepage"),
                description: t("features.compareCoins.description", "homepage"),
                icon: (
                  <GitCompare className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "purple",
                bulletPoints: [
                  t("features.compareCoins.bullets.0", "homepage"),
                  t("features.compareCoins.bullets.1", "homepage"),
                  t("features.compareCoins.bullets.2", "homepage")
                ],
              },
              {
                title: t("features.portfolioGenerator.title", "homepage"),
                description: t("features.portfolioGenerator.description", "homepage"),
                icon: (
                  <Sparkles className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "emerald",
                bulletPoints: [
                  t("features.portfolioGenerator.bullets.0", "homepage"),
                  t("features.portfolioGenerator.bullets.1", "homepage"),
                  t("features.portfolioGenerator.bullets.2", "homepage")
                ],
              },
              {
                title: t("features.portfolioAnalysis.title", "homepage"),
                description: t("features.portfolioAnalysis.description", "homepage"),
                icon: (
                  <BarChart4 className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "pink",
                bulletPoints: [
                  t("features.portfolioAnalysis.bullets.0", "homepage"),
                  t("features.portfolioAnalysis.bullets.1", "homepage"),
                  t("features.portfolioAnalysis.bullets.2", "homepage")
                ],
              },
              {
                title: t("features.launchpads.title", "homepage"),
                description: t("features.launchpads.description", "homepage"),
                icon: (
                  <Rocket className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "indigo",
                bulletPoints: [
                  t("features.launchpads.bullets.0", "homepage"),
                  t("features.launchpads.bullets.1", "homepage"),
                  t("features.launchpads.bullets.2", "homepage"),
                ],
              },
              {
                title: t("features.aiAssistant.title", "homepage"),
                description: t("features.aiAssistant.description", "homepage"),
                icon: (
                  <Bot className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "cyan",
                bulletPoints: [
                  t("features.aiAssistant.bullets.0", "homepage"),
                  t("features.aiAssistant.bullets.1", "homepage"),
                  t("features.aiAssistant.bullets.2", "homepage"),
                ],
              },
              {
                title: t("features.airdropScore.title", "homepage"),
                description: t("features.airdropScore.description", "homepage"),
                icon: (
                  <Gift className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "emerald",
                bulletPoints: [
                  t("features.airdropScore.bullets.0", "homepage"),
                  t("features.airdropScore.bullets.1", "homepage"),
                  t("features.airdropScore.bullets.2", "homepage"),
                ],
              },
              {
                title: t("features.gemScout.title", "homepage"),
                description: t("features.gemScout.description", "homepage"),
                icon: (
                  <Gem className="h-12 w-12 p-2.5 text-primary group-hover:animate-spin-once" />
                ),
                color: "blue",
                bulletPoints: [
                  t("features.gemScout.bullets.0", "homepage"),
                  t("features.gemScout.bullets.1", "homepage"),
                  t("features.gemScout.bullets.2", "homepage"),
                ],
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                variants={featureVariants}
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
                whileHover={{
                  y: -1, // Reduced by 50% from -2
                  x: -1, // Reduced by 50% from -2
                  scale: 1.005, // Reduced by another 50% from 1.01
                  boxShadow: "0 6px 15px rgba(var(--primary-rgb), 0.2)",
                  transition: {
                    type: "spring",
                    stiffness: 300,
                    damping: 15,
                  },
                }}
                whileTap={{ scale: 0.98 }}
                className="flex flex-col h-full bg-card/90 backdrop-blur-sm border border-border/40 rounded-xl overflow-hidden shadow-md group hover:border-primary/40 hover:shadow-xl hover:shadow-primary/20 transition-all duration-600 relative"
              >
                {/* Enhanced gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#0B86FB]/5 via-transparent to-[#8A3AFB]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-600"></div>

                {/* Animated border glow effect */}
                <motion.div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-600 pointer-events-none"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                >
                  <div className="absolute top-0 left-0 right-0 h-[1px] bg-gradient-to-r from-[#0B86FB]/60 via-[#5F63F7]/60 to-[#8A3AFB]/60"></div>
                  <div className="absolute bottom-0 right-0 left-0 h-[1px] bg-gradient-to-r from-[#8A3AFB]/60 via-[#5F63F7]/60 to-[#0B86FB]/60"></div>
                  <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-gradient-to-b from-[#0B86FB]/60 via-[#5F63F7]/60 to-[#8A3AFB]/60"></div>
                  <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-gradient-to-b from-[#8A3AFB]/60 via-[#5F63F7]/60 to-[#0B86FB]/60"></div>
                </motion.div>
                {/* Beta Testing Live badge for AI-Enhanced Score Analysis */}
                {(feature.title === t("features.aiRating.title", "homepage") ||
                  feature.title === t("features.idoRating.title", "homepage") ||
                  feature.title === t("features.compareCoins.title", "homepage")) && (
                  <Badge className="absolute top-3 right-3 px-3 py-1 bg-green-500/90 text-white flex items-center gap-1.5">
                    <CircleDot className="h-3 w-3 text-white animate-pulse" />
                    {t("badges.betaTestingLive", "homepage")}
                  </Badge>
                )}

                {feature.title === "Upcoming IDOs" && (
                  <Badge className="absolute top-3 right-3 px-3 py-1 bg-yellow-500/90 text-white flex items-center gap-1.5">
                    <CircleDot className="h-3 w-3 text-white" />
                    {t("badges.betaTestingSoon", "homepage")}
                  </Badge>
                )}

                {/* Coming Soon badge for the remaining 7 cards */}
                {(feature.title === t("features.portfolioGenerator.title", "homepage") ||
                  feature.title === t("features.portfolioAnalysis.title", "homepage") ||
                  feature.title === t("features.launchpads.title", "homepage") ||
                  feature.title === t("features.aiAssistant.title", "homepage") ||
                  feature.title === t("features.airdropScore.title", "homepage") ||
                  feature.title === t("features.gemScout.title", "homepage")) && (
                  <Badge className="absolute top-3 right-3 px-3 py-1 bg-gray-500/90 text-white flex items-center gap-1.5">
                    <CircleDot className="h-3 w-3 text-white opacity-80" />
                    {t("badges.comingSoon", "homepage")}
                  </Badge>
                )}
                <div className="p-5 sm:p-6 flex-grow relative z-10">
                  {/* Enhanced icon container with smooth animations */}
                  <motion.div
                    whileHover={{
                      rotate: [0, 5, 0, -5, 0],
                      transition: { duration: 0.5, ease: "easeInOut" },
                    }}
                    className={`w-16 h-16 sm:w-18 sm:h-18 rounded-xl bg-gradient-to-br from-[#0B86FB]/10 to-[#8A3AFB]/10 flex items-center justify-center mb-6 shadow-lg shadow-primary/15 group-hover:shadow-xl group-hover:shadow-primary/25 transition-all duration-600 relative overflow-hidden border border-primary/10 group-hover:border-primary/30`}
                  >
                    {/* Animated gradient background */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-[#0B86FB]/20 via-[#5F63F7]/20 to-[#8A3AFB]/20 opacity-0 group-hover:opacity-100"
                      animate={{
                        background: [
                          "linear-gradient(to right, rgba(11,134,251,0.2), rgba(95,99,247,0.2), rgba(138,58,251,0.2))",
                          "linear-gradient(to right, rgba(138,58,251,0.2), rgba(95,99,247,0.2), rgba(11,134,251,0.2))",
                          "linear-gradient(to right, rgba(11,134,251,0.2), rgba(95,99,247,0.2), rgba(138,58,251,0.2))",
                        ],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "linear",
                      }}
                    />

                    {/* Subtle pulsing glow effect */}
                    <motion.div
                      className="absolute inset-0 opacity-0 group-hover:opacity-100"
                      animate={{
                        boxShadow: [
                          "0 0 0 rgba(var(--primary-rgb), 0)",
                          "0 0 20px rgba(var(--primary-rgb), 0.3)",
                          "0 0 0 rgba(var(--primary-rgb), 0)",
                        ],
                      }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />

                    {/* Icon with animation */}
                    <div className="relative z-10">
                      <motion.div
                        animate={{
                          opacity: [0.8, 1, 0.8],
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          repeatType: "reverse",
                          ease: "easeInOut",
                        }}
                      >
                        {feature.icon}
                      </motion.div>
                    </div>
                  </motion.div>

                  {/* Enhanced title with gradient text */}
                  <motion.h3 className="text-xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB]">
                    {feature.title}
                  </motion.h3>

                  {/* Enhanced description with subtle animation */}
                  <motion.p
                    initial={{ opacity: 0.9 }}
                    whileInView={{ opacity: 1 }}
                    className="text-muted-foreground text-sm leading-relaxed mb-5"
                  >
                    {feature.description}
                  </motion.p>

                  {/* Enhanced bullet points with staggered animations */}
                  <motion.ul
                    className="space-y-3"
                    variants={staggerContainer}
                    initial="initial"
                    whileInView="animate"
                  >
                    {feature.bulletPoints.map((point, i) => (
                      <motion.li
                        key={i}
                        className="flex items-center text-sm" /* Changed from items-start to items-center for better alignment */
                        variants={featureVariants}
                        custom={i}
                        transition={{
                          type: "spring",
                          stiffness: 400,
                          damping: 25,
                        }}
                      >
                        <div className="rounded-full bg-gradient-to-r from-[#0B86FB]/20 to-[#8A3AFB]/20 p-1.5 mr-3 flex-shrink-0 flex items-center justify-center group-hover:shadow-sm group-hover:shadow-primary/20 border border-primary/10 group-hover:border-primary/30 transition-all duration-600">
                          <Check className="h-3 w-3 text-[#5F63F7]" />
                        </div>
                        <span className="opacity-80 group-hover:opacity-100 transition-opacity duration-600">
                          {point}
                        </span>
                      </motion.li>
                    ))}
                  </motion.ul>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Trusted By Section - Client Logos */}
      <section className="py-20 border-y border-border/30 bg-muted/5 w-full relative">
        {/* Enhanced background texture with professional radial gradients */}
        <div className="absolute inset-0 overflow-hidden">
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `
                radial-gradient(circle at 15% 25%, rgba(11, 134, 251, 0.05) 0%, transparent 45%),
                radial-gradient(circle at 85% 20%, rgba(11, 134, 251, 0.035) 0%, transparent 50%),
                radial-gradient(circle at 50% 90%, rgba(138, 58, 251, 0.04) 0%, transparent 60%),
                radial-gradient(circle at 75% 85%, rgba(11, 134, 251, 0.03) 0%, transparent 55%)
              `,
              pointerEvents: "none",
            }}
          />

          {/* Animated subtle grid pattern for corporate feel */}
          <motion.div
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `
                linear-gradient(to right, rgba(138, 58, 251, 0.08) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(11, 134, 251, 0.08) 1px, transparent 1px)
              `,
              backgroundSize: "60px 60px",
            }}
            animate={{
              opacity: [0.08, 0.12, 0.08],
              scale: [1, 1.02, 1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
            }}
          />
        </div>

        <div className="container mx-auto max-w-[1600px] px-4 sm:px-8 relative z-10">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
            className="text-center mb-14"
          >
            {/* Enhanced section header */}
            <motion.div
              className="inline-block mb-3"
              whileInView={{
                scale: [0.95, 1],
                opacity: [0, 1],
              }}
              transition={{
                duration: 0.8,
                ease: "easeOut",
              }}
            >
              <h2 className="text-3xl md:text-4xl font-bold relative inline-block pb-3">
                {/* Premium gradient text for headline */}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] via-[#5F63F7] to-[#8A3AFB] animate-gradient-slow">
                  {t("trustedBy.title", "homepage")}
                </span>

                {/* Animated underline effect */}
                <motion.span
                  className="absolute -bottom-1 left-0 right-0 h-[3px] bg-gradient-to-r from-[#0B86FB]/70 via-[#5F63F7]/70 to-[#8A3AFB]/70 rounded-full"
                  initial={{ width: "0%", left: "50%" }}
                  whileInView={{ width: "100%", left: "0%" }}
                  transition={{ duration: 1, ease: "easeOut", delay: 0.3 }}
                />
              </h2>
            </motion.div>

            {/* Enhanced description with subtle animation */}
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
              className="text-muted-foreground text-lg max-w-2xl mx-auto"
            >
              {t("trustedBy.description", "homepage")}
            </motion.p>
          </motion.div>

          {/* Premium container for partner logos */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            whileHover={{
              boxShadow: "0 15px 35px -15px rgba(var(--primary-rgb), 0.25)",
            }}
            className="bg-gradient-to-r from-background/80 via-muted/10 to-background/80 backdrop-blur-sm rounded-2xl p-12 border border-primary/20 shadow-lg shadow-primary/10 relative overflow-hidden w-full hover:border-primary/30 transition-all duration-600"
          >
            {/* Premium glass effect background */}
            <div className="absolute inset-0 backdrop-blur-[2px] bg-background/10"></div>

            {/* Enhanced radial gradient backgrounds */}
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_30%_20%,rgba(var(--primary-rgb),0.08)_0%,transparent_70%)]"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(var(--primary-rgb),0.06)_0%,transparent_70%)]"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(var(--primary-rgb),0.04)_0%,transparent_50%)]"></div>

            {/* Animated spotlight effect */}
            <motion.div
              className="absolute inset-0 opacity-0 bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5"
              animate={{
                opacity: [0, 0.15, 0],
                x: ["-100%", "100%", "100%"],
                backgroundSize: ["100% 100%", "150% 150%", "100% 100%"],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatType: "loop",
                times: [0, 0.5, 1],
                ease: "easeInOut",
              }}
            ></motion.div>

            {/* Premium border glow effect */}
            <motion.div
              className="absolute inset-0 rounded-2xl"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 1, delay: 0.5 }}
              style={{
                background:
                  "linear-gradient(var(--border-rotate-angle, 0deg), rgba(var(--primary-rgb), 0) 0%, rgba(var(--primary-rgb), 0.2) 20%, rgba(var(--primary-rgb), 0.3) 50%, rgba(var(--primary-rgb), 0.2) 80%, rgba(var(--primary-rgb), 0) 100%)",
                backgroundSize: "100% 100%",
                mask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                maskComposite: "exclude",
                WebkitMaskComposite: "xor",
                padding: "1px",
                opacity: 0.7,
                filter: "blur(0.5px)",
              }}
            >
              <motion.div
                className="w-12 h-12 object-contain"
                animate={{ "--border-rotate-angle": ["0deg", "360deg"] }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                style={{ width: "100%", height: "100%" }}
              />
            </motion.div>

            {/* Elegant pulse animation */}
            <motion.div
              className="absolute inset-0 opacity-0 bg-gradient-to-br from-[#0B86FB]/5 via-[#5F63F7]/5 to-[#8A3AFB]/5 rounded-2xl"
              animate={{
                opacity: [0, 0.12, 0],
                scale: [0.95, 1.01, 0.95],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut",
              }}
            ></motion.div>

            {/* Enhanced Partner Logos with Consistent Animations */}
            <div className="relative overflow-hidden mx-auto">
              <motion.div
                initial={{ x: 0 }}
                animate={{ x: "-50%" }}
                transition={{
                  duration: 25,
                  repeat: Infinity,
                  ease: "linear",
                  repeatType: "mirror",
                }}
                className="flex items-center"
              >
                {/* First set of logos with consistent spacing */}
                <div className="flex space-x-10 px-10">
                  {/* Logo 1: SolScan Explorer */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/solscan-logo-dark.d634e009.svg"
                          alt="SolScan Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      SolScan
                    </span>
                  </motion.div>
                  {/* 1-SolScan END*/}

                  {/* 2-: cryptorank */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/cryptorank.svg"
                          alt="Cryptorank Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Cryptorank
                    </span>
                  </motion.div>

                  {/* Logo 3: Deepseek */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/deepseek.svg"
                          alt="Deepseek Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Deepseek
                    </span>
                  </motion.div>
                  {/* Deepseek END*/}
                  {/* Logo : EtherScan Explorer */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/etherscan.png"
                          alt="EtherScan Logo"
                          className="w-full h-full object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      EtherScan
                    </span>
                  </motion.div>
                  {/* EtherScan END*/}

                  {/* Logo 5: Certik */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-18 h-18 flex items-center justify-center z-10">
                        <img
                          src="/logos/Certik.svg"
                          alt="Certik Logo"
                          className="w-full h-full object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Certik
                    </span>
                  </motion.div>
                  {/* Certik END*/}

                  {/* Logo 2: Coinbase */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(0, 82, 255, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#0052FF]/0 via-[#0052FF]/20 to-[#0052FF]/0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(0, 82, 255, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(0, 82, 255, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(0, 82, 255, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/coinbase.svg"
                          alt="Coinbase Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Coinbase
                    </span>
                  </motion.div>

                  {/* Logo 5: OpenAI */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/openai.svg"
                          alt="OPENAI Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      OpenAI
                    </span>
                  </motion.div>
                  {/* OpenAI END*/}
                  {/* Logo : BNB Smart Chain Explorer */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/bscscan.svg"
                          alt="BNB Smart Chain Explorer Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      BNB Scan
                    </span>
                  </motion.div>
                  {/* BNB Smart Chain Explorer END*/}

                  {/* Logo 5: Antrophic */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/antrophic.svg"
                          alt="Antrophic Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Anthropic
                    </span>
                  </motion.div>
                  {/* Antrophic END*/}

                  {/* Logo 5: lunarcrush */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/lunarcrush.svg"
                          alt="Lunarcrush Logo"
                          className="w-full h-full object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Lunarcrush
                    </span>
                  </motion.div>
                  {/* Lunarcrush END*/}

                  {/* Logo : Perplexity */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/perplexity.svg"
                          alt="Perplexity Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Perplexity
                    </span>
                  </motion.div>
                  {/* Perplexity END*/}

                  {/* Logo : Coinsguru */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/coinsguru-logo.png"
                          alt="Coinsguru Logo"
                          className="w-full h-full object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Coinsguru
                    </span>
                  </motion.div>
                  {/* Coinsguru END*/}
                </div>

                {/* Second set of logos (cloned for seamless infinite scroll) */}
                <div className="flex space-x-10">
                  {/* Same logos repeated */}
                  {/* Each logo copied from first set but with initial animation set to visible */}
                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/solscan-logo-dark.d634e009.svg"
                          alt="SolScan Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      SolScan
                    </span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/cryptorank.svg"
                          alt="Cryptorank Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Cryptorank
                    </span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/deepseek.svg"
                          alt="Deepseek Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Deepseek
                    </span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/etherscan.png"
                          alt="EtherScan Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      EtherScan
                    </span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/Certik.svg"
                          alt="Certik Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Certik
                    </span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(0, 82, 255, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#0052FF]/0 via-[#0052FF]/20 to-[#0052FF]/0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(0, 82, 255, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(0, 82, 255, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(0, 82, 255, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/coinbase.svg"
                          alt="Coinbase Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Coinbase
                    </span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/openai.svg"
                          alt="OPENAI Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      OpenAI
                    </span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/bscscan.svg"
                          alt="BNB Smart Chain Explorer Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      BNB Scan
                    </span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 0, opacity: 1 }}
                    className="flex flex-col items-center opacity-95 hover:opacity-100 transition-all duration-300 w-24 group"
                    whileHover={{
                      scale: 1.08,
                      y: -5,
                      filter: "drop-shadow(0 0 12px rgba(87, 65, 217, 0.5))",
                      transition: {
                        type: "spring",
                        stiffness: 400,
                        damping: 10,
                      },
                    }}
                  >
                    <div className="h-20 flex items-center justify-center relative">
                      <motion.div
                        className="absolute -inset-1 bg-gradient-to-r from-[#5741D9]/0 via-[#5741D9]/20 to-[#5741D9]/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                        animate={{
                          background: [
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.3) 0%, transparent 70%)",
                            "radial-gradient(circle at center, rgba(87, 65, 217, 0.2) 0%, transparent 70%)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="w-16 h-16 flex items-center justify-center z-10">
                        <img
                          src="/logos/lunarcrush.svg"
                          alt="Lunarcrush Logo"
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                    </div>
                    <span className="text-base font-semibold mt-2 text-foreground/90 group-hover:text-foreground transition-colors duration-300">
                      Lunarcrush
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </div>

            {/* Premium enterprise trust indicators */}
            <div className="relative mt-10 pt-8 border-t border-primary/10">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="flex flex-col md:flex-row items-center justify-between gap-6"
              >
                {/* Single row layout with all elements on one horizontal line */}
                <div className="w-full">
                  <div className="flex flex-col 2xl:flex-row justify-center 2xl:justify-between items-center space-y-6 2xl:space-y-0">
                    {/* First section - Metrics in a row with equal, optimized widths */}
                    <div className="grid sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
                      <motion.div className="text-center flex flex-col justify-center w-full md:w-auto md:min-w-[140px]">
                        <motion.div
                          animate={{
                            scale: [1, 1.01, 1],
                            opacity: [0.98, 1, 0.98],
                          }}
                          transition={{ duration: 3, repeat: Infinity }}
                          className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] to-[#5F63F7]"
                        >
                          1000+
                        </motion.div>
                        <div className="text-sm text-muted-foreground mt-1 whitespace-nowrap">
                          {t("trustedBy.stats.coinsAnalyzed", "homepage")}
                        </div>
                      </motion.div>

                      <motion.div className="text-center flex flex-col justify-center w-full md:w-auto md:min-w-[140px]">
                        <motion.div
                          animate={{
                            scale: [1, 1.01, 1],
                            opacity: [0.98, 1, 0.98],
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            delay: 0.2,
                          }}
                          className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#5F63F7] to-[#8A3AFB]"
                        >
                          2M+
                        </motion.div>
                        <div className="text-sm text-muted-foreground mt-1 whitespace-nowrap">
                          {t("trustedBy.stats.dataPoints", "homepage")}
                        </div>
                      </motion.div>

                      <motion.div className="text-center flex flex-col justify-center w-full md:w-auto md:min-w-[140px]">
                        <motion.div
                          animate={{
                            scale: [1, 1.01, 1],
                            opacity: [0.98, 1, 0.98],
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            delay: 0.4,
                          }}
                          className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB]"
                        >
                          750K+
                        </motion.div>
                        <div className="text-sm text-muted-foreground mt-1 whitespace-nowrap">
                          {t("trustedBy.stats.apiCalls", "homepage")}
                        </div>
                      </motion.div>

                      <motion.div className="text-center flex flex-col justify-center w-full md:w-auto md:min-w-[140px]">
                        <motion.div
                          animate={{
                            scale: [1, 1.01, 1],
                            opacity: [0.98, 1, 0.98],
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            delay: 0.6,
                          }}
                          className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#5F63F7] to-[#0B86FB]"
                        >
                          100+
                        </motion.div>
                        <div className="text-sm text-muted-foreground mt-1 whitespace-nowrap">
                          {t("trustedBy.stats.dataSources", "homepage")}
                        </div>
                      </motion.div>
                    </div>

                    {/* Second section - Bullet points in a row */}
                    <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4 lg:space-x-10">
                      <div className="flex items-center">
                        <div className="rounded-full bg-blue-500/90 w-2.5 h-2.5 animate-pulse mr-2 lg:mr-3"></div>
                        <span className="text-xs md:text-sm font-medium text-muted-foreground whitespace-nowrap">
                          {t("trustedBy.features.aiPowered", "homepage")}
                        </span>
                      </div>

                      <div className="flex items-center">
                        <div className="rounded-full bg-purple-500/90 w-2.5 h-2.5 animate-pulse mr-2 lg:mr-3"></div>
                        <span className="text-xs md:text-sm font-medium text-muted-foreground whitespace-nowrap">
                          {t("trustedBy.features.multiLayer", "homepage")}
                        </span>
                      </div>

                      <div className="flex items-center">
                        <div className="rounded-full bg-green-500/90 w-2.5 h-2.5 animate-pulse mr-2 lg:mr-3"></div>
                        <span className="text-xs md:text-sm font-medium text-muted-foreground whitespace-nowrap">
                          {t("trustedBy.features.enterprise", "homepage")}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* AI-Driven Intelligence Section */}
      <section className="py-20 bg-background w-full relative">
        {/* Enhanced background with subtle grid pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.03] pointer-events-none"></div>

        {/* Animated particle effects */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-gradient-to-r from-[#0B86FB]/10 to-[#8A3AFB]/10 blur-3xl opacity-20 animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 rounded-full bg-gradient-to-r from-[#8A3AFB]/10 to-[#0B86FB]/10 blur-3xl opacity-20 animate-pulse-slow-reverse"></div>
        </div>

        <div className="main-container container mx-auto max-w-[1600px] px-4 sm:px-8 relative z-10">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-3 relative inline-block pb-3"
              whileInView={{
                scale: [0.98, 1],
                opacity: [0.8, 1],
              }}
              transition={{
                duration: 0.8,
                ease: "easeOut",
              }}
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB] animate-gradient-slow relative">
                {t("intelligence.title", "homepage")}
              </span>
              <motion.span
                className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB] opacity-70"
                initial={{ width: "0%" }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 1, delay: 0.2, ease: "easeOut" }}
              />

              {/* Subtle highlight glow */}
              <motion.div
                className="absolute -inset-1 rounded-lg bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-purple-500/0 blur-xl"
                animate={{
                  opacity: [0, 0.2, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
            </motion.h2>

            <motion.p
              className="text-base md:text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed"
              initial={{ y: 10, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
{t("callToAction.primary", "homepage")}
            </motion.p>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="relative overflow-hidden w-full"
          >
            <RadialGradient
              center="30% 20%"
              opacity={0.08}
              transparentEndpoint={80}
            />
            <RadialGradient
              center="70% 80%"
              opacity={0.08}
              transparentEndpoint={80}
            />

            {/* Enhanced animated beam effect */}
            <motion.div
              className="absolute inset-0 opacity-0 bg-gradient-to-r from-primary/10 via-transparent to-primary/10"
              animate={{
                opacity: [0, 0.15, 0],
                x: ["-100%", "100%", "100%"],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatType: "loop",
                times: [0, 0.5, 1],
                ease: "easeInOut",
              }}
            ></motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-stretch relative z-10 px-2">
              {/* Left Side - CTA Content */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="rounded-xl px-4 sm:px-8 py-8 bg-card border border-border/40 relative overflow-hidden group hover:border-primary/30 hover:shadow-xl hover:shadow-primary/30 transition-all duration-600 flex flex-col h-full"
              >
                {/* Animated background effects */}
                <RadialGradient
                  center="30% 30%"
                  opacity={0.07}
                  transparentEndpoint={80}
                />
                <div className="absolute inset-0 bg-gradient-to-br from-[#0B86FB]/5 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-600"></div>

                {/* Glowing border effect on hover */}
                <motion.div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-600"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                >
                  <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-[#0B86FB]/80 via-[#5F63F7]/80 to-[#8A3AFB]/80"></div>
                  <div className="absolute bottom-0 right-0 left-0 h-[2px] bg-gradient-to-r from-[#8A3AFB]/80 via-[#5F63F7]/80 to-[#0B86FB]/80"></div>
                  <div className="absolute top-0 bottom-0 left-0 w-[2px] bg-gradient-to-b from-[#0B86FB]/80 via-[#5F63F7]/80 to-[#8A3AFB]/80"></div>
                  <div className="absolute top-0 bottom-0 right-0 w-[2px] bg-gradient-to-b from-[#8A3AFB]/80 via-[#5F63F7]/80 to-[#0B86FB]/80"></div>
                </motion.div>

                <div className="relative z-10 flex flex-col h-full">
                  <div className="flex items-center gap-3 mb-6">
                    {/* Enhanced title with icon animation */}
                    <div className="relative">
                      <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-[#0B86FB]/30 to-[#8A3AFB]/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-600"></div>
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-[#0B86FB]/20 to-[#8A3AFB]/20 flex items-center justify-center relative z-10">
                        <motion.div
                          animate={{ rotate: [0, 15, 0, -15, 0] }}
                          transition={{
                            duration: 5,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                        >
                          <Sparkles className="h-6 w-6 text-primary" />
                        </motion.div>
                      </div>
                    </div>
                    <motion.h3
                      className="text-xl md:text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB]"
                      initial={{ opacity: 0.9 }}
                      whileHover={{ opacity: 1, scale: 1.01 }}
                      transition={{ duration: 0.3 }}
                    >
                      {t("intelligence.subtitle", "homepage")}
                    </motion.h3>
                  </div>

                  <ul className="text-muted-foreground space-y-4 flex-1">
                    {/* Enhanced list items with animations */}
                    <motion.li
                      className="flex items-start border border-border/40 rounded-lg p-4 bg-muted/5 hover:bg-gradient-to-r hover:from-[#0B86FB]/5 hover:to-transparent hover:border-[#0B86FB]/40 transition-all duration-600 hover:shadow-md hover:shadow-[#0B86FB]/10 relative"
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                      }}
                    >
                      <div className="mr-3 mt-0.5">
                        <div className="w-9 h-9 rounded-full bg-gradient-to-r from-[#0B86FB]/10 to-[#0B86FB]/20 flex items-center justify-center">
                          <Lightbulb className="h-5 w-5 text-[#0B86FB]" />
                        </div>
                      </div>
                      <span className="pt-1.5">
{t("benefits.0.title", "homepage")}
                      </span>
                    </motion.li>

                    <motion.li
                      className="flex items-start border border-border/40 rounded-lg p-4 bg-muted/5 hover:bg-gradient-to-r hover:from-[#5F63F7]/5 hover:to-transparent hover:border-[#5F63F7]/40 transition-all duration-600 hover:shadow-md hover:shadow-[#5F63F7]/10 relative"
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                      }}
                    >
                      <div className="mr-3 mt-0.5">
                        <div className="w-9 h-9 rounded-full bg-gradient-to-r from-[#5F63F7]/10 to-[#5F63F7]/20 flex items-center justify-center">
                          <Zap className="h-5 w-5 text-[#5F63F7]" />
                        </div>
                      </div>
                      <span className="pt-1.5">
{t("benefits.1.title", "homepage")}
                      </span>
                    </motion.li>

                    <motion.li
                      className="flex items-start border border-border/40 rounded-lg p-4 bg-muted/5 hover:bg-gradient-to-r hover:from-[#8A3AFB]/5 hover:to-transparent hover:border-[#8A3AFB]/40 transition-all duration-600 hover:shadow-md hover:shadow-[#8A3AFB]/10 relative"
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                      }}
                    >
                      <div className="mr-3 mt-0.5">
                        <div className="w-9 h-9 rounded-full bg-gradient-to-r from-[#8A3AFB]/10 to-[#8A3AFB]/20 flex items-center justify-center">
                          <LineChart className="h-5 w-5 text-[#8A3AFB]" />
                        </div>
                      </div>
                      <span className="pt-1.5">
                        {t("benefits.2.title", "homepage")}
                      </span>
                    </motion.li>
                  </ul>

                  <div className="flex flex-col justify-center sm:flex-row gap-4 mt-8">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.98 }}
                      className="flex items-center justify-center"
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                      }}
                    >
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-700 text-primary-foreground px-8 py-6 group transition-all duration-600 transform hover:scale-105 hover:shadow-lg hover:shadow-primary/20 relative overflow-hidden animate-button-glow border-0"
                        onClick={() => setLocation("/generator")}
                      >
                        <span className="relative z-10 text-base font-medium">
                          {t("buttons.getStarted", "homepage")}
                        </span>
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-600 relative z-10" />
                        <motion.span
                          className="absolute inset-0 bg-gradient-to-r from-primary/90 via-blue-600/90 to-primary/90 opacity-0 group-hover:opacity-100 transition-all duration-600"
                          animate={{
                            backgroundPosition: [
                              "0% 50%",
                              "100% 50%",
                              "0% 50%",
                            ],
                          }}
                          transition={{
                            duration: 5,
                            repeat: Infinity,
                            repeatType: "loop",
                            ease: "linear",
                          }}
                        />
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </motion.div>

              {/* Right Side - FAQ Accordion */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="rounded-xl px-4 sm:px-8 py-8 bg-card border border-border/40 relative overflow-hidden group hover:border-primary/30 hover:shadow-xl hover:shadow-primary/30 transition-all duration-600 flex flex-col h-full"
              >
                {/* Animated background effects */}
                <RadialGradient
                  center="70% 30%"
                  opacity={0.07}
                  transparentEndpoint={80}
                />
                <div className="absolute inset-0 bg-gradient-to-tl from-[#5F63F7]/5 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-600"></div>

                {/* Glowing border effect on hover */}
                <motion.div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-600"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                >
                  <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-[#0B86FB]/80 via-[#5F63F7]/80 to-[#8A3AFB]/80"></div>
                  <div className="absolute bottom-0 right-0 left-0 h-[2px] bg-gradient-to-r from-[#8A3AFB]/80 via-[#5F63F7]/80 to-[#0B86FB]/80"></div>
                  <div className="absolute top-0 bottom-0 left-0 w-[2px] bg-gradient-to-b from-[#0B86FB]/80 via-[#5F63F7]/80 to-[#8A3AFB]/80"></div>
                  <div className="absolute top-0 bottom-0 right-0 w-[2px] bg-gradient-to-b from-[#8A3AFB]/80 via-[#5F63F7]/80 to-[#0B86FB]/80"></div>
                </motion.div>

                <div className="relative z-10 flex flex-col h-full">
                  <div className="flex items-center gap-3 mb-6">
                    {/* Enhanced title with icon animation */}
                    <div className="relative">
                      <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-[#0B86FB]/30 to-[#8A3AFB]/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-600"></div>
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-[#0B86FB]/20 to-[#8A3AFB]/20 flex items-center justify-center relative z-10">
                        <motion.div
                          animate={{ rotate: [0, -10, 10, -10, 0] }}
                          transition={{
                            duration: 5,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 0.5,
                          }}
                        >
                          <HelpCircle className="h-6 w-6 text-[#5F63F7]" />
                        </motion.div>
                      </div>
                    </div>
                    <motion.h3
                      className="text-xl md:text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB]"
                      initial={{ opacity: 0.9 }}
                      whileHover={{ opacity: 1, scale: 1.01 }}
                      transition={{ duration: 0.3 }}
                    >
                      {t("faq.title", "homepage")}
                    </motion.h3>
                  </div>

                  <div className="space-y-4 w-full flex-1">
                    <SimpleFAQItem
                      question={t("faq.questions.0.question", "homepage")}
                      answer={t("faq.questions.0.answer", "homepage")}
                    />
                    <SimpleFAQItem
                      question={t("faq.questions.1.question", "homepage")}
                      answer={t("faq.questions.1.answer", "homepage")}
                    />
                    <SimpleFAQItem
                      question={t("faq.questions.2.question", "homepage")}
                      answer={t("faq.questions.2.answer", "homepage")}
                    />
                  </div>

                  <div className="flex justify-center mt-8">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                      }}
                    >
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-700 text-primary-foreground px-8 py-6 group transition-all duration-600 transform hover:scale-105 hover:shadow-lg hover:shadow-primary/20 relative overflow-hidden animate-button-glow border-0"
                        onClick={() => {
                          setLocation("/faq");
                          window.scrollTo(0, 0);
                        }}
                      >
                        <span className="relative z-10 text-base font-medium">
                          {t("buttons.viewAllQuestions", "homepage")}
                        </span>
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-600 relative z-10" />
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-600"
                          animate={{
                            backgroundPosition: [
                              "0% 50%",
                              "100% 50%",
                              "0% 50%",
                            ],
                          }}
                          transition={{
                            duration: 5,
                            repeat: Infinity,
                            repeatType: "loop",
                            ease: "linear",
                          }}
                        />
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="py-20 bg-background w-full relative overflow-hidden">
        <RadialGradient
          center="20% 30%"
          opacity={0.08}
          transparentEndpoint={80}
        />
        <RadialGradient
          center="80% 70%"
          opacity={0.08}
          transparentEndpoint={80}
        />

        <div className="main-container container mx-auto max-w-[1600px] px-4 sm:px-8">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-3 relative inline-block pb-3"
              whileInView={{
                scale: [0.98, 1],
                opacity: [0.8, 1],
              }}
              transition={{
                duration: 0.8,
                ease: "easeOut",
              }}
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB] animate-gradient-slow relative">
                {t("testimonials.title", "homepage")}
              </span>
              <motion.span
                className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-[#0B86FB] to-[#8A3AFB] opacity-70"
                initial={{ width: "0%" }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 1, delay: 0.2, ease: "easeOut" }}
              />

              {/* Subtle highlight glow */}
              <motion.div
                className="absolute -inset-1 rounded-lg bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-purple-500/0 blur-xl"
                animate={{
                  opacity: [0, 0.2, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
            </motion.h2>

            <motion.p
              className="text-base md:text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed"
              initial={{ y: 10, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
{t("callToAction.secondary", "homepage")}
            </motion.p>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {/* Testimonial 1 */}
            <div className="relative group perspective-1000">
              <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/50 via-blue-500/50 to-purple-500/50 rounded-xl opacity-0 group-hover:opacity-70 blur-sm group-hover:blur transition-all duration-1000 group-hover:duration-500"></div>
              <motion.div
                whileHover={{
                  rotateX: 5,
                  rotateY: -5,
                  z: 10,
                  scale: 1.05,
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 25,
                }}
                className="rounded-xl p-6 bg-card border border-border/40 relative overflow-hidden group-hover:border-transparent shadow-lg group-hover:shadow-xl group-hover:shadow-primary/20 transition-all duration-500 preserve-3d"
              >
                <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-primary/80 via-blue-500/80 to-purple-500/80"></div>
                <div className="absolute bottom-0 right-0 left-0 h-[2px] bg-gradient-to-r from-purple-500/80 via-blue-500/80 to-primary/80"></div>
                <div className="absolute top-0 bottom-0 left-0 w-[2px] bg-gradient-to-b from-primary/80 via-blue-500/80 to-purple-500/80"></div>
                <div className="absolute top-0 bottom-0 right-0 w-[2px] bg-gradient-to-b from-purple-500/80 via-blue-500/80 to-primary/80"></div>

                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="flex items-start gap-4 relative">
                  <div className="w-14 h-14 rounded-full bg-gradient-to-br from-primary/20 to-blue-500/20 flex items-center justify-center text-primary flex-shrink-0 relative overflow-hidden shadow-inner transition-all duration-600">
                    <Users className="w-6 h-6 group-hover:scale-110 transition-transform duration-500" />
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                  <div>
                    <div className="flex gap-1 mb-2">
                      {[1, 2, 3, 4, 5].map((star, i) => (
                        <Star
                          key={star}
                          className="w-4 h-4 fill-yellow-500 text-yellow-500 transition-all duration-500"
                          style={{
                            animation: `pulse 2s infinite ${i * 0.2}s`,
                            transformStyle: "preserve-3d",
                          }}
                        />
                      ))}
                    </div>
                    <blockquote className="text-muted-foreground relative">
                      <span className="text-5xl absolute -top-2 -left-1 text-gradient-primary opacity-40 font-serif transform-gpu group-hover:scale-110 transition-transform duration-700">
                        "
                      </span>
                      <p className="italic pl-6 relative text-sm leading-relaxed">
{t("testimonials.0.text", "homepage")}
                      </p>
                      <span className="text-5xl absolute -bottom-8 right-0 text-gradient-primary opacity-40 font-serif transform rotate-180 transform-gpu group-hover:scale-110 transition-transform duration-700">
                        "
                      </span>
                    </blockquote>
                    <div className="mt-6 pl-6">
                      <p className="font-semibold text-sm">Alex Thompson</p>
                      <p className="text-xs text-muted-foreground">
                        Crypto Investor
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Testimonial 2 */}
            <div className="relative group perspective-1000">
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/50 via-purple-500/50 to-primary/50 rounded-xl opacity-0 group-hover:opacity-70 blur-sm group-hover:blur transition-all duration-1000 group-hover:duration-500"></div>
              <motion.div
                whileHover={{
                  rotateX: 5,
                  rotateY: 5,
                  z: 10,
                  scale: 1.05,
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 25,
                }}
                className="rounded-xl p-6 bg-card border border-border/40 relative overflow-hidden group-hover:border-transparent shadow-lg group-hover:shadow-xl group-hover:shadow-blue-500/20 transition-all duration-500 preserve-3d"
              >
                <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-blue-500/80 via-purple-500/80 to-primary/80"></div>
                <div className="absolute bottom-0 right-0 left-0 h-[2px] bg-gradient-to-r from-primary/80 via-purple-500/80 to-blue-500/80"></div>
                <div className="absolute top-0 bottom-0 left-0 w-[2px] bg-gradient-to-b from-blue-500/80 via-purple-500/80 to-primary/80"></div>
                <div className="absolute top-0 bottom-0 right-0 w-[2px] bg-gradient-to-b from-primary/80 via-purple-500/80 to-blue-500/80"></div>

                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-tl from-blue-500/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="flex items-start gap-4 relative">
                  <div className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center text-primary flex-shrink-0 relative overflow-hidden shadow-inner transition-all duration-600">
                    <Users className="w-6 h-6 group-hover:scale-110 transition-transform duration-500" />
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                  <div>
                    <div className="flex gap-1 mb-2">
                      {[1, 2, 3, 4, 5].map((star, i) => (
                        <Star
                          key={star}
                          className="w-4 h-4 fill-yellow-500 text-yellow-500 transition-all duration-500"
                          style={{
                            animation: `pulse 2s infinite ${i * 0.2}s`,
                            transformStyle: "preserve-3d",
                          }}
                        />
                      ))}
                    </div>
                    <blockquote className="text-muted-foreground relative">
                      <span className="text-5xl absolute -top-2 -left-1 text-gradient-primary opacity-40 font-serif transform-gpu group-hover:scale-110 transition-transform duration-700">
                        "
                      </span>
                      <p className="italic pl-6 relative text-sm leading-relaxed">
                        The detailed metrics and risk analysis features give me
                        confidence in my decisions. It's like having a financial
                        advisor dedicated to crypto.
                      </p>
                      <span className="text-5xl absolute -bottom-8 right-0 text-gradient-primary opacity-40 font-serif transform rotate-180 transform-gpu group-hover:scale-110 transition-transform duration-700">
                        "
                      </span>
                    </blockquote>
                    <div className="mt-6 pl-6">
                      <p className="font-semibold text-sm">Sarah Chen</p>
                      <p className="text-xs text-muted-foreground">
                        Portfolio Manager
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Testimonial 3 */}
            <div className="relative group perspective-1000">
              <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500/50 via-primary/50 to-blue-500/50 rounded-xl opacity-0 group-hover:opacity-70 blur-sm group-hover:blur transition-all duration-1000 group-hover:duration-500"></div>
              <motion.div
                whileHover={{
                  rotateX: 5,
                  rotateY: -5,
                  z: 10,
                  scale: 1.05,
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 25,
                }}
                className="rounded-xl p-6 bg-card border border-border/40 relative overflow-hidden group-hover:border-transparent shadow-lg group-hover:shadow-xl group-hover:shadow-purple-500/20 transition-all duration-500 preserve-3d"
              >
                <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-purple-500/80 via-primary/80 to-blue-500/80"></div>
                <div className="absolute bottom-0 right-0 left-0 h-[2px] bg-gradient-to-r from-blue-500/80 via-primary/80 to-purple-500/80"></div>
                <div className="absolute top-0 bottom-0 left-0 w-[2px] bg-gradient-to-b from-purple-500/80 via-primary/80 to-blue-500/80"></div>
                <div className="absolute top-0 bottom-0 right-0 w-[2px] bg-gradient-to-b from-blue-500/80 via-primary/80 to-purple-500/80"></div>

                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-500/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="flex items-start gap-4 relative">
                  <div className="w-14 h-14 rounded-full bg-gradient-to-br from-purple-500/20 to-primary/20 flex items-center justify-center text-primary flex-shrink-0 relative overflow-hidden shadow-inner transition-all duration-600">
                    <Users className="w-6 h-6 group-hover:scale-110 transition-transform duration-500" />
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                  <div>
                    <div className="flex gap-1 mb-2">
                      {[1, 2, 3, 4, 5].map((star, i) => (
                        <Star
                          key={star}
                          className="w-4 h-4 fill-yellow-500 text-yellow-500 transition-all duration-500"
                          style={{
                            animation: `pulse 2s infinite ${i * 0.2}s`,
                            transformStyle: "preserve-3d",
                          }}
                        />
                      ))}
                    </div>
                    <blockquote className="text-muted-foreground relative">
                      <span className="text-5xl absolute -top-2 -left-1 text-gradient-primary opacity-40 font-serif transform-gpu group-hover:scale-110 transition-transform duration-700">
                        "
                      </span>
                      <p className="italic pl-6 relative text-sm leading-relaxed">
                        As someone new to crypto, CoinScout's user-friendly
                        interface and educational resources have been
                        invaluable. I feel more confident with every investment.
                      </p>
                      <span className="text-5xl absolute -bottom-8 right-0 text-gradient-primary opacity-40 font-serif transform rotate-180 transform-gpu group-hover:scale-110 transition-transform duration-700">
                        "
                      </span>
                    </blockquote>
                    <div className="mt-6 pl-6">
                      <p className="font-semibold text-sm">Michael Rivera</p>
                      <p className="text-xs text-muted-foreground">
                        Beginner Investor
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* "See All Success Stories" butonu kaldırıldı */}
        </div>
      </section>

      {/* Enhanced Final Call-to-Action Section */}
    </div>
  );
}
