import React, { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { CreateAlertButton } from "@/components/alerts/CreateAlertButton";
import CoinService, { CoinFilters } from "@/lib/services/CoinService";
import { Pagination } from "@/components/Pagination";
import { CoinTableHeader } from "@/components/CoinTableHeader";
import CoinTableRow from "@/components/CoinTableRow";
import { ChevronLeft, ChevronRight, Filter } from "lucide-react";
import { Coin } from "@/types/coin";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TopMoversFilters, TopMoversFiltersState } from "@/components/TopMoversFilters";
import { useLanguage } from "@/contexts/LanguageContext";

export default function TopMovers() {
  const [, setLocation] = useLocation();
  const [search, setSearch] = useState("");
  const [tableCoins, setTableCoins] = useState<Coin[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { isLoggedIn } = useAuth();
  const { t } = useLanguage();
  
  // Eğer kullanıcı giriş yapmamışsa login sayfasına yönlendir
  useEffect(() => {
    if (!isLoggedIn) {
      // Kullanıcı giriş yaptıktan sonra bu sayfaya geri dönebilmesi için mevcut URL'i de gönder
      const currentPath = window.location.pathname;
      setLocation(`/login?returnTo=${encodeURIComponent(currentPath)}`);
    }
  }, [isLoggedIn, setLocation]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);
  const [minimumGain, setMinimumGain] = useState<string>("Any");
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  
  // Advanced filters state
  const [currentFilters, setCurrentFilters] = useState<TopMoversFiltersState>({
    marketCapRange: [1000000, 100000000000], // $1M to $100B
    projectScoreRange: [0, 100],
    categories: [],
    chains: [],
    minimumGain: "Any",
  });

  // Sorting state
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc" | false;
  }>({
    key: "",
    direction: false,
  });

  // Handle sort requests
  const requestSort = useCallback((key: string) => {
    setSortConfig((prevState) => {
      // If clicking the same column that's already sorted
      if (prevState.key === key) {
        if (prevState.direction === "asc") {
          return { key, direction: "desc" };
        } else if (prevState.direction === "desc") {
          return { key, direction: false }; // Third click - remove sorting
        } else {
          return { key, direction: "asc" }; // If no direction, set to ascending
        }
      }
      // If clicking a new column, default to ascending
      return { key, direction: "asc" };
    });
  }, []);

  // Fetch data with change=true parameter for top gainers and additional filters
  const fetchData = async (filters: CoinFilters = { change: true }) => {
    try {
      setIsLoading(true);
      // Always ensure change=true for Top Movers page
      filters.change = true;
      
      console.log("Fetching data with filters:", filters);
      const response = await CoinService.getCoinsList(filters);
      setTableCoins(response);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching top movers:", error);
      setIsLoading(false);
    }
  };
  
  // Handler for applying filters from the TopMoversFilters component
  const handleApplyFilters = (filters: TopMoversFiltersState & { isReset?: boolean }) => {
    setCurrentFilters(filters);
    setMinimumGain(filters.minimumGain); // Sync the minimumGain with the current filters
    
    if (filters.isReset) {
      const resetApiFilters: CoinFilters = {
        change: true,
        marketcap_min: null,
        marketcap_max: null,
        score_min: null,
        score_max: null,
        selectedcategories: null,
        selectedchains: null,
      };
      
      console.log("Resetting filters - using default values:", resetApiFilters);
      fetchData(resetApiFilters);
      return;
    }

    const apiFilters: CoinFilters = {
      change: true,
      marketcap_min: filters.marketCapRange[0],
      marketcap_max: filters.marketCapRange[1],
      score_min: filters.projectScoreRange[0],
      score_max: filters.projectScoreRange[1],
      selectedcategories: filters.categories.length > 0 ? filters.categories : null,
      selectedchains: filters.chains.length > 0 ? filters.chains : null,
    };
    
    console.log("Applying filters:", apiFilters);
    fetchData(apiFilters);
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Check for horizontal scroll
  useEffect(() => {
    const checkForHorizontalScroll = () => {
      if (tableContainerRef.current) {
        const { scrollWidth, clientWidth } = tableContainerRef.current;
        setHasHorizontalScroll(scrollWidth > clientWidth);
      }
    };

    // Initial check
    checkForHorizontalScroll();

    // Add resize listener
    window.addEventListener('resize', checkForHorizontalScroll);

    return () => {
      window.removeEventListener('resize', checkForHorizontalScroll);
    };
  }, [tableCoins]);

  // Filter and sort coins based on search and sortConfig
  const filteredAndSortedCoins = useMemo(() => {
    let result = [...tableCoins];

    // Filter by search
    if (search.trim()) {
      const searchLower = search.toLowerCase().trim();
      result = result.filter(
        (coin) =>
          coin.name.toLowerCase().includes(searchLower) ||
          coin.symbol.toLowerCase().includes(searchLower)
      );
    }

    // Filter by minimum gain percentage
    if (minimumGain !== "Any") {
      // Extract the number from the string (e.g., "> 5%" -> 5)
      const minGainValue = parseFloat(minimumGain.replace(/[^0-9.]/g, ""));
      
      if (!isNaN(minGainValue)) {
        result = result.filter((coin) => {
          // Use sevenDayChange as the gain percentage value
          return coin.sevenDayChange >= minGainValue;
        });
      }
    }

    // Sort based on sortConfig (only if user has selected a sorting option)
    if (sortConfig.key && sortConfig.direction !== false) {
      result = [...result].sort((a, b) => {
        let aValue, bValue;

        switch (sortConfig.key) {
          case "rank":
            aValue = a.rank;
            bValue = b.rank;
            break;
          case "name":
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case "tokenomics":
            aValue = a.tokenomics.score;
            bValue = b.tokenomics.score;
            break;
          case "security":
            aValue = a.security.score;
            bValue = b.security.score;
            break;
          case "social":
            aValue = a.social.score;
            bValue = b.social.score;
            break;
          case "market":
            aValue = a.market.score;
            bValue = b.market.score;
            break;
          case "insights":
            aValue = a.insights.score;
            bValue = b.insights.score;
            break;
          case "totalScore":
            aValue = a.totalScore.score;
            bValue = b.totalScore.score;
            break;
          case "sevenDayChange":
            aValue = a.sevenDayChange;
            bValue = b.sevenDayChange;
            break;
          default:
            aValue = a.rank;
            bValue = b.rank;
        }

        if (sortConfig.direction === "asc") {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }

    return result;
  }, [search, sortConfig, tableCoins, minimumGain]);

  // Calculate total pages
  const totalPages = useMemo(
    () => Math.ceil(filteredAndSortedCoins.length / pageSize),
    [filteredAndSortedCoins.length, pageSize]
  );

  // Get paginated coins
  const paginatedCoins = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredAndSortedCoins.slice(startIndex, startIndex + pageSize);
  }, [filteredAndSortedCoins, currentPage, pageSize]);

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-2 mb-8">
        <h1 className="text-2xl font-bold tracking-tight text-primary">{t("topMovers.title")}</h1>
        <p className="text-base text-muted-foreground">
          {t("topMovers.description")}
        </p>
      </div>

      {/* Filter cards are removed, now using the dialog-based filters instead */}

      <Card className="mt-6 max-w-[1600px] mx-auto w-full overflow-hidden">
        <CardHeader className="border-b border-border/40 px-6">
          <div className="flex flex-col lg:flex-row gap-4 justify-between">
            <div>
              <CardTitle>{t("topMovers.allCoins")}</CardTitle>
              <CardDescription>
                {t("topMovers.clickForAnalysis")}
              </CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Input
placeholder={t("topMovers.searchPlaceholder")}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-[260px]"
              />
              <div className="flex">
                <Button 
                  onClick={() => setFilterDialogOpen(true)} 
                  className="h-10 px-4 bg-[#132F4C]/50 border-0 text-[#E7EBF0]/80 hover:bg-[#132F4C]/70 hover:text-[#66B2FF]"
                >
                  <Filter className="w-4 h-4" />
<span className="mx-2">{t("topMovers.filters")}</span>
                </Button>
                <CreateAlertButton className="ml-2 h-10 px-4 bg-[#132F4C]/50 border-0 text-[#E7EBF0]/80 hover:bg-[#132F4C]/70 hover:text-[#66B2FF]" />
              </div>
              
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="scrollable-container relative group/scroll w-full mt-2 overflow-hidden">
            {hasHorizontalScroll && (
              <>
                <div className="absolute left-2 top-[45%] z-10 opacity-0 group-hover/scroll:opacity-100 transition-opacity duration-300 bg-[#132F4C]/80 rounded-full p-1 text-white/80 shadow-md">
                  <ChevronLeft className="h-5 w-5" />
                </div>
                <div className="absolute right-2 top-[45%] z-10 opacity-0 group-hover/scroll:opacity-100 transition-opacity duration-300 bg-[#132F4C]/80 rounded-full p-1 text-white/80 shadow-md">
                  <ChevronRight className="h-5 w-5" />
                </div>
              </>
            )}
            <div 
              ref={tableContainerRef}
              className="overflow-hidden w-full rounded-md scrollbar-custom pb-8 mx-auto"
              style={{ 
                display: "grid", 
                gridTemplateColumns: "minmax(0, 1fr)"
              }}>
              <Table
                id="top-movers-table"
                className="w-[100%] min-w-[940px] text-center mx-auto relative"
                responsive={true}
                containerClassName="overflow-hidden">
                <CoinTableHeader 
                  sortConfig={sortConfig}
                  requestSort={requestSort}
                  id="top-movers-header"
                  className="coin-list-header bg-[#132F4C]/90 w-full hover:bg-[#132F4C]/90 backdrop-blur-sm mt-2 sticky top-0 z-10 border-b border-[#1E4976]/50 shadow-md"
                />
                <TableBody className="relative">
                  {isLoading ? (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <div className="flex justify-center items-center">
                          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2"></div>
                          <span className="text-primary-foreground">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : paginatedCoins.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <span className="text-muted-foreground">No coins found</span>
                      </td>
                    </tr>
                  ) : (
                    paginatedCoins.map((coin, index) => (
                      <CoinTableRow
                        key={coin.id}
                        coin={coin}
                        index={index}
                        currentPage={currentPage}
                        pageSize={pageSize}
                        onRowClick={(coinId) => setLocation(`/coin/${coinId}`)}
                      />
                    ))
                  )}
                </TableBody>
              </Table>
              {hasHorizontalScroll && (
                <div
                  id="scroll-hint"
                  className="scrollbar-hint absolute bottom-4 right-1/2 transform translate-x-1/2 opacity-0 group-hover/scroll:opacity-100 transition-opacity duration-300 text-white/60 text-xs bg-[#132F4C]/70 rounded-full px-2 py-1 shadow-md"
                >
                  <span>← Scroll horizontally to see more →</span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <div id="pagination-section" className="mt-8 relative max-w-[1600px] mx-auto w-full">
        <div className="absolute inset-x-0 top-[-10px] h-[1px] bg-[#1E4976]/20"></div>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalItems={filteredAndSortedCoins.length}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          pageSizeOptions={[10, 20, 50, 100]}
          siblingsCount={1}
          className="mt-8 bg-[#0A1929]/10 pt-6 pb-8 rounded-lg"
        />
      </div>
      
      {/* Advanced Filters Dialog */}
      <Dialog open={filterDialogOpen} onOpenChange={setFilterDialogOpen}>
        <DialogContent className="sm:max-w-[95%] max-h-[90vh] overflow-y-auto bg-transparent border-0 p-0">
          <TopMoversFilters
            initialFilters={currentFilters}
            onFilterChange={(filters) => {
              handleApplyFilters(filters);
              setFilterDialogOpen(false);
            }}
            onClose={() => setFilterDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}