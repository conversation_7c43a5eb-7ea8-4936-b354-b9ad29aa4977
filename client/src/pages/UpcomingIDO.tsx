import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { FilterCard } from "@/components/FilterCard";
import {
  coinRowBaseClasses,
  coinRowEvenClass,
  coinRowOddClass,
} from "../styles/coinRowStyles.ts";

import { useLocation } from "wouter";
import { CombinedMetricBadge } from "@/components/CombinedMetricBadge";
import { TotalScoreBadge } from "@/components/TotalScoreBadge";
import { UpcomingTotalScoreBadge } from "@/components/UpcomingTotalScoreBadge";
// import { TotalAIScoreDialogStandalone } from '@/components/TotalAIScoreDialogStandalone';
import { SevenDayScoreBadge } from "@/components/SevenDayScoreBadge";
import { Pagination } from "@/components/Pagination";
import { isTourCompleted, isFirstVisit, markVisited } from "@/lib/tourUtils";
import { cn } from "@/lib/utils";
import { useMediaQuery } from "@/hooks/use-mobile";
import { CreateAlertButton } from "@/components/alerts/CreateAlertButton";
import { ContextAwareWatchlistButton } from "@/components/watchlists/ContextAwareWatchlistButton";
import { CoinLogo } from "@/components/CoinLogo";
import {
  UpcomingFilters,
  UpcomingFiltersState,
} from "@/components/upcoming/UpcomingFilters";
import {
  ScoreBadgeTableFull,
  ProjectData,
  BadgeData,
} from "@/components/ScoreBadgeTableFull";
import { TableBadgeRenderer } from "@/components/TableBadgeRenderer";
// import { UpcomingIdoPageBadgeRenderer } from '@/components/UpcomingIdoPageBadgeRenderer';
import { LaunchDateOpacityWrapper } from "@/components/LaunchDateOpacityWrapper";
import { UpcomingService } from "@/services/UpcomingService";
import { UpcomingCoin } from "@/types/upcoming-coin";
import TableScoreGauge from "@/components/TableScoreGauge";

// Using direct API data without transformation
// Function to convert UpcomingCoin data from API to ProjectData type
const mapApiDataToProjectData = (apiData: UpcomingCoin[]): ProjectData[] => {
  // console.log(
  //   "🔄 Using direct API data, data length:",
  //   apiData.length,
  // );

  return apiData.map((item, index) => {
    return {
      // Temel bilgiler
      id: item.id,
      name: item.name,
      symbol: item.symbol,
      image: item.image || "",
      launchDate: item.launchDate || "TBA",
      launchType: item.launchType || "IDO",
      rank: item.rank || (index + 1),

      // Skorlar - yeni model
      imcScore: {
        score: item.imcScore?.score || 0,
        value: item.imcScore?.status || "Bad"
      },
      financingScore: {
        score: item.financingScore?.score || 0,
        value: item.financingScore?.status || "Bad"
      },
      launchpadScore: {
        score: item.launchpadScore?.score || 0,
        value: item.launchpadScore?.status || "Bad"
      },
      investorScore: {
        score: item.investorScore?.score || 0,
        value: item.investorScore?.status || "Bad"
      },
      socialScore: {
        score: item.socialScore?.score || 0,
        value: item.socialScore?.status || "Bad"
      },
      totalAiScore: {
        score: item.totalAiScore?.score || 0,
        value: item.totalAiScore?.status || "Bad"
      },

      // Icon bileşeni
      icon: (
        <div className="relative w-8 h-8 bg-gradient-to-br from-gray-800 to-gray-900 rounded-full overflow-hidden flex items-center justify-center">
          {item.image ? (
            <img
              src={item.image}
              alt={`${item.symbol} logo`}
              className="w-7 h-7 object-contain"
              crossOrigin="anonymous"
              referrerPolicy="no-referrer"
              loading="lazy"
              onError={(e) => {
                console.log(`🖼️ Görsel yüklenirken hata: ${item.image}`);
                const target = e.target as HTMLImageElement;
                // Hata durumunda sembol adını içeren bir fallback görsel oluştur
                const fallbackImage = getFallbackImageUrl(item.symbol);
                target.src = fallbackImage;

                // Eğer fallback görselinde de hata oluşursa
                target.onerror = () => {
                  target.style.display = "none";
                  const parent = target.parentElement;
                  if (parent) {
                    parent.innerHTML = `<div class="w-full h-full flex items-center justify-center text-xs font-bold text-white">${item.symbol.substring(0, 3)}</div>`;
                  }
                };
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-xs font-bold text-white">
              {item.symbol.substring(0, 3)}
            </div>
          )}
        </div>
      )
    };
  });
};
// React virtualization for optimized long list rendering
import { FixedSizeList as List } from "react-window";
// Import the AI assistant component
import { FloatingAssistant } from "@/components/ui/floating-assistant";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { useEnhancedWatchlist } from "@/hooks/useEnhancedWatchlist";
import { getProxyImageUrl, getFallbackImageUrl } from "@/utils/imageProxy";
import { useLanguage } from "@/contexts/LanguageContext";
// Only import the icons that are actually used in the component
import {
  Star,
  ChevronLeft,
  ChevronRight,
  Rocket,
  Users,
  X,
  Filter,
  FilterX,
  BarChart3,
  Activity,
  Clock,
  ListFilter,
  HelpCircle,
  DollarSign,
  CircleDollarSign,
  Shield,
  Bookmark,
  Lightbulb,
  Search,
  RefreshCw,
  Ticket,
  Link2,
  FolderTree,
  FileText,
} from "lucide-react";
// The hover card and CoinLogo components are already imported above
import {
  LaunchpadBadgeIDO,
  InvestorBadgeIDO,
  RaisedBadgeIDO,
  InitialCapBadgeIDO,
  DateBadgeIDO,
} from "@/components/MonochromeBadges";
import { EnhancedVariationBadge } from "@/components/EnhancedVariationBadge";
import type { BadgeType } from "@/components/EnhancedVariationBadge";
import { RiRobotLine } from "react-icons/ri";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  EnhancedTableHeader,
  HeaderTooltipContent,
} from "@/components/EnhancedTableHeader";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
// Mock veri yerine artık API'den gerçek veriler kullanılıyor
// import { upcomingProjects } from "@/lib/mockData.ts";

interface CoinScore {
  score: number;
  label: string;
  status: "Excellent" | "Good" | "Fair" | "Poor" | "Bad";
}

interface Coin {
  id: string;
  rank: number;
  name: string;
  symbol: string;
  logo?: string; // API'den gelen logo URL'si
  image?: string; // API'den gelen image URL'si (yeni API)
  tokenomics: CoinScore;
  security: CoinScore;
  social: CoinScore;
  market: CoinScore;
  insights: CoinScore;
  totalScore: CoinScore;
  // API'den gelen ek skorlar (async diagramından birebir eşleştirildi)
  financingScore: CoinScore;
  totalAiScore: CoinScore;
  imcScore: CoinScore;
  investorScore: CoinScore;
  socialScore: CoinScore; // API'de socialScore olarak geliyor, launchpadScore ile aynı
  launchpadScore?: CoinScore; // socialScore ile değiştirilebilir
  launchDate?: string; // Formatlı launch date
  launchType?: string; // Launch type (IDO, IEO, ICO, etc.)
  sevenDayChange: number;
  saleType: "IDO" | "IEO" | "ICO" | "SHO" | "Seed" | "IGO" | "ISO";
  demo?: boolean; // Demo proje işaretleyici
}

// Sale type explanation tooltips
const saleTypeExplanations: Record<string, string> = {
  IDO: "Initial DEX Offering - Token sale conducted on a decentralized exchange (DEX)",
  IEO: "Initial Exchange Offering - Token sale hosted on a centralized cryptocurrency exchange",
  ICO: "Initial Coin Offering - First fundraising stage where new projects offer tokens to early investors",
  SHO: "Strong Holder Offering - Token sale giving priority to long-term token holders",
  Seed: "Seed Round - Early private funding round before public sale",
  IGO: "Initial Game Offering - Fundraising focused on blockchain gaming projects",
  ISO: "Initial Stake Offering - Token distribution through staking mechanism",
};

const getCapColor = (score: number) => {
  const baseClasses =
    "px-2 py-0.5 rounded text-[11px] font-medium transition-colors duration-300 text-center min-w-[95px] max-w-[95px] truncate";
  if (score >= 85)
    return `${baseClasses} text-gray-400 group-hover/row:text-emerald-500 bg-gray-400/10 group-hover/row:bg-emerald-500/10 hover:border hover:border-emerald-500/50`;
  if (score >= 75)
    return `${baseClasses} text-gray-400 group-hover/row:text-blue-500 bg-gray-400/10 group-hover/row:bg-blue-500/10 hover:border hover:border-blue-500/50`;
  if (score >= 65)
    return `${baseClasses} text-gray-400 group-hover/row:text-yellow-500 bg-gray-400/10 group-hover/row:bg-yellow-500/10 hover:border hover:border-yellow-500/50`;
  return `${baseClasses} text-gray-400 group-hover/row:text-red-500 bg-gray-400/10 group-hover/row:bg-red-500/10 hover:border hover:border-red-500/50`;
};

const CoinHoverContent = ({ coin }: { coin: Coin }) => {
  const { t } = useLanguage();
  return (
    <div className="flex flex-col space-y-3 p-4 max-w-[320px]">

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <CoinLogo symbol={coin.symbol} size="lg" className="w-6 h-6" />
          <div>
            <div className="font-semibold text-base text-[#E7EBF0]">
              {coin.name}
            </div>
            <div className="text-[#E7EBF0] text-sm font-medium">
              {coin.symbol}
            </div>
          </div>
        </div>
        <div className="px-2 py-1 rounded-md bg-[#132F4C] border border-[#1E4976] text-sm font-semibold text-[#66B2FF]">
          {t("rank", "upcoming").replace("{number}", coin.rank.toString())}
        </div>
      </div>

      <div className="pt-3 border-t border-[#1E4976]">
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex flex-col gap-1 bg-[#132F4C] p-2.5 rounded-md border border-[#1E4976]">
            <span className="text-xs text-[#E7EBF0] font-semibold">
              {t("saleType", "upcoming")}
            </span>
            <div className="flex items-center gap-1.5">
              <span className="text-base font-semibold text-[#E7EBF0] truncate">
                {coin.saleType}
              </span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle
                      size={18}
                      className="text-[#66B2FF]/70 cursor-help"
                    />
                  </TooltipTrigger>
                  <TooltipContent
                    side="top"
                    align="center"
                    className="max-w-[300px] p-3 text-xs"
                  >
                    <p className="font-medium whitespace-normal">
                      {saleTypeExplanations[coin.saleType]}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <div className="flex flex-col gap-1 bg-[#132F4C] p-2.5 rounded-md border border-[#1E4976]">
            <span className="text-xs text-[#E7EBF0] font-semibold">
              {t("totalAiScore", "upcoming")}
            </span>
            <span className="text-base font-semibold text-[#E7EBF0] truncate">
              {coin.totalScore.score} {t("points", "upcoming")}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-2 w-full">
          <div className="flex flex-col items-center">
            <div className="text-[10px] text-[#E7EBF0] text-center font-medium">
              {t("tokenomics", "upcoming")}
            </div>
            <div
              className={cn(
                "text-xs font-semibold whitespace-nowrap",
                coin.tokenomics.score >= 85
                  ? "text-emerald-500"
                  : "text-[#66B2FF]",
              )}
            >
              {coin.tokenomics.score}
            </div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-[10px] text-[#E7EBF0] text-center font-medium">
              {t("security", "upcoming")}
            </div>
            <div
              className={cn(
                "text-xs font-semibold whitespace-nowrap",
                coin.security.score >= 85
                  ? "text-emerald-500"
                  : "text-[#66B2FF]",
              )}
            >
              {coin.security.score}
            </div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-[10px] text-[#E7EBF0] text-center font-medium">
              {t("social", "upcoming")}
            </div>
            <div
              className={cn(
                "text-xs font-semibold whitespace-nowrap",
                coin.social.score >= 85 ? "text-emerald-500" : "text-[#66B2FF]",
              )}
            >
              {coin.social.score}
            </div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-[10px] text-[#E7EBF0] text-center font-medium">
              {t("market", "upcoming")}
            </div>
            <div
              className={cn(
                "text-xs font-semibold whitespace-nowrap",
                coin.market.score >= 85 ? "text-emerald-500" : "text-[#66B2FF]",
              )}
            >
              {coin.market.score}
            </div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-[10px] text-[#E7EBF0] text-center font-medium">
              {t("insights", "upcoming")}
            </div>
            <div
              className={cn(
                "text-xs font-semibold whitespace-nowrap",
                coin.insights.score >= 85
                  ? "text-emerald-500"
                  : "text-[#66B2FF]",
              )}
            >
              {coin.insights.score}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function UpcomingIDO() {
  const [, setLocation] = useLocation();
  
  // Add error boundary for watchlist context
  let watchlistState: any = {};
  try {
    watchlistState = useEnhancedWatchlist();
  } catch (error) {
    console.error("Error accessing watchlist context:", error);
    // Provide fallback values
    watchlistState = {
      isInIDOWatchlist: () => false,
      toggleIDOWatchlistItem: () => {},
      idoWatchlists: []
    };
  }
  
  const { isInIDOWatchlist, toggleIDOWatchlistItem, idoWatchlists } = watchlistState;
  const { t } = useLanguage(); // Import translation function

  // Log to show IDO watchlists are being used
  console.log(t("logs.usingIdoWatchlists"), idoWatchlists);
  const [loading, setLoading] = useState(true);
  const [apiError, setApiError] = useState<Error | null>(null);
  const [coins, setCoins] = useState<Coin[]>([]);

  // UpcomingIDO için veri çekme fonksiyonu - get_upcoming_idos_v2 API endpoint'ini kullanır
  const fetchUpcomingProjects = async () => {
    try {
      setLoading(true);
      setApiError(null);

      // Log for starting API fetch
      console.log(
        t("logs.fetchInitialDataStarted")
      );

      // UpcomingService'in V2 metodunu kullanarak veri çekme
      const upcomingProjects = await UpcomingService.getUpcomingProjectsV2();

      // Debug için veri kontrol
      console.log(t("logs.rawAPIData"), upcomingProjects);
      console.log(t("logs.apiDataLength"), upcomingProjects?.length);

      if (upcomingProjects && upcomingProjects.length > 0) {
        console.log(
          t("logs.fetchInitialDataCompleted"),
        );

        // API'den gelen UpcomingCoin verisini Coin tipine dönüştür
        if (upcomingProjects.length > 0) {
          console.log(
            t("logs.firstRecordDetails"),
            JSON.stringify(upcomingProjects[0], null, 2),
          );

          // İlk birkaç projenin image URL'lerini göster
          console.log(t("logs.imageUrlChecks"));
          upcomingProjects.slice(0, 5).forEach((project, index) => {
            console.log(
              `Proje ${index + 1} (${project.name}) - Image: ${project.image}`,
            );
          });

          // API'den gelen proje bilgilerini ayrıntılı olarak göster (debug)
          console.log(t("logs.allProjectsFromAPI"));
          upcomingProjects.forEach((project, index) => {
            console.log(`${index}. ${project.name} (${project.symbol}):`, {
              id: project.id,
              image: project.image,
              launchDate: project.launchDate,
            });
          });
        }

        // Oluşturduğumuz yeni doğrudan dönüşüm fonksiyonunu kullan
        // Bu yeni fonksiyon API'den gelen verileri olduğu gibi kullanır
        console.log(t("logs.usingDirectAPI"));

        // API'den gelen verilerin ilk birkaçını göster
        console.log(t("logs.rawAPIDataFirst5"));
        upcomingProjects.slice(0, 5).forEach((item, index) => {
          console.log(`${index + 1}. ${item.name} (${item.symbol}):`, {
            id: item.id,
            image: item.image,
            launchDate: item.launchDate,
            imcScore: item.imcScore,
            socialScore: item.socialScore,
          });
        });

        // API'den gelen veriyi doğrudan dönüştür ve Coins state'i güncelle
        const apiDataMapped = mapApiDataToProjectData(upcomingProjects);

        // UpcomingCoin'i Coin tipine dönüştür
        const convertedCoins: Coin[] = upcomingProjects.map((project, index) => ({
          id: project.id,
          rank: project.rank || index + 1,
          name: project.name,
          symbol: project.symbol,
          image: project.image,
          launchDate: project.launchDate,
          launchType: project.launchType,
          demo: project.demo || false,
          // Mock skorlar - gerçek uygulamada API'den gelecek
          tokenomics: { score: project.imcScore?.score || 0, label: "Tokenomics", status: project.imcScore?.status || "Bad" },
          security: { score: project.financingScore?.score || 0, label: "Security", status: project.financingScore?.status || "Bad" },
          social: { score: project.socialScore?.score || 0, label: "Social", status: project.socialScore?.status || "Bad" },
          market: { score: project.launchpadScore?.score || 0, label: "Market", status: project.launchpadScore?.status || "Bad" },
          insights: { score: project.investorScore?.score || 0, label: "Insights", status: project.investorScore?.status || "Bad" },
          totalScore: { score: project.totalAiScore?.score || 0, label: "Total", status: project.totalAiScore?.status || "Bad" },
          financingScore: { score: project.financingScore?.score || 0, label: "Financing", status: project.financingScore?.status || "Bad" },
          totalAiScore: { score: project.totalAiScore?.score || 0, label: "AI Score", status: project.totalAiScore?.status || "Bad" },
          imcScore: { score: project.imcScore?.score || 0, label: "IMC", status: project.imcScore?.status || "Bad" },
          investorScore: { score: project.investorScore?.score || 0, label: "Investor", status: project.investorScore?.status || "Bad" },
          socialScore: { score: project.socialScore?.score || 0, label: "Social", status: project.socialScore?.status || "Bad" },
          launchpadScore: { score: project.launchpadScore?.score || 0, label: "Launchpad", status: project.launchpadScore?.status || "Bad" },
          sevenDayChange: 0, // Mock değer
          saleType: (project.launchType as any) || "IDO",
        }));

        // Coins state'i güncelleniyor
        setCoins(convertedCoins);

        // Debug için coins state'inin ne zaman güncellendiğini görmek için
        console.log(
          "🔵 Coins state güncellendi, uzunluk:",
          convertedCoins.length,
        );
      } else {
        console.warn("⚠️ V2 API'den yüklenen proje sayısı 0");
        // API verisi boşsa boş dizi kullan - mock veri kullanmıyoruz
        setCoins([]);
        console.log(t("logs.emptyAPIResult"));
      }
    } catch (error) {
      console.error("❌ Upcoming projects V2 fetch error:", error);
      setApiError(
        error instanceof Error
          ? error
          : new Error("V2 API'den veri çekilirken bir hata oluştu."),
      );
      // Hata durumunda boş dizi kullan - mock veri kullanmıyoruz
      setCoins([]);
      console.log(t("logs.errorUsingEmptyArray"));
    } finally {
      setLoading(false);
    }
  };
  const [search, setSearch] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [saleTypeFilter, setSaleTypeFilter] = useState<string>("all");
  const [launchpadFilter, setLaunchpadFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [blockchainFilter, setBlockchainFilter] = useState<string>("all");
  const [investorFilter, setInvestorFilter] = useState<string>("all");

  // Filters dialog state
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState<UpcomingFiltersState>({
    projectScoreRange: [0, 100],
    categories: [],
    chains: [],
    saleType: "all", // Default to all sale types
    listingDate: "30", // Default to 30 days
  });

  // Total AI Score dialog state
  const [totalAIScoreDialogOpen, setTotalAIScoreDialogOpen] = useState(false);
  const [selectedTotalAIScore, setSelectedTotalAIScore] = useState(0);

  // Handler function for Total AI Score dialog
  const handleTotalAIScoreClick = useCallback((id: string, score: number) => {
    console.log(t("logs.totalAIScoreClicked"), id, score);
    setSelectedTotalAIScore(score);
    setTotalAIScoreDialogOpen(true);
  }, []);

  // Handler function for Filters
  const handleFilterChange = useCallback(
    (newFilters: UpcomingFiltersState) => {
      console.log(t("logs.filtersChanged"), newFilters);
      setFilters(newFilters);

      // Sync Sale Type filter with standalone filter if it's changed
      if (newFilters.saleType && newFilters.saleType !== filters.saleType) {
        // Update the standalone saleTypeFilter too for visual consistency
        setSaleTypeFilter(newFilters.saleType);
      }

      // Apply the advanced filters
      // Here you would need to update your coin filtering logic to include these advanced filters
    },
    [filters.saleType],
  );

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);

  // Sorting state for ScoreBadgeTableFull
  type SortField = "name" | "launchDate" | "imcScore" | "financingScore" | "launchpadScore" | "investorScore" | "socialScore" | "totalAiScore";
  type SortDirection = "asc" | "desc";
  const [tableSortField, setTableSortField] = useState<SortField>("totalAiScore");
  const [tableSortDirection, setTableSortDirection] = useState<SortDirection>("desc");

  // Old sorting state (keeping for compatibility)
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc" | false;
  }>({
    key: "",
    direction: false,
  });

  // Tour page visit detection
  const TOUR_ID = "upcoming-ido-tour";

  // First-time visit detection for tour
  useEffect(() => {
    console.log("🔄 UpcomingIDO - İlk useEffect tetiklendi");

    // API verisi çek
    const fetchInitialData = async () => {
      console.log("🔄 fetchInitialData başladı");
      await fetchUpcomingProjects();
      console.log("🔄 fetchInitialData tamamlandı");
    };

    fetchInitialData();

    // Check if this is the first visit to trigger the tour
    if (isFirstVisit(TOUR_ID) && !isTourCompleted(TOUR_ID)) {
      setTimeout(() => {
        const event = new CustomEvent("show-upcoming-ido-tour");
        document.dispatchEvent(event);
      }, 1200); // Match the delay used in CoinList
    }

    // Mark as visited
    markVisited(TOUR_ID);
  }, []);

  // Debounce search input with optimized timing (500ms is a good balance)
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Badge skorları gerçekten doğrudan API'den alınıyor
  const getBadgeScores = useCallback((coin: Coin) => {
    return {
      initialCap: coin.imcScore?.score || 0,
      raised: coin.financingScore?.score || 0,
      launchpad: coin.launchpadScore?.score || 0,
      investors: coin.investorScore?.score || 0,
      date: coin.totalAiScore?.score || 0,
    };
  }, []);

  // Optimize search matching function with memoization
  const searchMatcher = useCallback((searchText: string, coin: Coin) => {
    if (searchText === "") return true;
    const lowerSearch = searchText.toLowerCase();
    return (
      coin.name.toLowerCase().includes(lowerSearch) ||
      coin.symbol.toLowerCase().includes(lowerSearch)
    );
  }, []);

  // Handle sort requests for table
  const handleTableSortChange = useCallback((field: SortField, direction: SortDirection) => {
    setTableSortField(field);
    setTableSortDirection(direction);
    setCurrentPage(1); // Reset to first page when sorting changes
  }, []);

  // Handle sort requests (old method - keeping for compatibility)
  const requestSort = useCallback((key: string) => {
    setSortConfig((prevState) => {
      // If clicking the same column that's already sorted
      if (prevState.key === key) {
        if (prevState.direction === "asc") {
          return { key, direction: "desc" };
        } else if (prevState.direction === "desc") {
          return { key, direction: false }; // Third click - remove sorting
        } else {
          return { key, direction: "asc" }; // If no direction, set to ascending
        }
      }
      // If clicking a new column, default to ascending
      return { key, direction: "asc" };
    });
  }, []);

  // Filter and sort coins based on all selected filters - highly optimized
  const filteredCoins = useMemo(() => {
    // Debug log for coins state
    console.log("🔍 Filter çalışıyor, coins uzunluğu:", coins?.length || 0);

    // Coins null veya boş ise boş dizi döndür
    if (!coins || !Array.isArray(coins) || coins.length === 0) {
      console.warn("⚠️ filteredCoins hesaplanırken coins dizisi boş");
      return [];
    }

    // First apply all filters
    let result = coins;

    // Apply filters if any are active
    if (
      debouncedSearch !== "" ||
      saleTypeFilter !== "all" ||
      launchpadFilter !== "all" ||
      categoryFilter !== "all" ||
      blockchainFilter !== "all" ||
      investorFilter !== "all" ||
      // Check if any filters are active
      filters.projectScoreRange[0] > 0 ||
      filters.projectScoreRange[1] < 100 ||
      filters.categories.length > 0 ||
      filters.chains.length > 0 ||
      (filters.saleType && filters.saleType !== "all") ||
      filters.listingDate !== "30" // Default is 30, consider active if different
    ) {
      result = coins.filter((coin) => {
        // Search filter - use memoized matcher for better performance
        const matchesSearch = searchMatcher(debouncedSearch, coin);

        // Only check other filters if search matches
        if (!matchesSearch) return false;

        // Sale type filter - check both standalone filter and advanced filter
        // First check the standalone filter
        if (saleTypeFilter !== "all" && coin.saleType !== saleTypeFilter) {
          return false;
        }

        // Then check the advanced filter from UpcomingFilters
        if (
          filters.saleType &&
          filters.saleType !== "all" &&
          coin.saleType !== filters.saleType
        ) {
          return false;
        }

        // For demo purposes only - in a real app these would filter based on actual coin properties
        // Only run these checks when the filter isn't 'all'
        if (launchpadFilter !== "all") {
          const matchesLaunchpad = true; // Replace with actual implementation
          if (!matchesLaunchpad) return false;
        }

        if (categoryFilter !== "all") {
          const matchesCategory = true; // Replace with actual implementation
          if (!matchesCategory) return false;
        }

        if (blockchainFilter !== "all") {
          const matchesBlockchain = true; // Replace with actual implementation
          if (!matchesBlockchain) return false;
        }

        if (investorFilter !== "all") {
          const matchesInvestor = true; // Replace with actual implementation
          if (!matchesInvestor) return false;
        }

        // Apply filters
        // Project Score Range filter
        if (
          filters.projectScoreRange[0] > 0 ||
          filters.projectScoreRange[1] < 100
        ) {
          const totalScore = coin.totalScore?.score || 0;
          if (
            totalScore < filters.projectScoreRange[0] ||
            totalScore > filters.projectScoreRange[1]
          ) {
            return false;
          }
        }

        // Categories filter
        if (filters.categories.length > 0) {
          // This is a placeholder implementation
          // You would need to implement the actual category filtering based on your data structure
          const coinCategories: string[] = []; // Replace with actual coin categories
          const matchesCategory = filters.categories.some(
            (categoryId: string) => coinCategories.includes(categoryId),
          );
          if (!matchesCategory) return false;
        }

        // Chains filter
        if (filters.chains.length > 0) {
          // This is a placeholder implementation
          // You would need to implement the actual chain filtering based on your data structure
          const coinChain = ""; // Replace with actual coin chain
          const matchesChain = filters.chains.includes(coinChain);
          if (!matchesChain) return false;
        }

        // Listing Date filter
        if (filters.listingDate && filters.listingDate !== "30") {
          // This is a placeholder implementation
          // You would need to implement the actual listing date filtering based on your data structure
          // For example, checking if the coin's launch date is within the selected time period
          const daysAgo = parseInt(filters.listingDate, 10);
          const currentDate = new Date();
          const listingDate = new Date(coin.launchDate || ""); // Adjust based on your data structure

          // Calculate the difference in days
          const diffTime = Math.abs(
            currentDate.getTime() - listingDate.getTime(),
          );
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          if (diffDays > daysAgo) {
            return false;
          }
        }

        // If we get here, all filters passed
        return true;
      });
    }

    // Then apply sorting if active
    if (sortConfig.direction) {
      const { key, direction } = sortConfig;

      result = [...result].sort((a, b) => {
        // Extract the values to compare based on key
        let aValue, bValue;

        switch (key) {
          case "rank":
            aValue = a.rank;
            bValue = b.rank;
            break;
          case "name":
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case "symbol":
            aValue = a.symbol.toLowerCase();
            bValue = b.symbol.toLowerCase();
            break;
          case "tokenomics":
            aValue = a.tokenomics.score;
            bValue = b.tokenomics.score;
            break;
          case "security":
            aValue = a.security.score;
            bValue = b.security.score;
            break;
          case "social":
            aValue = a.social.score;
            bValue = b.social.score;
            break;
          case "market":
            aValue = a.market.score;
            bValue = b.market.score;
            break;
          case "initialCap":
            // For sample purposes, we're using tokenomics score since we don't have real initialCap data
            aValue = a.tokenomics.score;
            bValue = b.tokenomics.score;
            break;
          case "fundsRaised":
            // For sample purposes, we're using security score since we don't have real fundsRaised data
            aValue = a.security.score;
            bValue = b.security.score;
            break;
          case "launchpad":
            // For sample purposes, we're using social score since we don't have real launchpad data
            aValue = a.social.score;
            bValue = b.social.score;
            break;
          case "investors":
            // For sample purposes, we're using market score since we don't have real investors data
            aValue = a.market.score;
            bValue = b.market.score;
            break;
          case "score":
            aValue = a.totalScore.score;
            bValue = b.totalScore.score;
            break;
          case "launchDate":
            // For sample purposes, we're using the totalScore for now
            aValue = a.totalScore.score;
            bValue = b.totalScore.score;
            break;
          case "insights":
            aValue = a.insights.score;
            bValue = b.insights.score;
            break;
          case "totalScore":
            aValue = a.totalScore.score;
            bValue = b.totalScore.score;
            break;
          case "sevenDayChange":
            aValue = a.sevenDayChange;
            bValue = b.sevenDayChange;
            break;
          case "saleType":
            aValue = a.saleType;
            bValue = b.saleType;
            break;
          default:
            // Default to rank for unknown keys
            aValue = a.rank;
            bValue = b.rank;
        }

        // Conditional comparison logic
        if (typeof aValue === "string" && typeof bValue === "string") {
          return direction === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        } else {
          // For numbers or other comparable types
          return direction === "asc"
            ? aValue > bValue
              ? 1
              : -1
            : aValue < bValue
              ? 1
              : -1;
        }
      });
    }

    return result;
  }, [
    coins, // ÖNEMLİ: coins state'i değiştiğinde filteredCoins'i yeniden hesapla
    debouncedSearch,
    saleTypeFilter,
    launchpadFilter,
    categoryFilter,
    blockchainFilter,
    investorFilter,
    searchMatcher,
    sortConfig,
    filters, // Include filters in dependencies
  ]);

  // New sorted and filtered coins with table sorting
  const sortedAndFilteredCoins = useMemo(() => {
    let result = [...filteredCoins];

    // Apply table sorting
    if (tableSortField && tableSortDirection) {
      // Önce demo ve gerçek projeleri ayır
      const realProjects = result.filter(coin => !coin.demo);
      const demoProjects = result.filter(coin => coin.demo);

      // Gerçek projeleri sırala
      const sortedRealProjects = [...realProjects].sort((a, b) => {
        let aValue, bValue;

        switch (tableSortField) {
          case "name":
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case "launchDate":
            aValue = a.launchDate || "";
            bValue = b.launchDate || "";
            break;
          case "imcScore":
            aValue = a.imcScore?.score || 0;
            bValue = b.imcScore?.score || 0;
            break;
          case "financingScore":
            aValue = a.financingScore?.score || 0;
            bValue = b.financingScore?.score || 0;
            break;
          case "launchpadScore":
            aValue = a.launchpadScore?.score || 0;
            bValue = b.launchpadScore?.score || 0;
            break;
          case "investorScore":
            aValue = a.investorScore?.score || 0;
            bValue = b.investorScore?.score || 0;
            break;
          case "socialScore":
            aValue = a.socialScore?.score || 0;
            bValue = b.socialScore?.score || 0;
            break;
          case "totalAiScore":
            aValue = a.totalAiScore?.score || 0;
            bValue = b.totalAiScore?.score || 0;
            break;
          default:
            aValue = a.rank || 0;
            bValue = b.rank || 0;
        }

        if (typeof aValue === "string" && typeof bValue === "string") {
          return tableSortDirection === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        } else {
          return tableSortDirection === "asc"
            ? (aValue as number) - (bValue as number)
            : (bValue as number) - (aValue as number);
        }
      });

      // Demo projeleri de sırala (aynı mantıkla)
      const sortedDemoProjects = [...demoProjects].sort((a, b) => {
        let aValue, bValue;

        switch (tableSortField) {
          case "name":
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case "launchDate":
            aValue = a.launchDate || "";
            bValue = b.launchDate || "";
            break;
          case "imcScore":
            aValue = a.imcScore?.score || 0;
            bValue = b.imcScore?.score || 0;
            break;
          case "financingScore":
            aValue = a.financingScore?.score || 0;
            bValue = b.financingScore?.score || 0;
            break;
          case "launchpadScore":
            aValue = a.launchpadScore?.score || 0;
            bValue = b.launchpadScore?.score || 0;
            break;
          case "investorScore":
            aValue = a.investorScore?.score || 0;
            bValue = b.investorScore?.score || 0;
            break;
          case "socialScore":
            aValue = a.socialScore?.score || 0;
            bValue = b.socialScore?.score || 0;
            break;
          case "totalAiScore":
            aValue = a.totalAiScore?.score || 0;
            bValue = b.totalAiScore?.score || 0;
            break;
          default:
            aValue = a.rank || 0;
            bValue = b.rank || 0;
        }

        if (typeof aValue === "string" && typeof bValue === "string") {
          return tableSortDirection === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        } else {
          return tableSortDirection === "asc"
            ? (aValue as number) - (bValue as number)
            : (bValue as number) - (aValue as number);
        }
      });

      // Gerçek projeler önce, demo projeler sonra
      result = [...sortedRealProjects, ...sortedDemoProjects];
    }

    return result;
  }, [filteredCoins, tableSortField, tableSortDirection]);

  // Calculate total pages - memoized to prevent recalculation
  const totalPages = useMemo(
    () => Math.ceil(sortedAndFilteredCoins.length / pageSize),
    [sortedAndFilteredCoins.length, pageSize],
  );

  // Reset to page 1 when filters or sort change
  useEffect(() => {
    setCurrentPage(1);
  }, [
    debouncedSearch,
    saleTypeFilter,
    launchpadFilter,
    categoryFilter,
    blockchainFilter,
    investorFilter,
    pageSize,
    sortConfig,
    tableSortField,
    tableSortDirection,
    filters, // Include filters here too
  ]);

  // Use a more efficient pagination approach with memoization
  const paginatedCoins = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, sortedAndFilteredCoins.length);
    const result = sortedAndFilteredCoins.slice(startIndex, endIndex);

    console.log(t("logs.paginatedData"), result.length);
    console.log(t("logs.first2Records"), JSON.stringify(result.slice(0, 2), null, 2));

    // Sayfalanmış verilerde image alanlarını kontrol et
    console.log(t("logs.paginatedDataImageFields"));
    result.slice(0, 5).forEach((coin, index) => {
      console.log(
        `Sayfalanan coin ${index + 1} (${coin.name}) - Image: ${coin.image}`,
      );
    });

    return result;
  }, [sortedAndFilteredCoins, currentPage, pageSize]);

  // Using the shared TableBadgeRenderer component

  // Convert Coin type to ProjectData type for the shared ScoreBadgeTableFull component
  const convertToProjectData = useCallback((coins: Coin[]): ProjectData[] => {
    console.log(
      "🔄 convertToProjectData çağrıldı, veri uzunluğu:",
      coins?.length,
    );

    if (!coins || !Array.isArray(coins) || coins.length === 0) {
      console.error(
        "⚠️ convertToProjectData fonksiyonuna boş veya geçersiz bir coins dizisi geldi:",
        coins,
      );
      return [];
    }

    const result = coins.map((coin, index) => {
      // İlk kaydı ayrıntılı loglayalım
      if (index === 0) {
        console.log(t("logs.firstRecordDetails"), JSON.stringify(coin, null, 2));
        // Özellikle socialScore değerini kontrol edelim
        console.log(t("logs.firstRecordSocialScore"), coin.socialScore);
      }

      return {
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol,
        image: coin.image || "",
        icon: coin.image ? (
          <img
            src={coin.image}
            alt={`${coin.symbol} logo`}
            className="w-7 h-7 object-contain rounded-full"
            loading="lazy"
            crossOrigin="anonymous"
            referrerPolicy="no-referrer"
            onError={(e) => {
              console.log(
                `🖼️ Icon için görsel yüklenirken hata: ${coin.image}`,
              );
              const target = e.target as HTMLImageElement;
              target.style.display = "none";
              const parent = target.parentElement;
              if (parent) {
                parent.innerHTML = `<div class="w-full h-full flex items-center justify-center text-xs font-bold text-white">${coin.symbol.substring(0, 3)}</div>`;
              }
            }}
          />
        ) : (
          <div className="w-7 h-7 flex items-center justify-center bg-gray-800 rounded-full">
            <span className="text-xs font-bold text-white">
              {coin.symbol.substring(0, 3)}
            </span>
          </div>
        ),
        // API'den gelen değerleri doğrudan kullan - hiç değiştirmeden
        imcScore: {
          score: coin.imcScore?.score || 0,
          value: coin.imcScore?.status || "N/A",
        },
        financingScore: {
          score: coin.financingScore?.score || 0,
          value: coin.financingScore?.status || "N/A",
        },
        launchpadScore: {
          score: coin.launchpadScore?.score || 0,
          value: coin.launchpadScore?.status || "N/A",
        },
        investorScore: {
          score: coin.investorScore?.score || 0,
          value: coin.investorScore?.status || "N/A",
        },
        socialScore: {
          score: coin.socialScore?.score || 0,
          value: coin.socialScore?.status || "N/A",
        },
        totalAiScore: {
          score: coin.totalAiScore?.score || 0,
          value: coin.totalAiScore?.status || "N/A",
        },
        launchDate: coin.launchDate || "TBA",
        launchType: coin.launchType || "IDO",
        rank: coin.rank || index + 1,
        originalId: coin.id,
        demo: coin.demo || false, // Demo property'sini ekle
        // additionalData alanını kaldırdık - artık gerekli değil
      };
    });

    return result;
  }, []);

  // API'den gelen değerler doğrudan kullanılıyor, yardımcı fonksiyonlara gerek yok

  // Use IntersectionObserver for lazy loading images and animations
  const observerRef = useRef<IntersectionObserver | null>(null);
  const observedRowsRef = useRef<Set<string>>(new Set());

  // Setup IntersectionObserver for efficient rendering and animations
  useEffect(() => {
    // Cleanup existing observer
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create a new IntersectionObserver to detect when rows enter viewport
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const coinId = entry.target.getAttribute("data-coin-id");
          if (coinId) {
            if (entry.isIntersecting) {
              // Add to set of visible rows
              observedRowsRef.current.add(coinId);
            } else {
              // Optionally remove from set when no longer visible
              // observedRowsRef.current.delete(coinId);
            }
          }
        });
      },
      {
        root: null,
        rootMargin: "100px", // Load rows a bit before they enter viewport
        threshold: 0.1, // Trigger when at least 10% of the row is visible
      },
    );

    // Observe all coin rows
    const rows = document.querySelectorAll(".coin-row");
    rows.forEach((row) => {
      if (observerRef.current) {
        observerRef.current.observe(row);
      }
    });

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [paginatedCoins]); // Re-create when the visible coins change

  // Optimize row state tracking with shared hover state approach
  const [hoveredRowId, setHoveredRowId] = useState<string | null>(null);

  // Memoize the isRowVisible function
  const isRowVisible = useCallback((coinId: string) => {
    return observedRowsRef.current.has(coinId);
  }, []);

  // End of helper functions

  // Translation hook already imported at the top of the component

  return (
    <div className="container mx-auto py-4 sm:py-6">

      <div className="flex flex-col gap-2">
        <div id="top"></div>
        <h1 className="text-2xl font-bold tracking-tight text-primary">
          {t("upcoming.title")}
        </h1>
        <p className="text-base text-muted-foreground">
          {t("upcoming.subtitle")}
        </p>
      </div>
      {/* Filtreleme kutuları kaldırıldı */}
      <Card className="mt-4 md:mt-6 bg-card/100 backdrop-blur-sm border-border/50 transition-all duration-300 hover:border-border/70 hover:bg-card/60 hover:shadow-[0_0_20px_rgba(30,73,118,0.15)] hover:translate-y-[-1px] relative">
        <CardHeader className="border-b border-border/40 p-3 sm:p-6 pb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
            <div>
              <CardTitle className="text-lg sm:text-xl">{t("common.upcomingIdos")}</CardTitle>
              <CardDescription className="py-1 sm:py-2">
                {t("coinDetailDescription", "coinlist")}
              </CardDescription>
            </div>
            <div className="flex flex-col md:flex-row lg:items-center gap-4">
              <div className="relative w-full sm:w-[280px]">
                <div className="relative flex items-center w-full">
                  <Search className="absolute left-3 h-4 w-4 text-[#66B2FF]/70" />
                  <Input
                    placeholder={t("upcoming.search")}
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className={cn(
                      "search-input upcoming-search-input h-10 w-full bg-[#132F4C]/100 border-[#1E4976]/30 text-[#E7EBF0]/80 placeholder:text-[#E7EBF0]/50 pl-10",
                      "focus:bg-[#132F4C]/70 focus:border-[#1E4976]/50 transition-all duration-300 hover:bg-[#132F4C]/60 hover:border-[#1E4976]/40 rounded-md",
                      "focus:ring-1 focus:ring-[#66B2FF]/30 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[#66B2FF]/30",
                      "transition-all duration-300 pr-8",
                      search !== debouncedSearch &&
                        "shadow-[0_0_0_1px_rgba(0,184,217,0.5)]",
                    )}
                    aria-label="Search coins"
                    id="upcoming-search"
                    type="search"
                    name="search"
                    autoComplete="off"
                  />
                  {/* Visual indicator while debounce is active */}
                  {search !== debouncedSearch ? (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="absolute right-3 top-0 h-full flex items-center justify-center cursor-help">
                          <RefreshCw className="h-3.5 w-3.5 text-[#00B8D9]/70 animate-spin" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        side="right"
                        className="bg-[#132F4C]/95 border border-[#1E4976]/60 text-[#E7EBF0] p-2 text-xs rounded-md backdrop-blur-md"
                      >
                        <p>{t("common.searching")}</p>
                        <p className="text-[10px] text-[#E7EBF0]/70 mt-1">
                          {t("error.dataFetch")}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  ) : (
                    search && (
                      <div
                        className="absolute right-3 top-0 h-full flex items-center justify-center cursor-pointer transition-transform duration-300"
                        onClick={() => setSearch("")}
                        title={t("common.cancel")}
                        aria-label={t("common.cancel")}
                      >
                        <X className="h-3.5 w-3.5 text-[#E7EBF0]/50 hover:text-[#E7EBF0]/80 transition-colors" />
                      </div>
                    )
                  )}
                </div>
              </div>
              <div className="flex gap-4">
                {/* Filters Dialog */}
                <Dialog open={filtersOpen} onOpenChange={setFiltersOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="filter-button filters-button w-full sm:w-auto flex items-center gap-2 h-10 px-4
                      bg-[#132F4C]/100 border-[#1E4976]/30 text-[#E7EBF0]/80
                      hover:bg-[#132F4C]/70 hover:text-[#66B2FF] hover:border-[#1E4976]/50
                      focus:bg-[#132F4C]/70 focus:border-[#1E4976]/50
                      transition-all duration-300 rounded-md"
                      aria-label="Open filters"
                    >
                      <Filter className="h-4 w-4" aria-hidden="true" />
                      {t("upcoming.filters.title")}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl h-[90vh] p-0 bg-transparent backdrop-blur-[3px] border-none shadow-2xl overflow-auto">
                    <DialogTitle className="sr-only">
                      {t("upcoming.filters.title")}
                    </DialogTitle>
                    <div className="sr-only" id="filter-description">
                      {t("upcoming.filters.description")}
                    </div>
                    <UpcomingFilters
                      onFilterChange={handleFilterChange}
                      initialFilters={filters}
                      onClose={() => setFiltersOpen(false)}
                      showListingDateFilter={false}
                    />
                  </DialogContent>
                </Dialog>

                <CreateAlertButton
                  className="h-10 px-4
                  bg-[#132F4C]/100 border-[#1E4976]/30 text-[#E7EBF0]/80
                  hover:bg-[#132F4C]/70 hover:text-[#66B2FF] hover:border-[#1E4976]/50
                  focus:bg-[#132F4C]/70 focus:border-[#1E4976]/50
                  transition-all duration-300 rounded-md"
                  condition="both"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-2 sm:p-6 pt-0">
          <div className="rounded-md mt-2 overflow-x-auto w-full scrollbar-custom">
            {/* Shared ScoreBadgeTableFull Component Implementation */}
            <ScoreBadgeTableFull
              projects={convertToProjectData(paginatedCoins)}
              badgeRenderer={(props) => (
                <TableBadgeRenderer {...props} variant="tableCell" />
              )}
              onViewDetails={(project) => {
                console.log(t("logs.clickedProjectInfo"), project);

                // Doğrudan proje ID'si ile yönlendirme
                console.log(t("logs.redirectingWithDirectID"), project.id);
                setLocation(`/ido/${project.id}`);
              }}
              onTotalAIScoreClick={handleTotalAIScoreClick}
              tableClassName="min-w-[700px] w-full"
              headerClassName="bg-[#132F4C]/100 sticky top-0 z-10 border-b border-[#1E4976]/50"
              rowClassName="category-scores-row border-b border-[#1E4976]/30 min-h-[64px] coin-row ido-row group/row transition-all duration-300"
              isFavorite={(id) => isInIDOWatchlist(id.toString())}
              toggleFavorite={(id) => {
                // Find the actual coin in our dataset to get its proper information
                const targetCoin = paginatedCoins.find(
                  (coin) => coin.id === id.toString(),
                );

                if (targetCoin) {
                  // Create an IDO object with actual data from the coin
                  const idoProject = {
                    id: targetCoin.id,
                    symbol: targetCoin.symbol,
                    name: targetCoin.name,
                    image: targetCoin.image || "",
                    description: "",
                    category: "",
                    price: 0,
                    marketCap: 0,
                    change24h: 0,
                    launchDate:
                      targetCoin.launchDate || new Date().toISOString(),
                  };

                  console.log(
                    `Toggling IDO watchlist for ${targetCoin.name} (${targetCoin.symbol})`,
                    idoProject,
                  );

                  // Toggle the first IDO watchlist if available
                  if (idoWatchlists && idoWatchlists.length > 0) {
                    toggleIDOWatchlistItem(idoProject, idoWatchlists[0].id);
                  } else {
                    console.warn("No IDO watchlists available to toggle");
                  }
                } else {
                  console.error(
                    `Could not find coin with ID: ${id} in paginatedCoins`,
                  );
                }
              }}
              showWatchlist={true}
              showDetailsColumn={false}
              cellClassName="text-center py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/20"
              useEnhancedHeaders={true}
              addColumnOpacity={false}
              dimmedColumnWrapperClassName=""
              currentPage={currentPage}
              pageSize={pageSize}
              allProjects={convertToProjectData(sortedAndFilteredCoins)}
              externalSortField={tableSortField}
              externalSortDirection={tableSortDirection}
              onSortChange={handleTableSortChange}
            />
          </div>
        </CardContent>
      </Card>
      <div
        id="pagination-section"
        className="mt-8 relative"
        data-tour="pagination"
      >
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalItems={filteredCoins.length}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          className="mt-8 bg-[#0A1929]/10 pt-6 pb-8 rounded-lg shadow-sm"
        />
      </div>
    </div>
  );
}
