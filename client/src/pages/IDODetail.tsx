import React, { useState, useC<PERSON>back, useEffect } from "react";
import { use<PERSON>out<PERSON>, <PERSON>, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { motion, AnimatePresence } from "framer-motion";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import IDOService from "@/services/idoService";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import ScoreGauge from "@/components/coin-detail/ScoreGauge";
import {
  ChevronDown,
  ChevronUp,
  ArrowLeft,
  ExternalLink,
  AlertTriangle,
  Book,
  Twitter,
  MessageCircle,
  Github,
  Activity,
  Check,
  AlertOctagon,
  ChevronRight,
  ChevronLeft,
  FileText,
  Sparkles,
  Shield,
  Users,
  BarChart4,
  BarChart3,
  Lightbulb,
  Coins,
  CheckCircle,
  ListFilter,
  HeartPulse,
  List,
  Database,
  LayoutGrid,
  Copy,
} from "lucide-react";
// Import Discord icon for social links
import { FaDiscord as Discord } from "react-icons/fa";
import DetailedAnalysis from "@/components/coin-detail/DetailedAnalysis";
// Import our custom metrics component for IDOs
import { CoinStatus } from "@/types/CoinStatus";
import IMCScoreIcon from "@/components/icons/IMCScoreIcon";
import { StandardTooltip } from "@/components/shared/StandardTooltip";
import { DataDisplay } from "@/components/shared/DataDisplay";
import { useToast } from "@/hooks/use-toast";

// Mock veri kullanımı kaldırıldı - Artık API'den gerçek veriler alınıyor

// Import our custom components
import AllocationSection from "@/components/ido/AllocationSection";
import DistributionProgress from "@/components/ido/DistributionProgress";
import FundingInsights from "@/components/ido/FundingInsights";
import InvestorsTable from "@/components/ido/InvestorsTable";
import NextUnlockEvent from "@/components/ido/NextUnlockEvent";
import PriceProjectionTable from "@/components/ido/PriceProjectionTable";
import VestingSchedule from "@/components/ido/VestingSchedule";
import UnlockEvents from "@/components/ido/UnlockEvents";
import VestingInfo from "@/components/ido/VestingInfo";
import TeamSection from "@/components/ido/TeamSection";
import IDOMetricsBreakdown from "@/components/ido/IDOMetricsBreakdown";

// Helper function to map any status string to a valid CoinStatus
const mapToValidCoinStatus = (status: string): CoinStatus => {
  // Map old terminology to new terminology
  const statusMap: Record<string, CoinStatus> = {
    Excellent: "Excellent",
    Good: "Good",
    Fair: "Fair",
    Poor: "Poor",
    Bad: "Bad",
    Upcoming: "Upcoming",
    New: "New",
    Active: "Active",
    Growing: "Growing",
    Mature: "Mature",
    Declining: "Declining",
  };

  return statusMap[status] || "Fair";
};


export default function IDODetail() {
  const { isLoggedIn } = useAuth();
  const [, setLocation] = useLocation();
  const { t } = useLanguage();

  // Kullanıcı giriş yapmamışsa login sayfasına yönlendir
  useEffect(() => {
    if (!isLoggedIn) {
      // Kullanıcı giriş yaptıktan sonra bu sayfaya geri dönebilmesi için mevcut URL'i de gönder
      const currentPath = window.location.pathname;
      setLocation(`/login?returnTo=${encodeURIComponent(currentPath)}`);
    }
  }, [isLoggedIn, setLocation]);
  const [, params] = useRoute("/ido/:id");
  const id = params?.id || "kinto";
  const { toast } = useToast();

  // State for IDO data and view options
  const [idoData, setIdoData] = useState({} as any);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);  
  const [showAdvancedView, setShowAdvancedView] = useState(false);
  
  // Debug state changes
  useEffect(() => {
    console.log("Advanced view state changed:", showAdvancedView);
  }, [showAdvancedView]);

  // Scroll to top when navigating to this page
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [id]);

  // Fetch IDO data when component mounts or ID changes or when view mode changes
  useEffect(() => {
    const fetchIDOData = async () => {
      if (!id) return;

      setLoading(true);
      setError(null);

      try {
        console.log(
          `Fetching ${showAdvancedView ? "advanced" : "basic"} data for IDO with ID: ${id}`,
        );

        if (id === "kinto") {
          // Artık mock data kullanmak yerine gerçek API'den veri alınacak
          console.log("Fetching Kinto IDO data from API");
          // API'den gerçek Kinto verilerini al
          const data = await IDOService.getIDODetailById(
            "kinto",
            showAdvancedView,
          );
          setIdoData(data);
        } else {
          // For any other ID, fetch real data from API based on view mode
          const data = await IDOService.getIDODetailById(id, showAdvancedView);

          // Minimal processing - keep API data as is, only ensure arrays exist to prevent map errors
          const processedData = {
            ...data,
            // Only ensure arrays are defined to prevent map errors, but don't override null values
            socials: Array.isArray(data.socials) ? data.socials : [],
            contracts: Array.isArray(data.contracts) ? data.contracts : [],
            team: Array.isArray(data.team) ? data.team : [],
            investors: Array.isArray(data.investors) ? data.investors : [],
            tokenAllocation: data.tokenAllocation || [],
            unlockEvents: data.unlockEvents || [],
            tags: Array.isArray(data.tags) ? data.tags : [],
            vestingInfoLinks: Array.isArray(data.vestingInfoLinks)
              ? data.vestingInfoLinks
              : [],
            socialLinks: data.socialLinks || {},
          };

          setIdoData(processedData);
        }
      } catch (err) {
        console.error("Error fetching IDO data:", err);
        setError("Failed to load IDO data. Please try again later.");
        toast({
          title: "Error",
          description: `Failed to load ${showAdvancedView ? "advanced" : "basic"} IDO data.`,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchIDOData();
  }, [id, showAdvancedView, toast]);

  // State for tab navigation
  const [activeTab, setActiveTab] = useState("essentials");
  const [selectedDetailedCategoryIndex, setSelectedDetailedCategoryIndex] =
    useState(0);
  const [selectedContract, setSelectedContract] = useState(
    idoData.contracts && idoData.contracts.length > 0 ? 0 : -1,
  );

  // Modal states for feature requests and error reports
  const [isFeatureModalOpen, setIsFeatureModalOpen] = useState<boolean>(false);
  const [isErrorModalOpen, setIsErrorModalOpen] = useState<boolean>(false);
  const [featureRequest, setFeatureRequest] = useState<string>("");
  const [errorReport, setErrorReport] = useState<string>("");

  // Map status string to CoinStatus enum
  const mappedStatus = mapToValidCoinStatus(idoData.status);

  // Handle advanced view state changes
  const handleAdvancedViewChange = useCallback((isAdvanced: boolean) => {
    setShowAdvancedView(isAdvanced);
  }, []);

  // Animation variants for page transitions
  const pageVariants = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
  };

  // Handler for feature request modal
  const handleRequestFeature = useCallback(() => {
    setIsFeatureModalOpen(true);
  }, []);

  // Handler for error report modal
  const handleReportError = useCallback(() => {
    setIsErrorModalOpen(true);
  }, []);

  // Helper function to handle category selection for detailed analysis
  const handleDetailedCategoryChange = useCallback((index: number) => {
    setSelectedDetailedCategoryIndex(index);
  }, []);

  // Prepare data for DetailedAnalysis component - using standardized score thresholds
  const getScoreName = (score: number): string => {
    if (score >= 90) return "Excellent";
    if (score >= 75) return "Positive";
    if (score >= 65) return "Average";
    if (score >= 50) return "Weak";
    return "Critical";
  };

  // For backward compatibility, keep the old categories for other pages
  const idoCategories = [
    {
      name: "Tokenomics",
      score: 92,
      status: "Excellent" as CoinStatus,
      description: "Tokenomics metrics breakdown",
      weight: 0.25,
      metrics: [
        {
          name: "Market Cap / FDV Ratio",
          value: 80,
          score: 80,
          label: "Good",
          status: "Good" as CoinStatus,
          description:
            "Analyzes the ratio between current market cap and fully diluted valuation to assess token dilution risk.",
          category: "Tokenomics",
        },
        {
          name: "Max Supply",
          value: 100,
          score: 100,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Evaluates whether the project has a fixed maximum supply cap, contributing to scarcity and potential value preservation.",
          category: "Tokenomics",
        },
        {
          name: "Inflation Rate",
          value: 90,
          score: 90,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Measures the rate at which new tokens are created and released into circulation.",
          category: "Tokenomics",
        },
        {
          name: "Token Vesting",
          value: 100,
          score: 100,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Assesses the token release schedule and vesting periods for team, investors, and other stakeholders.",
          category: "Tokenomics",
        },
        {
          name: "Emission Score",
          value: 95,
          score: 95,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Evaluates the sustainability of token emission and distribution mechanisms.",
          category: "Tokenomics",
        },
      ],
    },
    {
      name: "Security",
      score: 95,
      status: "Excellent" as CoinStatus,
      description: "Security metrics breakdown",
      weight: 0.25,
      metrics: [
        {
          name: "Smart Contract Audit",
          value: 95,
          score: 95,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Assessment of smart contract security through thorough code audits by reputable firms.",
          category: "Security",
          methodologyData: {
            title: "Smart Contract Audit",
            whatAreWeScoring:
              "We evaluate the thoroughness and quality of smart contract audits conducted on the project's code. This includes assessing the reputation of auditing firms, the comprehensiveness of audit reports, and the project team's responsiveness to security recommendations.",
            whyIsThisImportant: {
              factors: [
                {
                  name: "Vulnerability detection",
                  explanation:
                    "Professional audits can identify critical vulnerabilities that could lead to loss of funds or contract compromise.",
                },
                {
                  name: "Investor confidence",
                  explanation:
                    "Thorough audits from reputable firms significantly increase investor trust in the project.",
                },
                {
                  name: "Security best practices",
                  explanation:
                    "Audit processes encourage adherence to security standards and best practices in smart contract development.",
                },
              ],
            },
            howDoWeScoreIt:
              "We analyze the number and quality of audits, the reputation of auditing firms, the scope of the audits, and how thoroughly the team has addressed audit findings. Multiple audits from top-tier firms with comprehensive scope and properly addressed findings earn the highest scores.",
            exampleCalculation: {
              example: [
                "Audit by a top-tier firm (e.g., CertiK, Trail of Bits, OpenZeppelin) = 30 points",
                "Comprehensive audit scope covering all critical contract components = 25 points",
                "All critical and high-severity issues resolved = 20 points",
                "Multiple independent audits = 15 points",
                "Public disclosure of audit reports = 10 points",
              ],
            },
            scoringLevels: [
              {
                level: "Excellent (85-100)",
                description:
                  "Multiple thorough audits from top-tier firms with complete resolution of all critical and high issues.",
              },
              {
                level: "Good (70-84)",
                description:
                  "At least one audit from a reputable firm with most critical issues resolved.",
              },
              {
                level: "Fair (50-69)",
                description:
                  "Basic audit with some issues pending resolution or audit from less established firms.",
              },
              {
                level: "Poor (0-49)",
                description:
                  "No professional audit, self-audited only, or major unresolved vulnerabilities.",
              },
            ],
            conclusion:
              "The Smart Contract Audit score is a critical indicator of a project's commitment to security and the safety of user funds. It directly correlates with the reduced risk of exploits and hacks.",
            disclaimer:
              "Smart contract audit scores are based on publicly available information and current best practices. Security is an evolving field, and no audit can guarantee absolute security.",
          },
        },
        {
          name: "Code Repository Quality",
          value: 92,
          score: 92,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Evaluation of code quality, documentation, and development practices in the project repository.",
          category: "Security",
          methodologyData: {
            title: "Code Repository Quality",
            whatAreWeScoring:
              "We assess the overall quality, maintenance, and developmental practices of the project's code repositories. This includes code organization, documentation quality, test coverage, and development activity.",
            whyIsThisImportant: {
              factors: [
                {
                  name: "Development standards",
                  explanation:
                    "High-quality repositories reflect disciplined engineering practices and reduce the likelihood of bugs and security issues.",
                },
                {
                  name: "Maintainability",
                  explanation:
                    "Well-organized and documented code is easier to maintain and less prone to introduce new vulnerabilities during updates.",
                },
                {
                  name: "Community trust",
                  explanation:
                    "Open, active repositories with quality documentation foster trust and enable community review and contributions.",
                },
              ],
            },
            howDoWeScoreIt:
              "We evaluate repository activity metrics, code quality indicators, documentation completeness, test coverage, and adherence to security best practices. Open-source repositories with comprehensive documentation, extensive test suites, and active maintenance receive the highest scores.",
            exampleCalculation: {
              example: [
                "Repository completeness and organization = 25 points",
                "Documentation quality and coverage = 20 points",
                "Test coverage and quality = 20 points",
                "Development activity and contributor diversity = 15 points",
                "Issue management and responsiveness = 10 points",
                "Security practices and configuration = 10 points",
              ],
            },
            scoringLevels: [
              {
                level: "Excellent (85-100)",
                description:
                  "Exceptionally maintained repository with comprehensive documentation, extensive tests, and active development.",
              },
              {
                level: "Good (70-84)",
                description:
                  "Well-organized repository with good documentation, adequate tests, and regular updates.",
              },
              {
                level: "Fair (50-69)",
                description:
                  "Basic repository with minimal documentation, limited testing, or irregular maintenance.",
              },
              {
                level: "Poor (0-49)",
                description:
                  "Poorly organized repository, minimal or outdated documentation, few or no tests, or inactive development.",
              },
            ],
            conclusion:
              "The Code Repository Quality score reflects the project's engineering standards and is a strong predictor of long-term code maintainability and security.",
            disclaimer:
              "Our code repository assessment is based on publicly accessible repositories and development activities. Some projects may have private repositories with different quality metrics.",
          },
        },
        {
          name: "Bug Bounty Program",
          value: 100,
          score: 100,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Assessment of the project's bug bounty program and responsiveness to security vulnerabilities.",
          category: "Security",
          methodologyData: {
            title: "Bug Bounty Program",
            whatAreWeScoring:
              "We evaluate the existence, quality, and effectiveness of the project's bug bounty program. This includes assessing reward structures, scope coverage, responsiveness to reports, and the program's track record.",
            whyIsThisImportant: {
              factors: [
                {
                  name: "Vulnerability discovery",
                  explanation:
                    "Bug bounties incentivize security researchers to find and responsibly disclose vulnerabilities before they can be exploited.",
                },
                {
                  name: "Security commitment",
                  explanation:
                    "A well-funded and well-managed bug bounty program demonstrates a project's commitment to ongoing security.",
                },
                {
                  name: "Resilience",
                  explanation:
                    "Projects with active bug bounty programs tend to have more robust and secure systems over time due to continuous external scrutiny.",
                },
              ],
            },
            howDoWeScoreIt:
              "We assess the program's reward structure, scope coverage, responsiveness to reports, disclosure policy, and historical handling of vulnerability reports. Programs with substantial rewards, comprehensive scope, quick resolution times, and transparent disclosure policies receive the highest scores.",
            exampleCalculation: {
              example: [
                "Reward structure and competitiveness = 25 points",
                "Scope comprehensiveness = 20 points",
                "Response and resolution time = 20 points",
                "Disclosure policy and transparency = 15 points",
                "Program maturity and track record = 20 points",
              ],
            },
            scoringLevels: [
              {
                level: "Excellent (85-100)",
                description:
                  "Comprehensive bug bounty with competitive rewards, full scope coverage, rapid response times, and transparent disclosure.",
              },
              {
                level: "Good (70-84)",
                description:
                  "Established bug bounty with adequate rewards, good scope coverage, and reasonable response times.",
              },
              {
                level: "Fair (50-69)",
                description:
                  "Basic bug bounty with limited rewards, partial scope coverage, or inconsistent response times.",
              },
              {
                level: "Poor (0-49)",
                description:
                  "No bug bounty program, or a program with minimal rewards, very limited scope, or poor responsiveness.",
              },
            ],
            conclusion:
              "The Bug Bounty Program score indicates a project's commitment to ongoing security improvement and its openness to external security review, which correlates with improved long-term security posture.",
            disclaimer:
              "Bug bounty evaluations are based on publicly available information. Some projects may have private bug bounty programs with different parameters than those analyzed here.",
          },
        },
        {
          name: "Security Team Qualifications",
          value: 95,
          score: 95,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Evaluation of the experience, credentials, and track record of the project's security team or security advisors.",
          category: "Security",
          methodologyData: {
            title: "Security Team Qualifications",
            whatAreWeScoring:
              "We assess the expertise, experience, and track record of the individuals responsible for the project's security, including internal security teams, external consultants, and security advisors.",
            whyIsThisImportant: {
              factors: [
                {
                  name: "Security expertise",
                  explanation:
                    "Experienced security professionals can anticipate, identify, and address security challenges more effectively.",
                },
                {
                  name: "Risk mitigation",
                  explanation:
                    "A qualified security team significantly reduces the risk of critical security oversights during development and operations.",
                },
                {
                  name: "Security culture",
                  explanation:
                    "Strong security leadership tends to foster a security-minded culture throughout the entire project development lifecycle.",
                },
              ],
            },
            howDoWeScoreIt:
              "We evaluate the credentials, experience, industry recognition, past work, and continuing education of the security team members. Teams with proven blockchain security expertise, relevant certifications, published research, and histories of successful security work receive the highest scores.",
            exampleCalculation: {
              example: [
                "Security team experience and expertise = 30 points",
                "Relevant credentials and certifications = 20 points",
                "Track record in blockchain security = 25 points",
                "Published research or contributions = 15 points",
                "Continuing education and skill development = 10 points",
              ],
            },
            scoringLevels: [
              {
                level: "Excellent (85-100)",
                description:
                  "Highly experienced security team with specialized blockchain security expertise, strong credentials, and proven track record.",
              },
              {
                level: "Good (70-84)",
                description:
                  "Qualified security professionals with relevant experience and adequate credentials.",
              },
              {
                level: "Fair (50-69)",
                description:
                  "Basic security expertise with limited blockchain-specific experience or credentials.",
              },
              {
                level: "Poor (0-49)",
                description:
                  "No dedicated security personnel, or team members with minimal security qualifications.",
              },
            ],
            conclusion:
              "The Security Team Qualifications score is a key indicator of a project's ability to implement and maintain robust security practices throughout its lifecycle.",
            disclaimer:
              "Security team assessments are based on publicly available information about team members and their credentials. The actual security expertise may differ from our assessment.",
          },
        },
      ],
    },
    {
      name: "Social & Community",
      score: 90,
      status: "Excellent" as CoinStatus,
      description: "Social & Community metrics breakdown",
      weight: 0.2,
      metrics: [
        {
          name: "Community Engagement",
          value: 88,
          score: 88,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Measures level of community engagement across social platforms and community channels.",
          category: "Social",
          methodologyData: {
            title: "Community Engagement",
            whatAreWeScoring:
              "We evaluate the project's ability to build, maintain, and grow an active and supportive community. This includes assessing user interaction quality, community growth trends, and overall community sentiment.",
            whyIsThisImportant: {
              factors: [
                {
                  name: "Project adoption",
                  explanation:
                    "An engaged community typically translates to higher user adoption and long-term project sustainability.",
                },
                {
                  name: "Market resilience",
                  explanation:
                    "Projects with strong communities tend to maintain support during market downturns and recovery periods.",
                },
                {
                  name: "Ecosystem development",
                  explanation:
                    "Active communities contribute to the project ecosystem through development, testing, and evangelism.",
                },
              ],
            },
            howDoWeScoreIt:
              "We analyze metrics across multiple platforms, including member growth rates, daily active users, content engagement, sentiment analysis, and community-driven initiatives. Communities showing organic growth, positive sentiment, and high-quality interactions earn the highest scores.",
            exampleCalculation: {
              example: [
                "Community size relative to project age = 20 points",
                "Growth trends and retention rates = 20 points",
                "Interaction quality and frequency = 20 points",
                "Community sentiment analysis = 15 points",
                "Community-driven initiatives = 15 points",
                "Community geographic distribution = 10 points",
              ],
            },
            scoringLevels: [
              {
                level: "Excellent (85-100)",
                description:
                  "Large, active community with high-quality engagement, positive sentiment, and substantial community-driven initiatives.",
              },
              {
                level: "Good (70-84)",
                description:
                  "Growing community with regular engagement and generally positive sentiment.",
              },
              {
                level: "Fair (50-69)",
                description:
                  "Smaller community with moderate engagement or mixed sentiment.",
              },
              {
                level: "Poor (0-49)",
                description:
                  "Small, inactive community with minimal engagement or negative sentiment.",
              },
            ],
            conclusion:
              "The Community Engagement score is a powerful indicator of a project's grassroots support and potential for long-term adoption and success.",
            disclaimer:
              "Community metrics can fluctuate dramatically over time and may be influenced by market conditions beyond a project's control. Our assessment represents a snapshot at the time of analysis.",
          },
        },
        {
          name: "Team Transparency",
          value: 95,
          score: 95,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Assesses how transparent and communicative the team is with their community.",
          category: "Social",
          methodologyData: {
            title: "Team Transparency",
            whatAreWeScoring:
              "We evaluate how openly and honestly the project team communicates with its community and stakeholders. This includes assessing their disclosure practices, communication frequency, and responsiveness to community concerns.",
            whyIsThisImportant: {
              factors: [
                {
                  name: "Trust building",
                  explanation:
                    "Transparent teams foster trust with users and investors, which is fundamental to project success.",
                },
                {
                  name: "Risk reduction",
                  explanation:
                    "Open communication reduces information asymmetry and allows stakeholders to make informed decisions.",
                },
                {
                  name: "Community alignment",
                  explanation:
                    "Transparent teams create better alignment between community expectations and project realities.",
                },
              ],
            },
            howDoWeScoreIt:
              "We assess the team's disclosure of personal identities, project roadmaps, technical challenges, financial information, and community feedback incorporation. Teams that maintain consistent, honest, and comprehensive communication earn the highest scores.",
            exampleCalculation: {
              example: [
                "Team identity transparency = 20 points",
                "Technical progress reporting = 20 points",
                "Financial transparency = 20 points",
                "Communication consistency = 15 points",
                "Community feedback implementation = 15 points",
                "Crisis communication effectiveness = 10 points",
              ],
            },
            scoringLevels: [
              {
                level: "Excellent (85-100)",
                description:
                  "Fully transparent team with comprehensive, regular, and honest communication across all aspects of the project.",
              },
              {
                level: "Good (70-84)",
                description:
                  "Transparent team with regular updates and good disclosure practices.",
              },
              {
                level: "Fair (50-69)",
                description:
                  "Partially transparent team with inconsistent communication or limited disclosure.",
              },
              {
                level: "Poor (0-49)",
                description:
                  "Opaque team with minimal communication or significant information withholding.",
              },
            ],
            conclusion:
              "The Team Transparency score reflects a project's commitment to ethical communication practices and is a strong predictor of stakeholder trust and long-term reputation.",
            disclaimer:
              "Our transparency assessment is based on publicly available communications and may not account for confidential information or strategic non-disclosure agreements that may legitimately restrict certain disclosures.",
          },
        },
        {
          name: "Social Media Presence",
          value: 90,
          score: 90,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Evaluates the project's presence and activity on major social media platforms.",
          category: "Social",
          methodologyData: {
            title: "Social Media Presence",
            whatAreWeScoring:
              "We assess the project's effectiveness in establishing and maintaining a strategic presence across relevant social media platforms. This includes evaluating content quality, audience engagement, and cross-platform consistency.",
            whyIsThisImportant: {
              factors: [
                {
                  name: "Market reach",
                  explanation:
                    "Effective social media presence extends the project's reach to potential users and investors.",
                },
                {
                  name: "Information distribution",
                  explanation:
                    "Social media serves as a primary channel for updates, education, and community interaction.",
                },
                {
                  name: "Brand perception",
                  explanation:
                    "Professional social media presence contributes significantly to a project's perceived legitimacy and authority.",
                },
              ],
            },
            howDoWeScoreIt:
              "We evaluate the project's activity across major platforms, content quality and consistency, audience engagement metrics, growth trends, and response management. Projects with strategic, active, and engaging presences on relevant platforms receive the highest scores.",
            exampleCalculation: {
              example: [
                "Cross-platform coverage and activity = 20 points",
                "Content quality and educational value = 20 points",
                "Audience engagement metrics = 20 points",
                "Follower growth and retention = 15 points",
                "Response management = 15 points",
                "Brand consistency = 10 points",
              ],
            },
            scoringLevels: [
              {
                level: "Excellent (85-100)",
                description:
                  "Strategic presence across all relevant platforms with high-quality content, strong engagement, and consistent brand messaging.",
              },
              {
                level: "Good (70-84)",
                description:
                  "Active presence on major platforms with quality content and solid engagement metrics.",
              },
              {
                level: "Fair (50-69)",
                description:
                  "Limited platform presence, inconsistent posting, or moderate engagement metrics.",
              },
              {
                level: "Poor (0-49)",
                description:
                  "Minimal social media presence, low-quality content, or poor engagement metrics.",
              },
            ],
            conclusion:
              "The Social Media Presence score indicates a project's effectiveness in digital communication and is a key factor in its market visibility and community growth potential.",
            disclaimer:
              "Social media metrics can be influenced by many factors outside a project's control, including algorithm changes, regional restrictions, and temporary market trends. Our assessment is based on observable patterns at the time of analysis.",
          },
        },
        {
          name: "Developer Community",
          value: 87,
          score: 87,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Evaluates the size, activity, and contributions of the project's developer community.",
          category: "Social",
          methodologyData: {
            title: "Developer Community",
            whatAreWeScoring:
              "We evaluate the health, engagement, and productivity of the project's developer ecosystem. This includes assessing the size of the developer community, contribution activity, and the effectiveness of developer support resources.",
            whyIsThisImportant: {
              factors: [
                {
                  name: "Ecosystem growth",
                  explanation:
                    "A strong developer community contributes to platform expansion through applications, tools, and integrations.",
                },
                {
                  name: "Technical sustainability",
                  explanation:
                    "Projects with active developer communities are more likely to maintain technical relevance and innovation.",
                },
                {
                  name: "Decentralization",
                  explanation:
                    "A diverse developer base reduces dependence on the core team and improves project resilience.",
                },
              ],
            },
            howDoWeScoreIt:
              "We analyze GitHub metrics, developer documentation quality, technical support responsiveness, hackathon participation, developer incentive programs, and third-party development activity. Projects with active, growing, and productive developer communities earn the highest scores.",
            exampleCalculation: {
              example: [
                "Developer community size and diversity = 20 points",
                "Contribution frequency and quality = 20 points",
                "Developer documentation and resources = 20 points",
                "Developer incentive programs = 15 points",
                "Technical support quality = 15 points",
                "Third-party development activity = 10 points",
              ],
            },
            scoringLevels: [
              {
                level: "Excellent (85-100)",
                description:
                  "Large, active developer community with substantial contributions, excellent documentation, and strong support systems.",
              },
              {
                level: "Good (70-84)",
                description:
                  "Solid developer community with regular contributions, good documentation, and adequate support.",
              },
              {
                level: "Fair (50-69)",
                description:
                  "Small developer community with limited contributions or incomplete documentation.",
              },
              {
                level: "Poor (0-49)",
                description:
                  "Minimal developer community, few external contributions, or inadequate developer resources.",
              },
            ],
            conclusion:
              "The Developer Community score reflects a project's ability to attract and retain technical talent, which is crucial for innovation, security, and long-term viability.",
            disclaimer:
              "Developer community assessments are based on publicly visible contributions and may not account for private development work. The quality and impact of contributions may differ from quantity-based metrics.",
          },
        },
      ],
    },
    {
      name: "Market Performance",
      score: 89,
      status: "Excellent" as CoinStatus,
      description: "Market Performance metrics breakdown",
      weight: 0.15,
      metrics: [
        {
          name: "IDO Price Evaluation",
          value: 85,
          score: 85,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Analyzes if the IDO price is fair and aligned with project valuation fundamentals.",
          category: "Market",
        },
        {
          name: "Fundraising Structure",
          value: 92,
          score: 92,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Evaluates the equity of token fundraising rounds and allocation distribution.",
          category: "Market",
        },
        {
          name: "Liquidity Strategy",
          value: 88,
          score: 88,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Assesses the project's plan for ensuring sufficient market liquidity at launch and beyond.",
          category: "Market",
        },
      ],
    },
    {
      name: "Insights",
      score: 83,
      status: "Good" as CoinStatus,
      description: "Market insights breakdown",
      weight: 0.15,
      metrics: [
        {
          name: "Competitive Analysis",
          value: 78,
          score: 78,
          label: "Good",
          status: "Good" as CoinStatus,
          description:
            "Evaluates the project's competitive positioning within its sector.",
          category: "Insights",
        },
        {
          name: "Growth Potential",
          value: 88,
          score: 88,
          label: "Excellent",
          status: "Excellent" as CoinStatus,
          description:
            "Assesses the potential for the project to gain adoption and market share.",
          category: "Insights",
        },
        {
          name: "Market Timing",
          value: 82,
          score: 82,
          label: "Good",
          status: "Good" as CoinStatus,
          description:
            "Evaluates if the project is launching at a favorable time in the market cycle.",
          category: "Insights",
        },
      ],
    },
  ];

  // Function to check if a tab has data
  const hasTabData = (tabKey: string): boolean => {
    switch (tabKey) {
      case 'essentials':
        // Check if team data exists and has meaningful content
        if (!idoData.team || !Array.isArray(idoData.team) || idoData.team.length === 0) {
          return false;
        }
        // Check if at least one team member has meaningful data (not all null/empty)
        return idoData.team.some((member: any) => 
          (member.name && member.name.trim() !== '') ||
          (member.role && member.role.trim() !== '') ||
          (member.bio && member.bio.trim() !== '') ||
          (member.linkedin && member.linkedin.trim() !== '') ||
          (member.twitter && member.twitter.trim() !== '') ||
          (member.image && member.image.trim() !== '') ||
          (member.position && member.position.trim() !== '') ||
          (member.avatar && member.avatar.trim() !== '')
        );
      
      case 'funding':
        // Check if funding data has meaningful content
        const hasFunding = idoData.funding && (
          (idoData.funding.totalRaised && idoData.funding.totalRaised.toString().trim() !== '' && idoData.funding.totalRaised !== '0') || 
          (idoData.funding.rounds && Array.isArray(idoData.funding.rounds) && idoData.funding.rounds.length > 0) ||
          (idoData.funding.valuations && Object.keys(idoData.funding.valuations).length > 0)
        );
        // Check if investors array has meaningful data
        const hasInvestors = idoData.investors && Array.isArray(idoData.investors) && idoData.investors.length > 0 && 
          idoData.investors.some((investor: any) => 
            (investor.name && investor.name.trim() !== '') ||
            (investor.logo && investor.logo.trim() !== '') ||
            (investor.website && investor.website.trim() !== '') ||
            (investor.description && investor.description.trim() !== '')
          );
        return hasFunding || hasInvestors;
      
      case 'tokenomics':
        // Check if allocation data exists and has meaningful values
        if (!idoData.allocation) return false;
        
        // Check direct allocation properties
        const hasDirectAllocations = Object.entries(idoData.allocation)
          .filter(([key]) => key !== 'allocations' && key !== 'maxSupply')
          .some(([, value]) => value !== null && value !== undefined && value !== 0);
        
        // Check allocations array for meaningful data
        const hasAllocationArray = idoData.allocation.allocations && 
          Array.isArray(idoData.allocation.allocations) &&
          idoData.allocation.allocations.some((allocation: any) => 
            (allocation.percentage !== null && allocation.percentage !== undefined && allocation.percentage !== 0) ||
            (allocation.value !== null && allocation.value !== undefined && allocation.value !== 0)
          );
        
        return hasDirectAllocations || hasAllocationArray;
      
      case 'releases':
        // Check if release schedule data exists and has meaningful content
        return idoData.releaseSchedule && Array.isArray(idoData.releaseSchedule) && idoData.releaseSchedule.length > 0 &&
          idoData.releaseSchedule.some((schedule: any) => 
            (schedule.date && schedule.date.toString().trim() !== '') ||
            (schedule.percentage && schedule.percentage !== null && schedule.percentage !== 0) ||
            (schedule.amount && schedule.amount !== null && schedule.amount !== 0) ||
            (schedule.description && schedule.description.trim() !== '')
          );
      
      case 'analysis':
        // Check if price projection data exists
        return idoData.priceProjection && Object.keys(idoData.priceProjection).length > 0;
      
      case 'details':
        // Check if project description or resource links exist
        const hasDescription = idoData.description && idoData.description.trim() !== '';
        const hasResourceLinks = idoData.resourceLinks && Array.isArray(idoData.resourceLinks) && idoData.resourceLinks.length > 0;
        const hasWhitepaper = idoData.whitepaper && idoData.whitepaper.trim() !== '';
        return hasDescription || hasResourceLinks || hasWhitepaper;
      
      default:
        return true;
    }
  };

  // Define tabs for advanced view with data validation
  const tabs = {
    essentials: "Team",
    funding: "Funding & Investment",
    tokenomics: "Tokenomics",
    releases: "Token Release",
    analysis: "Price Analysis",
    details: "Project Details",
  };

  // Ensure activeTab is valid - if current tab has no data, switch to first available tab
  React.useEffect(() => {
    const availableTabKeys = Object.keys(tabs).filter(key => hasTabData(key));
    if (availableTabKeys.length > 0 && !availableTabKeys.includes(activeTab)) {
      setActiveTab(availableTabKeys[0]);
    }
  }, [idoData, activeTab]);

  // Handler for feature request submission
  const handleFeatureSubmit = async () => {
    if (!featureRequest.trim()) {
      toast({
        title: "Hata",
        description: "Lütfen özellik önerinizi girin",
        variant: "destructive",
      });
      return;
    }

    try {
      // Send the feature request to the API
      const response = await IDOService.requestFeature({
        description: featureRequest,
      });

      console.log("Feature request API response:", response);

      // Show success toast
      toast({
        title: "Özellik Önerisi Gönderildi!",
        description:
          "Geri bildiriminiz için teşekkür ederiz! Öneriniz ekibimiz tarafından değerlendirilecektir.",
        variant: "default",
        className: "bg-blue-900 border-blue-700 text-white shadow-lg border-2",
        duration: 5000, // Show for 5 seconds
      });

      // Create a visual confirmation overlay
      const confirmationElement = document.createElement("div");
      confirmationElement.className =
        "fixed inset-0 flex items-center justify-center z-[1000] bg-black/50";
      confirmationElement.innerHTML = `
        <div class="bg-blue-900 border-2 border-blue-700 p-6 rounded-lg max-w-md text-center animate-in slide-in-from-bottom-10 duration-300">
          <div class="text-blue-400 mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-2">Özellik Önerisi Gönderildi!</h3>
          <p class="text-blue-100 mb-4">Geri bildiriminiz için teşekkür ederiz! Öneriniz ekibimiz tarafından değerlendirilecektir.</p>
        </div>
      `;

      document.body.appendChild(confirmationElement);

      // Remove confirmation after 3 seconds
      setTimeout(() => {
        confirmationElement.classList.add("fade-out");
        setTimeout(() => {
          document.body.removeChild(confirmationElement);
        }, 300);
      }, 3000);

      // Reset and close
      setFeatureRequest("");
      setIsFeatureModalOpen(false);
    } catch (error) {
      console.error("Error submitting feature request:", error);

      // Error toast
      toast({
        title: "Gönderim Hatası",
        description:
          "Öneriniz gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.",
        variant: "destructive",
      });
    }
  };

  // Handler for error report submission
  const handleErrorSubmit = async () => {
    if (!errorReport.trim()) {
      toast({
        title: "Hata",
        description: "Lütfen hata detaylarını girin",
        variant: "destructive",
      });
      return;
    }

    try {
      // Send the error report to the API
      const response = await IDOService.reportError({
        ido_id: id || "",
        ido_name: idoData?.name || "",
        detail: errorReport,
      });

      console.log("Error report API response:", response);

      // Show success toast
      toast({
        title: "Hata Raporu Gönderildi!",
        description:
          "Hata bildiriminiz için teşekkür ederiz. Ekibimiz en kısa sürede inceleyecektir.",
        variant: "default",
        className:
          "bg-slate-900 border-slate-700 text-white shadow-lg border-2",
        duration: 5000, // Show for 5 seconds
      });

      // Create a visual confirmation overlay
      const confirmationElement = document.createElement("div");
      confirmationElement.className =
        "fixed inset-0 flex items-center justify-center z-[1000] bg-black/50";
      confirmationElement.innerHTML = `
        <div class="bg-slate-900 border-2 border-slate-700 p-6 rounded-lg max-w-md text-center animate-in slide-in-from-bottom-10 duration-300">
          <div class="text-red-400 mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto">
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-2">Hata Raporu Gönderildi!</h3>
          <p class="text-slate-300 mb-4">Hata bildiriminiz için teşekkür ederiz. Ekibimiz en kısa sürede inceleyecektir.</p>
        </div>
      `;

      document.body.appendChild(confirmationElement);

      // Remove confirmation after 3 seconds
      setTimeout(() => {
        confirmationElement.classList.add("fade-out");
        setTimeout(() => {
          document.body.removeChild(confirmationElement);
        }, 300);
      }, 3000);

      // Reset and close
      setErrorReport("");
      setIsErrorModalOpen(false);
    } catch (error) {
      console.error("Error submitting error report:", error);

      // Error toast
      toast({
        title: "Gönderim Hatası",
        description:
          "Rapor gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.",
        variant: "destructive",
      });
    }
  };

  // Optionally parse API metrics data for display
  const getApiMetricsData = () => {
    // If the API provides metrics in the format we expect, use it
    if (idoData && "metrics" in idoData && Array.isArray(idoData.metrics)) {
      return idoData.metrics;
    }
    // Otherwise return an empty array
    return [];
  };

  return (
    <React.Fragment>
      {/* Feature Request Dialog */}
      <Dialog open={isFeatureModalOpen} onOpenChange={setIsFeatureModalOpen}>
        <DialogContent className="bg-slate-900 border border-slate-700 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">
              {t("ido.featureRequest.title", "Request a Feature")}
            </DialogTitle>
            <DialogDescription className="text-slate-300">
              {t(
                "ido.featureRequest.description",
                "Tell us what feature you'd like to see in CoinScout.",
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              value={featureRequest}
              onChange={(e) => setFeatureRequest(e.target.value)}
              placeholder="Describe the feature you'd like to see..."
              className="h-32 bg-slate-800 border-slate-700 text-white placeholder:text-slate-500 focus:border-blue-400"
            />
          </div>
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={() => setIsFeatureModalOpen(false)}
              className="text-slate-300 hover:text-white hover:bg-slate-800"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={handleFeatureSubmit}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Submit Request
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Error Report Dialog */}
      <Dialog open={isErrorModalOpen} onOpenChange={setIsErrorModalOpen}>
        <DialogContent className="bg-slate-900 border border-slate-700 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">
              Report an Error
            </DialogTitle>
            <DialogDescription className="text-slate-300">
              Help us improve by reporting any issues you encountered.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              value={errorReport}
              onChange={(e) => setErrorReport(e.target.value)}
              placeholder="Describe the error or issue..."
              className="h-32 bg-slate-800 border-slate-700 text-white placeholder:text-slate-500 focus:border-red-400"
            />
          </div>
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={() => setIsErrorModalOpen(false)}
              className="text-slate-300 hover:text-white hover:bg-slate-800"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={handleErrorSubmit}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Report Error
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <motion.div
        className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800"
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageVariants}
      >
        <div className="container mx-auto pt-6 px-4">
          <div className="flex justify-between items-center mb-6">
            <Link
              href="/upcoming"
              className="inline-flex items-center text-blue-500 hover:text-blue-400 transition-colors px-3 py-1 rounded-xl hover:bg-slate-800/90"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              <span>Back to Upcoming</span>
            </Link>

            {/* Advanced Mode Toggle - Temporarily hidden */}
            {/* 
            <div className="flex items-center">
              <div className="relative flex items-center gap-3 bg-slate-800/90 p-1.5 pl-3 pr-4 rounded-full border border-slate-700/40 shadow-md shadow-slate-900/40">
                {/* Label with animated glow effect */}
                {/* 
                <div className="flex items-center gap-2">
                  <motion.div
                    initial={{ opacity: 0.7 }}
                    animate={{
                      opacity: showAdvancedView ? [0.8, 1, 0.8] : 0.7,
                    }}
                    transition={{
                      duration: 1,
                      repeat: showAdvancedView ? Infinity : 0,
                      repeatType: "reverse",
                    }}
                    className="relative flex items-center justify-center"
                  >
                    {showAdvancedView && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 0.6 }}
                        transition={{ duration: 0.3 }}
                        className="absolute inset-0 text-indigo-400 blur-[6px]"
                      >
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                    <Sparkles
                      className={`h-4 w-4 ${showAdvancedView ? "text-indigo-300" : "text-slate-400"}`}
                    />
                  </motion.div>
                  <span
                    className={`text-sm font-medium transition-colors duration-300 ${
                      showAdvancedView
                        ? "bg-gradient-to-r from-indigo-200 to-violet-200 bg-clip-text text-transparent"
                        : "text-slate-300"
                    }`}
                  >
                    Advanced View
                  </span>
                </div>

                {/* Toggle button with enhanced animations */}
                {/* 
                <button
                  role="switch"
                  aria-checked={showAdvancedView}
                  aria-label={`${showAdvancedView ? "Disable" : "Enable"} advanced view`}
                  className={`relative w-14 h-7 rounded-full flex items-center px-0.5 cursor-pointer overflow-hidden transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-indigo-400 focus:ring-offset-slate-900`}
                  onClick={() => {
                    const newState = !showAdvancedView;
                    console.log("Advanced toggle clicked, new value:", newState);
                    setShowAdvancedView(newState);
                  }}
                >
                  {/* Background with gradient animation */}
                  {/* 
                  <motion.div
                    className="absolute inset-0 rounded-full"
                    initial={false}
                    animate={{
                      background: showAdvancedView
                        ? "linear-gradient(to right, rgb(129, 140, 248), rgb(167, 139, 250))"
                        : "rgb(51, 65, 85)",
                    }}
                    transition={{ duration: 0.3 }}
                  />

                  {/* Animated track with glow */}
                  {/* 
                  {showAdvancedView && (
                    <motion.div
                      className="absolute inset-0 opacity-30"
                      initial={{ opacity: 0 }}
                      animate={{
                        opacity: [0.2, 0.4, 0.2],
                        boxShadow: [
                          "inset 0 0 5px 2px rgba(129, 140, 248, 0.3)",
                          "inset 0 0 8px 2px rgba(129, 140, 248, 0.5)",
                          "inset 0 0 5px 2px rgba(129, 140, 248, 0.3)",
                        ],
                      }}
                      transition={{
                        duration: 1.8,
                        repeat: Infinity,
                        repeatType: "reverse",
                      }}
                    />
                  )}

                  <span className="sr-only">
                    {showAdvancedView ? "Disable" : "Enable"} advanced view
                  </span>

                  {/* Sliding handle with icon */}
                  {/* 
                  <motion.div
                    className={`flex items-center justify-center w-6 h-6 rounded-full relative z-10 ${
                      showAdvancedView ? "bg-white" : "bg-slate-200"
                    }`}
                    animate={{
                      x: showAdvancedView ? 27 : 1,
                      scale: showAdvancedView ? [1, 1.05, 1] : 1,
                      boxShadow: showAdvancedView
                        ? "0 0 10px 1px rgba(129, 140, 248, 0.5)"
                        : "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
                    }}
                    transition={{
                      x: {
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                      },
                      scale: {
                        duration: 1,
                        repeat: showAdvancedView ? Infinity : 0,
                        repeatType: "reverse",
                      },
                      boxShadow: { duration: 0.3 },
                    }}
                  >
                    <motion.div
                      animate={{
                        rotate: showAdvancedView ? 0 : 0,
                        scale: showAdvancedView ? 1 : 0.8,
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      {showAdvancedView ? (
                        <Database className="h-3 w-3 text-indigo-600" />
                      ) : (
                        <LayoutGrid className="h-3 w-3 text-slate-500" />
                      )}
                    </motion.div>
                  </motion.div>

                  {/* Subtle pulse animation around toggle when active */}
                  {/* 
                  {showAdvancedView && (
                    <motion.div
                      className="absolute right-1 w-6 h-6 rounded-full bg-indigo-400/20"
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{
                        scale: [0.8, 1.5, 0.8],
                        opacity: [0, 0.3, 0],
                      }}
                      transition={{
                        duration: 1.2,
                        repeat: Infinity,
                        repeatType: "loop",
                      }}
                    />
                  )}
                </button>
              </div>
            </div>
            */}
          </div>

          {/* Main content */}
          {/* Project Header Card */}
          <motion.div
            className="bg-slate-900/80 border border-slate-800 shadow-lg rounded-xl p-5 mb-6"
            layout
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
            }}
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Logo and Name - Enhanced with better visual effects */}
              <div className="bg-slate-800/70 rounded-xl p-5 border border-slate-700/40 shadow-md shadow-slate-900/40 flex flex-col justify-between h-full transition-all duration-300 hover:border-blue-500/30 hover:shadow-lg hover:shadow-slate-900/50 group relative overflow-hidden">
                {/* Background gradient effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

                <div className="flex justify-between items-center relative z-10">
                  <div className="flex items-center">
                    {/* Logo Container - Enhanced with visual effects */}
                    <div className="bg-slate-800/90 p-3 rounded-xl border border-slate-700/50 shadow-lg shadow-slate-900/40 w-20 h-20 flex items-center justify-center transition-all duration-300 group-hover:border-blue-500/40 mr-4 flex-shrink-0 overflow-hidden">
                      {/* Subtle animated glow effect */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="h-16 w-16 rounded-full overflow-hidden bg-slate-800/90 border border-slate-700/50 shadow-inner shadow-slate-900/20 relative z-10 group-hover:scale-105 transition-transform duration-300">
                        <img
                          src={idoData.logo}
                          alt={idoData.name}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            // Fallback if image fails to load
                            (e.target as HTMLImageElement).src =
                              "https://via.placeholder.com/64";
                          }}
                        />
                      </div>
                    </div>
                    <div>
                      <h1 className="text-white text-xl font-semibold mb-1 group-hover:text-blue-100 transition-colors duration-300">
                        <DataDisplay value={idoData.name} className="text-white text-xl font-semibold" />
                      </h1>
                      <div className="flex items-center">
                        <span className="text-slate-300 font-medium mr-2 bg-slate-800/80 px-2 py-0.5 rounded-md border border-slate-700/30">
                          <DataDisplay value={idoData.symbol} className="text-slate-300 font-medium" />
                        </span>
                        <span className="text-xs text-slate-500">•</span>
                        <span className="text-slate-300 ml-2 bg-slate-800/80 px-2 py-0.5 rounded-md border border-slate-700/30">
                          <DataDisplay value={idoData.category} className="text-slate-300" />
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Advanced toggle removed from header, moved to navigation area */}
                </div>

                {/* Supply distribution inner card */}
                <div className="mt-2 mb-2 relative z-10">
                  <div className="bg-slate-800/90 rounded-lg border border-slate-700/40 p-3 transition-all duration-300 group-hover:border-emerald-500/20">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-1.5 h-1.5 rounded-full bg-emerald-500 mr-2"></div>
                          <span className="text-xs text-slate-300">
                            Max Supply
                          </span>
                        </div>
                        <span className="text-xs font-medium text-white bg-slate-700/40 px-2 py-0.5 rounded-md">
                          <DataDisplay 
                            value={idoData.allocation?.maxSupply} 
                            className="text-xs font-medium text-white"
                          />
                        </span>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mr-2"></div>
                          <span className="text-xs text-slate-300">
                            Total Supply
                          </span>
                        </div>
                        <span className="text-xs font-medium text-white bg-slate-700/40 px-2 py-0.5 rounded-md">
                          <DataDisplay 
                            value={idoData.totalSupply} 
                            className="text-xs font-medium text-white"
                          />
                        </span>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-1.5 h-1.5 rounded-full bg-amber-500 mr-2"></div>
                          <span className="text-xs text-slate-300">
                            Initial Circulating
                          </span>
                        </div>
                        <span className="text-xs font-medium text-white bg-slate-700/40 px-2 py-0.5 rounded-md flex items-center">
                          <DataDisplay 
                            value={idoData.initialCirculatingSupply} 
                            className="text-xs font-medium text-white mr-1"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Price Card - Completely redesigned for better visual appeal */}
              <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 rounded-xl p-5 border border-slate-700/20 shadow-md flex flex-col justify-between h-full transition-all duration-300 hover:border-blue-500/20 hover:shadow-lg group relative overflow-hidden">
                {/* Background gradient effect */}
                <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

                {/* Header removed as requested */}

                <div className="relative z-10 space-y-4">
                  {/* Primary metrics section with enhanced styling */}
                  <div className="bg-slate-800/90 rounded-lg border border-slate-700/40 p-3 transition-all duration-300 group-hover:border-emerald-500/20">
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-slate-700/40 rounded-md p-2.5 border border-slate-700/40 transition-all duration-300 group-hover:border-slate-600/40">
                        <div className="text-xs text-slate-400 mb-1">
                          IDO Price
                        </div>
                        <div className="text-sm font-medium text-white flex items-center">
                          <span className="text-emerald-400 mr-1.5">$</span>
                          <DataDisplay 
                            value={typeof idoData.idoPrice === 'string' ? idoData.idoPrice.replace(/\$/g, "") : idoData.idoPrice} 
                            className="text-sm font-medium text-white"
                          />
                        </div>
                      </div>
                      <div className="bg-slate-700/40 rounded-md p-2.5 border border-slate-700/40 transition-all duration-300 group-hover:border-slate-600/40">
                        <div className="text-xs text-slate-400 mb-1 flex items-center gap-1.5">
                          <IMCScoreIcon className="w-3.5 h-3.5 text-slate-400" />
                          IMC
                        </div>
                        <div className="text-sm font-medium text-white flex items-center">
                          <span className="text-emerald-400 mr-1.5">$</span>
                          <DataDisplay 
                            value={typeof idoData.initialMarketCap === 'string' ? idoData.initialMarketCap.replace(/\$/g, "") : idoData.initialMarketCap} 
                            className="text-sm font-medium text-white"
                          />
                        </div>
                      </div>
                      <div className="bg-slate-700/40 rounded-md p-2.5 border border-slate-700/40 transition-all duration-300 group-hover:border-slate-600/40">
                        <div className="text-xs text-slate-400 mb-1">FDV</div>
                        <div className="text-sm font-medium text-white flex items-center">
                          <span className="text-emerald-400 mr-1.5">$</span>
                          <DataDisplay 
                            value={typeof idoData.fdv === 'string' ? idoData.fdv.replace(/\$/g, "") : idoData.fdv} 
                            className="text-sm font-medium text-white"
                          />
                        </div>
                      </div>
                      <div className="bg-slate-700/40 rounded-md p-2.5 border border-slate-700/40 transition-all duration-300 group-hover:border-slate-600/40">
                        <div className="text-xs text-slate-400 mb-1">
                          Funds Raised
                        </div>
                        <div className="text-sm font-medium text-white flex items-center">
                          <span className="text-emerald-400 mr-1.5">$</span>
                          <DataDisplay 
                            value={typeof idoData.funding?.totalRaised === 'string' ? idoData.funding.totalRaised.replace(/\$/g, "") : idoData.funding?.totalRaised} 
                            className="text-sm font-medium text-white"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Dynamic Launchpad Display based on API data */}
                  {idoData.launchpad && idoData.launchpad.length > 0 ? (
                    <div className="mt-3 relative z-10">
                      <div className="flex items-center bg-slate-800/90 px-3 py-2 rounded-lg border border-emerald-500/20 shadow-sm shadow-slate-900/30 group-hover:border-emerald-500/30 transition-all duration-300">
                        <CheckCircle className="h-4 w-4 mr-2 text-emerald-400" />
                        <span className="text-sm font-medium text-emerald-400">
                          {idoData.launchpad.length === 1 
                            ? `${idoData.launchpad[0]?.name || 'Launchpad'} Confirmed`
                            : `${idoData.launchpad.length} Launchpads Confirmed`
                          }
                        </span>
                      </div>
                    </div>
                  ) : idoData.launchpadInfo && idoData.launchpadInfo.name ? (
                    <div className="mt-3 relative z-10">
                      <div className="flex items-center bg-slate-800/90 px-3 py-2 rounded-lg border border-emerald-500/20 shadow-sm shadow-slate-900/30 group-hover:border-emerald-500/30 transition-all duration-300">
                        <CheckCircle className="h-4 w-4 mr-2 text-emerald-400" />
                        <span className="text-sm font-medium text-emerald-400">
                          {idoData.launchpadInfo.name} Confirmed
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="mt-3 relative z-10">
                      <div className="flex items-center bg-slate-800/90 px-3 py-2 rounded-lg border border-slate-600/20 shadow-sm shadow-slate-900/30 group-hover:border-slate-600/30 transition-all duration-300">
                        <AlertTriangle className="h-4 w-4 mr-2 text-slate-400" />
                        <span className="text-sm font-medium text-slate-400">
                          {t("ido.noLaunchpadInfo", "No Launchpad Information")}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Score Card - Enhanced with visual consistency */}
              <div className="bg-slate-800/70 rounded-xl p-4 border border-slate-700/40 shadow-md shadow-slate-900/40 flex flex-col justify-between h-full transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden">
                <div className="flex items-start justify-between relative z-10">
                  {/* Left Side: Score with Progress Circle */}
                  <div className="flex flex-col items-center">
                    {/* Score Gauge directly in the flex container */}
                    <ScoreGauge
                      className="shadow-md shadow-slate-900/40 rounded-xl mb-1"
                      score={idoData.score}
                      size="lg"
                      id="ido-health"
                      showLabel={false}
                      animated={false}
                    />
                  </div>

                  {/* Right Side: Quality Badge with enhanced styling */}
                  <div className="flex flex-col items-end">
                    <div className="bg-slate-800/90 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 p-2.5 transition-all duration-300 hover:border-blue-500/30">
                      <div
                        className={`rounded-xl py-1.5 px-4 flex items-center whitespace-nowrap ${
                          idoData.score >= 90
                            ? "bg-[#00D88A]/20 text-[#00D88A] border border-[#00D88A]/20"
                            : idoData.score >= 75
                              ? "bg-[#00B8D9]/20 text-[#00B8D9] border border-[#00B8D9]/20"
                              : idoData.score >= 65
                                ? "bg-[#FFAB00]/20 text-[#FFAB00] border border-[#FFAB00]/20"
                                :  idoData.score >= 50
                          ? "bg-[#FF5630]/20 text-[#FF5630] border border-[#FF5630]/20"
                          : "bg-[#FF3B3B]/20 text-[#FF3B3B] border border-[#FF3B3B]/20"
                        }`}
                      >
                        <HeartPulse className="h-4 w-4 mr-2" />
                        <span className="text-sm font-medium">
                          {idoData.score >= 90
                            ? "Excellent"
                            : idoData.score >= 75
                              ? "Positive"
                              : idoData.score >= 65
                                ? "Average"
                                :idoData.score >= 50
                          ? "Weak"
                          : "Critical"}
                        </span>
                      </div>
                    </div>
                    
                  </div>
                </div>

                {/* Score Range Indicator */}
                <div className="mt-3 relative z-10">
                  <p className="text-sm text-slate-300 mb-1.5 font-medium">
                    Score Range
                  </p>
                  <div className="h-2 w-full bg-slate-800/90 rounded-full relative overflow-hidden">
                    {/* Background gradient - smoother transitions */}
                    <div
                      className="absolute left-0 top-0 h-full"
                      style={{
                        width: "100%",
                        background: `linear-gradient(to right, 
                          #FF3B3B 0%, 
                          #FF3B3B 47.5%, 
                          #FF5630 52.5%, 
                          #FF5630 62.5%, 
                          #FFAB00 67.5%, 
                          #FFAB00 72.5%, 
                          #00B8D9 77.5%, 
                          #00B8D9 87.5%, 
                          #00D88A 92.5%, 
                          #00D88A 100%)`,
                      }}
                    />
                    {/* Position indicator dot */}
                    <div
                      className="absolute top-0 h-4 w-2 bg-white rounded-full transform -translate-y-1"
                      style={{
                        left: `${Math.max(0, Math.min(100, idoData.score))}%`,
                      }}
                    ></div>
                  </div>
                  <div className="flex justify-between mt-1.5 text-xs text-slate-400">
                    <span>Critical</span>
                    <span>Excellent</span>
                  </div>
                </div>
                

                {/* Visual element for depth */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-slate-500/5 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>
              </div>

              {/* Project Description - Moved to top card */}
              <div className="col-span-1 md:col-span-3 mt-6 border-t border-slate-800/50 pt-6">
                <div className="bg-slate-800/70 rounded-xl p-5 border border-slate-700/40 shadow-md shadow-slate-900/40 transition-all duration-300 hover:border-blue-500/30">
                  <div className="p-4 border-b border-slate-700/40 bg-slate-800/90 backdrop-blur-sm -mx-5 -mt-5 mb-5 rounded-t-xl">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2.5">
                        <div className="bg-slate-800/90 p-2 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 transition-all duration-300 hover:border-blue-500/30">
                          <FileText className="h-4 w-4 text-blue-400" />
                        </div>
                        <h2 className="text-white text-lg font-semibold">
                          Project Description
                        </h2>
                      </div>
                      <div className="text-slate-300 text-sm leading-relaxed px-1">
                        <DataDisplay 
                          value={idoData.description} 
                          className="text-slate-300 text-sm leading-relaxed" 
                          renderAsHtml={true}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Links and Tags */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mt-6 pt-4 border-t border-slate-800/50">
              {/* Tags */}
              <div className="flex items-center mb-4 sm:mb-3">
                <span className="text-slate-300 font-medium mr-2">
                  {t('coinDetail.categories')}:
                </span>
                <div className="flex flex-wrap gap-2">
                  {idoData.tags &&
                  Array.isArray(idoData.tags) &&
                  idoData.tags.length > 0 ? (
                    idoData.tags.map((tag: string, index: number) => (
                      <span
                        key={index}
                        className="bg-slate-800/90 text-slate-200 text-xs font-medium px-3.5 py-1.5 rounded-xl border border-slate-700/40 shadow-sm shadow-slate-900/20 transition-all duration-300 hover:border-blue-500/30 hover:shadow-md hover:shadow-slate-900/40"
                      >
                        <DataDisplay value={tag} className="text-slate-200 text-xs font-medium" />
                      </span>
                    ))
                  ) : (
                    // Fallback - Display category if tags aren't available
                    <span className="bg-slate-800/90 text-slate-200 text-xs font-medium px-3.5 py-1.5 rounded-xl border border-slate-700/40 shadow-sm shadow-slate-900/20 transition-all duration-300 hover:border-blue-500/30 hover:shadow-md hover:shadow-slate-900/40">
                      <DataDisplay value={idoData.category} fallback="Uncategorized" className="text-slate-200 text-xs font-medium" />
                    </span>
                  )}
                </div>
              </div>

              {/* Contract Information */}
              {idoData.contracts && idoData.contracts.length > 0 && (
                <div className="flex items-center mb-4 sm:mb-3">
                  <span className="text-slate-300 font-medium mr-2">
                    Contracts:
                  </span>
                  <select
                    className="bg-slate-800/90 text-slate-200 text-xs font-medium pl-3 pr-8 py-1.5 rounded-xl border border-slate-700/40 shadow-sm shadow-slate-900/20 transition-all duration-300 hover:border-blue-500/30 hover:shadow-md hover:shadow-slate-900/40 appearance-none cursor-pointer"
                    value={
                      selectedContract !== -1
                        ? idoData.contracts[selectedContract].network
                        : ""
                    }
                    onChange={(e) => {
                      const index = idoData.contracts
                        ? idoData.contracts.findIndex(
                            (contract: any) => contract.network === e.target.value,
                          )
                        : -1;
                      setSelectedContract(index);
                    }}
                  >
                    {idoData.contracts &&
                      idoData.contracts.map((contract: any, index: number) => (
                        <option key={index} value={contract.network}>
                          {contract.network}: {contract.address}
                        </option>
                      ))}
                  </select>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          onClick={() => {
                            if (idoData.contracts && selectedContract !== -1) {
                              navigator.clipboard.writeText(
                                idoData.contracts[selectedContract].address,
                              );
                            }
                          }}
                          className="ml-2 bg-slate-800/90 text-slate-300 hover:text-white p-1.5 rounded-xl transition-all duration-300 border border-slate-700/40 hover:border-blue-500/30 shadow-sm shadow-slate-900/20 hover:shadow-md hover:shadow-slate-900/40"
                        >
                          <Copy size={12} />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent
                        side="top"
                        className="bg-slate-800/90 p-2 border border-slate-700/40 shadow-md"
                      >
                        <p className="text-sm">Copy contract address</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              )}

              {/* Social Links */}
              <div className="flex items-center space-x-3">
                <span className="text-slate-300 font-medium mr-1">Links:</span>

                {/* Handle direct social media fields from new API structure */}
                {idoData.website && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <Book size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Website</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.twitter && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <Twitter size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Twitter / X</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.telegram && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.telegram}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <MessageCircle size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Telegram</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.discord && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.discord}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <MessageCircle size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Discord</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.medium && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.medium}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <FileText size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Medium</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.linkedin && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <Users size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>LinkedIn</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.reddit && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.reddit}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <MessageCircle size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Reddit</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.github && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <Github size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>GitHub</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.whitepaper && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.whitepaper}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <FileText size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Whitepaper</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {idoData.explorer && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={idoData.explorer}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                        >
                          <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                            <Database size={16} />
                          </span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Blockchain Explorer</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {/* Fallback for socialLinks object (legacy support) */}
                {(idoData as any).socialLinks &&
                  typeof (idoData as any).socialLinks === "object" &&
                  !Array.isArray((idoData as any).socialLinks) &&
                  Object.entries((idoData as any).socialLinks).map(
                    ([platform, url]: [string, any], index: number) => {
                      if (!url) return null; // Skip if URL is null

                      let icon = <ExternalLink size={16} />;
                      let tooltip = platform.charAt(0).toUpperCase() + platform.slice(1);

                      // Set icon based on platform
                      switch (platform.toLowerCase()) {
                        case "website":
                          icon = <Book size={16} />;
                          break;
                        case "twitter":
                          icon = <Twitter size={16} />;
                          tooltip = "Twitter / X";
                          break;
                        case "Telegram":
                        case "Telegram".toLowerCase():
                          icon = <MessageCircle size={16} />;
                          break;
                        case "Discord":
                        case "Discord".toLowerCase():
                          icon = <Discord size={16} />;
                          break;
                        case "Whitepaper":
                        case "Whitepaper".toLowerCase():
                          icon = <FileText size={16} />;
                          break;
                        default:
                          icon = <ExternalLink size={16} />;
                      }

                      return (
                        <TooltipProvider key={index}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <a
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="transition-all duration-300 p-2 rounded-full bg-slate-800/90 hover:bg-slate-700/90 border border-slate-700/40 shadow-md shadow-slate-900/40 ml-1 group"
                              >
                                <span className="text-blue-400 group-hover:text-blue-300 transition-colors">
                                  {icon}
                                </span>
                              </a>
                            </TooltipTrigger>
                            <TooltipContent side="bottom">
                              <p>{tooltip}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      );
                    },
                  )}

                {/* Format 2: Using socials array (from advanced mode) */}
                {Array.isArray(idoData.socials) &&
                  idoData.socials.map((link: any, index: number) => {
                    if (!link.url) return null; // Skip if URL is null

                    // Define icons for different platforms
                    let icon = <ExternalLink size={16} />;
                    let tooltip = link.platform;

                    // Set appropriate icon based on platform
                    switch (link.platform) {
                      case "Website":
                        icon = <Book size={16} />;
                        break;
                      case "Twitter":
                        icon = <Twitter size={16} />;
                        tooltip = "Twitter / X";
                        break;
                      case "Telegram":
                        icon = <MessageCircle size={16} />;
                        tooltip = "Telegram Chat";
                        break;
                      case "Discord":
                        icon = <MessageCircle size={16} />;
                        tooltip = "Discord Server";
                        break;
                      case "GitHub":
                        icon = <Github size={16} />;
                        tooltip = "GitHub Repository";
                        break;
                      case "Whitepaper":
                        icon = <FileText size={16} />;
                        tooltip = "Whitepaper";
                        break;
                      case "Medium":
                        icon = <FileText size={16} />;
                        tooltip = "Medium Blog";
                        break;
                      case "LinkedIn":
                        icon = <Users size={16} />;
                        tooltip = "LinkedIn";
                        break;
                      case "YouTube":
                        icon = <Activity size={16} />;
                        tooltip = "YouTube Channel";
                        break;
                      case "Facebook":
                        icon = <Users size={16} />;
                        tooltip = "Facebook Page";
                        break;
                      case "Reddit":
                        icon = <MessageCircle size={16} />;
                        tooltip = "Reddit Community";
                        break;
                      case "Explorer":
                        icon = <Database size={16} />;
                        tooltip = "Blockchain Explorer";
                        break;
                      case "Announcement":
                        icon = <FileText size={16} />;
                        tooltip = "Announcement Channel";
                        break;
                      default:
                        icon = <ExternalLink size={16} />;
                        break;
                    }

                    return (
                      <TooltipProvider key={index}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <a
                              href={link.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="bg-slate-800/90 hover:bg-slate-800/90 text-slate-300 hover:text-white p-2.5 rounded-xl transition-all duration-300 border border-slate-700/40 hover:border-slate-700/40 shadow-sm shadow-slate-900/20 hover:shadow-md hover:shadow-slate-900/40"
                            >
                              {icon}
                            </a>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            className="bg-slate-800/90 p-2 border border-slate-700/40 shadow-md"
                          >
                            <p className="text-sm">{tooltip}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })}

                {/* Display other links from API response */}
                {(idoData as any).otherLinks &&
                  (idoData as any).otherLinks.map(
                    (link: any, index: number) => {
                      if (!link.url) return null; // Skip if URL is null

                      return (
                        <TooltipProvider key={`other-${index}`}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <a
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="bg-slate-800/90 hover:bg-slate-800/90 text-slate-300 hover:text-white p-2.5 rounded-xl transition-all duration-300 border border-slate-700/40 hover:border-slate-700/40 shadow-sm shadow-slate-900/20 hover:shadow-md hover:shadow-slate-900/40"
                              >
                                <ExternalLink size={16} />
                              </a>
                            </TooltipTrigger>
                            <TooltipContent
                              side="top"
                              className="bg-slate-800/90 p-2 border border-slate-700/40 shadow-md"
                            >
                              <p className="text-sm">External Link</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      );
                    },
                  )}

                {/* All links are now displayed from the API data */}
              </div>
            </div>
          </motion.div>

          {/* Advanced View Content */}
          {showAdvancedView && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-6"
            >
              <div className="bg-slate-900/80 border border-slate-800 shadow-lg rounded-xl p-5 transition-all duration-300 hover:border-blue-500/30">
                <div className="p-4 border-b border-slate-800/30 bg-slate-800/30 backdrop-blur-sm -mx-5 -mt-5 mb-5 rounded-t-xl">
                  <div className="flex items-center gap-2.5">
                    <div className="bg-slate-800/90 p-2 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 transition-all duration-300 hover:border-blue-500/30">
                      <BarChart4 className="h-4 w-4 text-blue-400" />
                    </div>
                    <h2 className="text-white text-lg font-semibold">
                      Advanced Analysis
                    </h2>
                  </div>
                </div>

                <Tabs
                  defaultValue={activeTab}
                  className="w-full"
                  onValueChange={setActiveTab}
                >
                  <TabsList className="bg-slate-800/30 border border-slate-700/20 shadow-md shadow-slate-900/40 hover:border-blue-500/30 p-2 rounded-xl mb-6 w-full flex justify-center transition-all duration-300">
                    {Object.entries(tabs).map(([key, label]) => {
                      const hasData = hasTabData(key);
                      return (
                        <TabsTrigger
                          key={key}
                          value={key}
                          disabled={!hasData}
                          className={`
                            ${hasData 
                              ? "data-[state=active]:bg-blue-600 data-[state=active]:shadow-md shadow-slate-900/40 data-[state=active]:text-white text-slate-300 hover:bg-slate-700/90 hover:border-blue-500/30" 
                              : "text-slate-500 cursor-not-allowed opacity-50"
                            }
                            rounded-xl px-4 py-2 mx-1 transition-all duration-300 flex-shrink-0 border 
                            ${hasData ? "data-[state=active]:border-blue-600/40 border-transparent" : "border-slate-700/20"}
                            font-medium
                          `}
                        >
                          {label}
                        </TabsTrigger>
                      );
                    })}
                  </TabsList>

                  {/* Team Tab */}
                  <TabsContent value="essentials" className="outline-none">
                    <div className="grid grid-cols-1 gap-6">
                      {/* Team Section */}
                      {idoData.team && Array.isArray(idoData.team) ? (
                        <TeamSection
                          projectName={idoData.name || "Project"}
                          members={idoData.team}
                        />
                      ) : (
                        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                          <h3 className="text-lg font-semibold mb-4">
                            Team Information
                          </h3>
                          <p className="text-slate-400">
                            Team information is not available at this time.
                          </p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Funding & Investment Tab */}
                  <TabsContent value="funding" className="outline-none">
                    <div className="grid grid-cols-1 gap-6">
                      {/* Funding Insights */}
                      {idoData.funding ? (
                        <FundingInsights
                          funding={idoData.funding}
                        />
                      ) : (
                        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                          <h3 className="text-lg font-semibold mb-4">
                            Funding Insights
                          </h3>
                          <p className="text-slate-400">
                            Funding insight data is not available at this time.
                          </p>
                        </div>
                      )}

                      {/* Investors Table */}
                      {idoData.investors && Array.isArray(idoData.investors) ? (
                        <InvestorsTable investors={idoData.investors} />
                      ) : (
                        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                          <h3 className="text-lg font-semibold mb-4">
                            Investors
                          </h3>
                          <p className="text-slate-400">
                            Investor data is not available at this time.
                          </p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Tokenomics Tab */}
                  <TabsContent value="tokenomics" className="outline-none">
                    <div className="grid grid-cols-1 gap-6">
                      {/* Allocation Section */}
                      {idoData.allocation && 
                       (idoData.allocation.allocations && Array.isArray(idoData.allocation.allocations) && 
                        idoData.allocation.allocations.some((alloc: any) => alloc.percentage !== null || alloc.value !== null)) ? (
                        <AllocationSection
                          allocations={idoData.allocation.allocations}
                          className=""
                        />
                      ) : idoData.allocation && (
                        idoData.allocation.teamAndAdvisors || 
                        idoData.allocation.publicSale || 
                        idoData.allocation.privateSale || 
                        idoData.allocation.ecosystem || 
                        idoData.allocation.liquidity || 
                        idoData.allocation.treasury || 
                        idoData.allocation.marketing
                      ) ? (
                        <AllocationSection
                          allocation={{
                            teamAndAdvisors: idoData.allocation.teamAndAdvisors || 0,
                            publicSale: idoData.allocation.publicSale || 0,
                            privateSale: idoData.allocation.privateSale || 0,
                            ecosystem: idoData.allocation.ecosystem || 0,
                            liquidity: idoData.allocation.liquidity || 0,
                            treasury: idoData.allocation.treasury || 0,
                            marketing: idoData.allocation.marketing || 0,
                          }}
                          className=""
                        />
                      ) : (
                        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                          <h3 className="text-lg font-semibold mb-4">
                            Token Allocation
                          </h3>
                          <p className="text-slate-400">
                            {t('emptyState.noTokenomicsInfo')}
                          </p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Token Release Tab */}
                  <TabsContent value="releases" className="outline-none">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                      <div className="md:col-span-2">
                        {idoData.unlockEvents &&
                        Array.isArray(idoData.unlockEvents) &&
                        idoData.unlockEvents.length > 0 ? (
                          <UnlockEvents
                            events={idoData.unlockEvents.map((event: any) => ({
                              date: event?.date || new Date().toISOString(),
                              daysLeft: event?.daysLeft || 0,
                              unlockAmount: String(event?.unlockAmount || "0"),
                              unlockPercentage: event?.unlockPercentage || 0,
                              maxSupply: String(event?.maxSupply || "0"),
                              allocations: Array.isArray(event?.allocations)
                                ? event.allocations
                                : [],
                            }))}
                          />
                        ) : (
                          <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                            <h3 className="text-lg font-semibold mb-4">
                              Token Unlock Schedule
                            </h3>
                            <p className="text-slate-400">
                              Token unlock schedule data is not available at
                              this time.
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="md:col-span-1">
                        <div className="space-y-6">
                          {idoData.nextUnlockEvent ? (
                            <NextUnlockEvent
                              nextUnlockEvent={{
                                date:
                                  idoData.nextUnlockEvent?.date ||
                                  new Date().toISOString(),
                                percentage:
                                  idoData.nextUnlockEvent?.unlockPercentage ||
                                  0,
                                description:
                                  idoData.nextUnlockEvent?.description ||
                                  "Token unlock event",
                                tokens: Number(
                                  idoData.nextUnlockEvent?.unlockAmount || 0,
                                ),
                                daysRemaining:
                                  idoData.nextUnlockEvent?.daysRemaining || 0,
                              }}
                            />
                          ) : (
                            <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                              <h3 className="text-lg font-semibold mb-4">
                                Next Unlock Event
                              </h3>
                              <p className="text-slate-400">
                                Next unlock event data is not available.
                              </p>
                            </div>
                          )}
                          {idoData.vestingInfoLinks &&
                          Array.isArray(idoData.vestingInfoLinks) ? (
                            <VestingInfo links={idoData.vestingInfoLinks} />
                          ) : (
                            <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                              <h3 className="text-lg font-semibold mb-4">
                                Vesting Information
                              </h3>
                              <p className="text-slate-400">
                                Vesting information is not available.
                              </p>
                            </div>
                          )}

                          {/* Supply Distribution Progress */}
                          <div className="bg-slate-900/80 border border-slate-800 shadow-lg rounded-xl p-5 transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden group">
                            <h2 className="text-lg font-semibold bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-100 mb-4 flex items-center relative z-10">
                              <div className="bg-slate-800/90 p-2 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 mr-2.5 transition-all duration-300 hover:border-blue-500/30">
                                <BarChart3 className="h-4 w-4 text-blue-400" />
                              </div>
                              Supply Distribution Progress
                            </h2>
                            <div className="relative z-10">
                              {idoData.distribution ? (
                                <DistributionProgress
                                  distribution={{
                                    released:
                                      idoData.distribution?.released ||
                                      idoData.distribution?.nextUnlock ||
                                      0,
                                    locked: idoData.distribution?.locked || 0,
                                    nextRelease:
                                      idoData.distribution?.nextRelease ||
                                      new Date().toISOString(),
                                  }}
                                />
                              ) : (
                                <p className="text-slate-400">
                                  Supply distribution data is not available.
                                </p>
                              )}
                            </div>

                            {/* Visual element for depth */}
                            <div className="absolute top-0 right-0 w-32 h-32 bg-slate-500/5 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    {idoData.allocation && idoData.allocation.allocations ? (
                      <VestingSchedule
                        allocations={idoData.allocation.allocations}
                      />
                    ) : (
                      <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                        <h3 className="text-lg font-semibold mb-4">
                          Vesting Schedule
                        </h3>
                        <p className="text-slate-400">
                          Vesting schedule data is not available in this view
                          mode.
                        </p>
                      </div>
                    )}
                  </TabsContent>

                  {/* Price Analysis Tab */}
                  <TabsContent value="analysis" className="outline-none">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="md:col-span-2">
                        {idoData.priceProjections &&
                        Array.isArray(idoData.priceProjections) &&
                        idoData.priceProjections.length > 0 ? (
                          <PriceProjectionTable
                            projections={idoData.priceProjections}
                            btcPrice={idoData.btcPrice || 0}
                          />
                        ) : (
                          <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
                            <h3 className="text-lg font-semibold mb-4">
                              Price Projections
                            </h3>
                            <p className="text-slate-400">
                              Price projection data is not available in this
                              view mode.
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="md:col-span-1">
                        <div className="bg-slate-900/80 border border-slate-800 shadow-lg rounded-xl p-5 transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden group">
                          <h2 className="text-lg font-semibold bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-100 mb-4 flex items-center relative z-10">
                            <div className="bg-slate-800/90 p-2 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 mr-2.5 transition-all duration-300 hover:border-blue-500/30">
                              <Activity className="h-4 w-4 text-blue-400" />
                            </div>
                            Market Context
                          </h2>
                          <div className="text-slate-300 text-sm space-y-4 relative z-10">
                            <div className="bg-slate-800/90 rounded-xl p-4 border border-slate-700/40 shadow-md shadow-slate-900/40 hover:border-blue-500/30 hover:shadow-lg hover:shadow-slate-900/50 transition-all duration-300 group/card">
                              <div className="flex justify-between mb-3">
                                <span className="text-sm text-slate-300 font-medium group-hover/card:text-slate-200 transition-colors duration-300">
                                  IDO Price
                                </span>
                                <span className="text-sm font-semibold text-white group-hover/card:text-blue-50 transition-colors duration-300">
                                  {idoData.idoPrice}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-slate-300 font-medium group-hover/card:text-slate-200 transition-colors duration-300">
                                  Current BTC Price
                                </span>
                                <span className="text-sm font-semibold text-white group-hover/card:text-blue-50 transition-colors duration-300">
                                  ${" "}
                                  {idoData.btcPrice
                                    ? idoData.btcPrice.toLocaleString()
                                    : ""}
                                </span>
                              </div>
                            </div>
                            <div className="bg-amber-500/15 rounded-xl p-4 border border-amber-500/20 shadow-md shadow-slate-900/40 hover:border-amber-500/30 hover:shadow-lg hover:shadow-slate-900/50 hover:bg-amber-500/20 transition-all duration-300 group/notes">
                              <h3 className="text-sm font-semibold bg-clip-text text-transparent bg-gradient-to-r from-amber-100 to-amber-300 mb-2 flex items-center">
                                <div className="bg-slate-800/90 p-1.5 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 mr-2 transition-all duration-300 hover:border-blue-500/30 group-hover/notes:bg-amber-700/20">
                                  <AlertOctagon className="h-3.5 w-3.5 text-amber-400 group-hover/notes:text-amber-300 transition-colors duration-300" />
                                </div>
                                Price Notes
                              </h3>
                              <p className="text-sm text-slate-300 leading-relaxed group-hover/notes:text-slate-200 transition-colors duration-300">
                                Projections are based on current market
                                conditions and similar project performances.
                                Actual results may vary significantly.
                              </p>
                            </div>

                            {/* Visual element for depth */}
                            <div className="absolute top-0 right-0 w-32 h-32 bg-slate-500/5 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  {/* Project Details Tab */}
                  <TabsContent value="details" className="outline-none">
                    <div className="grid grid-cols-1 gap-6">
                      {/* Project Details */}
                      <div className="bg-slate-900/80 border border-slate-800 shadow-lg rounded-xl p-5 transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden group">
                        <h2 className="text-lg font-semibold bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-100 mb-4 flex items-center relative z-10">
                          <div className="bg-slate-800/90 p-2 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 mr-2.5 transition-all duration-300 hover:border-blue-500/30">
                            <FileText className="h-4 w-4 text-blue-400" />
                          </div>
                          Project Details
                        </h2>
                        <div className="text-slate-300 text-sm space-y-3 relative z-10">
                          <div className="leading-relaxed">
                            <DataDisplay 
                              value={idoData.description} 
                              className="text-slate-300 text-sm leading-relaxed prose prose-invert prose-sm max-w-none" 
                              renderAsHtml={true}
                            />
                          </div>
                          <div className="mt-5">
                            <h3 className="text-base font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-50 to-blue-200 mb-3 flex items-center">
                              <div className="bg-slate-800/90 p-1.5 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 mr-2 transition-all duration-300 hover:border-blue-500/30">
                                <ExternalLink className="h-3.5 w-3.5 text-blue-400" />
                              </div>
                              Resources
                            </h3>
                            <div className="flex flex-wrap gap-2">
                              {idoData.resourceLinks &&
                              Array.isArray(idoData.resourceLinks)
                                ? idoData.resourceLinks.map((link: any, index: number) => (
                                    <a
                                      key={index}
                                      href={link.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="bg-slate-800/90 border border-slate-700/40 hover:border-blue-500/30 text-blue-400 hover:text-blue-300 text-xs px-3.5 py-2 rounded-xl inline-flex items-center transition-all duration-300 shadow-md shadow-slate-900/40 font-medium"
                                    >
                                      <ExternalLink
                                        size={12}
                                        className="mr-1.5"
                                      />
                                      Document {index + 1}
                                    </a>
                                  ))
                                : null}
                              {idoData.whitepaper && (
                                <a
                                  href={idoData.whitepaper}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="bg-slate-800/90 border border-slate-700/40 hover:border-blue-500/30 text-blue-400 hover:text-blue-300 text-xs px-3.5 py-2 rounded-xl inline-flex items-center transition-all duration-300 shadow-md shadow-slate-900/40 font-medium"
                                >
                                  <Book size={12} className="mr-1.5" />
                                  Whitepaper
                                </a>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Visual element for depth */}
                        <div className="absolute top-0 right-0 w-32 h-32 bg-slate-500/5 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </motion.div>
          )}

          {/* Category Metrics Breakdown Section - Enhanced for visual consistency */}
          <AnimatePresence mode="wait">
            <motion.div
              className="mt-12 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.4,
                ease: [0.25, 0.1, 0.25, 1.0],
              }}
              key="category-metrics"
            >
              <div className="bg-slate-900/80 border border-slate-800 shadow-lg rounded-xl p-5 transition-all duration-300 hover:border-blue-500/30 relative overflow-hidden group">
                <div className="absolute inset-0 bg-slate-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-slate-500/5 rounded-full -translate-x-10 -translate-y-10 opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none"></div>

                {/* Main header section with balanced spacing and alignment */}
                <div className="flex items-center justify-between mb-6">
                  {/* Left side - Title with icon */}
                  <motion.div
                    className="flex items-center gap-2.5"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1, duration: 0.3 }}
                  >
                    <div className="bg-slate-800/90 p-2 rounded-xl border border-slate-700/40 shadow-md shadow-slate-900/40 transition-all duration-300 hover:border-blue-500/30">
                      <Sparkles className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
                    </div>
                    <h2 className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-100 group-hover:from-white group-hover:to-blue-50 transition-all duration-300">
                      IDO Metrics Analysis
                    </h2>
                  </motion.div>
                </div>

                {/* Use the IDOMetricsBreakdown component for IDO pages */}
                <IDOMetricsBreakdown
                  metrics={(idoData as any)?.metrics || []}
                  overallScore={idoData.metrics?.totalScore || idoData.score}
                  onRequestFeature={handleRequestFeature}
                  onReportError={handleReportError}
                />
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </motion.div>
    </React.Fragment>
  );
}

// Using ChevronRight from lucide-react import
