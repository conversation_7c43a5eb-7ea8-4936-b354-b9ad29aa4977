import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader as Original<PERSON>ardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { GettingStartedGuide } from "@/components/GettingStartedGuide";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Save,
  Share2,
  FileDown,
  FolderOpen,
  TrendingDown,
  BarChart2,
  Info,
  AlertTriangle,
  TrendingUp,
  DollarSign,
  Plus,
  ActivitySquare,
  ShieldCheck,
  ChartLine,
  Clock,
  Shield,
  Flame
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Slider } from "@/components/ui/slider";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, Legend, LineChart, Line } from 'recharts';
import { useToast } from "@/hooks/use-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  RiskProfileType,
  PortfolioSuggestion,
} from '@/lib/types';
import {
  getPortfolioSuggestion,
  analyzePortfolio,
  riskProfiles
} from '@/lib/portfolioData';
import { InvestmentTutorial } from "@/components/InvestmentTutorial";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/use-auth";
import { LoginPromptButton } from "@/components/auth/LoginPromptButton";

interface Asset {
  symbol: string;
  fullName: string;
  percentage: number;
  value: number;
  color: string;
  description?: string;
}

interface PortfolioStats {
  riskScore: { value: number; change: string };
  totalValue: { value: number; change: string };
  concentration: { value: number; change: string };
}

interface PortfolioMetrics {
  riskScore: { value: number; change: string };
  expectedReturn: { value: number; change: string };
  sharpeRatio: { value: number; change: string };
  diversification: { value: number; change: string };
}

interface SavedPortfolio {
  id: string;
  name: string;
  createdAt: string;
  lastModified: string;
  budget: string;
  customBudget: string;
  riskProfile: string;
  timeframe: string;
  selectedSectors: string[];
  portfolioStats: PortfolioStats;
  portfolioAllocation: Asset[];
}

interface ScoreMetric {
  name: string;
  value: number;
}

const performanceData = [
  { date: '2024-12', value: 5000, score: 76 },
  { date: '2025-01', value: 5250, score: 82 },
  { date: '2025-02', value: 5100, score: 79 },
  { date: '2025-03', value: 5400, score: 85 },
  { date: '2025-04', value: 5800, score: 88 },
  { date: '2025-05', value: 5600, score: 84 },
  { date: '2025-06', value: 6000, score: 89 }
].map(item => ({
  ...item,
  formattedValue: `$${(item.value).toLocaleString()}`,
  percentageChange: ((item.value - 5000) / 5000 * 100).toFixed(1),
  scoreChange: (item.score - 76).toFixed(1)
}));

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-card/95 backdrop-blur-sm border border-border/40 rounded-lg p-3 shadow-lg">
        <p className="text-sm font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm">
            <span className="font-medium">{entry.name}: </span>
            {entry.name === 'Portfolio Value' ?
              `$${entry.value.toLocaleString()}` :
              `${entry.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.5,
      staggerChildren: 0.1
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};


const SectorDiversificationMetric = ({ sectorCount, score }: { sectorCount: number; score: number }) => (
  <Card>
    <OriginalCardHeader>
      <CardTitle>Sector Diversification</CardTitle>
    </OriginalCardHeader>
    <CardContent>
      <div>Sectors: {sectorCount}</div>
      <div>Score: {score}</div>
    </CardContent>
  </Card>
);

{/* Card Header Component */}
const CardHeader = ({ number, title, tooltip }: { number: string; title: React.ReactNode; tooltip: React.ReactNode }) => (
  <div className="flex items-center justify-between mb-6">
    <div className="flex items-center gap-3 flex-1">
      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
        <span className="text-primary font-medium">{number}</span>
      </div>
      <span className="text-xl font-semibold flex-1">{title}</span>
    </div>
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger>
          <Info className="h-5 w-5 text-muted-foreground hover:text-primary transition-colors" />
        </TooltipTrigger>
        <TooltipContent
          side="left"
          align="center"
          sideOffset={5}
          className="z-[9999] bg-card/95 backdrop-blur-sm border border-border/40 p-4 shadow-lg"
        >
          {tooltip}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
);



const PortfolioHealthScore = ({ scoreContributions, totalScore }: { scoreContributions: ScoreMetric[]; totalScore: number }) => (
  <Card>
    <OriginalCardHeader>
      <CardTitle>Portfolio Health Score</CardTitle>
    </OriginalCardHeader>
    <CardContent>
      <div>Score: {totalScore}</div>
      {/* Add score contributions here */}
    </CardContent>
  </Card>
);


export default function Generator() {
  const { toast } = useToast();
  const { user } = useAuth();
  // Add loading state to fix first-load rendering issues
  const [isLoading, setIsLoading] = useState(true);
  const [budget, setBudget] = useState('medium');
  const [customBudget, setCustomBudget] = useState('');
  const [riskProfile, setRiskProfile] = useState<RiskProfileType>('balanced');
  const [timeframe, setTimeframe] = useState('medium');
  const [selectedSectors, setSelectedSectors] = useState(['DeFi', 'AI', 'L2']);
  const [showResults, setShowResults] = useState(false);
  const [portfolioStats, setPortfolioStats] = useState<PortfolioStats>({
    riskScore: { value: 5.0, change: '+2.5%' },
    totalValue: { value: 5000, change: '+2.5%' },
    concentration: { value: 8.0, change: '+2.1%' }
  });
  const [portfolioAllocation, setPortfolioAllocation] = useState<Asset[]>([
    {
      symbol: 'BTC',
      fullName: 'Bitcoin',
      percentage: 20,
      value: 1000,
      color: 'bg-emerald-500',
      description: 'The first and most well-known cryptocurrency, designed as a decentralized digital currency.'
    },
    {
      symbol: 'ETH',
      fullName: 'Ethereum',
      percentage: 20,
      value: 1000,
      color: 'bg-emerald-500',
      description: 'A decentralized platform that enables smart contracts and decentralized applications (DApps).'
    },
    {
      symbol: 'SOL',
      fullName: 'Solana',
      percentage: 20,
      value: 1000,
      color: 'bg-blue-500',
      description: 'High-performance blockchain platform known for its fast transaction speeds and low costs.'
    },
    {
      symbol: 'ADA',
      fullName: 'Cardano',
      percentage: 20,
      value: 1000,
      color: 'bg-blue-500',
      description: 'Proof-of-stake blockchain platform with a focus on sustainability and scalability.'
    },
    {
      symbol: 'MATIC',
      fullName: 'Polygon',
      percentage: 20,
      value: 1000,
      color: 'bg-violet-500',
      description: 'Layer 2 scaling solution for Ethereum that enables faster and cheaper transactions.'
    }
  ]);
  const [isEditingWeights, setIsEditingWeights] = useState(false);
  const [portfolioName, setPortfolioName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [portfolioMetrics, setPortfolioMetrics] = useState<PortfolioMetrics>({
    riskScore: { value: 0, change: '0%' },
    expectedReturn: { value: 0, change: '0%' },
    sharpeRatio: { value: 0, change: '0%' },
    diversification: { value: 0, change: '0%' }
  });
  const [portfolioSuggestion, setPortfolioSuggestion] = useState<PortfolioSuggestion | null>(null);
  const [hoveredValue, setHoveredValue] = useState('');
  
  // Initialize component and set loading to false after mounting
  useEffect(() => {
    // Small delay to ensure component is fully mounted
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 200);
    
    return () => clearTimeout(timer);
  }, []);

  const scoreMetrics: ScoreMetric[] = [
    { name: "AI Confidence", value: 85 },
    { name: "Tokenomics", value: 78 },
    { name: "Security", value: 92 },
    { name: "Social", value: 88 },
    { name: "Market", value: 83 },
    { name: "Fundamentals", value: 87 }
  ];

  const handleSliderChange = (value: number, assetIndex: number) => {
    const currentAsset = portfolioAllocation[assetIndex];
    const diff = value - currentAsset.percentage;

    setPortfolioAllocation(prev => {
      const otherAssets = prev.filter((_, i) => i !== assetIndex);
      const totalOtherPercentages = otherAssets.reduce((sum, asset) => sum + asset.percentage, 0);

      return prev.map((asset, i) => {
        if (i === assetIndex) {
          return {
            ...asset,
            percentage: value,
            value: (value / 100) * portfolioStats.totalValue.value
          };
        }

        const ratio = asset.percentage / totalOtherPercentages;
        const adjustedPercentage = Math.max(0, asset.percentage - (diff * ratio));
        return {
          ...asset,
          percentage: adjustedPercentage,
          value: (adjustedPercentage / 100) * portfolioStats.totalValue.value
        };
      });
    });
  };

  const handleSaveWeights = () => {
    setIsEditingWeights(false);
  };

  const trendingSectors = ['DeFi', 'AI', 'Layer 1', 'Layer 2', 'RWA'];
  const additionalSectors = ['GameFi', 'Metaverse', 'Utility']; // Removed Memecoins as requested

  const handleSectorToggle = (sector: string) => {
    setSelectedSectors(prev =>
      prev.includes(sector)
        ? prev.filter(s => s !== sector)
        : [...prev, sector]
    );
  };

  const handleGeneratePortfolio = () => {
    setShowResults(false);

    if (!riskProfile || customBudget !== '' && parseFloat(customBudget) <= 0) {
      toast({
        title: "Error",
        description: "Please select a risk profile and enter a valid budget amount",
        variant: "destructive",
      });
      return;
    }

    const suggestion = getPortfolioSuggestion(riskProfile, selectedSectors);
    const analysis = analyzePortfolio(
      suggestion.assets.reduce((acc, asset) => ({
        ...acc,
        [asset.cryptoId]: asset.weight
      }), {}),
      riskProfile
    );

    setPortfolioSuggestion(suggestion);
    setPortfolioMetrics({
      riskScore: {
        value: (analysis?.riskMetrics?.totalVolatility || 0) * 10,
        change: '+2.1%'
      },
      expectedReturn: {
        value: (suggestion?.expectedReturn || 0) * 100,
        change: '+3.5%'
      },
      sharpeRatio: {
        value: analysis?.riskMetrics?.sharpeRatio || 0,
        change: '+1.2%'
      },
      diversification: {
        value: (analysis?.diversificationScore || 0) * 10,
        change: '+1.8%'
      }
    });

    setShowResults(true);
  };

  const handleSavePortfolio = async () => {
    if (!showResults) {
      toast({
        title: "Cannot save portfolio",
        description: "Please generate a portfolio first before saving.",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);

    const portfolioToSave: SavedPortfolio = {
      id: crypto.randomUUID(),
      name: portfolioName || `Portfolio ${new Date().toLocaleDateString()}`,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      budget,
      customBudget,
      riskProfile,
      timeframe,
      selectedSectors,
      portfolioStats,
      portfolioAllocation,
    };

    try {
      // TODO: Implement API call to save portfolio
      // For now, we'll just show a success message
      toast({
        title: "Portfolio saved",
        description: "Your portfolio has been saved successfully.",
      });
    } catch (error) {
      toast({
        title: "Error saving portfolio",
        description: "There was an error saving your portfolio. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSharePortfolio = () => {
    // In a real implementation, this would generate a shareable link
    toast({
      title: "Share portfolio",
      description: "Portfolio sharing link has been copied to clipboard.",
    });
  };

  const handleExportPDF = () => {
    // In a real implementation, this would generate and download a PDF
    console.log("Exporting PDF...");
  };

  const handleOpenSavedPortfolios = () => {
    // In a real implementation, this would open a modal with saved portfolios
    toast({
      title: "Saved Portfolios",
      description: "Opening your saved portfolios...",
    });
  };

  // Show loading indicator when the component is initializing
  if (isLoading) {
    return (
      <div className="flex flex-col h-screen justify-center items-center bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 text-primary animate-spin" />
          <h2 className="text-xl font-medium text-primary">Loading Portfolio Generator</h2>
          <p className="text-muted-foreground text-sm">Please wait while we prepare the AI generator...</p>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <GettingStartedGuide />
      <div className="min-h-screen bg-background text-foreground">
        <div className="container mx-auto px-4 max-w-[1540px]">
          <div className="py-12">
            <div className="mb-10">
              <h1 className="text-4xl font-bold tracking-tight text-primary mb-4">
                AI Portfolio Generator
              </h1>
              <p className="text-lg text-muted-foreground leading-relaxed max-w-[75%]">
                Generate optimized cryptocurrency portfolios using advanced AI algorithms. Our system analyzes market trends, risk factors, and performance metrics to create personalized investment strategies.
              </p>
            </div>

            <motion.div
              initial="hidden"
              animate="visible"
              variants={containerVariants}
              className="relative mb-12 p-10 rounded-lg bg-gradient-to-r from-blue-500/10 to-violet-500/10"
            >
              <div className="absolute inset-0 bg-background/40 backdrop-blur-sm rounded-lg" />
              <div className="relative space-y-12">
                <div>
                  <h2 className="text-2xl font-semibold mb-6">Financial Parameters</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Budget Card */}
                    <motion.div variants={cardVariants} id="budget-section">
                      <Card className="h-[320px] transition-all duration-200 ease-out hover:shadow-lg hover:scale-[1.01] hover:bg-card/80 hover:border-primary/20 transform-gpu">
                        <CardContent className="h-full p-6">
                          <CardHeader
                            number="1"
                            title={<div className="flex items-center gap-2">Budget <DollarSign className="h-5 w-5" /></div>}
                            tooltip={
                              <div className="w-[24rem] p-6">
                                <div className="space-y-4">
                                  <h3 className="font-semibold text-lg">Budget Allocation</h3>
                                  <div className="space-y-3">
                                    <p className="font-medium text-lg text-muted-foreground">Input Options:</p>
                                    <ul className="list-disc pl-5 space-y-1.5 text-lg">
                                      <li>Predefined tiers: Low, Mid, High Budget</li>
                                      <li>Custom amount input available</li>
                                    </ul>
                                  </div>
                                  <div className="space-y-3">
                                    <p className="font-medium text-lg text-muted-foreground">Portfolio Size:</p>
                                    <ul className="list-disc pl-5 space-y-1.5 text-lg">
                                      <li><span className="text-primary">Low Budget:</span> 5-7 projects</li>
                                      <li><span className="text-primary">Mid Budget:</span> 8-12 projects</li>
                                      <li><span className="text-primary">High Budget:</span> 13-20 projects</li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            }
                          />
                          <div className="grid grid-cols-3 gap-4 mb-6">
                            <Button
                              onClick={() => setBudget('low')}
                              className={cn(
                                "w-full h-12",
                                "bg-[#132F4C] text-[#00E4FF]",
                                "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                "border border-[#1E4976] transition-all duration-300",
                                budget === 'low' && "bg-[#173A5E]"
                              )}
                            >
                              Low
                            </Button>
                            <Button
                              onClick={() => setBudget('medium')}
                              className={cn(
                                "w-full h-12",
                                "bg-[#132F4C] text-[#00E4FF]",
                                "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                "border border-[#1E4976] transition-all duration-300",
                                budget === 'medium' && "bg-[#173A5E]"
                              )}
                            >
                              Medium
                            </Button>
                            <Button
                              onClick={() => setBudget('high')}
                              className={cn(
                                "w-full h-12",
                                "bg-[#132F4C] text-[#00E4FF]",
                                "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                "border border-[#1E4976] transition-all duration-300",
                                budget === 'high' && "bg-[#173A5E]"
                              )}
                            >
                              High
                            </Button>
                          </div>
                          <div className="relative">
                            <Input
                              type="number"
                              placeholder="Enter custom amount"
                              value={customBudget}
                              onChange={(e) => setCustomBudget(e.target.value)}
                              className="pl-16 h-12 w-full"
                            />
                            <span className="absolute left-6 top-1/2 -translate-y-1/2 text-muted-foreground">
                              USD
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>

                    {/* Risk Profile Card */}
                    <motion.div variants={cardVariants} id="risk-profile-section">
                      <Card className="h-[320px] transition-all duration-200 ease-out hover:shadow-lg hover:scale-[1.01] hover:bg-card/80 hover:border-primary/20 transform-gpu">
                        <CardContent className="h-full p-6">
                          <CardHeader
                            number="2"
                            title={<div className="flex items-center gap-2">Risk Profile <Shield className="h-5 w-5" /></div>}
                            tooltip={
                              <div className="w-[24rem] p-6">
                                <div className="space-y-4">
                                  <h3 className="font-semibold text-lg">Risk Management</h3>
                                  <div className="space-y-3">
                                    <p className="font-medium text-lg text-muted-foreground">Market Cap Allocation:</p>
                                    <div className="space-y-1.5 text-lg">
                                      <div className="grid grid-cols-4 gap-3 text-center font-medium">
                                        <div>Profile</div>
                                        <div>Low Cap</div>
                                        <div>Mid Cap</div>
                                        <div>High Cap</div>
                                      </div>
                                      <div className="grid grid-cols-4 gap-3 text-center">
                                        <div>High Risk</div>
                                        <div>50%</div>
                                        <div>20%</div>
                                        <div>30%</div>
                                      </div>
                                      <div className="grid grid-cols-4 gap-3 text-center">
                                        <div>Balanced</div>
                                        <div>30%</div>
                                        <div>30%</div>
                                        <div>40%</div>
                                      </div>
                                      <div className="grid grid-cols-4 gap-3 text-center">
                                        <div>Low Risk</div>
                                        <div>10%</div>
                                        <div>40%</div>
                                        <div>50%</div>
                                      </div>
                                      <div className="grid grid-cols-4 gap-3 text-center">
                                        <div>CoinScout</div>
                                        <div>20%</div>
                                        <div>50%</div>
                                        <div>30%</div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            }
                          />
                          <div className="grid grid-cols-2 gap-4">
                            {[
                              { value: 'low', label: 'Low Risk' },
                              { value: 'balanced', label: 'Balanced' },
                              { value: 'high', label: 'High Risk' },
                              { value: 'recommended', label: 'CoinScout Recommended' }
                            ].map((option) => (
                              <Button
                                key={option.value}
                                onClick={() => setRiskProfile(option.value as RiskProfileType)}
                                className={cn(
                                  "w-full h-12",
                                  "bg-[#132F4C] text-[#00E4FF]",
                                  "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                  "border border-[#1E4976] transition-all duration-300",
                                  riskProfile === option.value && "bg-[#173A5E]"
                                )}
                              >
                                {option.label}
                              </Button>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>

                    {/* Trending Categories Card */}
                    <motion.div variants={cardVariants} id="sectors-section">
                      <Card className="h-[320px] transition-all duration-200 ease-out hover:shadow-lg hover:scale-[1.01] hover:bg-card/80 hover:border-primary/20 transform-gpu">
                        <CardContent className="h-full p-6">
                          <CardHeader
                            number="3"
                            title={<div className="flex items-center gap-2">Trending Categories <Flame className="h-5 w-5" /></div>}
                            tooltip={
                              <div className="w-[24rem] p-6">
                                <div className="space-y-4">
                                  <h3 className="font-semibold text-lg">Trending Categories</h3>
                                  <div className="space-y-3">
                                    <p className="font-medium text-lg text-muted-foreground">Features:</p>
                                    <ul className="list-disc pl-5 space-y-1.5 text-lg">
                                      <li>Auto-detected trending categories</li>
                                      <li>Optimized allocation for top 1-3 trending categories</li>
                                      <li>Custom category selection available</li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            }
                          />
                          <div className="space-y-4">
                            <div className="flex flex-wrap gap-2">
                              {trendingSectors.map((sector) => (
                                <Badge
                                  key={sector}
                                  variant={selectedSectors.includes(sector) ? 'default' : 'outline'}
                                  className="cursor-pointer text-sm px-4 py-1.5 transition-all duration-200"
                                  onClick={() => handleSectorToggle(sector)}
                                >
                                  {sector}
                                </Badge>
                              ))}
                            </div>
                            <div className="space-y-2">
                              <p className="text-base text-muted-foreground">Additional categories:</p>
                              <div className="flex flex-wrap gap-2">
                                {additionalSectors.map((sector) => (
                                  <Badge
                                    key={sector}
                                    variant={selectedSectors.includes(sector) ? 'default' : 'outline'}
                                    className="cursor-pointer text-sm px-4 py-1.5 transition-all duration-200"
                                    onClick={() => handleSectorToggle(sector)}
                                  >
                                    {sector}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>

                    {/* Investment Timeframe Card */}
                    <motion.div variants={cardVariants} id="timeframe-section">
                      <Card className="h-[320px] transition-all duration-200 ease-out hover:shadow-lg hover:scale-[1.01] hover:bg-card/80 hover:border-primary/20 transform-gpu">
                        <CardContent className="h-full p-6">
                          <CardHeader
                            number="4"
                            title={<div className="flex items-center gap-2">Investment Timeframe <Clock className="h-5 w-5" /></div>}
                            tooltip={
                              <div className="w-[24rem] p-6">
                                <div className="space-y-4">
                                  <h3 className="font-semibold text-lg">Investment Horizon</h3>
                                  <div className="space-y-3">
                                    <p className="font-medium text-lg text-muted-foreground">Time Ranges:</p>
                                    <ul className="list-disc pl-5 space-y-1.5 text-lg">
                                      <li>
                                        <span className="text-primary">Short-Term:</span>
                                        <br />
                                        Focus on high volatility projects (1-6 months)
                                      </li>
                                      <li>
                                        <span className="text-primary">Medium-Term:</span>
                                        <br />
                                        Balance between growth and stability (6-24 months)
                                      </li>
                                      <li>
                                        <span className="text-primary">Long-Term:</span>
                                        <br />
                                        Prioritize established projects (2+ years)
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            }
                          />
                          <div className="grid grid-cols-3 gap-4">
                            <Button
                              onClick={() => setTimeframe('short')}
                              className={cn(
                                "w-full h-12",
                                "bg-[#132F4C] text-[#00E4FF]",
                                "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                "border border-[#1E4976] transition-all duration-300",
                                timeframe === 'short' && "bg-[#173A5E]"
                              )}
                            >
                              Short Term
                            </Button>
                            <Button
                              onClick={() => setTimeframe('medium')}
                              className={cn(
                                "w-full h-12",
                                "bg-[#132F4C] text-[#00E4FF]",
                                "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                "border border-[#1E4976] transition-all duration-300",
                                timeframe === 'medium' && "bg-[#173A5E]"
                              )}
                            >
                              Medium Term
                            </Button>
                            <Button
                              onClick={() => setTimeframe('long')}
                              className={cn(
                                "w-full h-12",
                                "bg-[#132F4C] text-[#00E4FF]",
                                "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                "border border-[#1E4976] transition-all duration-300",
                                timeframe === 'long' && "bg-[#173A5E]"
                              )}
                            >
                              Long Term
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </div>


                  <div className="mt-8">
                    <motion.div
                      className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-10"
                      variants={cardVariants}
                    >
                      <div className="flex items-center justify-between w-full">
                        <Button
                          onClick={handleGeneratePortfolio}
                          className={cn(
                            "px-10",
                            "bg-[#132F4C] text-[#00E4FF]",
                            "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                            "border border-[#1E4976] transition-all duration-300"
                          )}
                        >
                          Generate Portfolio
                        </Button>
                      </div>

                      {showResults && (
                        <div className="flex items-center gap-3">
                          <Input
                            placeholder="Portfolio name"
                            value={portfolioName}
                            onChange={(e) => setPortfolioName(e.target.value)}
                            className="w-48"
                          />
                          <div className="flex items-center gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  {user ? (
                                    <Button
                                      onClick={handleSavePortfolio}
                                      disabled={isSaving}
                                      className={cn(
                                        "flex-1 sm:flex-none flex items-center gap-2",
                                        "bg-[#132F4C] text-[#00E4FF]",
                                        "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                        "border border-[#1E4976] transition-all duration-300"
                                      )}
                                    >
                                      <Save className="h-5 w-5" />
                                      <span className="sm:hidden lg:inline">{isSaving ? 'Saving...' : 'Save'}</span>
                                    </Button>
                                  ) : (
                                    <LoginPromptButton
                                      className={cn(
                                        "flex-1 sm:flex-none flex items-center gap-2",
                                        "bg-[#132F4C] text-[#00E4FF]",
                                        "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                        "border border-[#1E4976] transition-all duration-300"
                                      )}
                                      feature="Portfolio Saving"
                                      onClick={handleSavePortfolio}
                                    >
                                      <Save className="h-5 w-5 mr-2" />
                                      <span className="sm:hidden lg:inline">Save</span>
                                    </LoginPromptButton>
                                  )}
                                </TooltipTrigger>
                                <TooltipContent side="top" align="center" sideOffset={5} className="z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 p-4 shadow-lg">
                                  <p className="text-sm">Save this portfolio</p>
                                </TooltipContent>
                              </Tooltip>
                              </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    onClick={handleSharePortfolio}
                                    className={cn(
                                      "flex-1 sm:flex-none flex items-center gap-2",
                                      "bg-[#132F4C] text-[#00E4FF]",
                                      "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                               "border border-[#1E4976] transition-all duration-300"
                                    )}
                                  >
                                    <Share2 className="h-5 w-5" />
                                    <span className="sm:hidden lg:inline">{t('watchlist.share')}</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent side="top" align="center" sideOffset={5} className="z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 p-4 shadow-lg">
                                  <p className="text-sm">Share portfolio with others</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    onClick={handleExportPDF}
                                    className={cn(
                                      "flex-1 sm:flex-none flex items-center gap-2",
                                      "bg-[#132F4C] text-[#00E4FF]",
                                      "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                      "border border-[#1E4976] transition-all duration-300"
                                    )}
                                  >
                                    <FileDown className="h-5 w-5" />
                                    <span className="sm:hidden lg:inline">Export</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent side="top" align="center" sideOffset={5} className="z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 p-4 shadow-lg">
                                  <p className="text-sm">Export as PDF report</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    onClick={handleOpenSavedPortfolios}
                                    className={cn(
                                      "flex-1 sm:flex-none flex items-center gap-2",
                                      "bg-[#132F4C] text-[#00E4FF]",
                                      "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                      "border border-[#1E4976] transition-all duration-300"
                                    )}
                                  >
                                    <FolderOpen className="h-5 w-5" />
                                    <span className="sm:hidden lg:inline">Open</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent side="top" align="center" sideOffset={5} className="z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 p-4 shadow-lg">
                                  <p className="text-sm">View saved portfolios</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      )}
                    </motion.div>

                    {showResults && (
                      <motion.div
                        variants={cardVariants}
                        className="grid gap-6"
                      >
                        <div className="mb-8">
                          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                            <ActivitySquare className="h-5 w-5 text-primary" />
                            Portfolio Statistics
                          </h2>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Card className="bg-[#132F4C] border-[#1E4976]">
                              <CardContent className="pt-6">
                                <div className="flex items-center justify-between mb-2">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center gap-2 cursor-help">
                                          <Shield className="h-5 w-5 text-blue-500" />
                                          <span className="text-sm text-muted-foreground">Risk Score</span>
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent side="top" align="center" sideOffset={5} className="w-80 p-4 z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 shadow-lg">
                                        <div className="space-y-3">
                                          <div className="flex items-center gap-2">
                                            <Shield className="h-5 w-5 text-blue-500" />
                                            <h4 className="font-semibold text-lg">Risk Assessment</h4>
                                          </div>
                                          <div className="space-y-2">
                                            <p className="text-sm text-muted-foreground leading-relaxed">
                                              Portfolio risk level on a scale of 1-10:
                                            </p>
                                            <ul className="space-y-1.5 text-sm">
                                              <li className="flex items-center gap-2">
                                                <span className="h-2 w-2 rounded-full bg-green-500"></span>
                                                <span>1-3: Conservative • Stable assets • Low volatility</span>
                                              </li>
                                              <li className="flex items-center gap-2">
                                                <span className="h-2 w-2 rounded-full bg-yellow-500"></span>
                                                <span>4-7: Moderate • Balanced risk • Mixed assets</span>
                                              </li>
                                              <li className="flex items-center gap-2">
                                                <span className="h-2 w-2 rounded-full bg-red-500"></span>
                                                <span>8-10: Aggressive • High growth potential • Volatile</span>
                                              </li>
                                            </ul>
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Info className="h-4 w-4 text-muted-foreground/50 cursor-help hover:text-muted-foreground transition-colors" />
                                      </TooltipTrigger>
                                      <TooltipContent side="top" align="center" sideOffset={5} className="w-80 p-4 z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 shadow-lg">
                                        <div className="space-y-3">
                                          <h4 className="font-semibold text-lg">Portfolio Metrics</h4>
                                          <p className="text-sm text-muted-foreground">View detailed metrics and performance indicators for your portfolio.</p>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                                <div className="space-y-1">
                                  <div className="text-2xl font-bold">{portfolioStats.riskScore.value.toFixed(1)}/10</div>
                                  <div className={cn(
                                    "text-sm flex items-center gap-1",
                                    portfolioStats.riskScore.change.startsWith('+') ? 'text-emerald-500' : 'text-red-500'
                                  )}>
                                    {portfolioStats.riskScore.change.startsWith('+') ?
                                      <TrendingUp className="h-3 w-3" /> :
                                      <TrendingDown className="h-3 w-3" />
                                    }
                                    {portfolioStats.riskScore.change}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>

                            <Card className="bg-[#132F4C] border-[#1E4976]">
                              <CardContent className="pt-6">
                                <div className="flex items-center justify-between mb-2">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center gap-2 cursor-help">
                                          <DollarSign className="h-5 w-5 text-emerald-500" />
                                          <span className="text-sm text-muted-foreground">Total Value</span>
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent side="top" align="center" sideOffset={5} className="w-80 p-4 z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 shadow-lg">
                                        <div className="space-y-3">
                                          <div className="flex items-center gap-2">
                                            <DollarSign className="h-5 w-5 text-emerald-500" />
                                            <h4 className="font-semibold text-lg">Portfolio Value</h4>
                                          </div>
                                          <div className="space-y-2">
                                            <p className="text-sm text-muted-foreground leading-relaxed">
                                              Real-time portfolio valuation metrics:
                                            </p>
                                            <ul className="space-y-1.5 text-sm">
                                              <li className="flex items-center gap-2">
                                                <ChartLine className="h-4 w-4 text-emerald-500" />
                                                <span>Live market price tracking</span>
                                              </li>
                                              <li className="flex items-center gap-2">
                                                <TrendingUp className="h-4 w-4 text-blue-500" />
                                                <span>24h performance analysis</span>
                                              </li>
                                              <li className="flex items-center gap-2">
                                                <ActivitySquare className="h-4 w-4 text-violet-500" />
                                                <span>Profit/Loss monitoring</span>
                                              </li>
                                            </ul>
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Info className="h-4 w-4 text-muted-foreground/50 cursor-help hover:text-muted-foreground transition-colors" />
                                      </TooltipTrigger>
                                      <TooltipContent side="top" align="center" sideOffset={5} className="w-80 p-4 z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 shadow-lg">
                                        <div className="space-y-3">
                                          <h4 className="font-semibold text-lg">Portfolio Metrics</h4>
                                          <p className="text-sm text-muted-foreground">View detailed metrics and performance indicators for your portfolio.</p>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                                <div className="space-y-1">
                                  <div className="text-2xl font-bold">${portfolioStats.totalValue.value.toLocaleString()}</div>
                                  <div className={cn(
                                    "text-sm flex items-center gap-1",
                                    portfolioStats.totalValue.change.startsWith('+') ? 'text-emerald-500' : 'text-red-500'
                                  )}>
                                    {portfolioStats.totalValue.change.startsWith('+') ?
                                      <TrendingUp className="h-3 w-3" /> :
                                      <TrendingDown className="h-3 w-3" />
                                    }
                                    {portfolioStats.totalValue.change}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>

                            <Card className="bg-[#132F4C] border-[#1E4976]">
                              <CardContent className="pt-6">
                                <div className="flex items-center justify-between mb-2">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center gap-2 cursor-help">
                                          <BarChart2 className="h-5 w-5 text-violet-500" />
                                          <span className="text-sm text-muted-foreground">Concentration</span>
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent side="top" align="center" sideOffset={5} className="w-80 p-4 z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 shadow-lg">
                                        <div className="space-y-3">
                                          <div className="flex items-center gap-2">
                                            <BarChart2 className="h-5 w-5 text-violet-500" />
                                            <h4 className="font-semibold text-lg">Asset Distribution</h4>
                                          </div>
                                          <div className="space-y-2">
                                            <p className="text-sm text-muted-foreground leading-relaxed">
                                              Portfolio diversification score (1-10):
                                            </p>
                                            <ul className="space-y-1.5 text-sm">
                                              <li className="flex items-center gap-2">
                                                <span className="h-2 w-2 rounded-full bg-emerald-500"></span>
                                                <span>8-10: Optimal • Well-balanced across sectors</span>
                                              </li>
                                              <li className="flex items-center gap-2">
                                                <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                                                <span>5-7: Average • Room for improvement</span>
                                              </li>
                                              <li className="flex items-center gap-2">
                                                <span className="h-2 w-2 rounded-full bg-red-500"></span>
                                                <span>1-4: High risk • Need diversification</span>
                                              </li>
                                            </ul>
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Info className="h-4 w-4 text-muted-foreground/50 cursor-help hover:text-muted-foreground transition-colors" />
                                      </TooltipTrigger>
                                      <TooltipContent side="top" align="center" sideOffset={5} className="w-80 p-4 z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 shadow-lg">
                                        <div className="space-y-3">
                                          <h4 className="font-semibold text-lg">Portfolio Metrics</h4>
                                          <p className="text-sm text-muted-foreground">View detailed metrics and performance indicators for your portfolio.</p>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                                <div className="space-y-1">
                                  <div className="text-2xl font-bold">{portfolioStats.concentration.value.toFixed(1)}/10</div>
                                  <div className={cn(
                                    "text-sm flex items-center gap-1",
                                    portfolioStats.concentration.change.startsWith('+') ? 'text-emerald-500' : 'text-red-500'
                                  )}>
                                    {portfolioStats.concentration.change.startsWith('+') ?
                                      <TrendingUp className="h-3 w-3" /> :
                                      <TrendingDown className="h-3 w-3" />
                                    }
                                    {portfolioStats.concentration.change}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        </div>

                        <Card className="w-full">
                          <OriginalCardHeader>
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-xl font-semibold">Portfolio Allocation</CardTitle>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="outline"
                                      onClick={() => setIsEditingWeights(!isEditingWeights)}
                                      className={cn(
                                        "bg-[#132F4C] text-[#00E4FF]",
                                        "hover:bg-[#173A5E] hover:text-[#00E4FF]",
                                        "border border-[#1E4976] transition-all duration-300"
                                      )}
                                    >
                                      {isEditingWeights ? 'Save Weights' : 'Edit Weights'}
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent side="top" align="center" sideOffset={5} className="z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 p-4 shadow-lg">
                                    <p className="text-sm">Adjust the allocation percentages of your portfolio assets</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </OriginalCardHeader>
                          <CardContent>
                            <div className="grid gap-6">
                              {portfolioAllocation.map((asset, index) => (
                                <div key={asset.symbol} className="space-y-3 transition-all duration-200 ease-out hover:transform hover:scale-[1.01] hover:bg-card/5 hover:rounded-lg hover:shadow-lg p-3">
                                  <div className="flex items-center justify-between">
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Label className="text-lg font-medium cursor-help">
                                            {asset.fullName} ({asset.symbol})
                                          </Label>
                                        </TooltipTrigger>
                                        <TooltipContent side="top" align="center" sideOffset={5} className="w-80 p-4 z-[9999] relative bg-card/95 backdrop-blur-sm border border-border/40 shadow-lg">
                                          <p className="text-sm text-muted-foreground">{asset.description}</p>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                    <div className="flex items-center gap-4">
                                      <span className="text-lg font-medium">
                                        {asset.percentage.toFixed(1)}%
                                      </span>
                                      <span className="text-lg text-muted-foreground">
                                        ${asset.value.toFixed(2)}
                                      </span>
                                    </div>
                                  </div>
                                  {isEditingWeights ? (
                                    <Slider
                                      value={[asset.percentage]}
                                      onValueChange={([value]) => handleSliderChange(value, index)}
                                      max={100}
                                      step={1}
                                      className="h-2"
                                    />
                                  ) : (
                                    <Progress value={asset.percentage} className="h-2" />
                                  )}
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          <Card className="h-[300px] transition-all duration-200 ease-out hover:shadow-lg hover:scale-[1.01] hover:bg-card/80 hover:border-primary/20 transform-gpu">
                            <OriginalCardHeader className="pb-4">
                              <CardTitle className="text-lg font-medium">Performance History</CardTitle>
                            </OriginalCardHeader>
                            <CardContent>
                              <ResponsiveContainer width="100%" height={200}>
                                <AreaChart
                                  data={performanceData}
                                  margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
                                >
                                  <defs>
                                    <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                                      <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.3}/>
                                      <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0}/>
                                    </linearGradient>
                                  </defs>
                                  <CartesianGrid strokeDasharray="3 3" className="stroke-border/40" />
                                  <XAxis
                                    dataKey="date"
                                    className="text-xs text-muted-foreground"
                                    tickLine={false}
                                  />
                                  <YAxis
                                    className="text-xs text-muted-foreground"
                                    tickFormatter={(value) => `$${value.toLocaleString()}`}
                                    tickLine={false}
                                  />
                                  <RechartsTooltip content={<CustomTooltip />} />
                                  <Area
                                    type="monotone"
                                    dataKey="value"
                                    name="Portfolio Value"
                                    stroke="hsl(var(--primary))"
                                    fillOpacity={1}
                                    fill="url(#colorValue)"
                                    strokeWidth={2}
                                  />
                                </AreaChart>
                              </ResponsiveContainer>
                            </CardContent>
                          </Card>

                          <Card className="h-[300px] transition-all duration-200 ease-out hover:shadow-lg hover:scale-[1.01] hover:bg-card/80 hover:border-primary/20 transform-gpu">
                            <OriginalCardHeader className="pb-4">
                              <CardTitle className="text-lg font-medium">Score Trend</CardTitle>
                            </OriginalCardHeader>
                            <CardContent>
                              <ResponsiveContainer width="100%" height={200}>
                                <LineChart
                                  data={performanceData}
                                  margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
                                >
                                  <CartesianGrid strokeDasharray="3 3" className="stroke-border/40" />
                                  <XAxis
                                    dataKey="date"
                                    className="text-xs text-muted-foreground"
                                    tickLine={false}
                                  />
                                  <YAxis
                                    domain={[60, 100]}
                                    className="text-xs text-muted-foreground"
                                    tickLine={false}
                                  />
                                  <RechartsTooltip content={<CustomTooltip />} />
                                  <Line
                                    type="monotone"
                                    dataKey="score"
                                    name="Portfolio Score"
                                    stroke="hsl(var(--primary))"
                                    strokeWidth={2}
                                    dot={{
                                      stroke: 'hsl(var(--primary))',
                                      strokeWidth: 2,
                                      fill: 'hsl(var(--background))'
                                    }}
                                    activeDot={{
                                      stroke: 'hsl(var(--primary))',
                                      strokeWidth: 2,
                                      fill: 'hsl(var(--primary))',
                                      r: 6
                                    }}
                                  />
                                </LineChart>
                              </ResponsiveContainer>
                            </CardContent>
                          </Card>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}