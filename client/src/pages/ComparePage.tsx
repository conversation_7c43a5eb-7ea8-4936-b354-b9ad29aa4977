import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";
import ConceptBaseLayout from "@/components/compare-demo/ConceptBaseLayout";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ComparisonCoin } from "@/data/compareDemoData";
import { CoinSelector } from "@/components/compare-demo/CoinSelector";
import { useComparisonStore } from "@/stores/comparisonStore";
import {
  ArrowDown,
  ArrowUp,
  Info,
  Medal,
  AlertTriangle,
  Activity,
  ExternalLink,
  Award,
  Trophy,
  ChevronDown,
  Loader2,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

// Utility function for consistent coin logo handling
const CoinLogo = ({
  coin,
  size = "md",
  className = "",
}: {
  coin: ComparisonCoin;
  size?: "sm" | "md" | "lg";
  className?: string;
}) => {
  const sizeClasses = {
    sm: "h-5 w-5",
    md: "h-6 w-6",
    lg: "h-10 w-10",
  };

  const [useInitials, setUseInitials] = useState(false);

  // Coin sembolünden baş harfleri alıp göster
  const initials = coin.symbol.slice(0, 2).toUpperCase();

  // Eğer sadece baş harfleri gösteriyorsak
  if (useInitials) {
    return (
      <div className={className}>
        <div
          className={`${sizeClasses[size]} rounded-full bg-blue-600/20 flex items-center justify-center text-blue-400 text-xs font-bold`}
        >
          {initials}
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <img
        src={`https://cdn.jsdelivr.net/gh/atomiclabs/cryptocurrency-icons@bea1507f8c/128/color/${coin.symbol.toLowerCase()}.png`}
        alt={coin.name}
        className={`${sizeClasses[size]} rounded-full`}
        onError={(e) => {
          // Try another CDN as backup
          const img = e.target as HTMLImageElement;
          img.src = `https://raw.githubusercontent.com/spothq/cryptocurrency-icons/master/128/color/${coin.symbol.toLowerCase()}.png`;

          // Final fallback to initials
          img.onerror = () => {
            setUseInitials(true);
          };
        }}
      />
    </div>
  );
};

export default function CompareDemoConcept2() {
  const { t } = useLanguage();
  const [activeCategory, setActiveCategory] = useState("Tokenomics");
  const [, setLocation] = useLocation();
  const { isLoggedIn } = useAuth();

  // Use comparison store
  const { selectedCoins } = useComparisonStore();

  // Eğer kullanıcı giriş yapmamışsa login sayfasına yönlendir
  useEffect(() => {
    if (!isLoggedIn) {
      // Kullanıcı giriş yaptıktan sonra bu sayfaya geri dönebilmesi için mevcut URL'i de gönder
      const currentPath = window.location.pathname;
      setLocation(`/login?returnTo=${encodeURIComponent(currentPath)}`);
    }
  }, [isLoggedIn, setLocation]);

  // Handlers for coin selector events
  const handleCoinAdded = (coin: ComparisonCoin) => {
    console.log("Coin added to comparison:", coin.name);
  };

  const handleCoinRemoved = (coinId: string) => {
    console.log("Coin removed from comparison:", coinId);
  };

  // Find the best overall performer among selected coins
  const findBestOverallPerformer = () => {
    if (selectedCoins.length === 0) return null;
    return selectedCoins.reduce(
      (best: ComparisonCoin, current: ComparisonCoin) => (current.rating > best.rating ? current : best),
      selectedCoins[0],
    );
  };

  const bestPerformer = findBestOverallPerformer();

  // Get all unique metrics from all coins for the active category
  const getMetricsForCategory = (categoryName: string) => {
    const metrics = new Set<string>();
    selectedCoins.forEach((coin) => {
      const score = coin.scores?.find((s) => s.name === categoryName);
      if (score && score.subScores) {
        score.subScores.forEach((subScore) => {
          metrics.add(subScore.name);
        });
      }
    });
    return Array.from(metrics);
  };

  const metrics = getMetricsForCategory(activeCategory);

  const getStatusColor = (score: number): string => {
    if (score >= 85) return "bg-emerald-500";
    if (score >= 70) return "bg-blue-500";
    if (score >= 50) return "bg-amber-500";
    return "bg-red-500";
  };

  const getStatusTextColor = (score: number): string => {
    if (score >= 85) return "text-emerald-500";
    if (score >= 70) return "text-blue-500";
    if (score >= 50) return "text-amber-500";
    return "text-red-500";
  };

  const getBackgroundColor = (score: number): string => {
    if (score >= 85) return "bg-emerald-500/10";
    if (score >= 70) return "bg-blue-500/10";
    if (score >= 50) return "bg-amber-500/10";
    return "bg-red-500/10";
  };

  const formatNumber = (num: number): string => {
    if (num >= 1_000_000_000) {
      return `$${(num / 1_000_000_000).toFixed(1)}B`;
    } else if (num >= 1_000_000) {
      return `$${(num / 1_000_000).toFixed(1)}M`;
    } else if (num >= 1_000) {
      return `$${(num / 1_000).toFixed(1)}K`;
    }
    return `$${num.toFixed(2)}`;
  };

  // Function to find the best coin for a specific metric
  const findBestCoinForMetric = (metricName: string): ComparisonCoin | null => {
    let bestCoin: ComparisonCoin | null = null;
    let highestScore = -1;

    selectedCoins.forEach((coin) => {
      const score = coin.scores?.find((s) => s.name === activeCategory);
      if (score && score.subScores) {
        const subScore = score.subScores.find((sub) => sub.name === metricName);
        if (subScore && subScore.value > highestScore) {
          bestCoin = coin;
          highestScore = subScore.value;
        }
      }
    });

    return bestCoin;
  };

  // Function to get metric value for a coin
  const getMetricScore = (coin: ComparisonCoin, metricName: string) => {
    const score = coin.scores?.find((s) => s.name === activeCategory);
    if (score && score.subScores) {
      const subScore = score.subScores.find((sub) => sub.name === metricName);
      return subScore?.value || 0;
    }
    return 0;
  };

  // Function to get metric description
  const getMetricDescription = (metricName: string) => {
    // Basit açıklama döndür - API'de bu veri olmadığı için
    return `${metricName} score for ${activeCategory}`;
  };

  const translateCategoryName = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'tokenomics':
        return t("tokenomics", "compare", "Tokenomics");
      case 'security':
        return t("security", "compare", "Security");
      case 'socials':
        return t("socials", "compare", "Socials");
      case 'market':
        return t("market", "compare", "Market");
      case 'insights':
        return t("insights", "compare", "Insights");
      default:
        return categoryName;
    }
  };

  return (
    <ConceptBaseLayout conceptId="concept-2" title="" description="">
      {/* Selector and Best Performer Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Coin Selector Card */}
        <Card className="bg-slate-800/80 border-slate-700/40 shadow-md shadow-slate-900/40">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div id="top"></div>
              <div>
                <CardTitle className="text-xl font-semibold flex items-center gap-2">
                  <span className="text-blue-400">
                    <Activity className="w-5 h-5" />
                  </span>
                  {t("title", "compare", "Compare Cryptocurrencies")}
                </CardTitle>
                {/* Description and coin count removed as requested */}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <CoinSelector
              onCoinAdded={handleCoinAdded}
              onCoinRemoved={handleCoinRemoved}
              maxSelections={4}
            />
          </CardContent>
        </Card>

        {/* Best Performer Card */}
        <Card className="bg-slate-800/80 border-slate-700/40 shadow-md shadow-slate-900/40">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-xl font-semibold flex items-center gap-2">
                  <span className="text-amber-400">
                    <Award className="w-5 h-5" />
                  </span>
                  {t("bestPerformer", "compare", "Best Performer")}
                </CardTitle>
              </div>
              {bestPerformer && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs gap-1 p-0 border-none hover:bg-transparent"
                >
                  View Detailed Analysis <ExternalLink className="h-3 w-3" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {bestPerformer ? (
              <div>
                {/* Coin Info */}
                <div className="flex items-center gap-3 mb-5">
                  <CoinLogo coin={bestPerformer} size="lg" />
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="text-lg font-bold">
                        {bestPerformer.name}
                      </h3>
                      <Badge
                        variant="outline"
                        className="bg-slate-700/50 text-slate-300 border-slate-600"
                      >
                        {bestPerformer.symbol}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Overall Score */}
                <div className="mb-3">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-medium text-slate-300">
                      {t("overallScore", "compare", "Overall Score")}
                    </h4>
                    <span
                      className={`font-bold text-lg ${getStatusTextColor(bestPerformer.rating)}`}
                    >
                      {bestPerformer.rating}/100
                    </span>
                  </div>
                  <div className="relative pt-1">
                    <div className="flex mb-1 justify-between">
                      <span className="text-xs text-slate-500">0</span>
                      <span className="text-xs text-slate-500">20</span>
                      <span className="text-xs text-slate-500">40</span>
                      <span className="text-xs text-slate-500">60</span>
                      <span className="text-xs text-slate-500">80</span>
                      <span className="text-xs text-slate-500">100</span>
                    </div>
                    <div className="flex h-2 mb-4 overflow-hidden rounded bg-slate-700">
                      <div
                        style={{ width: `${bestPerformer.rating}%` }}
                        className={`${getStatusColor(bestPerformer.rating)}`}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-slate-400">
                <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p>{t("noCoinsSelected", "compare", "No coins selected for comparison")}</p>
                <p className="text-sm mt-2">
                  {t("selectCoinsForBest", "compare", "Please select coins to see the best performer")}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Coin Comparison Card with Tabs */}
      <Card className="bg-slate-800/80 border-slate-700/40 shadow-md shadow-slate-900/40">
        {/* No card header needed anymore */}

        <Tabs defaultValue="overview">
          {/* Tab Controls - Positioned at the top of the card with better spacing */}
          <div className="px-6 py-4 mb-2">
            <div className="bg-slate-900/50 p-1 rounded-lg border border-slate-700/50 inline-block">
              <TabsList className="bg-transparent">
                <TabsTrigger
                  value="overview"
                  className="data-[state=active]:bg-slate-800 data-[state=active]:text-white text-slate-400 hover:text-white transition-all"
                >
                  {t("overview", "compare", "Overview")}
                </TabsTrigger>
                <TabsTrigger
                  value="metrics"
                  className="data-[state=active]:bg-slate-800 data-[state=active]:text-white text-slate-400 hover:text-white transition-all"
                >
                  {t("metrics", "compare", "Metrics")}
                </TabsTrigger>
              </TabsList>
            </div>
          </div>
          {/* Overview Tab Content */}
          <TabsContent value="overview" className="mt-0">
            <CardContent>
              <div className="overflow-x-auto">
                <Table className="w-full">
                  <TableHeader className="bg-slate-900/40">
                    <TableRow className="py-3 h-16">
                      <TableHead className="text-left py-3">{t("metric", "compare", "Metric")}</TableHead>
                      {selectedCoins.map((coin) => (
                        <TableHead key={coin.id} className="text-center">
                          <div className="flex flex-col items-center">
                            <CoinLogo coin={coin} size="md" className="mb-1" />
                            {coin.name}
                          </div>
                        </TableHead>
                      ))}
                      <TableHead className="text-center w-[120px]">
                        <div className="flex flex-col items-center">
                          <div className="h-6 w-6 bg-amber-500/80 rounded-full flex items-center justify-center mb-1">
                            <Trophy className="h-3 w-3 text-white" />
                          </div>
                          {t("winner", "compare", "Winner")}
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {/* Price */}
                    <TableRow className="border-slate-700/40">
                      <TableCell className="font-medium">{t("price", "compare", "Price")}</TableCell>
                      {selectedCoins.map((coin) => (
                        <TableCell key={coin.id} className="text-center">
                          <div className="font-medium bg-slate-800/50 py-1 px-2 rounded">
                            ${(coin.price || 0).toLocaleString()}
                          </div>
                        </TableCell>
                      ))}
                      <TableCell className="text-center"></TableCell>
                    </TableRow>

                    {/* Market Cap */}
                    <TableRow className="border-slate-700/40">
                      <TableCell className="font-medium">{t("marketCap", "compare", "Market Cap")}</TableCell>
                      {selectedCoins.map((coin) => (
                        <TableCell key={coin.id} className="text-center">
                          <div className="font-medium bg-slate-800/50 py-1 px-2 rounded">
                            {formatNumber(coin.marketCap || 0)}
                          </div>
                        </TableCell>
                      ))}
                      <TableCell className="text-center"></TableCell>
                    </TableRow>

                    {/* API'den gelen skorları tabloda göster */}
                    {selectedCoins.length > 0 && (
                      <>
                        {/* Tüm coinler için mevcut score kategorilerini topla */}
                        {(() => {
                          // Get all unique score categories from all coins
                          const allCategories = new Set<string>();
                          selectedCoins.forEach((coin) => {
                            if (coin.scores && Array.isArray(coin.scores)) {
                              coin.scores.forEach((score) => {
                                allCategories.add(score.name);
                              });
                            }
                          });

                          // Convert Set to Array and sort if needed
                          return Array.from(allCategories).map(
                            (categoryName) => (
                              <TableRow
                                key={categoryName}
                                className="border-slate-700/40"
                              >
                                <TableCell className="font-medium">
                                  {categoryName}
                                </TableCell>
                                {selectedCoins.map((coin) => {
                                  console.log(
                                    `Looking for ${categoryName} in coin ${coin.name} with scores:`,
                                    coin.scores
                                      ? coin.scores.map((s) => s.name)
                                      : "no scores",
                                  );

                                  // Coin'in scores'ı yoksa hata logla
                                  if (!coin.scores) {
                                    console.error(
                                      `No scores array for coin ${coin.name}!`,
                                    );
                                    console.log(
                                      "Full coin object:",
                                      JSON.stringify(coin, null, 2),
                                    );
                                  }

                                  // coin.scores dizisinde bu kategoriyi bul
                                  const scoreItem = coin.scores?.find(
                                    (s) => s.name === categoryName,
                                  );
                                  console.log(
                                    `Found score ${categoryName} for ${coin.name}:`,
                                    scoreItem,
                                  );

                                  // Score varsa total değerini al, yoksa 0 kullan
                                  const score = scoreItem?.total || 0;
                                  
                                  // Bu kategoride en iyi coini bul (kupa için)
                                  let bestCoinForCategory: ComparisonCoin | null = null;
                                  let highestScoreForCategory = -1;
                                  
                                  selectedCoins.forEach((c: ComparisonCoin) => {
                                    const catScoreItem = c.scores?.find((s: any) => s.name === categoryName);
                                    const catScore = catScoreItem ? catScoreItem.total : 0;
                                    if (catScore > highestScoreForCategory) {
                                      highestScoreForCategory = catScore;
                                      bestCoinForCategory = c;
                                    }
                                  });
                                  
                                  // Bu coin bu kategorinin en iyisi mi?
                                  const isThisCoinBestForCategory = bestCoinForCategory?.id === coin.id && score > 0;

                                  console.log(
                                    `Score for ${categoryName} / ${coin.name}: ${score}/100`,
                                  );

                                  return (
                                    <TableCell
                                      key={coin.id}
                                      className="text-center"
                                    >
                                      <div
                                        className={`transition-all duration-300 px-3 py-2 rounded ${getBackgroundColor(score)} relative group w-32 h-10 mx-auto flex items-center justify-center`}
                                      >
                                        <div
                                          className={`font-bold ${getStatusTextColor(score)}`}
                                        >
                                          {score}/100
                                        </div>
                                        {isThisCoinBestForCategory && (
                                          <motion.div
                                            initial={{ opacity: 0, scale: 0.8 }}
                                            animate={{ opacity: 1, scale: 1 }}
                                            transition={{ duration: 0.3 }}
                                            className="absolute -top-2 -right-2 h-5 w-5 bg-amber-500 rounded-full flex items-center justify-center"
                                          >
                                            <Trophy className="h-3 w-3 text-white" />
                                          </motion.div>
                                        )}
                                      </div>
                                    </TableCell>
                                  );
                                })}

                                {/* Bu kategori için en iyi coini belirle */}
                                <TableCell className="text-center">
                                  {(() => {
                                    let bestCoin: ComparisonCoin | null = null;
                                    let highestScore = -1;

                                    selectedCoins.forEach((coin: ComparisonCoin) => {
                                      const scoreItem = coin.scores?.find(
                                        (s: any) => s.name === categoryName,
                                      );
                                      const score = scoreItem
                                        ? scoreItem.total
                                        : 0;
                                      if (score > highestScore) {
                                        highestScore = score;
                                        bestCoin = coin;
                                      }
                                    });

                                    return highestScore > 0 && bestCoin ? (
                                      <div className="flex items-center justify-center gap-1.5">
                                        <CoinLogo coin={bestCoin} size="sm" />
                                        <span className="text-sm text-slate-300">
                                          {bestCoin.symbol}
                                        </span>
                                      </div>
                                    ) : (
                                      <div className="text-slate-500 text-sm">
                                        No winner
                                      </div>
                                    );
                                  })()}
                                </TableCell>
                              </TableRow>
                            ),
                          );
                        })()}
                      </>
                    )}

                    {/* Overall Rating */}
                    <TableRow className="border-slate-700/40 bg-slate-900/40">
                      <TableCell className="font-medium">
                        {t("overallRating", "compare", "Overall Rating")}
                      </TableCell>

                      {selectedCoins.length > 0 ? (
                        <>
                          {selectedCoins.map((coin) => {
                            // Find the best rated coin
                            let bestCoinId = null;
                            let highestRating = -1;

                            for (const c of selectedCoins) {
                              if (c.rating > highestRating) {
                                highestRating = c.rating;
                                bestCoinId = c.id;
                              }
                            }

                            const isBest = coin.id === bestCoinId;

                            return (
                              <TableCell key={coin.id} className="text-center">
                                <div
                                  className={`transition-all duration-300 px-3 py-2 rounded ${getBackgroundColor(coin.rating)} relative group w-32 h-10 mx-auto flex items-center justify-center`}
                                >
                                  <div
                                    className={`font-bold ${getStatusTextColor(coin.rating)}`}
                                  >
                                    {coin.rating}/100
                                  </div>
                                  {isBest && (
                                    <motion.div
                                      initial={{ opacity: 0, scale: 0.8 }}
                                      animate={{ opacity: 1, scale: 1 }}
                                      transition={{ duration: 0.3 }}
                                      className="absolute -top-2 -right-2 h-5 w-5 bg-amber-500 rounded-full flex items-center justify-center"
                                    >
                                      <Trophy className="h-3 w-3 text-white" />
                                    </motion.div>
                                  )}
                                </div>
                              </TableCell>
                            );
                          })}

                          <TableCell className="text-center">
                            {(() => {
                              // Find the best rated coin
                              const bestRatedCoin = selectedCoins.reduce(
                                (prev, current) =>
                                  prev.rating > current.rating ? prev : current,
                                selectedCoins[0],
                              );

                              return (
                                <div className="flex items-center justify-center gap-1.5">
                                  <CoinLogo coin={bestRatedCoin} size="sm" />
                                  <span className="text-sm text-slate-300">
                                    {bestRatedCoin.symbol}
                                  </span>
                                </div>
                              );
                            })()}
                          </TableCell>
                        </>
                      ) : (
                        <>
                          <TableCell className="text-center">
                            <div className="text-slate-500 text-sm">
                              No data
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="text-slate-500 text-sm">
                              No winner
                            </div>
                          </TableCell>
                        </>
                      )}
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </TabsContent>

          {/* Metrics Tab Content */}
          <TabsContent value="metrics" className="mt-0">
            <CardContent>
              <div className="space-y-6">
                {/* Category Selection */}
                <div className="bg-slate-900/50 rounded-lg border border-slate-700/40 p-4">
                  <h3 className="text-lg font-medium text-white mb-3">
                    {t("categorySelection", "compare", "Category Selection")}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedCoins.length > 0 &&
                      (() => {
                        // Tüm coinler için mevcut score kategorilerini topla
                        const allCategories = new Set<string>();
                        selectedCoins.forEach((coin) => {
                          if (coin.scores && Array.isArray(coin.scores)) {
                            coin.scores.forEach((score) => {
                              allCategories.add(score.name);
                            });
                          }
                        });

                        // Convert Set to Array and sort if needed
                        return Array.from(allCategories);
                      })().map((categoryName) => (
                        <button
                          key={categoryName}
                          className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-300 ${
                            activeCategory === categoryName
                              ? "bg-blue-600 text-white"
                              : "bg-slate-800 text-slate-300 hover:bg-slate-700"
                          }`}
                          onClick={() => setActiveCategory(categoryName)}
                        >
                          {translateCategoryName(categoryName)}
                        </button>
                      ))}
                  </div>
                </div>

                {selectedCoins.length === 0 ? (
                  <div className="text-center py-12 text-slate-400">
                    <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-20" />
                    <p>{t("noCoinsSelected", "compare", "No coins selected for comparison")}</p>
                    <p className="text-sm mt-2">
                      {t("selectCoinsForMetrics", "compare", "Please select coins to view metrics")}
                    </p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table className="w-full">
                      <TableHeader className="bg-slate-900/40">
                        <TableRow className="py-3 h-16">
                          <TableHead className="text-left py-3">
                            {t("metric", "compare", "Metric")}
                          </TableHead>
                          {selectedCoins.map((coin) => (
                            <TableHead key={coin.id} className="text-center">
                              <div className="flex flex-col items-center">
                                <CoinLogo
                                  coin={coin}
                                  size="md"
                                  className="mb-1"
                                />
                                {coin.name}
                              </div>
                            </TableHead>
                          ))}
                          <TableHead className="text-center w-[120px]">
                            <div className="flex flex-col items-center">
                              <div className="h-6 w-6 bg-amber-500/80 rounded-full flex items-center justify-center mb-1">
                                <Trophy className="h-3 w-3 text-white" />
                              </div>
                              {t("winner", "compare", "Winner")}
                            </div>
                          </TableHead>
                        </TableRow>
                      </TableHeader>

                      <TableBody>
                        {/* Render metrics for selected category */}
                        {metrics.map((metricName) => {
                          const bestCoin = findBestCoinForMetric(metricName);
                          return (
                            <TableRow
                              key={metricName}
                              className="border-slate-700/40"
                            >
                              <TableCell className="font-medium">
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <div className="flex items-center gap-1 cursor-help">
                                        {translateCategoryName(metricName)}
                                        <Info className="h-4 w-4 text-slate-500" />
                                      </div>
                                    </TooltipTrigger>
                                    <TooltipContent
                                      side="right"
                                      className="max-w-sm bg-slate-900 border-slate-700 text-white"
                                    >
                                      <p>{getMetricDescription(metricName)}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </TableCell>

                              {/* Score for each coin */}
                              {selectedCoins.map((coin) => {
                                const score = getMetricScore(coin, metricName);
                                // Only show medal on the best coin
                                const isBest =
                                  bestCoin &&
                                  coin.id === bestCoin.id &&
                                  score > 0;

                                return (
                                  <TableCell
                                    key={`${coin.id}-${metricName}`}
                                    className="text-center"
                                  >
                                    <div
                                      className={`transition-all duration-300 px-3 py-2 rounded ${getBackgroundColor(
                                        score,
                                      )} relative group w-32 h-10 mx-auto flex items-center justify-center`}
                                    >
                                      <div
                                        className={`font-bold ${getStatusTextColor(
                                          score,
                                        )}`}
                                      >
                                        {score}/100
                                      </div>
                                      {isBest && (
                                        <motion.div
                                          initial={{ opacity: 0, scale: 0.8 }}
                                          animate={{ opacity: 1, scale: 1 }}
                                          transition={{ duration: 0.3 }}
                                          className="absolute -top-2 -right-2 h-5 w-5 bg-amber-500 rounded-full flex items-center justify-center"
                                        >
                                          <Medal className="h-3 w-3 text-white" />
                                        </motion.div>
                                      )}
                                    </div>
                                  </TableCell>
                                );
                              })}

                              {/* Winner column */}
                              <TableCell className="text-center">
                                {bestCoin && (
                                  <div className="flex items-center justify-center gap-1.5">
                                    <CoinLogo coin={bestCoin} size="sm" />
                                    <span className="text-sm text-slate-300">
                                      {bestCoin.symbol}
                                    </span>
                                  </div>
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            </CardContent>
          </TabsContent>
        </Tabs>
      </Card>
    </ConceptBaseLayout>
  );
}
